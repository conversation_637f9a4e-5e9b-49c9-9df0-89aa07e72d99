<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bonc.ioc.parent</groupId>
        <artifactId>mcp-project-parent-nacos-mysql</artifactId>
        <version>1.1.11-SNAPSHOT</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.bonc.ioc</groupId>
    <artifactId>bzf-system-busisigning</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>bzf-system-busisigning</name>
    <description>bzf-system-busisigning</description>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- 权限框架 分别有mcp-security-shiro 和 mcp-security-yh 和 mcp-security-none -->
        <!-- mcp-security-shiro 老框架中使用的 shiro -->
        <!-- mcp-security-yh 与炎黄对接 -->
        <!-- mcp-security-none 与不使用权限 -->
        <mcp-security.artifactId>mcp-security-yh</mcp-security.artifactId>
        <!-- 日志框架 分别有mcp-log-logback 和 mcp-log-log4j2 -->
        <mcp-log.artifactId>mcp-log-log4j2</mcp-log.artifactId>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <!--EasyExcel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.junrar</groupId>
            <artifactId>junrar</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.4.3</version>
        </dependency>
        <dependency>
            <groupId>com.sinovatech.rd.bms</groupId>
            <artifactId>bms-saas-api</artifactId>
            <version>1.0.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.18.0</version>
        </dependency>
        <!--  解决 Illegal DefaultValue null for parameter type integer    异常  -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.21</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.5.21</version>
        </dependency>
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.5.0-b01</version>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-ocr</artifactId>
            <version>3.1.871</version>
        </dependency>

        <!-- 炎黄 -->
        <dependency>
            <groupId>com.sinovatech.saas</groupId>
            <artifactId>saas-spec</artifactId>
            <version>1.4.1.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.sinovatech.saas</groupId>
            <artifactId>saas-api-base</artifactId>
            <version>1.4.1.1-RELEASE</version>
        </dependency>

        <!--汉字转拼音-->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>

    </dependencies>
    <repositories>
        <repository>
            <id>Public Repositories</id>
            <name>Maven Repository</name>
            <url>http://10.0.9.180:7083/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
                <!-- updataPolicy:配置maven从远程仓库检查更新的频率 -->
                <!-- 默认daily-maven每天检查一次 -->
                <!-- never-从不检查；-->
                <!-- always-每次构件都要检查更新；-->
                <!-- interval:X -每隔X分钟检查一次更新（X为整数） -->
                <updatePolicy>always</updatePolicy>
                <!-- checksumPolicy用来配置Maven检查校验和文件失败后的策略。 -->
                <!-- (默认值)warn-maven会执行构建时输出警告信息；-->
                <!-- fail-maven遇到校验和错处就让构建失败；-->
                <!-- ignore-使maven完全忽略校验和错误。 -->
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <!-- updataPolicy:配置maven从远程仓库检查更新的频率 -->
                <!-- 默认daily-maven每天检查一次 -->
                <!-- never-从不检查；-->
                <!-- always-每次构件都要检查更新；-->
                <!-- interval:X -每隔X分钟检查一次更新（X为整数） -->
                <updatePolicy>always</updatePolicy>
                <!-- checksumPolicy用来配置Maven检查校验和文件失败后的策略。 -->
                <!-- (默认值)warn-maven会执行构建时输出警告信息；-->
                <!-- fail-maven遇到校验和错处就让构建失败；-->
                <!-- ignore-使maven完全忽略校验和错误。 -->
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>

    <profiles>
        <profile>
            <id>default</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>license</id>
            <dependencies>
                <dependency>
                    <groupId>com.bonc.ioc</groupId>
                    <artifactId>mcp-license-client</artifactId>
                    <version>1.0.1-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <!-- 炎黄打包的参数 -->
        <profile>
            <id>yh</id>
            <properties>
                <mcp-security.artifactId>mcp-security-yh</mcp-security.artifactId>
                <mcp-log.artifactId>mcp-log-log4j2</mcp-log.artifactId>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <finalName>bzf-system-busisigning</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
           <!-- <plugin>
                <groupId>cn.smallbun.screw</groupId>
                <artifactId>screw-maven-plugin</artifactId>
                <version>1.0.5</version>
                <dependencies>
                    <dependency>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP</artifactId>
                        <version>3.4.5</version>
                    </dependency>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.20</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <username>bzf_manager</username>
                    <password>bzf@Bonc12#$</password>
                    <driverClassName>com.mysql.cj.jdbc.Driver</driverClassName>
                    <jdbcUrl>jdbc:mysql://**********:13306/bzf_system_busisigning</jdbcUrl>
                    <fileType>WORD</fileType>
                    <openOutputDir>false</openOutputDir>
                    <produceType>freemarker</produceType>
                    <description>数据库文档生成</description>
                    <version>${project.version}</version>
                    <title>数据库文档</title>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>-->
        </plugins>
    </build>

</project>
