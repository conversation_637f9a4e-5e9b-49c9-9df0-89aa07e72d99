# 查询账单列表接口

### 3.4. 查询账单列表接口



*   **对接人**：白荣兵

*   **发起方**：业务中台、PC 端或者 APP 端

*   **服务方**：计费中心

*   **url**：POST /charge/v1/bill/listByContract

*   **说明**：根据查询条件查询账单列表。

#### 请求参数



| 序号 | 字段名                   | 中文名称                    | 类型     | 必须 | 最大长度 | 说明                                                                                               |
| -- | --------------------- | ----------------------- | ------ | -- | ---- | ------------------------------------------------------------------------------------------------ |
| 1  | contractCode          | 合同 ID                   | String | 否  | 64   |                                                                                                  |
| 2  | projectName           | 项目名称                    | String | 否  | 200  |                                                                                                  |
| 3  | billId                | 账单 ID                   | String | 否  | 20   |                                                                                                  |
| 4  | accountingMonth       | 账单会计月                   | String | 否  | 128  |                                                                                                  |
| 5  | invoicingStatus       | 开票状态                    | String | 否  | 32   | 01 已开票02 未开票                                                                                     |
| 6  | buildingNo            | 楼栋                      | String | 否  | 200  |                                                                                                  |
| 7  | unitNo                | 单元                      | String | 否  | 200  |                                                                                                  |
| 8  | floorNo               | 所在楼层                    | String | 否  | 128  |                                                                                                  |
| 9  | roomNo                | 门牌号                     | String | 否  | 128  |                                                                                                  |
| 10 | houseNo               | 房源编号                    | String | 否  | 128  |                                                                                                  |
| 11 | certNo                | 证件号码                    | String | 否  | 128  | 当为企业的商业账单时为商租企业社会信用代码                                                                            |
| 12 | payableStartDate      | 应缴费开始日期                 | String | 否  | 128  |                                                                                                  |
| 13 | payableEndDate        | 应缴费结束日期                 | String | 否  | 128  |                                                                                                  |
| 14 | chargeSubjectNo       | 计费科目编号                  | String | 否  | 10   | 01 房屋租金02 押金03 家具家电租金04 车位租金05 仓储租金06 能源费07 物业费08 违约金09 损坏赔偿10 水费11 电费12 燃气费13 空置费15 保证金16 过期占用费 |
| 15 | tenantName            | 租户名称                    | String | 否  | 50   |                                                                                                  |
| 16 | status                | 账单状态列表（可多选，使用 “,” 分隔）   | List   | 否  | 32   | 01 正常02 冻结03 关闭                                                                                  |
| 17 | billStatus            | 账单缴费状态列表（可多选，使用 “,” 分隔） | List   | 否  | 10   | 01 已缴足额支付02 已缴部分支付03 未缴                                                                          |
| 18 | accountStatus         | 对账状态                    | String | 否  | 32   | 01 对齐02 未对齐03 账单关闭                                                                               |
| 19 | primaryChargeCode     | 主收款单 code               | String | 否  | 20   |                                                                                                  |
| 20 | calCurrentMonth       | 是否计算退租当月应退租金            | String | 否  | 32   | 01 是02 否                                                                                         |
| 21 | exitDate              | 租户实际退场日期                | String | 否  | 32   | 如果上面字段为是，该字段必填                                                                                   |
| 22 | fullPage              | 全量分页标识                  | String | 否  | 32   | 如果传 "true" 就是全量查，不传的话就是分页查                                                                       |
| 23 | tenantCode            | 租户 code                 | String | 否  | 128  |                                                                                                  |
| 24 | primaryChargeCodeFlag | 主收款单标识                  | String | 否  | 128  |                                                                                                  |
| 25 | chargeSubjectPeriod   | 收费科目当期期次                | String | 否  | 32   |                                                                                                  |
| 26 | chargeStartDate       | 收费科目开始时间                | String | 否  | 128  |                                                                                                  |
| 27 | chargeEndDate         | 收费科目结束时间                | String | 否  | 128  |                                                                                                  |
| 28 | isHavePreCharge       | 是否有预收                   | String | 否  | 10   | 01 无预收02 有预收                                                                                     |
| 29 | projectId             | 项目 ID                   | String | 是  | 64   | 业务中台的项目 ID                                                                                       |
| 30 | chargeOwner           | 个人 / 企业标识               | String | 是  | 10   | 01 企业，02 个人                                                                                      |
| 31 | billPayStatus         | 账单支付状态（可多选，使用 “,” 分隔）   | List   | 否  | 10   | 01 待支付02 支付中03 部分支付04 支付完成05 支付失败06 退款中07 部分退款08 退款完成09 退款失败                                     |
| 32 | houseName             | 房源名称                    | String | 否  |      |                                                                                                  |
| 33 | invoiceAbility        | 发票开具能力                  | String | 否  |      | 01 可开具02 不可开具                                                                                    |
| 34 | projectFormat         | 房屋实际用途                  | String | 否  |      | 01 公租房02 保租房03 商业04 共有产权房05 仓储 06 车位                                                             |
| 35 | serviceMoneyFlag      | 是否有增值服务费                | String | 否  |      | 01 否02 是                                                                                         |
| 36 | isWithhold            | 是否支持代扣                  | int    | 否  | 1    | 0: 不支持，1: 支持                                                                                     |
| 37 | chargeStartTime       | 收款开始时间                  | String | 否  | 128  |                                                                                                  |
| 38 | chargeEndTime         | 收款截止时间                  | String | 否  | 128  |                                                                                                  |
| 39 | payType               | 分摊方式                    | String | 否  |      | 1 按比例分摊2 按金额分摊3 企业全额支付4 个人全额支付                                                                   |
| 40 | leaseType             | 租赁类型                    | String | 否  |      | 01 散租02 趸租大合同03 趸租管理协议04 其他                                                                      |

#### 返回参数



| 序号 | 字段名                    | 中文名称        | 类型         | 最大长度 | 说明                                                                                         |   |
| -- | ---------------------- | ----------- | ---------- | ---- | ------------------------------------------------------------------------------------------ | - |
| 1  | billId                 | 账单 ID       | Long       |      |                                                                                            |   |
| 2  | contractCode           | 合同 ID       | String     |      |                                                                                            |   |
| 3  | tenantName             | 租户名称        | String     |      |                                                                                            |   |
| 4  | certNo                 | 证件号码        | String     |      |                                                                                            |   |
| 5  | tenantMobile           | 联系电话        | String     |      |                                                                                            |   |
| 6  | projectName            | 项目名称        | String     |      |                                                                                            |   |
| 7  | buildingNo             | 楼栋          | String     |      |                                                                                            |   |
| 8  | unitNo                 | 单元          | String     |      |                                                                                            |   |
| 9  | floorNo                | 所在楼层        | String     |      |                                                                                            |   |
| 10 | roomNo                 | 门牌号         | String     |      |                                                                                            |   |
| 11 | billChargeSubject      | 账单对应的收费科目   | String     |      | 01 房屋租金02 押金03 家具家电租金04 车位租金05 仓储租金07 物业费08 违约金09 损坏赔偿10 水费11 电费12 燃气费13 空置费15 保证金16 过期占用费 |   |
| 12 | billStatus             | 账单缴费状态      | String     |      | 01 已缴足额支付02 已缴部分支付03 未缴                                                                    |   |
| 13 | accountingMonth        | 账单会计月       | String     |      |                                                                                            |   |
| 14 | chargeSubjectPeriod    | 账单周期        | String     |      | 从 1 开始递增                                                                                   |   |
| 15 | payableDate            | 应缴费日期       | String     |      |                                                                                            |   |
| 16 | shouldPayAmount        | 应缴金额        | BigDecimal |      |                                                                                            |   |
| 17 | payedAmount            | 实缴金额        | BigDecimal |      |                                                                                            |   |
| 18 | replacePayAmount       | 待缴金额        | BigDecimal |      |                                                                                            |   |
| 19 | prepaymentAmount       | 预收金额        | BigDecimal |      |                                                                                            |   |
| 20 | targetAmount           | 收款金额        | BigDecimal |      |                                                                                            |   |
| 21 | changeShouldPayAmount  | 调整后应缴金额     | BigDecimal |      | 退租的时候，当月实际租金放这里                                                                            |   |
| 22 | chargeStandardCny      | 收款标准币种      | String     |      | CNY 人民币                                                                                    |   |
| 23 | chargeCycle            | 缴费周期        | String     |      | 01 月 02 季 03 半年 04 年                                                                       |   |
| 24 | isProvision            | 是否计提        | String     |      | 0 未计提1 已计提                                                                                 |   |
| 25 | chargeType             | 是否循环计费      | String     |      | 01 循环02 单次                                                                                 |   |
| 26 | subsidyRadio           | 补贴比例        | String     |      |                                                                                            |   |
| 27 | subsidyMoney           | 补贴金额        | String     |      |                                                                                            |   |
| 28 | status                 | 账单状态        | String     |      | 01 正常02 冻结03 关闭                                                                            |   |
| 29 | accountStatus          | 账单对账状态      | String     |      | 01 对齐02 未对齐03 账单关闭                                                                         |   |
| 30 | invoicingStatus        | 开票状态        | String     |      | 01 已开票02 未开票                                                                               |   |
| 31 | invoiceMoney           | 开票金额        | BigDecimal |      |                                                                                            |   |
| 32 | primaryChargeCode      | 关联收款单       | Long       |      |                                                                                            |   |
| 33 | chargeSubjectBeginDate | 收费科目起始日期    | String     |      |                                                                                            |   |
| 34 | chargeSubjectEndDate   | 收费科目终止日期    | String     |      |                                                                                            |   |
| 35 | billPayStatus          | 账单支付状态      | String     |      | 01 待支付02 支付中03 部分支付04 支付完成（足额支付）05 支付失败06 退款中07 部分退款08 退款完成09 退款失败                         |   |
| 36 | projectId              | 项目 ID       | String     | 是    | 64                                                                                         |   |
| 37 | deductionMoney         | 抵扣金额        | BigDecimal | 否    |                                                                                            |   |
| 38 | reletUnitName          | 趸租单位名称      | String     | 否    |                                                                                            |   |
| 39 | houseCount             | 签约房源套数      | Integer    | 否    |                                                                                            |   |
| 40 | payType                | 分摊方式        | String     | 否    | 1 按比例分摊2 按金额分摊3 企业全额支付4 个人全额支付                                                             |   |
| 41 | payRate                | 分摊比例        | BigDecimal | 否    |                                                                                            |   |
| 42 | payTime                | 成功支付时间      | String     | 否    | 系统优化新增字段 by hezhihao 2023/06/16                                                            |   |
| 43 | authorizedAgent        | 委托代理人       | String     | 是    |                                                                                            |   |
| 44 | authorizedAgentMobile  | 委托代理人电话     | String     | 是    |                                                                                            |   |
| 45 | projectFormat          | 房屋实际用途      | String     | 是    | 01 公租房02 保租房03 商业04 共有产权房05 仓储 06 车位                                                       |   |
| 46 | chargeStandardMoney    | 收款标准金额      | BigDecimal | 是    |                                                                                            |   |
| 47 | invoiceAbility         | 发票开具能力      | String     | 是    | 01 可开具02 不可开具                                                                              |   |
| 48 | chargeOwner            | 账单类别        | String     | 是    | 01 企业02 个人                                                                                 |   |
| 49 | invoiceMoney           | 开票金额        | BigDecimal | 是    |                                                                                            |   |
| 50 | payableInvoiceMoney    | 应开金额        | BigDecimal | 是    |                                                                                            |   |
| 51 | toBeInvoicedMoney      | 待开金额        | BigDecimal | 是    |                                                                                            |   |
| 52 | redFlushMoney          | 红冲金额        | BigDecimal | 是    |                                                                                            |   |
| 53 | refundMoney            | 退款金额        | BigDecimal | 是    |                                                                                            |   |
| 54 | serviceMoney           | 增值服务费       | BigDecimal | 是    |                                                                                            |   |
| 55 | leaseType              | 租赁类型        | String     | 是    | 01 散租02 趸租大合同03 趸租管理协议04 其他                                                                |   |
| 56 | isWithhold             | 是否支持代扣      | int        | 是    | 0: 不支持，1: 支持                                                                               |   |
| 57 | chargeTime             | 收款时间        | String     | 是    |                                                                                            |   |
| 58 | unevenMoney            | 未对平金额       | BigDecimal | 是    |                                                                                            |   |
| 59 | deductionStatus        | 抵扣状态        | String     | 是    | 01 抵扣成功02 冻结03 作废                                                                          |   |
| 60 | inDeductionMoney       | 被抵扣金额       | BigDecimal | 否    |                                                                                            |   |
| 61 | appReqNo               | APP 支付交易流水号 | String     | 否    |                                                                                            |   |

#### 应退房租计算公式

查询状态是（01 已缴足额支付）的房租账单，当月的应退房租等于当月应缴金额减去调整后应缴金额 changeShouldPayAmount。当月之后的应退房租取当月之后账单的预收金额 prepaymentAmount 之和。然后将当月的应退房租加上当月之后的应退房租加一起就是该租户的应退房租。

#### 应收房租结算公式

查询状态是（02 已缴部分支付 和 03 未缴）的房租账单，将所有账单的代缴金额累加在一起就是应收房租。

> （注：文档部分内容可能由 AI 生成）