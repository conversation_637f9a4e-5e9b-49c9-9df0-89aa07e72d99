# 缴费周期变更功能设计说明

## 概述
根据缴费周期变更功能需求，采用**主表字段扩展方案**，在合同变更主表 `bbs_contract_change` 中直接添加缴费周期变更相关字段。

## 设计方案选择

### 方案对比
| 方案 | 优点 | 缺点 | 选择 |
|------|------|------|------|
| 独立表方案 | 表职责单一、便于扩展 | 需要JOIN查询、数据一致性复杂 | ❌ |
| **主表字段方案** | **查询简单、数据一致性好、维护简单** | 主表字段增多 | ✅ **采用** |

### 选择理由
1. **业务逻辑合理**：缴费周期变更是合同变更的具体类型，属于合同变更的属性
2. **查询效率高**：避免JOIN查询，所有信息在一个表中
3. **数据一致性**：不存在主表和子表的数据同步问题
4. **维护简单**：只需要维护一个表的结构

## 数据库字段设计

### 在 bbs_contract_change 表中新增字段

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|-------|------|------|----------|--------|------|
| change_scope | varchar | 2 | YES | | 缴费周期变更范围（1-合同缴费周期变更，2-账期缴费周期变更） |
| payment_cycle_old | varchar | 10 | YES | | 变更前缴费周期（01-月付，02-季付，03-半年付，04-年付，05-两月付，06-四月付） |
| payment_cycle | varchar | 10 | YES | | 变更后缴费周期（01-月付，02-季付，03-半年付，04-年付，05-两月付，06-四月付） |
| bill_cycle_old | longtext | | YES | | 变更前账单周期描述（多个账单周期组合） |
| bill_cycle | longtext | | YES | | 变更后账单周期描述 |
| bill_cycle_start | datetime | | YES | | 变更后账单开始周期 |
| bill_cycle_end | datetime | | YES | | 变更后账单结束周期 |
| selected_bills_json | longtext | | YES | | 选择的账单信息JSON（当变更范围为2时使用） |

## 枚举定义

### 缴费周期变更范围（CHANGE_PAYMENT_CYCLE）
- 1：合同缴费周期变更
- 2：账期缴费周期变更

### 缴费周期类型（PAYMENT_CYCLE_CODE）
- 01：月付
- 02：季付
- 03：半年付
- 04：年付
- 05：两月付
- 06：四月付

## 功能特点

### 1. 字段复用机制
- 当 `change_type` 为缴费周期变更类型时，启用缴费周期相关字段
- 其他变更类型时，缴费周期字段为空，不影响业务逻辑

### 2. JSON存储选择账单
- 使用 `selected_bills_json` 字段存储选择的账单信息
- 当 `change_scope=2` (账期缴费周期变更) 时使用
- 灵活存储任意结构的账单数据

### 3. 类型转换机制
为了保证前端接口的兼容性，设计了完整的类型转换方案：

#### 存储时转换流程
```
前端选择账单 (List<BbpmBillManagementPageResultVo>)
    ↓ PaymentCycleBillConverter.convertToJsonVos()
转换为简化对象 (List<BbsPaymentCycleBillJsonVo>)
    ↓ JSON序列化
存储到数据库 (selected_bills_json字段)
```

#### 查询时转换流程
```
数据库查询 (selected_bills_json字段)
    ↓ JSON反序列化
解析为简化对象 (List<BbsPaymentCycleBillJsonVo>)
    ↓ PaymentCycleBillConverter.convertToFullVos()
重新查询完整数据 (List<BbpmBillManagementPageResultVo>)
    ↓ 返回给前端
前端接收标准格式数据
```

#### 转换工具类
- `PaymentCycleBillConverter`：提供完整的类型转换功能
- `BbsPaymentCycleBillJsonVo`：专门用于JSON序列化的轻量级对象

## 核心类结构

### 1. 主表Entity扩展
在 `BbsContractChangeEntity` 中添加缴费周期变更相关字段

### 2. 主表VO扩展
在 `BbsContractChangeVo` 中添加：
- 缴费周期变更基础字段
- 字典翻译字段
- 选择的账单列表（List<BbpmBillManagementPageResultVo>）

### 3. JSON存储VO
`BbsPaymentCycleBillJsonVo` - JSON存储专用VO类，只包含核心字段：
- billId（账单ID）
- billCode（账单编号）
- billName（账单名称）
- billCycle（账单周期）
- totalAmount（账单总金额）

### 4. 枚举类
- `PaymentCycleChangeScopeEnum` - 缴费周期变更范围枚举
- `PaymentCycleEnum` - 缴费周期枚举

## 实施方案

### SQL执行顺序
1. 执行 `sql/payment_cycle_change_fields.sql` 添加字段和索引

### 代码修改
1. 在主表Entity中添加缴费周期变更字段
2. 在主表VO中添加相关字段和验证
3. 保留枚举类和工具类
4. 更新相关的Service和Controller逻辑

## 设计优势

1. **查询性能优异**：所有数据在一个表中，避免JOIN查询
2. **数据一致性强**：不存在主表和子表的数据同步问题
3. **维护成本低**：只需要维护一个表的结构
4. **事务安全性**：所有变更信息在同一个事务中处理
5. **扩展性好**：可以继续在主表中添加其他变更类型的字段
6. **类型兼容**：前端使用标准账单VO，与现有系统完全兼容
7. **业务逻辑清晰**：缴费周期变更作为合同变更的属性，符合业务语义

## 使用示例

### 保存缴费周期变更
```java
// 1. 构建合同变更信息（包含缴费周期变更）
BbsContractChangeVo vo = new BbsContractChangeVo();
vo.setContractCode("CONTRACT001");
vo.setChangeType("PAYMENT_CYCLE_CHANGE"); // 变更类型为缴费周期变更

// 2. 设置缴费周期变更信息
vo.setChangeScope("2"); // 账期缴费周期变更
vo.setPaymentCycle("02"); // 变更为季付
vo.setSelectedBills(selectedBillList); // 前端选择的账单

// 3. 保存
// 在Service中会自动将selectedBills转换为JSON存储到selected_bills_json字段
service.save(vo);
```

### 查询缴费周期变更
```java
// 1. 查询合同变更（包含缴费周期变更信息）
BbsContractChangeVo vo = service.getById(ccId);

// 2. 自动转换
// 如果是缴费周期变更类型，selectedBills字段会自动从JSON转换为完整的账单对象列表
if ("PAYMENT_CYCLE_CHANGE".equals(vo.getChangeType())) {
    List<BbpmBillManagementPageResultVo> bills = vo.getSelectedBills();
    String changeScope = vo.getChangeScope();
    String newCycle = vo.getPaymentCycle();
}
```

## 总结

采用主表字段扩展方案，将缴费周期变更信息直接存储在合同变更主表中，实现了：
- **架构简化**：减少了表间关联的复杂性
- **性能提升**：避免了JOIN查询，提高查询效率
- **维护简便**：只需维护一个表的结构
- **业务合理**：符合缴费周期变更是合同变更属性的业务逻辑

这种设计既满足了功能需求，又保持了系统的简洁性和高效性。 