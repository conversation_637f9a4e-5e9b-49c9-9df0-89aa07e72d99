# 收款单查询接口

### 3.11. 收款单查询接口



*   **对接人**：项翼彪

*   **发起方**：业务中台、PC 端或者 APP 端

*   **服务方**：计费中心

*   **url**：


    *   POST /charge/v1/receipt/listByBill

    *   newurl：POST /charge/v2/receipt/listByBill（202402 新增，新接口优化了性能）

    *   统计金额：/charge/v1/receipt/chargeMoneyTotal

*   **说明**：根据账单 ID 返回收款单列表。

#### 请求参数



| 序号 | 字段名                           | 中文名称        | 类型       | 必须 | 最大长度 | 说明                                                                                    |
| -- | ----------------------------- | ----------- | -------- | -- | ---- | ------------------------------------------------------------------------------------- |
| 1  | contractId                    | 合同 ID       | String   | 否  | 64   |                                                                                       |
| 2  | billId                        | 账单 ID       | Long     | 否  | 20   |                                                                                       |
| 3  | projectName                   | 项目名称        | String   | 否  | 32   | 按项目分库，不支持项目名称查询                                                                       |
| 4  | residentialQuartersName       | 小区或楼宇名称     | String   | 否  | 32   |                                                                                       |
| 5  | buildingNo                    | 楼栋          | String   | 否  | 32   |                                                                                       |
| 6  | unitNo                        | 单元          | String   | 否  | 32   |                                                                                       |
| 7  | floorNo                       | 所在楼层        | String   | 否  | 32   |                                                                                       |
| 8  | roomNo                        | 门牌号         | String   | 否  | 32   |                                                                                       |
| 9  | tenantName                    | 租户 / 组织名称   | String   | 否  | 32   |                                                                                       |
| 10 | certNo                        | 租户证件号码      | String   | 否  | 32   |                                                                                       |
| 11 | chargeCode                    | 主收款单唯一标识码   | Long     | 否  | 20   |                                                                                       |
| 12 | chargeSubject                 | 收费科目        | String   | 否  | 38   |                                                                                       |
| 13 | voucherStatus                 | 收款单是否生成正式凭证 | String   | 否  | 38   |                                                                                       |
| 14 | accountingMonth               | 收款单会计月      | String   | 否  | 8    |                                                                                       |
| 15 | payerName                     | 缴费人姓名       | String   | 否  | 32   |                                                                                       |
| 16 | chargeStatus                  | 收款单状态       | String   | 否  | 38   | 01、 正常02、 冻结03、 关闭                                                                    |
| 17 | reconciliationStatus          | 对账状态        | String   | 否  | 2    |                                                                                       |
| 18 | operatorMame                  | 制单人姓名       | String   | 否  | 32   |                                                                                       |
| 19 | chargeDateStart               | 收款日期开始      | DateTime | 否  |      |                                                                                       |
| 20 | chargeDateEnd                 | 收款日期结束      | DateTime | 否  |      |                                                                                       |
| 21 | prepaymentOffsetCode          | 预交冲抵单唯一标识码  | Long     | 否  | 20   |                                                                                       |
| 22 | current                       | 页码          | String   | 是  | 100  |                                                                                       |
| 23 | size                          | 每页显示多少条     | String   | 是  | 100  |                                                                                       |
| 24 | fullPage                      | 全量分页标识      | String   | 否  | 32   |                                                                                       |
| 25 | houseName                     | 房源名称        | String   | 否  | 100  |                                                                                       |
| 26 | optType                       | 查询类型        | String   | 否  | 32   | 查询类型取值范围：01、02。当 optType="01" 时为详情查询，optType="02" 时为列表查询。optType 不传或者为空字符串时默认 01 详情查询 |
| 27 | fullPage                      | 是否全量查询      | String   | 否  | 4    | 详情查询时传递该字段，取值 fullPage="Y"，列表查询时请勿使用该字段                                               |
| 28 | (新增) projectId                | 项目 ID       | String   | 是  | 64   | 业务中台 ID                                                                               |
| 29 | (419 版新增) paymentType         | 支付方式        | String   | 否  | 4    | paymentType 取值 01、02、03、04 、05。（01 - 银行卡代扣02-pos 机03-app 支付04 - 现金支付05 - 对公转账）        |
| 30 | (5 月 31 日新增 by hezhihao)owner | 个人 / 企业标识   | String   | 否  | 10   | 01 企业02 个人                                                                            |
| 31 | projectFormatList             | 房屋实际用途      | List     | 否  |      | 01 公租房、02 保租房、03 商业、04 共有产权房、05 仓储、06 车位2023.09.25 新增                                 |

#### 返回参数



| 序号 | 字段名                           | 中文名称          | 类型         | 最大长度 | 说明                                                                           |
| -- | ----------------------------- | ------------- | ---------- | ---- | ---------------------------------------------------------------------------- |
| 1  | projectName                   | 项目名称          | String     | 32   |                                                                              |
| 2  | residentialQuartersName       | 小区 / 楼宇名称     | String     | 32   |                                                                              |
| 3  | buildingNO                    | 楼号            | String     | 32   |                                                                              |
| 4  | unitNo                        | 单元号           | String     | 32   |                                                                              |
| 5  | floorNo                       | 所在楼层          | String     | 32   |                                                                              |
| 6  | roomNo                        | 房间号           | String     | 32   |                                                                              |
| 7  | address                       | 房源地址          | String     | 200  |                                                                              |
| 8  | tenantName                    | 租户 / 组织名称     | String     | 32   |                                                                              |
| 9  | certNo                        | 客户证件号码        | String     | 32   |                                                                              |
| 10 | tenantMobile                  | 客户联系电话        | String     | 32   |                                                                              |
| 11 | operatorName                  | 制单人           | String     | 32   |                                                                              |
| 12 | chargeCode                    | 收款单唯一标识码      | Long       | 20   |                                                                              |
| 13 | contractCode                  | 合同唯一标识码       | String     | 32   |                                                                              |
| 14 | chargeDate                    | 收款单日期         | DateTime   |      |                                                                              |
| 15 | chargeStatus                  | 收款单状态         | String     | 2    |                                                                              |
| 16 | bankReceiptNo                 | 银行回单号         | String     | 32   |                                                                              |
| 17 | bankReceiptDate               | 银行回单日期        | date       |      |                                                                              |
| 18 | chargeSubject                 | 收费科目          | String     | 38   | 如果有多个，拼接返回。如：01;02                                                           |
| 19 | paymentType                   | 支付方式          | String     | 38   | 01 银行卡代扣02 pos 机03 app 支付04 现金支付05 对公转账                                      |
| 20 | chargeMoney                   | 收款金额          | Decimal    | 20,4 |                                                                              |
| 21 | serFee                        | 手续费金额         | Decimal    | 20,4 |                                                                              |
| 22 | reconciliationStatus          | 收款单对账状态       | String     | 2    |                                                                              |
| 23 | isPrePaymentOffset            | 是否进行预交冲抵      | String     | 2    | 是 / 否                                                                        |
| 24 | Bank                          | 收款银行          | String     | 32   |                                                                              |
| 25 | bankBranch                    | 收款银行支行        | String     | 32   |                                                                              |
| 26 | interBankNo                   | 收款银行联号        | String     | 32   |                                                                              |
| 27 | bankAccountName               | 收款银行户名        | String     | 32   |                                                                              |
| 28 | bankAccountNo                 | 收款银行账号        | String     | 32   |                                                                              |
| 29 | cny                           | 币种            | String     | 32   |                                                                              |
| 30 | bankAccountType               | 账户性质          | String     | 32   |                                                                              |
| 31 | paymentChannel                | 支付渠道          | String     | 32   |                                                                              |
| 32 | chargeDate                    | 支付时间          | DateTime   |      |                                                                              |
| 33 | payerName                     | 缴费人姓名         | String     | 32   |                                                                              |
| 34 | paymentBank                   | 缴费银行          | String     | 32   |                                                                              |
| 35 | paymentBankAccountNo          | 缴费银行账户        | String     | 32   |                                                                              |
| 36 | paymentBankBranchCode         | 缴费银行支行编码      | String     | 32   |                                                                              |
| 37 | receipt                       | 主收款单 ID       | Long       | 20   |                                                                              |
| 38 | billChargeSubject             | 账单对应的收费科目     | String     | 10   | 01 房屋租金02 押金03 家具家电租金04 车位租金05 仓库租金07 物业费08 违约金09 损坏赔偿10 水费11 电费12 燃气费13 空置费 |
| 39 | chargeSubjectBeginDate        | 收费科目起始日期      | Date       |      |                                                                              |
| 40 | chargeSubjectEndDate          | 收费科目终止日期      | Date       |      |                                                                              |
| 41 | shouldPayAmount               | 应缴金额          | BigDecimal | 13,4 |                                                                              |
| 42 | payedAmount                   | 实缴金额          | BigDecimal | 13,4 |                                                                              |
| 43 | payChannel                    | 支付方式          | String     | 4    | 01 银行卡代扣02 pos 机03 app 支付04 现金支付05 对公转账                                      |
| 44 | PayTime                       | 支付时间          | Time       |      |                                                                              |
| 45 | posDeviceId                   | POS 设备号       | String     | 20   |                                                                              |
| 46 | reconciliationResult          | 对账结果          | String     | 4    | 01 对平02 未对平03 待对账                                                            |
| 47 | houseName                     | 房源名称单独字段      | String     | 100  |                                                                              |
| 48 | (新增) projectId                | 项目 ID         | String     | 32   |                                                                              |
| 49 | (5 月 31 日新增 by hezhihao)owner | 个人 / 企业标识     | String     | 10   | 01 企业02 个人                                                                   |
| 50 | checkNo                       | 支票编号          | String     | 64   |                                                                              |
| 51 | checkAttachmentId             | 支票附件 ID       | String     | 300  |                                                                              |
| 52 | projectFormat                 | 房屋实际用途        | String     | 38   | 01 公租房、02 保租房、03 商业、04 共有产权房、05 仓储、06 车位                                     |
| 53 | （20240116 新增）posRefeNo        | POS 机检索参考号    | String     | 8    |                                                                              |
| 54 | （20240116 新增）posTermId        | POS 机终端号      | String     | 15   | 3 位或 15 位                                                                    |
| 55 | （20240123 新增）paymentNature    | 支付类型          | String     | 2    | 01 - 支付宝支付02 - 微信支付03 - 银联支付04 - 现金支付05 - 工银 E 支付06 - 支票支付07 - 对公转账          |
| 56 | room(202402 新增 - 仅 v2 接口支持)   | 停车属性仅 v2 接口支持 | Object     |      | 停车相关属性 key 目前仅供参考后续可能会变更                                                     |
| 57 | room.houseName                | 车位区域          | String     |      |                                                                              |
| 58 | room.roomType                 | 车场所在位置 code   | String     |      | 01 - 地上；02 - 地下                                                              |
| 59 | room.roomTypeName             | 车场所在位置名称      | String     |      | 01 - 地上；02 - 地下                                                              |
| 60 | room.floorNo                  | 车位楼层          | String     |      |                                                                              |
| 61 | room.houseNo                  | 车位编号          | String     |      |                                                                              |
| 62 | room.parkPropertyType         | 车位资产类型        | String     |      | 01 - 地下资产；02 - 人防车位；03 - 地面                                                  |
| 63 | chargeStartEndDate            | 账期            | String     |      | 如果有多个，拼接返回。如：2022.02.05-2023.02.0;2024.02.05-2025.02.0                       |
| 64 | multiProjectFlag              | 回单是否多项目认款     | String     |      | （01 - 是；02 - 否）                                                              |
| 65 | backFlag                      | 回退状态          | String     |      | 01 - 已回退；02 - 正常                                                             |
| 66 | backReason                    | 回退原因          | String     |      |                                                                              |
| 67 | backTime                      | 回退时间          | String     |      |                                                                              |
| 68 | actualChargeMoney             | 实收金额          | BigDecimal |      |                                                                              |
| 69 | preChargeMoney                | 预收金额          | BigDecimal |      |                                                                              |

> （注：文档部分内容可能由 AI 生成）