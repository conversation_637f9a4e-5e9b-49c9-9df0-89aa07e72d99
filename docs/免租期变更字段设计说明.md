# 免租期变更字段设计说明

## 字段设计

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `free_rent_periods_old` | LONGTEXT | 变更前免租期（JSON数组） |
| `free_period_type_old` | VARCHAR(50) | 变更前免租期分类 |
| `free_rent_start` | VARCHAR(50) | 变更后开始免租期 |
| `free_rent_end` | VARCHAR(50) | 变更后结束免租期 |
| `free_rent_refund_method` | VARCHAR(2) | 免租期退/缴方式 |

## JSON格式示例
```json
[
  {
    "start": "2024-03-05",
    "end": "2024-04-04",
    "days": 30,
    "description": "2024-03-05至2024-04-04，30天"
  }
]
```

## VO类字段
```java
// 变更前免租期
private String freeRentPeriodsOld;
private String freePeriodTypeOld;

// 变更后免租期  
private String freeRentStart;
private String freeRentEnd;

@McpDictPoint(dictType = "REFUND_METHOD")
private String freeRentRefundMethod;
private String freeRentRefundMethodName;
```

## 字典配置
```sql
-- 退/缴方式字典
INSERT INTO mcp_dict (dict_type, dict_code, dict_value, meaning, enabled_flag) VALUES
('REFUND_METHOD', '2', '2', '退回银行卡', '1'),
('REFUND_METHOD', '3', '3', '抵扣租金', '1');
``` 