# 合同变更主表字段添加指南

## 概述
本指南说明如何在合同变更主表 `bbs_contract_change` 中添加缴费周期变更相关字段，实现方案二的主表字段扩展方案。

## 数据库字段添加

### 1. 执行SQL脚本
执行 `sql/payment_cycle_change_fields.sql` 文件，在主表中添加8个缴费周期变更字段：

```sql
-- 缴费周期变更范围
ALTER TABLE `bbs_contract_change` ADD COLUMN `change_scope` varchar(2) COMMENT '缴费周期变更范围（1-合同缴费周期变更，2-账期缴费周期变更）';

-- 缴费周期字段
ALTER TABLE `bbs_contract_change` ADD COLUMN `payment_cycle_old` varchar(10) COMMENT '变更前缴费周期';
ALTER TABLE `bbs_contract_change` ADD COLUMN `payment_cycle` varchar(10) COMMENT '变更后缴费周期';

-- 账单周期描述字段
ALTER TABLE `bbs_contract_change` ADD COLUMN `bill_cycle_old` longtext COMMENT '变更前账单周期描述';
ALTER TABLE `bbs_contract_change` ADD COLUMN `bill_cycle` longtext COMMENT '变更后账单周期描述';

-- 账单周期时间字段
ALTER TABLE `bbs_contract_change` ADD COLUMN `bill_cycle_start` datetime COMMENT '变更后账单开始周期';
ALTER TABLE `bbs_contract_change` ADD COLUMN `bill_cycle_end` datetime COMMENT '变更后账单结束周期';

-- 选择账单JSON字段
ALTER TABLE `bbs_contract_change` ADD COLUMN `selected_bills_json` longtext COMMENT '选择的账单信息JSON';
```

## Entity类修改

### 在 BbsContractChangeEntity 中添加字段

```java
// 在现有的 BbsContractChangeEntity 类中添加以下字段：

/**
 * 缴费周期变更范围（1-合同缴费周期变更，2-账期缴费周期变更）
 */
@ApiModelProperty(value = "缴费周期变更范围（1-合同缴费周期变更，2-账期缴费周期变更）")
private String changeScope;

/**
 * 变更前缴费周期（01-月付，02-季付，03-半年付，04-年付，05-两月付，06-四月付）
 */
@ApiModelProperty(value = "变更前缴费周期（01-月付，02-季付，03-半年付，04-年付，05-两月付，06-四月付）")
private String paymentCycleOld;

/**
 * 变更后缴费周期（01-月付，02-季付，03-半年付，04-年付，05-两月付，06-四月付）
 */
@ApiModelProperty(value = "变更后缴费周期（01-月付，02-季付，03-半年付，04-年付，05-两月付，06-四月付）")
private String paymentCycle;

/**
 * 变更前账单周期描述（多个账单周期组合）
 */
@ApiModelProperty(value = "变更前账单周期描述（多个账单周期组合）")
private String billCycleOld;

/**
 * 变更后账单周期描述
 */
@ApiModelProperty(value = "变更后账单周期描述")
private String billCycle;

/**
 * 变更后账单开始周期
 */
@ApiModelProperty(value = "变更后账单开始周期")
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
private Date billCycleStart;

/**
 * 变更后账单结束周期
 */
@ApiModelProperty(value = "变更后账单结束周期")
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
private Date billCycleEnd;

/**
 * 选择的账单信息JSON（当变更范围为2时使用）
 */
@ApiModelProperty(value = "选择的账单信息JSON（当变更范围为2时使用）")
private String selectedBillsJson;

// 对应的 Getter 和 Setter 方法
// ...getter/setter methods...

// 在 toString() 方法中添加这些字段
```

### 字段常量定义
```java
// 在 BbsContractChangeEntity 中添加字段常量
public static final String FIELD_CHANGE_SCOPE = "change_scope";
public static final String FIELD_PAYMENT_CYCLE_OLD = "payment_cycle_old";
public static final String FIELD_PAYMENT_CYCLE = "payment_cycle";
public static final String FIELD_BILL_CYCLE_OLD = "bill_cycle_old";
public static final String FIELD_BILL_CYCLE = "bill_cycle";
public static final String FIELD_BILL_CYCLE_START = "bill_cycle_start";
public static final String FIELD_BILL_CYCLE_END = "bill_cycle_end";
public static final String FIELD_SELECTED_BILLS_JSON = "selected_bills_json";
```

## VO类修改

### 在 BbsContractChangeVo 中添加字段

```java
// 在现有的 BbsContractChangeVo 类中添加以下字段：

@ApiModelProperty(value = "缴费周期变更范围（1-合同缴费周期变更，2-账期缴费周期变更）")
@McpDictPoint(dictCode = "CHANGE_PAYMENT_CYCLE", overTransCopyTo = "changeScopeName")
private String changeScope;

@ApiModelProperty(value = "缴费周期变更范围名称")
private String changeScopeName;

@ApiModelProperty(value = "变更前缴费周期（01-月付，02-季付，03-半年付，04-年付，05-两月付，06-四月付）")
@McpDictPoint(dictCode = "PAYMENT_CYCLE_CODE", overTransCopyTo = "paymentCycleOldName")
private String paymentCycleOld;

@ApiModelProperty(value = "变更前缴费周期名称")
private String paymentCycleOldName;

@ApiModelProperty(value = "变更后缴费周期（01-月付，02-季付，03-半年付，04-年付，05-两月付，06-四月付）")
@McpDictPoint(dictCode = "PAYMENT_CYCLE_CODE", overTransCopyTo = "paymentCycleName")
private String paymentCycle;

@ApiModelProperty(value = "变更后缴费周期名称")
private String paymentCycleName;

@ApiModelProperty(value = "变更前账单周期描述（多个账单周期组合）")
private String billCycleOld;

@ApiModelProperty(value = "变更后账单周期描述")
private String billCycle;

@ApiModelProperty(value = "变更后账单周期开始工银")
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
private Date billCycleStart;

@ApiModelProperty(value = "变更后账单周期结束工银")
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
private Date billCycleEnd;

@ApiModelProperty(value = "选择的账单信息JSON（当变更范围为2时使用）")
private String selectedBillsJson;

@ApiModelProperty(value = "选择的账单列表（当变更范围为2时必填）")
private List<BbpmBillManagementPageResultVo> selectedBills;

// 对应的 Getter 和 Setter 方法
// ...getter/setter methods...
```

### 验证注解（根据业务需要添加）
```java
// 如果是缴费周期变更类型，可以添加相应的验证注解
@NotBlank(message = "变更后缴费周期不能为空", groups = {PaymentCycleValidatorGroup.class})
private String paymentCycle;

@NotBlank(message = "缴费周期变更范围不能为空", groups = {PaymentCycleValidatorGroup.class})
private String changeScope;
```

## Service层修改

### 数据转换逻辑

```java
// 在保存时，将 selectedBills 转换为 JSON 存储
public void saveContractChange(BbsContractChangeVo vo) {
    // 如果是缴费周期变更且选择了账单
    if (isPaymentCycleChange(vo.getChangeType()) && 
        PaymentCycleChangeScopeEnum.isBillPaymentCycleChange(vo.getChangeScope()) &&
        !CollectionUtils.isEmpty(vo.getSelectedBills())) {
        
        // 转换为JSON存储
        String jsonString = PaymentCycleBillConverter.convertToJson(vo.getSelectedBills());
        vo.setSelectedBillsJson(jsonString);
    }
    
    // 保存到数据库
    // ...save logic...
}

// 在查询时，将 JSON 转换为 selectedBills
public BbsContractChangeVo getById(String ccId) {
    BbsContractChangeVo vo = // ...query logic...
    
    // 如果是缴费周期变更且有JSON数据
    if (isPaymentCycleChange(vo.getChangeType()) && 
        StringUtils.hasText(vo.getSelectedBillsJson())) {
        
        // 转换JSON为完整的账单信息
        List<BbpmBillManagementPageResultVo> bills = 
            PaymentCycleBillConverter.convertJsonToFullBillInfo(vo.getSelectedBillsJson());
        vo.setSelectedBills(bills);
    }
    
    return vo;
}

// 判断是否为缴费周期变更类型
private boolean isPaymentCycleChange(String changeType) {
    return "PAYMENT_CYCLE_CHANGE".equals(changeType);
}
```

## Controller层修改

### 在现有Controller中添加缴费周期变更逻辑

```java
// 在 BbsiContractChangeV2Controller 中，现有的方法可以直接处理缴费周期变更
// 因为字段已经在主表VO中，不需要额外的接口

// 可以添加一些特定的验证方法
@GetMapping("/validatePaymentCycleChange")
public AppReply<String> validatePaymentCycleChange(@RequestParam String contractCode,
                                                   @RequestParam String changeScope) {
    // 验证缴费周期变更的业务逻辑
    // ...validation logic...
    return AppReply.success("验证通过");
}
```

## 字典配置

### 需要在系统字典中配置以下项目：

#### CHANGE_PAYMENT_CYCLE（缴费周期变更范围）
```
1 - 合同缴费周期变更
2 - 账期缴费周期变更
```

#### PAYMENT_CYCLE_CODE（缴费周期类型）
```
01 - 月付
02 - 季付  
03 - 半年付
04 - 年付
05 - 两月付
06 - 四月付
```

## 测试验证

### 1. 数据库字段验证
```sql
-- 验证字段是否添加成功
DESCRIBE bbs_contract_change;
```

### 2. 功能测试
- 测试合同缴费周期变更（change_scope=1）
- 测试账期缴费周期变更（change_scope=2）
- 测试JSON字段的存储和查询
- 测试类型转换功能

## 注意事项

1. **向后兼容性**：新添加的字段都是可空字段，不影响现有数据
2. **性能考虑**：longtext字段较大，查询时注意性能
3. **数据一致性**：确保selectedBills和selectedBillsJson的数据一致性
4. **事务处理**：相关的业务操作应在同一事务中执行
5. **字典配置**：确保相关字典项目已正确配置

## 实施步骤建议

1. **第一步**：执行SQL脚本添加字段
2. **第二步**：修改Entity和VO类
3. **第三步**：配置系统字典
4. **第四步**：修改Service层逻辑
5. **第五步**：测试验证功能
6. **第六步**：更新相关文档

通过以上步骤，可以成功将缴费周期变更功能集成到合同变更主表中，实现方案二的架构设计。 