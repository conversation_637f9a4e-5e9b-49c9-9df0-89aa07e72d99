# 免租期变更代码集成示例

## Entity类字段
```java
// 变更前免租期
private String freeRentPeriodsOld;
private String freePeriodTypeOld;

// 变更后免租期
private String freeRentStart;
private String freeRentEnd;
private String freeRentRefundMethod;
```

## VO类字段
```java
// 变更前免租期
private String freeRentPeriodsOld;
private String freePeriodTypeOld;

// 变更后免租期  
private String freeRentStart;
private String freeRentEnd;

@McpDictPoint(dictType = "REFUND_METHOD")
private String freeRentRefundMethod;
private String freeRentRefundMethodName;
```

## JSON转换示例
```java
// 存储变更前免租期
List<Map<String, Object>> periods = new ArrayList<>();
Map<String, Object> period = new HashMap<>();
period.put("start", "2024-03-05");
period.put("end", "2024-04-04");
period.put("days", 30);
period.put("description", "2024-03-05至2024-04-04，30天");
periods.add(period);

vo.setFreeRentPeriodsOld(JSON.toJSONString(periods));

// 设置退/缴方式
vo.setFreeRentRefundMethod("2"); // 退回银行卡
// 或
vo.setFreeRentRefundMethod("3"); // 抵扣租金
```

## 变更类型枚举
```java
FREE_RENT_PERIOD_CHANGE("29", "免租期变更")
```

## 字典配置
```sql
-- 退/缴方式字典
INSERT INTO mcp_dict (dict_type, dict_code, dict_value, meaning, enabled_flag) VALUES
('REFUND_METHOD', '2', '2', '退回银行卡', '1'),
('REFUND_METHOD', '3', '3', '抵扣租金', '1');
```

## Service集成示例
```java
// 在setBerforeInformationOld方法中增加免租期变更处理
if (changeTypeItem.contains(ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode())) {
    if (StringUtils.isBlank(vo.getFreeRentPeriodsOld())) {
        setFreeRentPeriodChangeInformation(vo, bbctContractManagementVo);
    }
}

// 在getPreviewBillByVo方法中增加免租期变更的试算处理
boolean isFreeRentPeriodChange = changeTypeItem.contains(ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode());
if (isFreeRentPeriodChange) {
    // 免租期变更试算逻辑
    BbsChangePreviewBillResultVo bills = AbstractContractChangeFactory.getInstance(vo).getPreviewBills();
    resultVo.setPreviewBillResultVo(bills);
    return resultVo;
}

// 设置免租期变更前信息方法示例
private void setFreeRentPeriodChangeInformation(BbsiContractChangeVo vo, BbctContractManagementVo bbctContractManagementVo) {
    // 从合同扩展信息中获取免租期信息
    ContractOtherInfo contractOtherInfo = JsonToObjectUtil.jsonToContractOtherInfo(bbctContractManagementVo.getContractExtend());
    if (contractOtherInfo != null) {
        List<FreeSectionVo> freeRentSections = contractOtherInfo.getFreeRentSections();
        if (freeRentSections != null && !freeRentSections.isEmpty()) {
            // 转换为JSON格式存储
            vo.setFreeRentPeriodsOld(JSON.toJSONString(freeRentSections));
            vo.setFreePeriodTypeOld(contractOtherInfo.getFreePeriodType());
        }
    }
}
``` 