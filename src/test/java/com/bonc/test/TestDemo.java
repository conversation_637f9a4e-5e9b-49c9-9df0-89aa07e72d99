package com.bonc.test;

import com.alibaba.druid.filter.config.ConfigTools;

import com.bonc.ioc.common.base.mybatisplus.generator.CodeGeneration;
import com.bonc.ioc.common.util.SpringContextUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.List;

/**
 * test demo
 *
 * <AUTHOR>
 * @date 2021/5/20 10:35
 * @change: 2021/5/20 10:35 by jin.xu for init
 */
public class TestDemo extends AbstractServiceTest {

    /**
     * 公钥
     */
    @Value("${public-key}")
    private String publicKey;

    /**
     * 密码
     */
    @Value("${spring.datasource.password}")
    private String password;

    /**
     * 解密
     *
     * @throws Exception
     */
//    @Test
    public void decrypt() throws Exception {
        System.out.println("publicKey : " + publicKey);
        System.out.println("password : " + ConfigTools.decrypt(ConfigTools.getPublicKey(publicKey), password));
    }

    /**
     * 加密
     *
     * @throws Exception
     */
//    @Test
    public void encrypt() throws Exception {


    }

//    @Test
    public void dataBasePasswordEncode() throws Exception {

    }

//    @Test
    public void codeGeneration() {
        CodeGeneration codeGeneration = SpringContextUtils.getBean(CodeGeneration.class);

        List<String> tableNameList = new ArrayList<>();
        tableNameList.add("bbs_renewal_template_seat");
        String packageName = "com.bonc.ioc.bzf.busisigning";
        String author = "King-Y";
        codeGeneration.processMysqlBase(author, tableNameList, packageName);
    }
}
