<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsChangePayableDateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsChangePayableDateEntity">
                            <id column="date_id" property="dateId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="cc_id" property="ccId" javaType="String"/>
                            <result column="bill_id" property="billId" javaType="String"/>
                            <result column="contract_code" property="contractCode" javaType="String"/>
                            <result column="house_name" property="houseName" javaType="String"/>
                            <result column="payable_date" property="payableDate" javaType="String"/>
                            <result column="payable_date_old" property="payableDateOld" javaType="String"/>

        <result column="charge_subject_period" property="chargeSubjectPeriod" javaType="String"/>
        <result column="charge_subject_begin_date" property="chargeSubjectBeginDate" javaType="String"/>
        <result column="charge_subject_end_date" property="chargeSubjectEndDate" javaType="String"/>
        <result column="bill_charge_subject" property="replacePayAmount" javaType="String"/>
        <result column="replace_pay_amount" property="billStatus" javaType="String"/>
        <result column="bill_status" property="billChargeSubject" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangePayableDatePageResultVo">
                        <result column="cc_id" property="ccId" javaType="String"/>
                        <result column="bill_id" property="billId" javaType="String"/>
                        <result column="contract_code" property="contractCode" javaType="String"/>
                        <result column="house_name" property="houseName" javaType="String"/>
                        <result column="payable_date" property="payableDate" javaType="String"/>
                        <result column="payable_date_old" property="payableDateOld" javaType="String"/>
        <result column="charge_subject_period" property="chargeSubjectPeriod" javaType="String"/>
        <result column="charge_subject_begin_date" property="chargeSubjectBeginDate" javaType="String"/>
        <result column="charge_subject_end_date" property="chargeSubjectEndDate" javaType="String"/>
        <result column="bill_charge_subject" property="replacePayAmount" javaType="String"/>
        <result column="replace_pay_amount" property="billStatus" javaType="String"/>
        <result column="bill_status" property="billChargeSubject" javaType="String"/>
    </resultMap>

    <resultMap id="voMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangePayableDateVo">
        <result column="cc_id" property="ccId" javaType="String"/>
        <result column="bill_id" property="billId" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="house_name" property="houseName" javaType="String"/>
        <result column="payable_date" property="payableDate" javaType="String"/>
        <result column="payable_date_old" property="payableDateOld" javaType="String"/>
        <result column="charge_subject_period" property="chargeSubjectPeriod" javaType="String"/>
        <result column="charge_subject_begin_date" property="chargeSubjectBeginDate" javaType="String"/>
        <result column="charge_subject_end_date" property="chargeSubjectEndDate" javaType="String"/>
        <result column="bill_charge_subject" property="replacePayAmount" javaType="String"/>
        <result column="replace_pay_amount" property="billStatus" javaType="String"/>
        <result column="bill_status" property="billChargeSubject" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.date_id
        ,base.cc_id
        ,base.bill_id
        ,base.contract_code
        ,base.house_name
        ,base.payable_date
        ,base.payable_date_old
            ,base.charge_subject_period
            ,base.charge_subject_begin_date
             ,base.charge_subject_end_date
             ,base.bill_charge_subject
             ,base.replace_pay_amount
             ,base.bill_status
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_payable_date base
        <where>
            <if test="'' != vo.dateId and vo.dateId != null">
                and base.date_id = #{vo.dateId}
            </if>
            <if test="'' != vo.ccId and vo.ccId != null">
                and base.cc_id = #{vo.ccId}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.houseName and vo.houseName != null">
                and base.house_name = #{vo.houseName}
            </if>
            <if test="'' != vo.payableDate and vo.payableDate != null">
                and base.payable_date = #{vo.payableDate}
            </if>
            <if test="'' != vo.payableDateOld and vo.payableDateOld != null">
                and base.payable_date_old = #{vo.payableDateOld}
            </if>
        </where>
    </select>


    <select id="selectByCcId" resultMap="voMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_payable_date base
        where base.del_flag = '1' and base.cc_id = #{ccId}
    </select>


    <delete id="deleteByCcId">
        DELETE FROM bbs_change_payable_date
        WHERE cc_id = #{ccId};
    </delete>
</mapper>
