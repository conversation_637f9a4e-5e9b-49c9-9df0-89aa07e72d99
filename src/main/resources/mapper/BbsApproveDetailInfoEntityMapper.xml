<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsApproveDetailInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsApproveDetailInfoEntity">
                            <id column="approve_id" property="approveId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="approve_detail_id" property="approveDetailId" javaType="String"/>
                            <result column="parent_id" property="parentId" javaType="String"/>
                            <result column="comment_explanation" property="commentExplanation" javaType="String"/>
                            <result column="approve_type" property="approveType" javaType="String"/>
                            <result column="approve_status" property="approveStatus" javaType="String"/>
                            <result column="submit_time" property="submitTime" javaType="Date"/>
                            <result column="approve_time" property="approveTime" javaType="Date"/>
                            <result column="submit_user_id" property="submitUserId" javaType="String"/>
                            <result column="approver_user_id" property="approverUserId" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsApproveDetailInfoPageResultVo">
                        <result column="approve_detail_id" property="approveDetailId" javaType="String"/>
                        <result column="parent_id" property="parentId" javaType="String"/>
                        <result column="comment_explanation" property="commentExplanation" javaType="String"/>
                        <result column="approve_type" property="approveType" javaType="String"/>
                        <result column="approve_status" property="approveStatus" javaType="String"/>
                        <result column="submit_time" property="submitTime" javaType="Date"/>
                        <result column="approve_time" property="approveTime" javaType="Date"/>
                        <result column="submit_user_id" property="submitUserId" javaType="String"/>
                        <result column="approver_user_id" property="approverUserId" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.approve_detail_id
        ,base.approve_id
        ,base.parent_id
        ,base.comment_explanation
        ,base.approve_type
        ,base.approve_status
        ,base.submit_time
        ,base.approve_time
        ,base.submit_user_id
        ,base.approver_user_id
        ,base.del_flag
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_approve_detail_info base
        <where>
            <if test="'' != vo.approveDetailId and vo.approveDetailId != null">
                and base.approve_detail_id = #{vo.approveDetailId}
            </if>
            <if test="'' != vo.approveId and vo.approveId != null">
                and base.approve_id = #{vo.approveId}
            </if>
            <if test="'' != vo.parentId and vo.parentId != null">
                and base.parent_id = #{vo.parentId}
            </if>
            <if test="'' != vo.commentExplanation and vo.commentExplanation != null">
                and base.comment_explanation = #{vo.commentExplanation}
            </if>
            <if test="'' != vo.approveType and vo.approveType != null">
                and base.approve_type = #{vo.approveType}
            </if>
            <if test="'' != vo.approveStatus and vo.approveStatus != null">
                and base.approve_status = #{vo.approveStatus}
            </if>
            <if test="vo.submitTime != null">
                and base.submit_time = #{vo.submitTime}
            </if>
            <if test="vo.approveTime != null">
                and base.approve_time = #{vo.approveTime}
            </if>
            <if test="'' != vo.submitUserId and vo.submitUserId != null">
                and base.submit_user_id = #{vo.submitUserId}
            </if>
            <if test="'' != vo.approverUserId and vo.approverUserId != null">
                and base.approver_user_id = #{vo.approverUserId}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
    <!-- 批量新增数据 -->
    <insert id="insertBatch">
        insert into bbs_approve_detail_info(approve_detail_id,approve_id,approve_status,approver_role_id,approver_user_id,approve_remark,del_flag,create_user,create_time,modify_user,modify_time,cid,tenant_id,object_version_number)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.approveDetailId},#{entity.approveId},#{entity.approveStatus},#{entity.approverRoleId},#{entity.approverUserId},#{entity.approveRemark},#{entity.delFlag},#{entity.createUser},#{entity.createTime},#{entity.modifyUser},#{entity.modifyTime},#{entity.cid},#{entity.tenantId},#{entity.objectVersionNumber})
        </foreach>
    </insert>
</mapper>
