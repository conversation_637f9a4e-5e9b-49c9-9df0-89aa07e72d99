<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApplyApproveDetailInfoMapper">

    <!-- 这个xml是用来查询 流程日志 的 -->

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveDetailInfoResultVo">
        <result column="approve_detail_id" property="approveDetailId" javaType="String"/>
        <result column="approve_id" property="approveId" javaType="String"/>
        <result column="approve_time" property="approveTime" javaType="Date"/>
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="approve_status" property="approveStatus" javaType="String"/>
        <result column="submit_user_id" property="submitUserId" javaType="String"/>
        <result column="submit_user_name" property="submitUserName" javaType="String"/>
        <result column="approver_user_id" property="approverUserId" javaType="String"/>
        <result column="approver_user_name" property="approverUserName" javaType="String"/>
        <result column="comment_explanation" property="commentExplanation" javaType="String"/>
        <result column="submit_time" property="submitTime" javaType="Date"/>
    </resultMap>

    <select id="selectByListRecord" resultMap="QueryResultMap">
        select base.approve_detail_id,
               base.approve_id,
               base.parent_id,
               base.comment_explanation,
               base.approve_status,
               base.submit_time,
               base.approve_time,
               base.submit_user_id,
               base.approver_user_id,
               base.submit_user_name,
               base.approver_user_name
        from bbs_renewal_apply_approve_detail_info base
        where base.del_flag = 1 and base.parent_id = #{parentId}
        order by create_time desc
    </select>

</mapper>
