<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApplyApproveInfoMapper">

    <!-- 这个xml是用来查询分页列表的 -->

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveInfoPageResultVo">
        <result column="renewal_apply_info_id" property="renewalApplyInfoId" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="community_name" property="communityName" javaType="String"/>
        <result column="product_name" property="productName" javaType="String"/>
        <result column="tenant_name" property="tenantName" javaType="String"/>
        <result column="customer_type" property="customerType" javaType="String"/>
        <result column="customer_type_name" property="customerTypeName" javaType="String"/>
        <result column="customer_tel" property="customerTel" javaType="String"/>
        <result column="business_format" property="businessFormat" javaType="String"/>
        <result column="business_format_name" property="businessFormatName" javaType="String"/>
        <result column="submit_user_tel" property="submitUserTel" javaType="String"/>
        <result column="approve_id" property="approveId" javaType="String"/>
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="approve_status" property="approveStatus" javaType="String"/>
        <result column="submit_time" property="submitTime" javaType="Date"/>
        <result column="approve_time" property="approveTime" javaType="Date"/>
        <result column="submit_user_id" property="submitUserId" javaType="String"/>
        <result column="submit_user_name" property="submitUserName" javaType="String"/>
        <result column="approver_user_id" property="approverUserId" javaType="String"/>
        <result column="approver_user_name" property="approverUserName" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 bbs_renewal_apply_info -->
    <sql id="Base_Renewal_Apply_List">
        app.renewal_apply_info_id
        ,app.contract_code
        ,app.community_name
        ,app.product_name
        ,app.tenant_name
        ,app.customer_type
        ,app.customer_type_name
        ,app.customer_tel
        ,app.business_format
        ,app.business_format_name
        ,app.submit_user_tel
        ,app.del_flag
        ,app.tenant_id
    </sql>

    <!-- 通用查询结果列 bbs_renewal_apply_approve_info -->
    <sql id="Base_Renewal_Apply_Approve_List">
        approve.approve_id
        ,approve.parent_id
        ,approve.approver_user_id
        ,approve.approver_user_name
        ,approve.approve_time
        ,approve.submit_user_id
        ,approve.submit_user_name
        ,approve.submit_time
    </sql>

    <select id="selectByPageRecord" resultMap="QueryResultMap">
        select
        app.renewal_apply_info_id, app.contract_code, app.community_name, app.product_name, app.tenant_name,
        app.customer_type, app.customer_type_name, app.customer_tel, app.business_format,app.submit_user_tel,
        app.business_format_name, app.del_flag, app.tenant_id, approve.approve_id, approve.parent_id,
        approve.approver_user_id, approve.approver_user_name, approve.approve_time, approve.submit_user_id ,
        approve.submit_user_name, approve.submit_time, (CASE approve.approve_status WHEN '1' THEN '已通过' WHEN '2' THEN
        '未通过' WHEN '3' THEN '待审核' WHEN '4' then '撤回' ELSE '--' END) as approve_status
        from bbs_renewal_apply_info as app
        left join bbs_renewal_apply_approve_info as approve on app.renewal_apply_info_id = approve.parent_id
        <where>
            <if test="'' != vo.communityName and vo.communityName != null">
                <!--                小区名称（模糊搜索）-->
                and app.community_name like concat('%',#{vo.communityName},'%')
            </if>
            <if test="('' != vo.communityBuildingName and vo.communityBuildingName != null) or ('' != vo.groupName and vo.groupName != null) or ('' != vo.buildingName and vo.buildingName != null) or ('' != vo.unitName and vo.unitName != null)">
                <!--                商铺地址（级联搜索）-->
                and app.renewal_apply_info_id in (select distinct product.renewal_apply_info_id from
                bbs_renewal_apply_info_product as product where 1=1
                <if test="'' != vo.communityBuildingName and vo.communityBuildingName != null">
                    and product.community_building_name = #{vo.communityBuildingName}
                </if>
                <if test="'' != vo.groupName and vo.groupName != null">
                    and product.group_name = #{vo.groupName}
                </if>
                <if test="'' != vo.buildingName and vo.buildingName != null">
                    and product.building_name = #{vo.buildingName}
                </if>
                <if test="'' != vo.unitName and vo.unitName != null">
                    and product.unit_name = #{vo.unitName}
                </if>
                )
            </if>
            <if test="'' != vo.productName and vo.productName != null">
                <!--                租户（模糊搜索、按租户姓名或单位名称）-->
                <!--                and app.tenant_name = #{vo.tenantName}-->
                and app.product_name like concat('%',#{vo.productName},'%')
            </if>
            <if test="'' != vo.tenantName and vo.tenantName != null">
                <!--                租户（模糊搜索、按租户姓名或单位名称）-->
                <!--                and app.tenant_name = #{vo.tenantName}-->
                and app.tenant_name like concat('%',#{vo.tenantName},'%')
            </if>
            <if test="'' != vo.customerTel and vo.customerTel != null">
                <!--                联系电话（精确搜索）-->
                and app.customer_tel = #{vo.customerTel}
            </if>
            <if test="'' != vo.customerTypeName and vo.customerTypeName != null">
                <!--                业态（取值从商业房态维护找）customer_type、customer_type_name-->
                and app.customer_type = #{vo.customerTypeName}
            </if>
            <if test="'' != vo.businessFormat and vo.businessFormat != null">
                <!--                租户性质（企业、个人）business_format、business_format_name-->
                and app.business_format = #{vo.businessFormat}
            </if>
            <if test="'' != vo.businessFormatName and vo.businessFormatName != null">
                and app.business_format_name like concat('%',#{vo.businessFormatName},'%')
            </if>
            <!--            <if test="vo.approveTime != null">-->
            <if test="'' != vo.contractCode and vo.contractCode != null">
                <!--                合同编号（精确搜索）app.contract_code-->
                <!--                and app.contract_code = #{vo.contractCode}-->
                and app.contract_code like concat('%',#{vo.contractCode},'%')
            </if>
            <if test="'' != vo.submitUserId and vo.submitUserId != null">
                <!--                审批人（模糊搜索）approve.approver_user_id、approve.approver_user_name-->
                <!--                and approve.submit_user_id = #{vo.submitUserId}-->
                and approve.submit_user_id like concat('%',#{vo.submitUserId},'%')
            </if>
            <if test="'' != vo.submitUserName and vo.submitUserName != null">
                and approve.submit_user_name like concat('%',#{vo.submitUserName},'%')
            </if>
            <if test="vo.submitStartTime  != null and vo.submitStartTime != ''">
                <!--                创建时间（可以取时间范围内，或者具体某一天）app.create_time-->
                <!--                and app.create_time = #{vo.createTime}-->
                and approve.submit_time &gt;= concat(#{vo.submitStartTime},' 00:00:00')
            </if>
            <if test="vo.submitEndTime  != null and vo.submitEndTime != ''">
                and approve.submit_time &lt;= concat(#{vo.submitEndTime},' 23:59:59')
            </if>
            <if test="vo.approveStartTime  != null and vo.approveStartTime != ''">
                and approve.approve_time &gt;= concat(#{vo.approveStartTime},' 00:00:00')
            </if>
            <if test="vo.approveEndTime  != null and vo.approveEndTime != ''">
                and approve.approve_time &lt;= concat(#{vo.approveEndTime},' 23:59:59')
            </if>
            <if test="vo.approveStatus  != null and vo.approveStatus != ''">
                and approve.approve_status = #{vo.approveStatus}
            </if>
            <if test="vo.approverUserName  != null and vo.approverUserName != ''">
                and approve.approver_user_name like concat('%',#{vo.approverUserName},'%')
            </if>
            <if test="vo.delFlag != null">
                and app.del_flag = #{vo.delFlag} and approve.del_flag = #{vo.delFlag}
            </if>
        </where>
        order by app.create_time desc
    </select>

</mapper>
