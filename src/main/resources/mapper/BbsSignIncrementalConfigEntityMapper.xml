<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsSignIncrementalConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsSignIncrementalConfigEntity">
                            <id column="sic_id" property="sicId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="sign_info_id" property="signInfoId" javaType="String"/>
                            <result column="standard_type" property="standardType" javaType="String"/>
                            <result column="adjustment_point" property="adjustmentPoint" javaType="String"/>
                            <result column="adjustment_point_name" property="adjustmentPointName" javaType="String"/>
                            <result column="time_point" property="timePoint" javaType="Integer"/>
                            <result column="unit" property="unit" javaType="String"/>
                            <result column="unit_name" property="unitName" javaType="String"/>
                            <result column="increase" property="increase" javaType="Double"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsSignIncrementalConfigPageResultVo">
                        <result column="sign_info_id" property="signInfoId" javaType="String"/>
                        <result column="standard_type" property="standardType" javaType="String"/>
                        <result column="adjustment_point" property="adjustmentPoint" javaType="String"/>
                        <result column="adjustment_point_name" property="adjustmentPointName" javaType="String"/>
                        <result column="time_point" property="timePoint" javaType="Integer"/>
                        <result column="unit" property="unit" javaType="String"/>
                        <result column="unit_name" property="unitName" javaType="String"/>
                        <result column="increase" property="increase" javaType="Double"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.sic_id
        ,base.sign_info_id
        ,base.standard_type
        ,base.adjustment_point
        ,base.adjustment_point_name
        ,base.time_point
        ,base.unit
        ,base.unit_name
        ,base.increase
        ,base.del_flag
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_sign_incremental_config base
        <where>
            <if test="'' != vo.sicId and vo.sicId != null">
                and base.sic_id = #{vo.sicId}
            </if>
            <if test="'' != vo.signInfoId and vo.signInfoId != null">
                and base.sign_info_id = #{vo.signInfoId}
            </if>
            <if test="'' != vo.standardType and vo.standardType != null">
                and base.standard_type = #{vo.standardType}
            </if>
            <if test="'' != vo.adjustmentPoint and vo.adjustmentPoint != null">
                and base.adjustment_point = #{vo.adjustmentPoint}
            </if>
            <if test="'' != vo.adjustmentPointName and vo.adjustmentPointName != null">
                and base.adjustment_point_name = #{vo.adjustmentPointName}
            </if>
            <if test="vo.timePoint != null">
                and base.time_point = #{vo.timePoint}
            </if>
            <if test="'' != vo.unit and vo.unit != null">
                and base.unit = #{vo.unit}
            </if>
            <if test="'' != vo.unitName and vo.unitName != null">
                and base.unit_name = #{vo.unitName}
            </if>
            <if test="vo.increase != null">
                and base.increase = #{vo.increase}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
</mapper>
