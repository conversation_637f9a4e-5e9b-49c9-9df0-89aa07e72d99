<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsTemplateSeatMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsTemplateSeatEntity">
        <id column="seat_id" property="seatId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="parent_type" property="parentType" javaType="String"/>
        <result column="reverse_display" property="reverseDisplay" javaType="Integer"/>
        <result column="seat_title" property="seatTitle" javaType="String"/>
        <result column="seat_key" property="seatKey" javaType="String"/>
        <result column="default_val" property="defaultVal" javaType="String"/>
        <result column="value" property="value" javaType="String"/>
        <result column="is_show" property="isShow" javaType="String"/>
        <result column="editable" property="editable" javaType="String"/>
        <result column="is_required" property="isRequired" javaType="String"/>
        <result column="module_type" property="moduleType" javaType="String"/>
        <result column="sort" property="sort" javaType="Integer"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsTemplateSeatPageResultVo">
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="parent_type" property="parentType" javaType="String"/>
        <result column="reverse_display" property="reverseDisplay" javaType="Integer"/>
        <result column="seat_title" property="seatTitle" javaType="String"/>
        <result column="seat_key" property="seatKey" javaType="String"/>
        <result column="default_val" property="defaultVal" javaType="String"/>
        <result column="value" property="value" javaType="String"/>
        <result column="is_show" property="isShow" javaType="String"/>
        <result column="editable" property="editable" javaType="String"/>
        <result column="is_required" property="isRequired" javaType="String"/>
        <result column="module_type" property="moduleType" javaType="String"/>
        <result column="sort" property="sort" javaType="Integer"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base
        .
        object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.seat_id
        ,base.parent_id
        ,base.parent_type
        ,base.reverse_display
        ,base.seat_title
        ,base.seat_key
        ,base.default_val
        ,base.value
        ,base.is_show
        ,base.editable
        ,base.is_required
        ,base.module_type
        ,base.sort
        ,base.del_flag
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_template_seat base
        <where>
            <if test="'' != vo.seatId and vo.seatId != null">
                and base.seat_id = #{vo.seatId}
            </if>
            <if test="'' != vo.parentId and vo.parentId != null">
                and base.parent_id = #{vo.parentId}
            </if>
            <if test="'' != vo.parentType and vo.parentType != null">
                and base.parent_type = #{vo.parentType}
            </if>
            <if test="vo.reverseDisplay != null">
                and base.reverse_display = #{vo.reverseDisplay}
            </if>
            <if test="'' != vo.seatTitle and vo.seatTitle != null">
                and base.seat_title = #{vo.seatTitle}
            </if>
            <if test="'' != vo.seatKey and vo.seatKey != null">
                and base.seat_key = #{vo.seatKey}
            </if>
            <if test="'' != vo.defaultVal and vo.defaultVal != null">
                and base.default_val = #{vo.defaultVal}
            </if>
            <if test="'' != vo.value and vo.value != null">
                and base.value = #{vo.value}
            </if>
            <if test="'' != vo.isShow and vo.isShow != null">
                and base.is_show = #{vo.isShow}
            </if>
            <if test="'' != vo.editable and vo.editable != null">
                and base.editable = #{vo.editable}
            </if>
            <if test="'' != vo.isRequired and vo.isRequired != null">
                and base.is_required = #{vo.isRequired}
            </if>
            <if test="'' != vo.moduleType and vo.moduleType != null">
                and base.module_type = #{vo.moduleType}
            </if>
            <if test="vo.sort != null">
                and base.sort = #{vo.sort}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
</mapper>
