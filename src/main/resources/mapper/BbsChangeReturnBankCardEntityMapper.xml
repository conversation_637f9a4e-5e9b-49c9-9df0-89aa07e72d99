<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsChangeReturnBankCardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsChangeReturnBankCardEntity">
                            <id column="card_id" property="cardId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="cc_id" property="ccId" javaType="String"/>
                            <result column="source_type" property="sourceType" javaType="String"/>
                            <result column="back_type" property="backType" javaType="String"/>
                            <result column="bank_account_id" property="bankAccountId" javaType="String"/>
                            <result column="bank_account_name" property="bankAccountName" javaType="String"/>
                            <result column="bank_code" property="bankCode" javaType="String"/>
                            <result column="bank_name" property="bankName" javaType="String"/>
                            <result column="sub_bank_code" property="subBankCode" javaType="String"/>
                            <result column="sub_bank_name" property="subBankName" javaType="String"/>
                            <result column="bank_province_code" property="bankProvinceCode" javaType="String"/>
                            <result column="bank_province_name" property="bankProvinceName" javaType="String"/>
                            <result column="bank_city_code" property="bankCityCode" javaType="String"/>
                            <result column="bank_city_name" property="bankCityName" javaType="String"/>
                            <result column="customer_id_type" property="customerIdType" javaType="String"/>
                            <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
                            <result column="change_reason" property="changeReason" javaType="String"/>
                            <result column="related_file_id" property="relatedFileId" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangeReturnBankCardPageResultVo">
                        <result column="cc_id" property="ccId" javaType="String"/>
                        <result column="source_type" property="sourceType" javaType="String"/>
                        <result column="back_type" property="backType" javaType="String"/>
                        <result column="bank_account_id" property="bankAccountId" javaType="String"/>
                        <result column="bank_account_name" property="bankAccountName" javaType="String"/>
                        <result column="bank_code" property="bankCode" javaType="String"/>
                        <result column="bank_name" property="bankName" javaType="String"/>
                        <result column="sub_bank_code" property="subBankCode" javaType="String"/>
                        <result column="sub_bank_name" property="subBankName" javaType="String"/>
                        <result column="bank_province_code" property="bankProvinceCode" javaType="String"/>
                        <result column="bank_province_name" property="bankProvinceName" javaType="String"/>
                        <result column="bank_city_code" property="bankCityCode" javaType="String"/>
                        <result column="bank_city_name" property="bankCityName" javaType="String"/>
                        <result column="customer_id_type" property="customerIdType" javaType="String"/>
                        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
                        <result column="change_reason" property="changeReason" javaType="String"/>
                        <result column="related_file_id" property="relatedFileId" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>


    <resultMap id="voMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangeReturnBankCardVo">
        <result column="cc_id" property="ccId" javaType="String"/>
        <result column="source_type" property="sourceType" javaType="String"/>
        <result column="back_type" property="backType" javaType="String"/>
        <result column="bank_account_id" property="bankAccountId" javaType="String"/>
        <result column="bank_account_name" property="bankAccountName" javaType="String"/>
        <result column="bank_code" property="bankCode" javaType="String"/>
        <result column="bank_name" property="bankName" javaType="String"/>
        <result column="sub_bank_code" property="subBankCode" javaType="String"/>
        <result column="sub_bank_name" property="subBankName" javaType="String"/>
        <result column="bank_province_code" property="bankProvinceCode" javaType="String"/>
        <result column="bank_province_name" property="bankProvinceName" javaType="String"/>
        <result column="bank_city_code" property="bankCityCode" javaType="String"/>
        <result column="bank_city_name" property="bankCityName" javaType="String"/>
        <result column="customer_id_type" property="customerIdType" javaType="String"/>
        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
        <result column="change_reason" property="changeReason" javaType="String"/>
        <result column="related_file_id" property="relatedFileId" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.card_id
        ,base.cc_id
        ,base.source_type
        ,base.back_type
        ,base.bank_account_id
        ,base.bank_account_name
        ,base.bank_code
        ,base.bank_name
        ,base.sub_bank_code
        ,base.sub_bank_name
        ,base.bank_province_code
        ,base.bank_province_name
        ,base.bank_city_code
        ,base.bank_city_name
        ,base.customer_id_type
        ,base.customer_id_number
        ,base.change_reason
        ,base.related_file_id
        ,base.del_flag
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_return_bank_card base
        <where>
            base.del_flag='1'
            <if test="'' != vo.cardId and vo.cardId != null">
                and base.card_id = #{vo.cardId}
            </if>
            <if test="'' != vo.ccId and vo.ccId != null">
                and base.cc_id = #{vo.ccId}
            </if>
            <if test="'' != vo.sourceType and vo.sourceType != null">
                and base.source_type = #{vo.sourceType}
            </if>
            <if test="'' != vo.backType and vo.backType != null">
                and base.back_type = #{vo.backType}
            </if>
            <if test="'' != vo.bankAccountId and vo.bankAccountId != null">
                and base.bank_account_id = #{vo.bankAccountId}
            </if>
            <if test="'' != vo.bankAccountName and vo.bankAccountName != null">
                and base.bank_account_name = #{vo.bankAccountName}
            </if>
            <if test="'' != vo.bankCode and vo.bankCode != null">
                and base.bank_code = #{vo.bankCode}
            </if>
            <if test="'' != vo.bankName and vo.bankName != null">
                and base.bank_name = #{vo.bankName}
            </if>
            <if test="'' != vo.subBankCode and vo.subBankCode != null">
                and base.sub_bank_code = #{vo.subBankCode}
            </if>
            <if test="'' != vo.subBankName and vo.subBankName != null">
                and base.sub_bank_name = #{vo.subBankName}
            </if>
            <if test="'' != vo.bankProvinceCode and vo.bankProvinceCode != null">
                and base.bank_province_code = #{vo.bankProvinceCode}
            </if>
            <if test="'' != vo.bankProvinceName and vo.bankProvinceName != null">
                and base.bank_province_name = #{vo.bankProvinceName}
            </if>
            <if test="'' != vo.bankCityCode and vo.bankCityCode != null">
                and base.bank_city_code = #{vo.bankCityCode}
            </if>
            <if test="'' != vo.bankCityName and vo.bankCityName != null">
                and base.bank_city_name = #{vo.bankCityName}
            </if>
            <if test="'' != vo.customerIdType and vo.customerIdType != null">
                and base.customer_id_type = #{vo.customerIdType}
            </if>
            <if test="'' != vo.customerIdNumber and vo.customerIdNumber != null">
                and base.customer_id_number = #{vo.customerIdNumber}
            </if>
            <if test="'' != vo.changeReason and vo.changeReason != null">
                and base.change_reason = #{vo.changeReason}
            </if>
            <if test="'' != vo.relatedFileId and vo.relatedFileId != null">
                and base.related_file_id = #{vo.relatedFileId}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>


    <select id="selectBankCards" resultMap="voMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_return_bank_card base
        <where>
            base.del_flag='1'
            <if test="'' != vo.cardId and vo.cardId != null">
                and base.card_id = #{vo.cardId}
            </if>
            <if test="'' != vo.ccId and vo.ccId != null">
                and base.cc_id = #{vo.ccId}
            </if>
            <if test="'' != vo.sourceType and vo.sourceType != null">
                and base.source_type = #{vo.sourceType}
            </if>
            <if test="'' != vo.backType and vo.backType != null">
                and base.back_type = #{vo.backType}
            </if>
            <if test="'' != vo.bankAccountId and vo.bankAccountId != null">
                and base.bank_account_id = #{vo.bankAccountId}
            </if>
            <if test="'' != vo.bankAccountName and vo.bankAccountName != null">
                and base.bank_account_name = #{vo.bankAccountName}
            </if>
            <if test="'' != vo.bankCode and vo.bankCode != null">
                and base.bank_code = #{vo.bankCode}
            </if>
            <if test="'' != vo.bankName and vo.bankName != null">
                and base.bank_name = #{vo.bankName}
            </if>
            <if test="'' != vo.subBankCode and vo.subBankCode != null">
                and base.sub_bank_code = #{vo.subBankCode}
            </if>
            <if test="'' != vo.subBankName and vo.subBankName != null">
                and base.sub_bank_name = #{vo.subBankName}
            </if>
            <if test="'' != vo.bankProvinceCode and vo.bankProvinceCode != null">
                and base.bank_province_code = #{vo.bankProvinceCode}
            </if>
            <if test="'' != vo.bankProvinceName and vo.bankProvinceName != null">
                and base.bank_province_name = #{vo.bankProvinceName}
            </if>
            <if test="'' != vo.bankCityCode and vo.bankCityCode != null">
                and base.bank_city_code = #{vo.bankCityCode}
            </if>
            <if test="'' != vo.bankCityName and vo.bankCityName != null">
                and base.bank_city_name = #{vo.bankCityName}
            </if>
            <if test="'' != vo.customerIdType and vo.customerIdType != null">
                and base.customer_id_type = #{vo.customerIdType}
            </if>
            <if test="'' != vo.customerIdNumber and vo.customerIdNumber != null">
                and base.customer_id_number = #{vo.customerIdNumber}
            </if>
            <if test="'' != vo.changeReason and vo.changeReason != null">
                and base.change_reason = #{vo.changeReason}
            </if>
            <if test="'' != vo.relatedFileId and vo.relatedFileId != null">
                and base.related_file_id = #{vo.relatedFileId}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>

    <delete id="deleteByCcId">
        DELETE FROM bbs_change_return_bank_card
        WHERE cc_id = #{ccId};
    </delete>

</mapper>
