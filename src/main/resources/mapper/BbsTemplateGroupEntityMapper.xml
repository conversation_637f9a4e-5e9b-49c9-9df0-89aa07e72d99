<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsTemplateGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsTemplateGroupEntity">
        <id column="template_group_id" property="templateGroupId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="template_type" property="templateType" javaType="String"/>
        <result column="template_id" property="templateId" javaType="String"/>
        <result column="success_template_id" property="successTemplateId" javaType="String"/>
        <result column="failed_template_id" property="failedTemplateId" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsTemplateGroupPageResultVo">
        <result column="template_type" property="templateType" javaType="String"/>
        <result column="template_id" property="templateId" javaType="String"/>
        <result column="success_template_id" property="successTemplateId" javaType="String"/>
        <result column="failed_template_id" property="failedTemplateId" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base
        .
        object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.template_group_id
        ,base.template_type
        ,base.template_id
        ,base.success_template_id
        ,base.failed_template_id
        ,base.del_flag
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_template_group base
        <where>
            <if test="'' != vo.templateGroupId and vo.templateGroupId != null">
                and base.template_group_id = #{vo.templateGroupId}
            </if>
            <if test="'' != vo.templateType and vo.templateType != null">
                and base.template_type = #{vo.templateType}
            </if>
            <if test="'' != vo.templateId and vo.templateId != null">
                and base.template_id = #{vo.templateId}
            </if>
            <if test="'' != vo.successTemplateId and vo.successTemplateId != null">
                and base.success_template_id = #{vo.successTemplateId}
            </if>
            <if test="'' != vo.failedTemplateId and vo.failedTemplateId != null">
                and base.failed_template_id = #{vo.failedTemplateId}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
</mapper>
