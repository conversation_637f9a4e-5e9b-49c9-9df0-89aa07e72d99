<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsContractChangeTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsContractChangeTemplateEntity">
                            <id column="relation_id" property="relationId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="change_type" property="changeType" javaType="String"/>
                            <result column="template_id" property="templateId" javaType="String"/>
                            <result column="template_name" property="templateName" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsContractChangeTemplatePageResultVo">
                        <result column="change_type" property="changeType" javaType="String"/>
                        <result column="template_id" property="templateId" javaType="String"/>
                        <result column="template_name" property="templateName" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.relation_id
        ,base.change_type
        ,base.template_id
        ,base.template_name
        ,base.del_flag
        ,base.create_user_name
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_contract_change_template base
        <where>
            <if test="'' != vo.relationId and vo.relationId != null">
                and base.relation_id = #{vo.relationId}
            </if>
            <if test="'' != vo.changeType and vo.changeType != null">
                and base.change_type = #{vo.changeType}
            </if>
            <if test="'' != vo.templateId and vo.templateId != null">
                and base.template_id = #{vo.templateId}
            </if>
            <if test="'' != vo.templateName and vo.templateName != null">
                and base.template_name = #{vo.templateName}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.createUserName and vo.createUserName != null">
                and base.create_user_name = #{vo.createUserName}
            </if>
        </where>
    </select>
</mapper>
