<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsImportRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsImportRecordEntity">
                            <id column="import_id" property="importId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="trace_id" property="traceId" javaType="String"/>
                            <result column="batch_no" property="batchNo" javaType="String"/>
                            <result column="import_key" property="importKey" javaType="String"/>
                            <result column="import_success" property="importSuccess" javaType="String"/>
                            <result column="import_body" property="importBody" javaType="String"/>
                            <result column="import_result" property="importResult" javaType="String"/>
                            <result column="import_exception" property="importException" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsImportRecordPageResultVo">
                        <result column="trace_id" property="traceId" javaType="String"/>
                        <result column="batch_no" property="batchNo" javaType="String"/>
                        <result column="import_key" property="importKey" javaType="String"/>
                        <result column="import_success" property="importSuccess" javaType="String"/>
                        <result column="import_body" property="importBody" javaType="String"/>
                        <result column="import_result" property="importResult" javaType="String"/>
                        <result column="import_exception" property="importException" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.import_id
        ,base.trace_id
        ,base.batch_no
        ,base.import_key
        ,base.import_success
        ,base.import_body
        ,base.import_result
        ,base.import_exception
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_import_record base
        <where>
            <if test="'' != vo.importId and vo.importId != null">
                and base.import_id = #{vo.importId}
            </if>
            <if test="'' != vo.traceId and vo.traceId != null">
                and base.trace_id = #{vo.traceId}
            </if>
            <if test="'' != vo.batchNo and vo.batchNo != null">
                and base.batch_no = #{vo.batchNo}
            </if>
            <if test="'' != vo.importKey and vo.importKey != null">
                and base.import_key = #{vo.importKey}
            </if>
            <if test="'' != vo.importSuccess and vo.importSuccess != null">
                and base.import_success = #{vo.importSuccess}
            </if>
            <if test="'' != vo.importBody and vo.importBody != null">
                and base.import_body = #{vo.importBody}
            </if>
            <if test="'' != vo.importResult and vo.importResult != null">
                and base.import_result = #{vo.importResult}
            </if>
            <if test="'' != vo.importException and vo.importException != null">
                and base.import_exception = #{vo.importException}
            </if>
        </where>
    </select>
</mapper>
