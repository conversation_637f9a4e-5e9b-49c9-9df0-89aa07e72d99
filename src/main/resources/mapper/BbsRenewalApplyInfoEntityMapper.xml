<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApplyInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyInfoEntity">
                            <id column="renewal_apply_info_id" property="renewalApplyInfoId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="contract_code" property="contractCode" javaType="String"/>
                            <result column="community_name" property="communityName" javaType="String"/>
                            <result column="product_name" property="productName" javaType="String"/>
                            <result column="tenant_name" property="tenantName" javaType="String"/>
                            <result column="customer_type" property="customerType" javaType="String"/>
                            <result column="customer_type_name" property="customerTypeName" javaType="String"/>
                            <result column="customer_tel" property="customerTel" javaType="String"/>
                            <result column="business_format" property="businessFormat" javaType="String"/>
                            <result column="business_format_name" property="businessFormatName" javaType="String"/>
                            <result column="approve_status" property="approveStatus" javaType="String"/>
                            <result column="submit_user_tel" property="submitUserTel" javaType="String"/>
                            <result column="submit_illustrate" property="submitIllustrate" javaType="String"/>
                            <result column="file_id" property="fileId" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApplyInfoPageResultVo">
                        <result column="contract_code" property="contractCode" javaType="String"/>
                        <result column="community_name" property="communityName" javaType="String"/>
                        <result column="product_name" property="productName" javaType="String"/>
                        <result column="tenant_name" property="tenantName" javaType="String"/>
                        <result column="customer_type" property="customerType" javaType="String"/>
                        <result column="customer_type_name" property="customerTypeName" javaType="String"/>
                        <result column="customer_tel" property="customerTel" javaType="String"/>
                        <result column="business_format" property="businessFormat" javaType="String"/>
                        <result column="business_format_name" property="businessFormatName" javaType="String"/>
                        <result column="approve_status" property="approveStatus" javaType="String"/>
                        <result column="submit_user_tel" property="submitUserTel" javaType="String"/>
                        <result column="submit_illustrate" property="submitIllustrate" javaType="String"/>
                        <result column="file_id" property="fileId" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.renewal_apply_info_id
        ,base.contract_code
        ,base.community_name
        ,base.product_name
        ,base.tenant_name
        ,base.customer_type
        ,base.customer_type_name
        ,base.customer_tel
        ,base.business_format
        ,base.business_format_name
        ,base.approve_status
        ,base.submit_user_tel
        ,base.submit_illustrate
        ,base.file_id
        ,base.del_flag
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_renewal_apply_info base
        <where>
            <if test="'' != vo.renewalApplyInfoId and vo.renewalApplyInfoId != null">
                and base.renewal_apply_info_id = #{vo.renewalApplyInfoId}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.communityName and vo.communityName != null">
                and base.community_name = #{vo.communityName}
            </if>
            <if test="'' != vo.productName and vo.productName != null">
                and base.product_name = #{vo.productName}
            </if>
            <if test="'' != vo.tenantName and vo.tenantName != null">
                and base.tenant_name = #{vo.tenantName}
            </if>
            <if test="'' != vo.customerType and vo.customerType != null">
                and base.customer_type = #{vo.customerType}
            </if>
            <if test="'' != vo.customerTypeName and vo.customerTypeName != null">
                and base.customer_type_name = #{vo.customerTypeName}
            </if>
            <if test="'' != vo.customerTel and vo.customerTel != null">
                and base.customer_tel = #{vo.customerTel}
            </if>
            <if test="'' != vo.businessFormat and vo.businessFormat != null">
                and base.business_format = #{vo.businessFormat}
            </if>
            <if test="'' != vo.businessFormatName and vo.businessFormatName != null">
                and base.business_format_name = #{vo.businessFormatName}
            </if>
            <if test="'' != vo.approveStatus and vo.approveStatus != null">
                and base.approve_status = #{vo.approveStatus}
            </if>
            <if test="'' != vo.submitUserTel and vo.submitUserTel != null">
                and base.submit_user_tel = #{vo.submitUserTel}
            </if>
            <if test="'' != vo.submitIllustrate and vo.submitIllustrate != null">
                and base.submit_illustrate = #{vo.submitIllustrate}
            </if>
            <if test="'' != vo.fileId and vo.fileId != null">
                and base.file_id = #{vo.fileId}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
    <select id="slectBusinessFormat" resultType="java.util.Map">
        select business_format      as "businessFormat",
               business_format_name as "businessFormatName"
        from bbs_sign_info
        where contract_code = #{contractCode}
    </select>
    <select id="selectInfoByContractNo" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApplyInfoVo">
        SELECT brai.renewal_apply_info_id,
               brai.contract_code,
               brai.community_name,
               brai.product_name,
               brai.tenant_name,
               brai.customer_type,
               brai.customer_type_name,
               brai.customer_tel,
               brai.business_format,
               brai.business_format_name,
               brai.approve_status,
               brai.submit_user_tel,
               brai.submit_illustrate,
               brai.file_id,
               brai.del_flag,
               brai.cid,
               brai.create_user,
               brai.create_time,
               brai.modify_user,
               brai.modify_time,
               brai.tenant_id,
               brai.object_version_number
        FROM bbs_renewal_apply_info brai
                 left join bbs_renewal_apply_approve_info braai on brai.renewal_apply_info_id = braai.parent_id
        WHERE brai.del_flag = 1
          and brai.renewal_apply_info_id = (select renewal_apply_info_id
                                            from bbs_renewal_apply_info
                                            where contract_code = #{contractCode}
                                            order by create_time desc limit 1)
    </select>
</mapper>
