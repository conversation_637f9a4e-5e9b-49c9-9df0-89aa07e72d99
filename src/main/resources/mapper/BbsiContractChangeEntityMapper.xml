<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsiContractChangeMapper">


    <select id="selectCustomInfo" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeCustomerVo">
        select distinct a.* from
            (select sign.sign_id                                                                                                  as signId,
                    sign.contract_code                                                                                            as contractCode,
                    customer.customer_type                                                                                        as customerType,
                    case when contractRes.customer_no is null then customer.customer_no else contractRes.customer_no end          as
                                                                                                                                     customerNo,
                    case when contractRes.customer_name is null then customer.customer_name else contractRes.customer_name end    as
                                                                                                                                     customerName,
                    customer.legal_name                                                                                           as legalName,
                    customer.customer_id_type                                                                                     as customerIdTypeCode,
                    certificateType.meaning                                                                                       as customerIdTypeName,
                    case
                        when customer.customer_type = '00' then customer.customer_id_number
                        else customer.customer_credit_code end                                                                    as customerIdNumber,
                    case when contractRes.mail_address is null then customer.mail_address else contractRes.mail_address end       as
                                                                                                                                     mailAddress,
                    case
                        when contractRes.bank_name is null then customer.bank_name
                        else contractRes.bank_name end                                                                            as bankName,
                    case when contractRes.bank_name_code is null then customer.bank_name_code else contractRes.bank_name_code end as
                                                                                                                                     bankNameCode,
                    case
                        when contractRes.bank_subbranch_name is null then customer.bank_subbranch_name
                        else
                            contractRes.bank_subbranch_name end                                                                   as bankSubbranchName,
                    case
                        when contractRes.bank_subbranch_code is null then customer.bank_subbranch_code
                        else
                            contractRes.bank_subbranch_code end                                                                   as bankSubbranchCode,
                    case
                        when contractRes.bank_card is null then customer.bank_card
                        else contractRes.bank_card end                                                                            as bankCard,
                    customer.bank_phone                                                                                           as bankPhone,
                    case when contractRes.customer_tel is null then customer.customer_tel else contractRes.customer_tel end       as
                                                                                                                                     customerTel,
                    customer.customer_work_tel                                                                                    as customerWorkTel,
                    product.project_id                                                                                            as projectId
             from (select sign_id, contract_code
                   from bbs_sign_info
                   where del_flag = 1
                   union all
                   select sign_id, contract_code
                   from bbs_renewal_info
                   where del_flag = 1) sign
                      left join bbs_contract_change_result contractRes on sign.contract_code = contractRes.contract_code
                      LEFT JOIN (select sign_info_id, rr_id
                                 from bbs_result_relation
                                 union all
                                 select sign_info_id, rr_id
                                 from bbs_renewal_relation) relation on sign.sign_id = relation.sign_info_id
                      LEFT JOIN (select rr_id,
                                        legal_name,
                                        customer_type,
                                        customer_no,
                                        customer_name,
                                        customer_id_type,
                                        customer_id_number,
                                        mail_address,
                                        bank_name,
                                        bank_name_code,
                                        bank_subbranch_name,
                                        bank_subbranch_code,
                                        bank_card,
                                        bank_phone,
                                        customer_tel,
                                        customer_work_tel,
                                        customer_credit_code
                                 from bbs_result_customer
                                 union all
                                 select rr_id,
                                        legal_name,
                                        customer_type,
                                        customer_no,
                                        customer_name,
                                        customer_id_type,
                                        customer_id_number,
                                        mail_address,
                                        bank_name,
                                        bank_name_code,
                                        bank_subbranch_name,
                                        bank_subbranch_code,
                                        bank_card,
                                        bank_phone,
                                        customer_tel,
                                        customer_work_tel,
                                        customer_credit_code
                                 from bbs_renewal_customer) customer
                                on relation.rr_id = customer.rr_id
                      LEFT JOIN (SELECT CODE,
                                        meaning
                                 FROM bbs_dict
                                 WHERE type_code = 'CERTIFICATE_TYPE') certificateType
                                ON customer.customer_id_type = certificateType.CODE
                      LEFT JOIN (select rr_id, project_id
                                 from bbs_result_product
                                 union all
                                 select rr_id, project_id
                                 from bbs_renewal_product) product ON relation.rr_id = product.rr_id
             where sign.contract_code = #{contractCode} ) a
    </select>

    <select id="selectByPageRecord" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeListPageResultVo">
        select a.* from (
            SELECT contract.del_flag,
                   contract.cc_id                                                                        as ccId,
            contract.contract_code                                                                       as contractCode,
            contract.change_user_name                                                                    as changeUserName,
            contract.create_time                                                                         as createTime,
            ( SELECT GROUP_CONCAT( meaning ) AS a FROM bbs_dict WHERE type_code = 'CHANGE_TYPE_ITEM' AND FIND_IN_SET( CODE, contract.change_type_item ) ) AS changeTypeItem,
            changeType.CODE                                                                              AS changeTypeCode,
            contract.change_status                                                                       as changeStatus,
        ( SELECT GROUP_CONCAT( meaning ) AS a FROM bbs_dict WHERE type_code = 'CHANGE_STATUS' AND FIND_IN_SET( CODE, contract.change_status ) ) AS changeStatusName,
            approve.approve_status                                                                       as changeApproveStatus,
            changeType.meaning                                                                           AS changeType,
            customerType.CODE                                                                            AS customerTypeCode,
            customerType.meaning                                                                         AS customerType,
            case
            when contract.customer_name_old is null then
                 customer.customer_name
            else contract.customer_name_old end                                                          as customerName,
            case
            when contract.customer_tel_old is null then
                customer.customer_tel
            else contract.customer_tel_old end                                                           as customerTel,
            case
            when contract.mail_address_old is null then
              customer.mail_address
            else contract.mail_address_old end                                                           as mailAddress,
            GROUP_CONCAT(product.product_name SEPARATOR ', ')                                            AS productName,
            product.community_building_name                                                              as communityBuildingName,
            sum(cast(product.house_struct_area as decimal(18,2)))                                        as houseStructArea,
            GROUP_CONCAT(CONCAT(product.rent_standard, '(', product.rent_standard_unit_name, ')') SEPARATOR ', ') AS rent,
            sign.contract_begin_time                                                                     as contractBeginTime,
            sign.contract_end_time                                                                       as contractEndTime,
            customer.customer_id_number                                                                  as customerIdNumber,
            customer.customer_credit_code                                                                as customerCreditCode,
            customer.legal_name                                                                          as legalName,
            customer.consignor_name                                                                      as consignorName,
            customer.consignor_mobile                                                                    as consignorMobile,
            customer.contact_name                                                                        as contactName,
            sign.business_format                                                                         as businessFormat,
            product.project_id                                                                           as projectId
            FROM bbs_contract_change contract
            LEFT JOIN (SELECT CODE,
            meaning
            FROM bbs_dict
            WHERE type_code = 'CHANGE_TYPE') changeType ON contract.change_type = changeType.CODE
            LEFT JOIN (select contract_code,
            contract_begin_time,
            contract_end_time,
            business_format,
            business_format_name,
            sign_id
            from bbs_sign_info
            union all
            select contract_code,
            contract_begin_time,
            contract_end_time,
            business_format,
            business_format_name,
            sign_id
            from bbs_renewal_info) sign ON sign.contract_code = contract.contract_code
            LEFT JOIN (select sign_info_id, rr_id
            from bbs_result_relation
            union all
            select sign_info_id, rr_id
            from bbs_renewal_relation) relation
            ON sign.sign_id = relation.sign_info_id
            LEFT JOIN (select rr_id,
            customer_type,
            customer_name,
            customer_tel,
            mail_address,
            customer_id_number,
            customer_credit_code,
            legal_name,
            consignor_name,
            consignor_mobile,
            contact_name
            from bbs_result_customer
            union all
            select rr_id,
            customer_type,
            customer_name,
            customer_tel,
            mail_address,
            customer_id_number,
            customer_credit_code,
            legal_name,
            consignor_name,
            consignor_mobile,
            contact_name
        from bbs_renewal_customer) customer ON relation.rr_id = customer.rr_id
            LEFT JOIN (SELECT CODE,
            meaning
            FROM bbs_dict
            WHERE type_code = 'CUSTOMER_TYPE') customerType ON customer.customer_type = customerType.CODE
            LEFT JOIN (select rr_id,
            project_id,
            product_name,
            community_building_name,
            house_struct_area,
            rent,
            rent_standard,
            rent_standard_unit_name
            from bbs_result_product
            union all
            select rr_id,
            project_id,
            product_name,
            community_building_name,
            house_struct_area,
            rent,
            rent_standard,
            rent_standard_unit_name
            from bbs_renewal_product) product ON relation.rr_id = product.rr_id
            LEFT JOIN bbs_contract_change_result contractRes ON contract.contract_code = contractRes.contract_code
            LEFT JOIN bbs_contract_change_approve_info approve ON contract.cc_id = approve.cc_id
        <where>
            <if test="'' != vo.communityBuildingName and vo.communityBuildingName != null">
                and product.community_building_name like concat('%',#{vo.communityBuildingName},'%')
            </if>
            <if test="'' != vo.productName and vo.productName != null">
                and product.product_name like concat('%',#{vo.productName},'%')
            </if>
            <if test="'' != vo.customerName and vo.customerName != null">
                and case when contract.customer_name_old is null then
                  customer.customer_name like concat('%',#{vo.customerName},'%')
                else contract.customer_name_old like concat('%',#{vo.customerName},'%') end
            </if>
            <if test="'' != vo.customerTel and vo.customerTel != null">
                and case when contract.customer_tel_old is null then
                customer.customer_tel= #{vo.customerTel}
                else contract.customer_tel_old = #{vo.customerTel} end
            </if>
            <if test="'' != vo.customerTypeCode and vo.customerTypeCode != null">
                and customerType.CODE = #{vo.customerTypeCode}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and contract.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.changeUserName and vo.changeUserName != null">
                and contract.change_user_name like concat('%',#{vo.changeUserName},'%')
            </if>
            <if test="vo.createTime != null">
                and date_format(contract.create_time,'%Y-%m-%d') = date_format(#{vo.createTime},'%Y-%m-%d')
            </if>
            <if test="vo.startCreateTime != null">
                and date_format(contract.create_time,'%Y-%m-%d') &gt;= date_format(#{vo.startCreateTime},'%Y-%m-%d')
            </if>
            <if test="vo.endCreateTime != null">
                and date_format(contract.create_time,'%Y-%m-%d') &lt;= date_format(#{vo.endCreateTime},'%Y-%m-%d')
            </if>
            <if test="'' != vo.businessFormat and vo.businessFormat != null">
                and sign.business_format_name  like concat('%',#{vo.businessFormat},'%')
            </if>
            <if test="'' != vo.changeTypeItem and vo.changeTypeItem != null">
                <foreach item="item" index="index" collection="vo.changeTypeItem.split(',')" open="and (" separator="OR"
                         close=")">
                    FIND_IN_SET( #{item}, contract.change_type_item)
                </foreach>
            </if>
            <if test="'' != vo.ccId and vo.ccId != null">
                and contract.cc_id = #{vo.ccId}
            </if>
            <if test="'' != vo.changeStatus and vo.changeStatus != null">
                and contract.change_status = #{vo.changeStatus}
            </if>
            and case when contract.source = 'APP' then approve.approve_status in('1')  else 1=1 end
            and contract.del_flag = '1'
            group by ccId
            order by contract.create_time desc
        </where>
        ) a where a.del_flag = 1
    </select>

<!--    <select id="selectByPageChangeRecord"-->
<!--            resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeRecordPageResultVo">-->
<!--        select-->
<!--            contract.cc_id as ccId,-->
<!--            contract.contract_code as contractCode,-->
<!--            changeType.CODE AS changeTypeCode,-->
<!--            changeType.meaning AS changeTypeName,-->
<!--            contract.create_time as createTime,-->
<!--            contract.change_user_name as changeUserName,-->
<!--            approveStatus.meaning as changeStatusName,-->
<!--            case when approve.approver_user_name is null then '&#45;&#45;'-->
<!--            else approve.approver_user_name-->
<!--            end as approverUserName,-->
<!--            approve.approve_time as approveTime-->
<!--        from bbs_contract_change contract-->
<!--        LEFT JOIN (-->
<!--            SELECT-->
<!--                CODE,-->
<!--                meaning-->
<!--            FROM-->
<!--                bbs_dict-->
<!--            WHERE-->
<!--                type_code = 'CHANGE_TYPE'-->
<!--        ) changeType ON contract.change_type = changeType. CODE-->
<!--        LEFT JOIN bbs_contract_change_approve_info approve on contract.cc_id = approve.cc_id-->
<!--        LEFT JOIN (-->
<!--            SELECT-->
<!--                CODE,-->
<!--                meaning-->
<!--            FROM-->
<!--                bbs_dict-->
<!--            WHERE-->
<!--                type_code = 'CHANGE_APPROVE_STATUS'-->
<!--        ) approveStatus ON approve.approve_status = approveStatus. CODE-->
<!--        where contract.contract_code = #{vo.contractCode} and change_status = '1'-->
<!--        and contract.change_status = '1'-->
<!--        order by contract.create_time desc-->
<!--    </select>-->

    <select id="selectByPageChangeRecord"
            resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeRecordPageResultVo">
        SELECT
            contract.cc_id AS ccId,
            contract.contract_code AS contractCode,
            changeType.CODE AS changeTypeCode,
            changeType.meaning AS changeTypeName,
            contract.create_time AS createTime,
            contract.change_user_name AS changeUserName,
            approveStatus.meaning AS changeStatusName,
            CASE
                WHEN approve.approver_user_name IS NULL THEN
                    '--' ELSE approve.approver_user_name
                END AS approverUserName,
            approve.approve_time AS approveTime
        FROM
            bbs_contract_change contract
                LEFT JOIN ( SELECT CODE, meaning FROM bbs_dict WHERE type_code = 'CHANGE_TYPE' ) changeType ON contract.change_type = changeType.
                CODE LEFT JOIN bbs_contract_change_approve_info approve ON contract.cc_id = approve.cc_id
                LEFT JOIN ( SELECT CODE, meaning FROM bbs_dict WHERE type_code = 'CHANGE_APPROVE_STATUS' ) approveStatus ON approve.approve_status = approveStatus.CODE
                LEFT JOIN bbs_sign_info bsi on bsi.sign_id=contract.sign_id
        WHERE
            contract.contract_code = #{vo.contractCode}
          AND approve.approve_status = '1'
          AND contract.change_status = '1'
          AND (bsi.sign_status is NULL OR bsi.sign_status = '3')
        ORDER BY
            contract.create_time DESC
    </select>

    <!--    审批记录结果查询-->
    <select id="selectByChangeApproveChange" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsContractChangeApproveDetailVo">
        select
        a.approver_user_name,
        a.approve_time,
        a.approve_status,
        a.comment_explanation
        from
        bbs_contract_change_approve_detail a
        LEFT JOIN bbs_contract_change_approve_info b
        on
        a.cc_id=b.cc_id
        WHERE
        a.cc_id=#{vo.ccId}

    </select>

    <select id="selectAppChangeRecordList" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeRecordVo">
        select
            contract.cc_id as ccId,
            contract.contract_code as contractCode,
            changeType.CODE AS changeTypeCode,
            changeType.meaning AS changeTypeName,
            contract.change_type as changeType,
            contract.create_time as createTime,
            approve.approve_status as approveStatusCode,
            CASE WHEN approve.approve_status = '1' THEN '已结束'
                 WHEN approve.approve_status = '2' THEN '已结束'
                 WHEN approve.approve_status = '3' THEN '办理中'
                 ELSE '--' END AS approveStatusName
        from bbs_contract_change contract
        LEFT JOIN (
            SELECT
                CODE,
                meaning
            FROM
                bbs_dict
            WHERE
                type_code = 'CHANGE_TYPE'
        ) changeType ON contract.change_type = changeType. CODE
        LEFT JOIN bbs_contract_change_approve_info approve on contract.cc_id = approve.cc_id
        where contract.contract_code = #{contractCode}
        and contract.change_status != '0'
        and contract.del_flag = '1'
        order by ifnull(contract.modify_time, contract.create_time) desc
    </select>

    <select id="selectApproveDetail" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsContractChangeApproveDetailVo">
        SELECT
            approve.approve_detail_id AS approveDetailId,
            approve.cc_id AS ccId,
            approve.description AS description,
            approve.approve_time AS approveTime,
            approve.comment_explanation AS commentExplanation,
            approve.approver_user_id AS approverUserId,
            approve.approver_user_name AS approverUserName,
            approveStatus.meaning AS approveStatusName,
            approve.approve_status AS approveStatusCode
        FROM
            bbs_contract_change_approve_detail approve
        LEFT JOIN (
            SELECT
                CODE,
                meaning
            FROM
                bbs_dict
            WHERE
                type_code = 'CHANGE_APPROVE_STATUS'
        ) approveStatus ON approve.approve_status = approveStatus. CODE
        where approve.cc_id = #{ccId}
        order by approve.approve_time desc
    </select>

    <!--    审批记录结果查询-->
    <select id="selectByChangeApproveChange" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsContractChangeApproveDetailVo">
        select
        a.approver_user_name,
        a.approve_time,
        a.approve_status,
        a.comment_explanation
        from
        bbs_contract_change_approve_detail a
        LEFT JOIN bbs_contract_change_approve_info b
        on
        a.cc_id=b.cc_id
        WHERE
        a.cc_id=#{vo.ccId}

    </select>


    <select id="selectContractAuditPageRecord" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeListPageResultVo">
        select a.* from (
        SELECT
        contract.cc_id as ccId,
        contract.contract_code as contractCode,
        contract.change_user_name as changeUserName,
        contract.create_time as createTime,
        changeType.CODE AS changeTypeCode,
        contract.change_status as changeStatus,
        changeType.meaning AS changeType,
        customerType.CODE AS customerTypeCode,
        customerType.meaning AS customerType,
        approve.approver_user_name as approverUserName,
        approve.approve_time as approveTime,
        approve.approve_status as approveStatus,
        case when contract.customer_name is null then
        case when contractRes.customer_name IS NOT NULL then contractRes.customer_name else customer.customer_name end
        else contract.customer_name end as customerName,
        case when contract.customer_tel is null then
        case when contractRes.customer_tel IS NOT NULL then contractRes.customer_tel else customer.customer_tel end
        else contract.customer_tel end as customerTel,
        case when contract.mail_address is null then
        case when contractRes.mail_address IS NOT NULL then contractRes.mail_address else customer.mail_address end
        else contract.mail_address end as mailAddress,
        GROUP_CONCAT( product.product_name SEPARATOR ', ') AS productName,
        product.community_building_name as communityBuildingName,
        sum(cast(product.house_struct_area as decimal(18,2))) as houseStructArea,
        GROUP_CONCAT(CONCAT( product.rent,'(', product.rent_unit,')') SEPARATOR ', ') AS rent,
        sign.contract_begin_time as contractBeginTime,
        sign.contract_end_time as contractEndTime,
        customer.customer_id_number as customerIdNumber,
        customer.customer_credit_code as customerCreditCode,
        customer.legal_name as legalName,
        customer.consignor_name as consignorName,
        customer.consignor_mobile as consignorMobile,
        sign.business_format as businessFormat
        FROM
        bbs_contract_change contract
        LEFT JOIN (
        SELECT
        CODE,
        meaning
        FROM
        bbs_dict
        WHERE
        type_code = 'CHANGE_TYPE'
        ) changeType ON contract.change_type = changeType. CODE
        LEFT JOIN bbs_sign_info sign ON sign.contract_code = contract.contract_code
        LEFT JOIN bbs_result_relation relation ON sign.sign_id = relation.sign_info_id
        LEFT JOIN bbs_result_customer customer ON relation.rr_id = customer.rr_id
        LEFT JOIN bbs_contract_change_approve_detail approve ON approve.cc_id = contract.cc_id
        LEFT JOIN (
        SELECT
        CODE,
        meaning
        FROM
        bbs_dict
        WHERE
        type_code = 'CUSTOMER_TYPE'
        ) customerType ON customer.customer_type = customerType. CODE
        LEFT JOIN bbs_result_product product ON relation.rr_id = product.rr_id
        LEFT JOIN bbs_contract_change_result contractRes ON contract.contract_code = contractRes.contract_code
        <where>
            <if test="'' != vo.communityBuildingName and vo.communityBuildingName != null">
                and product.community_building_name like concat('%',#{vo.communityBuildingName},'%')
            </if>
            <if test="'' != vo.productName and vo.productName != null">
                and product.product_name like concat('%',#{vo.productName},'%')
            </if>
            <if test="'' != vo.customerName and vo.customerName != null">
                and case when contract.customer_name is null then
                case when contractRes.customer_name IS NOT NULL then contractRes.customer_name like
                concat('%',#{vo.customerName},'%') else customer.customer_name like concat('%',#{vo.customerName},'%')
                end
                else contract.customer_name like concat('%',#{vo.customerName},'%') end
            </if>
            <if test="'' != vo.customerTel and vo.customerTel != null">
                and case when contract.customer_tel is null then
                case when contractRes.customer_tel IS NOT NULL then contractRes.customer_tel= #{vo.customerTel} else
                customer.customer_tel= #{vo.customerTel} end
                else contract.customer_tel= #{vo.customerTel} end
            </if>
            <if test="'' != vo.customerTypeCode and vo.customerTypeCode != null">
                and customerType.CODE = #{vo.customerTypeCode}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and contract.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.changeUserName and vo.changeUserName != null">
                and contract.change_user_name like concat('%',#{vo.changeUserName},'%')
            </if>
            <if test="vo.createTime != null">
                and date_format(contract.create_time,'%Y-%m-%d') = date_format(#{vo.createTime},'%Y-%m-%d')
            </if>
            <if test="vo.startCreateTime != null">
                and date_format(contract.create_time,'%Y-%m-%d') &gt;= date_format(#{vo.startCreateTime},'%Y-%m-%d')
            </if>
            <if test="vo.endCreateTime != null">
                and date_format(contract.create_time,'%Y-%m-%d') &lt;= date_format(#{vo.endCreateTime},'%Y-%m-%d')
            </if>
            <if test="'' != vo.businessFormat and vo.businessFormat != null">
                and sign.business_format = #{vo.businessFormat}
            </if>
            <!--            排序字段-->
            <!--            <if test="memberParam.sortField != null and memberParam.sortField != ''-->
            <!--            and memberParam.sortRules != null and memberParam.sortRules != ''">-->
            <!--                order by ${memberParam.sortField} ${memberParam.sortRules}-->
            <!--            </if>-->
            and contract.del_flag = '1'
            group by ccId
            order by contract.create_time desc
        </where>
        ) a

    </select>
    <select id="selectByContractCodeAndApproveStatus" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo">
        select bcc.cc_id                              as "ccId",
               bcc.contract_code                      as "contractCode",
               bcc.change_type                        as "changeType",
               bcc.change_type_item                   as "changeTypeItem",
               bcc.customer_no                        as "customerNo",
               bcc.customer_no_old                    as "customerNoOld",
               bcc.customer_name                      as "customerName",
               bcc.customer_name_old                  as "customerNameOld",
               bcc.customer_id_type                   as "customerIdType",
               bcc.customer_id_number                 as "customerIdNumber",
               bcc.customer_tel_old                   as "customerTelOld",
               bcc.customer_tel                       as "customerTel",
               bcc.mail_address_old                   as "mailAddressOld",
               bcc.mail_address                       as "mailAddress",
               bcc.bank_phone                         as "bankPhone",
               bcc.bank_subbranch_code_old            as "bankSubbranchCodeOld",
               bcc.bank_subbranch_code                as "bankSubbranchCode",
               bcc.bank_subbranch_name_old            as "bankSubbranchNameOld",
               bcc.bank_subbranch_name                as "bankSubbranchName",
               bcc.bank_name_code_old                 as "bankNameCodeOld",
               bcc.bank_name_code                     as "bankNameCode",
               bcc.bank_name_old                      as "bankNameOld",
               bcc.bank_name                          as "bankName",
               bcc.bank_card_old                      as "bankCardOld",
               bcc.bank_card                          as "bankCard",
               bcc.change_data_urls                   as "changeDataUrls",
               bcc.change_note                        as "changeNote",
               bcc.current_transactors                as "currentTransactors",
               bcc.consignor_name                     as "consignorName",
               bcc.consignor_mobile                   as "consignorMobile",
               bcc.consignor_signatory_relation_type  as "consignorSignatoryRelationType",
               bcc.consignor_certification_type       as "consignorCertificationType",
               bcc.consignor_identity_card            as "consignorIdentityCard",
               bcc.consignor_data_urls                as "consignorDataUrls",
               bcc.consignor_identity_card_photo_url  as "consignorIdentityCardPhotoUrl",
               bcc.transactor_identity_card_photo_url as "transactorIdentityCardPhotoUrl",
               bcc.change_user_id                     as "changeUserId",
               bcc.change_user_name                   as "changeUserName",
               bcc.del_flag                           as "delFlag",
               bcc.source                             as "source",
               bcc.change_status                      as "changeStatus",
               bcc.lessee                             as "lessee",
               bcc.lessee_tel                         as "lesseeTel",
               bcc.create_user                        as "createUser",
               bcc.create_time                        as "createTime",
               bcc.modify_user                        as "modifyUser",
               bcc.modify_time                        as "modifyTime",
               bcc.tenant_id                          as "tenantId",
               bcc.cid                                as "cid",
               bcc.object_version_number              as "objectVersionNumber"
        from bbs_contract_change bcc
        where bcc.del_flag='1' and  bcc.cc_id in (select bccai.cc_id
                            from bbs_contract_change_approve_info bccai
                            where bccai.contract_code = #{contractCode}
                              and bccai.approve_status = '3')
    </select>
    <select id="selectChangeResultBycontractCode" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeResultVo">
        SELECT bccr.contract_code          as "contractCode",
               bccr.customer_no            as "customerNo",
               bccr.customer_name          as "customerName",
               bccr.customer_tel           as "customerTel",
               bccr.mail_address           as "mailAddress",
               bccr.bank_subbranch_code    as "bankSubbranchCode",
               bccr.bank_subbranch_name    as "bankSubbranchName",
               bccr.bank_name_code         as "bankNameCode",
               bccr.bank_name              as "bankName",
               bccr.bank_card              as "bankCard",
               bccr.bank_ncc_category_code as "bankNccCategoryCode"
        FROM bbs_contract_change_result bccr
        WHERE bccr.contract_code = #{contractCode};

    </select>
    <select id="selectAgreementCount" resultType="int">
        SELECT
        count( 1 )
        FROM
        bbs_contract_change
        WHERE
        del_flag = 1
        AND change_type in ('2','4')
        AND contract_code = #{contractCode};
    </select>

    <select id="selectByChangeTypeItems" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo">
        select * from bbs_contract_change a
        <where>
            a.del_flag = '1'
            and a.contract_code = #{contractCode}
            <if test="'' != changeTypeItems and changeTypeItems != null">
                <foreach item="item" index="index" collection="changeTypeItems.split(',')" open="and (" separator="OR"
                         close=")">
                     FIND_IN_SET( #{item}, a.change_type_item)
                </foreach>
            </if>
        </where>
    </select>

    <!-- 批量查询合同是否有进行中的变更（状态为0暂存、2待审核或3未通过，以及change_status=1且关联的bbs_sign_info中sign_status!=3） -->
    <select id="selectContractsWithOngoingChanges" resultType="java.lang.String">
        SELECT DISTINCT bcc.contract_code
        FROM bbs_contract_change bcc
        LEFT JOIN (select * from bbs_sign_info where del_flag = '1') bsi ON bcc.sign_id = bsi.sign_id
        WHERE bcc.del_flag = '1'
        AND (
            bcc.change_status IN ('0', '2', '3')
            OR (bcc.change_status = '1' AND bsi.sign_status NOT IN('3','8') )
        )
        AND bcc.contract_code IN
        <foreach collection="contractCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
