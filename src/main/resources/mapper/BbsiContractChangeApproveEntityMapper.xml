<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsiContractChangeApproveMapper">


    <select id="selectByPageRecord"
            resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeApproveListPageResultVo">
        select a.*
        from (SELECT relation.rr_id,
        contract.cc_id                                                                               as ccId,
        contract.contract_code                                                                       as contractCode,
        contract.change_user_name                                                                    as changeUserName,
        contract.create_time                                                                         as createTime,
        contract.change_type_item                                                                    AS changeTypeItem,
        changeType.CODE                                                                              AS changeTypeCode,
        contract.change_status                                                                       as changeStatus,

        (CASE
        WHEN changeType.CODE = '4' THEN ( SELECT GROUP_CONCAT( meaning ) AS a FROM bbs_dict WHERE type_code = 'CHANGE_TYPE_ITEM' AND FIND_IN_SET( CODE, contract.change_type_item ) )
        ELSE changeType.meaning
        END) AS changeType,

        customerType.CODE                                                                            AS customerTypeCode,
        customerType.meaning                                                                         AS customerType,
        approve.approve_id                                                                           as approveId,
        approve.approver_user_name                                                                   as approverUserName,
        approver_user_id                                                                             as approveUserId,
        approve.approve_time                                                                         as approveTime,
        approve.approve_status                                                                       as approveStatus,
        ifnull(contract.customer_name_old, customer.customer_name)                                       as customerName,
        ifnull(contract.customer_tel_old, customer.customer_tel)                                         as customerTel,
        ifnull(contract.mail_address_old, customer.mail_address)                                         as mailAddress,
        GROUP_CONCAT(product.product_name SEPARATOR ', ')                                            AS productName,
        product.community_building_name                                                              as communityBuildingName,
        sum(cast(product.house_struct_area as decimal(18,2)))                                        as houseStructArea,
        GROUP_CONCAT(CONCAT(product.rent_standard, '(', product.rent_standard_unit_name, ')') SEPARATOR ', ') AS rent,
        sign.contract_begin_time                                                                     as contractBeginTime,
        sign.contract_end_time                                                                       as contractEndTime,
        customer.customer_id_number                                                                  as customerIdNumber,
        customer.customer_credit_code                                                                as customerCreditCode,
        customer.legal_name                                                                          as legalName,
        customer.consignor_name                                                                      as consignorName,
        customer.consignor_mobile                                                                    as consignorMobile,
        sign.business_format                                                                         as businessFormat
        FROM bbs_contract_change contract
        LEFT JOIN (SELECT CODE, meaning FROM bbs_dict WHERE type_code = 'CHANGE_TYPE') changeType
        ON contract.change_type = changeType.CODE
        LEFT JOIN (select contract_code,
        sign_id,
        contract_begin_time,
        contract_end_time,
        business_format,
        business_format_name
        from bbs_sign_info
        union all
        select contract_code,
        sign_id,
        contract_begin_time,
        contract_end_time,
        business_format,
        business_format_name
        from bbs_renewal_info) sign ON sign.contract_code = contract.contract_code
        LEFT JOIN (select rr_id, sign_info_id
        from bbs_result_relation
        union all
        select rr_id, sign_info_id
        from bbs_renewal_relation) relation ON sign.sign_id = relation.sign_info_id
        LEFT JOIN (select customer_id_number,
        mail_address,
        rr_id,
        customer_credit_code,
        legal_name,
        consignor_name,
        consignor_mobile,
        customer_type,
        customer_name,
        customer_tel
        from bbs_result_customer
        union all
        select customer_id_number,
        mail_address,
        rr_id,
        customer_credit_code,
        legal_name,
        consignor_name,
        consignor_mobile,
        customer_type,
        customer_name,
        customer_tel
        from bbs_renewal_customer) customer ON relation.rr_id = customer.rr_id
        LEFT JOIN bbs_contract_change_approve_info approve ON approve.cc_id = contract.cc_id
        LEFT JOIN (SELECT CODE, meaning FROM bbs_dict WHERE type_code = 'CUSTOMER_TYPE') customerType
        ON customer.customer_type = customerType.CODE
        LEFT JOIN (select rr_id,
        product_name,
        community_building_name,
        house_struct_area,
        rent,
        rent_unit,
        rent_standard_unit_name,
        community_building_no,
        unit_no,
        group_no,
        building_no,
        rent_standard
        from bbs_result_product
        union all
        select rr_id,
        product_name,
        community_building_name,
        house_struct_area,
        rent,
        rent_unit,
        rent_standard_unit_name,
        community_building_no,
        unit_no,
        group_no,
        building_no,
        rent_standard
        from bbs_renewal_product) product ON relation.rr_id = product.rr_id
        LEFT JOIN bbs_contract_change_result contractRes ON contract.contract_code = contractRes.contract_code
        <where>
            contract.del_flag = 1
            and approve.approve_id is not null
            and relation.rr_id is not null
            <if test="'' != vo.communityBuildingName and vo.communityBuildingName != null">
                and product.community_building_name like concat('%',#{vo.communityBuildingName},'%')
            </if>
            <!--            商铺地址模糊匹配-->
            <if test="vo.productName != null and vo.productName !=''">
                and product.product_name like concat('%',#{vo.productName},'%')
            </if>
            <if test="vo.communityName != null and vo.communityName !=''">
                and product.community_building_no =#{vo.communityName}
            </if>
            <if test="vo.groupName != null and vo.groupName !=''">
                and product.group_no =#{vo.groupName}
            </if>
            <if test="vo.buildingName != null and vo.buildingName !=''">
                and product.building_no =#{vo.buildingName}
            </if>
            <if test="vo.unitName != null and vo.unitName !=''">
                and product.unit_no =#{vo.unitName}
            </if>
            <!--            商铺地址模糊匹配-->
            <if test="'' != vo.customerName and vo.customerName != null">
                and case when contract.customer_name_old is null then
                  customer.customer_name like concat('%',#{vo.customerName},'%')
                else contract.customer_name_old like concat('%',#{vo.customerName},'%') end
            </if>
            <if test="'' != vo.customerTel and vo.customerTel != null">
                and case when contract.customer_tel_old is null then
                  customer.customer_tel= #{vo.customerTel}
                else contract.customer_tel_old= #{vo.customerTel} end
            </if>
            <if test="'' != vo.customerTypeCode and vo.customerTypeCode != null">
                and customerType.CODE = #{vo.customerTypeCode}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and contract.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.changeUserName and vo.changeUserName != null">
                and contract.change_user_name like concat('%',#{vo.changeUserName},'%')
            </if>
            <if test="'' != vo.approverUserName and vo.approverUserName != null">
                and approve.approver_user_name like concat('%',#{vo.approverUserName},'%')
            </if>
            <if test="vo.createTime != null">
                and date_format(contract.create_time,'%Y-%m-%d') = date_format(#{vo.createTime},'%Y-%m-%d')
            </if>
            <if test="vo.startCreateTime != null">
                and date_format(contract.create_time,'%Y-%m-%d') &gt;= date_format(#{vo.startCreateTime},'%Y-%m-%d')
            </if>
            <if test="vo.endCreateTime != null">
                and date_format(contract.create_time,'%Y-%m-%d') &lt;= date_format(#{vo.endCreateTime},'%Y-%m-%d')
            </if>
            <if test="vo.approveStartTime  != null and vo.approveStartTime != ''">
                and approve.approve_time &gt;= concat(#{vo.approveStartTime},' 00:00:00')
            </if>
            <if test="vo.approveEndTime  != null and vo.approveEndTime != ''">
                and approve.approve_time &lt;= concat(#{vo.approveEndTime},' 23:59:59')
            </if>
            <if test="'' != vo.businessFormat and vo.businessFormat != null">
                and sign.business_format_name  like concat('%',#{vo.businessFormat},'%')
            </if>
            <if test="'' != vo.approveStatus and null != vo.approveStatus">
                and approve.approve_status = #{vo.approveStatus}
            </if>
            <if test="'' != vo.changeTypeItem and vo.changeTypeItem != null">
                <foreach item="item" index="index" collection="vo.changeTypeItem.split(',')" open="and (" separator="OR"
                         close=")">
                    FIND_IN_SET( #{item}, contract.change_type_item)
                </foreach>
            </if>

            <!--            排序字段-->
            <if test="null != vo.sort and '' != vo.sort
             and
             null != vo.orderBy  and '' != vo.orderBy  ">
                order by ${vo.sort} ${vo.orderBy}
            </if>
            group by ccId
            order by contract.create_time desc
        </where>
        ) a
    </select>


    <select id="selectByContractCode" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsContractChangeApproveDetailVo">
        select
        approver_user_name,
        approve_time,
        comment_explanation,
        description,
        approve_status,
        create_user
        from
        bbs_contract_change_approve_detail
        where
        cc_id=#{ccId}
    </select>

    <select id="selectContractChangeApproveInfosByContractCodes" resultType="java.util.Map">
        select
            t.contract_code as "contractCode",
            t.approve_status  as "approveStatus"
        from
            bbs_contract_change_approve_info t
            left join bbs_contract_change t2 on t.cc_id = t2.cc_id
        where
            t2.del_flag = '1'
            and t.contract_code in
            <foreach collection="contractCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        order by t.create_time
    </select>




    <select id="selectContractChangeApprove" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeApproveVo">
        select
        cc_id,
        r.contract_code,
        change_type,
        current_transactors,
        consignor_name ,
        consignor_mobile,
        consignor_signatory_relation_type,
        consignor_certification_type,
        consignor_certification_type,
        consignor_data_urls,
        consignor_identity_card_photo_url,
        change_data_urls,
        change_note,
        customer_name_old,
        r.customer_name,
        mail_address_old,
        r.mail_address,
        bank_name_old,
        r.bank_name,
        bank_subbranch_name_old,
        r.bank_subbranch_name,
        bank_card_old,
        r.bank_card,
        customer_tel_old,
        r.customer_tel
        from
        bbs_contract_change c
        left join bbs_contract_change_result r
        on
        c.contract_code=r.contract_code
        where
        c.cc_id=#{vo.ccId}
    </select>
    <select id="selectMsgInfo" resultType="java.util.Map">
        select ifnull(ifnull(r.customer_name, brc.customer_name), brc2.customer_name) as "customerName",
               ifnull(ifnull(r.customer_tel, brc.customer_tel), brc2.customer_tel)    as "customerTel"
        FROM bbs_contract_change_approve_info a
                 LEFT JOIN bbs_contract_change_result r on a.contract_code = r.contract_code
                 left join bbs_renewal_info bri on a.contract_code = bri.contract_code
                 left join bbs_renewal_relation brr on brr.sign_info_id = bri.sign_id
                 left join bbs_renewal_customer brc on brc.rr_id = brr.rr_id
                 left join bbs_sign_info bsi on a.contract_code = bsi.contract_code
                 left join bbs_result_relation brr2 on brr2.sign_info_id = bsi.sign_id
                 left join bbs_result_customer brc2 on brc2.rr_id = brr2.rr_id
        where a.cc_id = #{ccId}
    </select>
</mapper>
