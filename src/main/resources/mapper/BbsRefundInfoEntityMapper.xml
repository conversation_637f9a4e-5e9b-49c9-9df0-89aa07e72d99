<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsRefundInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsRefundInfoEntity">
        <id column="refund_id" property="refundId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="contract_no" property="contractNo" javaType="String"/>
        <result column="agreement_no" property="agreementNo" javaType="String"/>
        <result column="sign_id" property="signId" javaType="String"/>
        <result column="change_type" property="changeType" javaType="String"/>
        <result column="refund_status" property="refundStatus" javaType="String"/>
        <result column="refund_process" property="refundProcess" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="customer_no" property="customerNo" javaType="String"/>
        <result column="customer_name" property="customerName" javaType="String"/>
        <result column="agreement_file_id" property="agreementFileId" javaType="String"/>
        <result column="back_type" property="backType" javaType="String"/>
        <result column="bank_account_id" property="bankAccountId" javaType="String"/>
        <result column="bank_account_name" property="bankAccountName" javaType="String"/>
        <result column="bank_code" property="bankCode" javaType="String"/>
        <result column="bank_name" property="bankName" javaType="String"/>
        <result column="sub_bank_code" property="subBankCode" javaType="String"/>
        <result column="sub_bank_name" property="subBankName" javaType="String"/>
        <result column="bank_province_code" property="bankProvinceCode" javaType="String"/>
        <result column="bank_province_name" property="bankProvinceName" javaType="String"/>
        <result column="bank_city_code" property="bankCityCode" javaType="String"/>
        <result column="bank_city_name" property="bankCityName" javaType="String"/>
        <result column="customer_id_type" property="customerIdType" javaType="String"/>
        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
        <result column="change_reason" property="changeReason" javaType="String"/>
        <result column="related_file_id" property="relatedFileId" javaType="String"/>
        <result column="payment_code" property="paymentCode" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsRefundInfoPageResultVo">
        <result column="contract_no" property="contractNo" javaType="String"/>
        <result column="agreement_no" property="agreementNo" javaType="String"/>
        <result column="sign_id" property="signId" javaType="String"/>
        <result column="change_type" property="changeType" javaType="String"/>
        <result column="refund_status" property="refundStatus" javaType="String"/>
        <result column="refund_process" property="refundProcess" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="customer_no" property="customerNo" javaType="String"/>
        <result column="customer_name" property="customerName" javaType="String"/>
        <result column="agreement_file_id" property="agreementFileId" javaType="String"/>
        <result column="back_type" property="backType" javaType="String"/>
        <result column="bank_account_id" property="bankAccountId" javaType="String"/>
        <result column="bank_account_name" property="bankAccountName" javaType="String"/>
        <result column="bank_code" property="bankCode" javaType="String"/>
        <result column="bank_name" property="bankName" javaType="String"/>
        <result column="sub_bank_code" property="subBankCode" javaType="String"/>
        <result column="sub_bank_name" property="subBankName" javaType="String"/>
        <result column="bank_province_code" property="bankProvinceCode" javaType="String"/>
        <result column="bank_province_name" property="bankProvinceName" javaType="String"/>
        <result column="bank_city_code" property="bankCityCode" javaType="String"/>
        <result column="bank_city_name" property="bankCityName" javaType="String"/>
        <result column="customer_id_type" property="customerIdType" javaType="String"/>
        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
        <result column="change_reason" property="changeReason" javaType="String"/>
        <result column="related_file_id" property="relatedFileId" javaType="String"/>
        <result column="payment_code" property="paymentCode" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.refund_id
        ,base.contract_no
        ,base.agreement_no
        ,base.sign_id
        ,base.change_type
        ,base.refund_status
        ,base.refund_process
        ,base.project_id
        ,base.customer_no
        ,base.customer_name
        ,base.agreement_file_id
        ,base.back_type
        ,base.bank_account_id
        ,base.bank_account_name
        ,base.bank_code
        ,base.bank_name
        ,base.sub_bank_code
        ,base.sub_bank_name
        ,base.bank_province_code
        ,base.bank_province_name
        ,base.bank_city_code
        ,base.bank_city_name
        ,base.customer_id_type
        ,base.customer_id_number
        ,base.change_reason
        ,base.related_file_id
        ,base.payment_code
        ,base.del_flag
        ,base.create_user_name
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_refund_info base
        <where>
            and base.del_flag = 1
            <if test="'' != vo.refundId and vo.refundId != null">
                and base.refund_id = #{vo.refundId}
            </if>
            <if test="'' != vo.contractNo and vo.contractNo != null">
                and base.contract_no = #{vo.contractNo}
            </if>
            <if test="'' != vo.agreementNo and vo.agreementNo != null">
                and base.agreement_no = #{vo.agreementNo}
            </if>
            <if test="'' != vo.signId and vo.signId != null">
                and base.sign_id = #{vo.signId}
            </if>
            <if test="'' != vo.changeType and vo.changeType != null">
                and base.change_type = #{vo.changeType}
            </if>
            <if test="'' != vo.refundStatus and vo.refundStatus != null">
                and base.refund_status = #{vo.refundStatus}
            </if>
            <if test="'' != vo.refundProcess and vo.refundProcess != null">
                and base.refund_process = #{vo.refundProcess}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.customerNo and vo.customerNo != null">
                and base.customer_no = #{vo.customerNo}
            </if>
            <if test="'' != vo.customerName and vo.customerName != null">
                and base.customer_name like concat('%',#{vo.customerName},'%')
            </if>
            <if test="'' != vo.agreementFileId and vo.agreementFileId != null">
                and base.agreement_file_id = #{vo.agreementFileId}
            </if>
            <if test="'' != vo.backType and vo.backType != null">
                and base.back_type = #{vo.backType}
            </if>
            <if test="'' != vo.bankAccountId and vo.bankAccountId != null">
                and base.bank_account_id = #{vo.bankAccountId}
            </if>
            <if test="'' != vo.bankAccountName and vo.bankAccountName != null">
                and base.bank_account_name = #{vo.bankAccountName}
            </if>
            <if test="'' != vo.bankCode and vo.bankCode != null">
                and base.bank_code = #{vo.bankCode}
            </if>
            <if test="'' != vo.bankName and vo.bankName != null">
                and base.bank_name = #{vo.bankName}
            </if>
            <if test="'' != vo.subBankCode and vo.subBankCode != null">
                and base.sub_bank_code = #{vo.subBankCode}
            </if>
            <if test="'' != vo.subBankName and vo.subBankName != null">
                and base.sub_bank_name = #{vo.subBankName}
            </if>
            <if test="'' != vo.bankProvinceCode and vo.bankProvinceCode != null">
                and base.bank_province_code = #{vo.bankProvinceCode}
            </if>
            <if test="'' != vo.bankProvinceName and vo.bankProvinceName != null">
                and base.bank_province_name = #{vo.bankProvinceName}
            </if>
            <if test="'' != vo.bankCityCode and vo.bankCityCode != null">
                and base.bank_city_code = #{vo.bankCityCode}
            </if>
            <if test="'' != vo.bankCityName and vo.bankCityName != null">
                and base.bank_city_name = #{vo.bankCityName}
            </if>
            <if test="'' != vo.customerIdType and vo.customerIdType != null">
                and base.customer_id_type = #{vo.customerIdType}
            </if>
            <if test="'' != vo.customerIdNumber and vo.customerIdNumber != null">
                and base.customer_id_number = #{vo.customerIdNumber}
            </if>
            <if test="'' != vo.changeReason and vo.changeReason != null">
                and base.change_reason = #{vo.changeReason}
            </if>
            <if test="'' != vo.relatedFileId and vo.relatedFileId != null">
                and base.related_file_id = #{vo.relatedFileId}
            </if>
            <if test="'' != vo.paymentCode and vo.paymentCode != null">
                and base.payment_code = #{vo.paymentCode}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.createUserName and vo.createUserName != null">
                and base.create_user_name like concat('%',#{vo.createUserName},'%')
            </if>
            <if test="'' != vo.createTimeEnd and vo.createTimeEnd != null">
                and base.create_time &lt;= concat(#{vo.createTimeEnd},' 23:59:59')
            </if>
            <if test="'' != vo.createTimeBegin and vo.createTimeBegin != null">
                and base.create_time &gt;= concat(#{vo.createTimeBegin},' 00:00:00')
            </if>
        </where>
        ORDER BY base.create_time desc
    </select>
    <delete id="deleteBySignId">
        DELETE FROM bbs_refund_info
        WHERE sign_id = #{signId};
    </delete>
</mapper>
