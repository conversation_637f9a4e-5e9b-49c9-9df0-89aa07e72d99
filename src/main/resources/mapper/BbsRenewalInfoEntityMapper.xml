<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsRenewalInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsRenewalInfoEntity">
        <id column="sign_id" property="signId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="transaction_type" property="transactionType" javaType="String"/>
        <result column="contract_fees" property="contractFees" javaType="String"/>
        <result column="contract_template_id" property="contractTemplateId" javaType="String"/>
        <result column="contract_template_annex_urls" property="contractTemplateAnnexUrls" javaType="String"/>
        <result column="contract_template_name" property="contractTemplateName" javaType="String"/>
        <result column="parent_contract_code" property="parentContractCode" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="contract_begin_time" property="contractBeginTime" javaType="Date"/>
        <result column="contract_end_time" property="contractEndTime" javaType="Date"/>
        <result column="contract_url" property="contractUrl" javaType="String"/>
        <result column="contract_annex_urls" property="contractAnnexUrls" javaType="String"/>
        <result column="contract_type" property="contractType" javaType="String"/>
        <result column="sign_type" property="signType" javaType="String"/>
        <result column="sign_time" property="signTime" javaType="Date"/>
        <result column="sign_begin_time" property="signBeginTime" javaType="Date"/>
        <result column="sign_end_time" property="signEndTime" javaType="Date"/>
        <result column="product_source_type" property="productSourceType" javaType="String"/>
        <result column="business_format" property="businessFormat" javaType="String"/>
        <result column="business_format_name" property="businessFormatName" javaType="String"/>
        <result column="payment_cycle_code" property="paymentCycleCode" javaType="String"/>
        <result column="payment_cycle_value" property="paymentCycleValue" javaType="Integer"/>
        <result column="cash_pledge_code" property="cashPledgeCode" javaType="String"/>
        <result column="cash_pledge_value" property="cashPledgeValue" javaType="String"/>
        <result column="rent_tax_rate" property="rentTaxRate" javaType="Double"/>
        <result column="rent_free_period_type" property="rentFreePeriodType" javaType="String"/>
        <result column="rent_fp_fixed_date" property="rentFpFixedDate" javaType="String"/>
        <result column="rent_fp_fixed_value" property="rentFpFixedValue" javaType="Integer"/>
        <result column="rent_incremental_flag" property="rentIncrementalFlag" javaType="String"/>
        <result column="rent_incremental_type" property="rentIncrementalType" javaType="String"/>
        <result column="prop_tax_rate" property="propTaxRate" javaType="Double"/>
        <result column="prop_free_period_type" property="propFreePeriodType" javaType="String"/>
        <result column="prop_fp_fixed_date" property="propFpFixedDate" javaType="String"/>
        <result column="prop_fp_fixed_value" property="propFpFixedValue" javaType="Integer"/>
        <result column="prop_incremental_flag" property="propIncrementalFlag" javaType="String"/>
        <result column="prop_incremental_type" property="propIncrementalType" javaType="String"/>
        <result column="sign_status" property="signStatus" javaType="String"/>
        <result column="first_bank_name_code" property="firstBankNameCode" javaType="String"/>
        <result column="first_account_name" property="firstAccountName" javaType="String"/>
        <result column="first_bank_name" property="firstBankName" javaType="String"/>
        <result column="first_account_id" property="firstAccountId" javaType="String"/>
        <result column="contract_watermark" property="contractWatermark" javaType="String"/>
        <result column="second_wartmark_name" property="secondWartmarkName" javaType="String"/>
        <result column="second_wartmark_logo_id" property="secondWartmarkLogoId" javaType="String"/>
        <result column="second_wartmark_logo_name" property="secondWartmarkLogoName" javaType="String"/>
        <result column="second_wartmark_logo_url" property="secondWartmarkLogoUrl" javaType="String"/>
        <result column="invoicing_before_payment" property="invoicingBeforePayment" javaType="String"/>
        <result column="system_station_message_template_group_id" property="systemStationMessageTemplateGroupId"
                javaType="String"/>
        <result column="mobile_message_template_group_id" property="mobileMessageTemplateGroupId" javaType="String"/>
        <result column="upload_file_note" property="uploadFileNote" javaType="String"/>
        <result column="transactor_type" property="transactorType" javaType="String"/>
        <result column="relation_contract" property="relationContract" javaType="String"/>
        <result column="relation_contract_code" property="relationContractCode" javaType="String"/>
        <result column="contract_is_effective" property="contractIsEffective" javaType="String"/>
        <result column="deposit_return_way" property="depositReturnWay" javaType="String"/>
        <result column="deposit_return_amount" property="depositReturnAmount" javaType="Double"/>
        <result column="rent_deposit_return_way" property="rentDepositReturnWay" javaType="String"/>
        <result column="rent_deposit_return_amount" property="rentDepositReturnAmount" javaType="Double"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="lease_cycle" property="leaseCycle" javaType="String"/>
        <result column="lease_cycle_value" property="leaseCycleValue" javaType="String"/>
        <result column="sign_template_group_id" property="signTemplateGroupId" javaType="String"/>
        <result column="area_type" property="areaType" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsRenewalInfoPageResultVo">
        <result column="transaction_type" property="transactionType" javaType="String"/>
        <result column="contract_fees" property="contractFees" javaType="String"/>
        <result column="contract_template_id" property="contractTemplateId" javaType="String"/>
        <result column="contract_template_annex_urls" property="contractTemplateAnnexUrls" javaType="String"/>
        <result column="contract_template_name" property="contractTemplateName" javaType="String"/>
        <result column="parent_contract_code" property="parentContractCode" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="contract_begin_time" property="contractBeginTime" javaType="Date"/>
        <result column="contract_end_time" property="contractEndTime" javaType="Date"/>
        <result column="contract_url" property="contractUrl" javaType="String"/>
        <result column="contract_annex_urls" property="contractAnnexUrls" javaType="String"/>
        <result column="contract_type" property="contractType" javaType="String"/>
        <result column="sign_type" property="signType" javaType="String"/>
        <result column="sign_time" property="signTime" javaType="Date"/>
        <result column="sign_begin_time" property="signBeginTime" javaType="Date"/>
        <result column="sign_end_time" property="signEndTime" javaType="Date"/>
        <result column="product_source_type" property="productSourceType" javaType="String"/>
        <result column="business_format" property="businessFormat" javaType="String"/>
        <result column="business_format_name" property="businessFormatName" javaType="String"/>
        <result column="payment_cycle_code" property="paymentCycleCode" javaType="String"/>
        <result column="payment_cycle_value" property="paymentCycleValue" javaType="Integer"/>
        <result column="cash_pledge_code" property="cashPledgeCode" javaType="String"/>
        <result column="cash_pledge_value" property="cashPledgeValue" javaType="String"/>
        <result column="rent_tax_rate" property="rentTaxRate" javaType="Double"/>
        <result column="rent_free_period_type" property="rentFreePeriodType" javaType="String"/>
        <result column="rent_fp_fixed_date" property="rentFpFixedDate" javaType="String"/>
        <result column="rent_fp_fixed_value" property="rentFpFixedValue" javaType="Integer"/>
        <result column="rent_incremental_flag" property="rentIncrementalFlag" javaType="String"/>
        <result column="rent_incremental_type" property="rentIncrementalType" javaType="String"/>
        <result column="prop_tax_rate" property="propTaxRate" javaType="Double"/>
        <result column="prop_free_period_type" property="propFreePeriodType" javaType="String"/>
        <result column="prop_fp_fixed_date" property="propFpFixedDate" javaType="String"/>
        <result column="prop_fp_fixed_value" property="propFpFixedValue" javaType="Integer"/>
        <result column="prop_incremental_flag" property="propIncrementalFlag" javaType="String"/>
        <result column="prop_incremental_type" property="propIncrementalType" javaType="String"/>
        <result column="sign_status" property="signStatus" javaType="String"/>
        <result column="first_bank_name_code" property="firstBankNameCode" javaType="String"/>
        <result column="first_account_name" property="firstAccountName" javaType="String"/>
        <result column="first_bank_name" property="firstBankName" javaType="String"/>
        <result column="first_account_id" property="firstAccountId" javaType="String"/>
        <result column="contract_watermark" property="contractWatermark" javaType="String"/>
        <result column="second_wartmark_name" property="secondWartmarkName" javaType="String"/>
        <result column="second_wartmark_logo_id" property="secondWartmarkLogoId" javaType="String"/>
        <result column="second_wartmark_logo_name" property="secondWartmarkLogoName" javaType="String"/>
        <result column="second_wartmark_logo_url" property="secondWartmarkLogoUrl" javaType="String"/>
        <result column="invoicing_before_payment" property="invoicingBeforePayment" javaType="String"/>
        <result column="system_station_message_template_group_id" property="systemStationMessageTemplateGroupId"
                javaType="String"/>
        <result column="mobile_message_template_group_id" property="mobileMessageTemplateGroupId" javaType="String"/>
        <result column="upload_file_note" property="uploadFileNote" javaType="String"/>
        <result column="transactor_type" property="transactorType" javaType="String"/>
        <result column="relation_contract" property="relationContract" javaType="String"/>
        <result column="relation_contract_code" property="relationContractCode" javaType="String"/>
        <result column="contract_is_effective" property="contractIsEffective" javaType="String"/>
        <result column="deposit_return_way" property="depositReturnWay" javaType="String"/>
        <result column="deposit_return_amount" property="depositReturnAmount" javaType="Double"/>
        <result column="rent_deposit_return_way" property="rentDepositReturnWay" javaType="String"/>
        <result column="rent_deposit_return_amount" property="rentDepositReturnAmount" javaType="Double"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="lease_cycle" property="leaseCycle" javaType="String"/>
        <result column="lease_cycle_value" property="leaseCycleValue" javaType="String"/>
        <result column="sign_template_group_id" property="signTemplateGroupId" javaType="String"/>
        <result column="area_type" property="areaType" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.sign_id
        ,base.transaction_type
        ,base.contract_fees
        ,base.contract_template_id
        ,base.contract_template_annex_urls
        ,base.contract_template_name
        ,base.parent_contract_code
        ,base.contract_code
        ,base.contract_begin_time
        ,base.contract_end_time
        ,base.contract_url
        ,base.contract_annex_urls
        ,base.contract_type
        ,base.sign_type
        ,base.sign_time
        ,base.sign_begin_time
        ,base.sign_end_time
        ,base.product_source_type
        ,base.business_format
        ,base.business_format_name
        ,base.payment_cycle_code
        ,base.payment_cycle_value
        ,base.cash_pledge_code
        ,base.cash_pledge_value
        ,base.rent_tax_rate
        ,base.rent_free_period_type
        ,base.rent_fp_fixed_date
        ,base.rent_fp_fixed_value
        ,base.rent_incremental_flag
        ,base.rent_incremental_type
        ,base.prop_tax_rate
        ,base.prop_free_period_type
        ,base.prop_fp_fixed_date
        ,base.prop_fp_fixed_value
        ,base.prop_incremental_flag
        ,base.prop_incremental_type
        ,base.sign_status
        ,base.first_bank_name_code
        ,base.first_account_name
        ,base.first_bank_name
        ,base.first_account_id
        ,base.contract_watermark
        ,base.second_wartmark_name
        ,base.second_wartmark_logo_id
        ,base.second_wartmark_logo_name
        ,base.second_wartmark_logo_url
        ,base.invoicing_before_payment
        ,base.system_station_message_template_group_id
        ,base.mobile_message_template_group_id
        ,base.upload_file_note
        ,base.transactor_type
        ,base.relation_contract
        ,base.relation_contract_code
        ,base.contract_is_effective
        ,base.deposit_return_way
        ,base.deposit_return_amount
        ,base.rent_deposit_return_way
        ,base.rent_deposit_return_amount
        ,base.del_flag
        ,base.lease_cycle
        ,base.lease_cycle_value
        ,base.sign_template_group_id
        ,base.area_type
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_renewal_info base
        <where>
            <if test="'' != vo.signId and vo.signId != null">
                and base.sign_id = #{vo.signId}
            </if>
            <if test="'' != vo.transactionType and vo.transactionType != null">
                and base.transaction_type = #{vo.transactionType}
            </if>
            <if test="'' != vo.contractFees and vo.contractFees != null">
                and base.contract_fees = #{vo.contractFees}
            </if>
            <if test="'' != vo.contractTemplateId and vo.contractTemplateId != null">
                and base.contract_template_id = #{vo.contractTemplateId}
            </if>
            <if test="'' != vo.contractTemplateAnnexUrls and vo.contractTemplateAnnexUrls != null">
                and base.contract_template_annex_urls = #{vo.contractTemplateAnnexUrls}
            </if>
            <if test="'' != vo.contractTemplateName and vo.contractTemplateName != null">
                and base.contract_template_name = #{vo.contractTemplateName}
            </if>
            <if test="'' != vo.parentContractCode and vo.parentContractCode != null">
                and base.parent_contract_code = #{vo.parentContractCode}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="vo.contractBeginTime != null">
                and base.contract_begin_time = #{vo.contractBeginTime}
            </if>
            <if test="vo.contractEndTime != null">
                and base.contract_end_time = #{vo.contractEndTime}
            </if>
            <if test="'' != vo.contractUrl and vo.contractUrl != null">
                and base.contract_url = #{vo.contractUrl}
            </if>
            <if test="'' != vo.contractAnnexUrls and vo.contractAnnexUrls != null">
                and base.contract_annex_urls = #{vo.contractAnnexUrls}
            </if>
            <if test="'' != vo.contractType and vo.contractType != null">
                and base.contract_type = #{vo.contractType}
            </if>
            <if test="vo.signTime != null">
                and base.sign_time = #{vo.signTime}
            </if>
            <if test="vo.signBeginTime != null">
                and base.sign_begin_time = #{vo.signBeginTime}
            </if>
            <if test="vo.signEndTime != null">
                and base.sign_end_time = #{vo.signEndTime}
            </if>
            <if test="'' != vo.productSourceType and vo.productSourceType != null">
                and base.product_source_type = #{vo.productSourceType}
            </if>
            <if test="'' != vo.businessFormat and vo.businessFormat != null">
                and base.business_format = #{vo.businessFormat}
            </if>
            <if test="'' != vo.businessFormatName and vo.businessFormatName != null">
                and base.business_format_name = #{vo.businessFormatName}
            </if>
            <if test="'' != vo.paymentCycleCode and vo.paymentCycleCode != null">
                and base.payment_cycle_code = #{vo.paymentCycleCode}
            </if>
            <if test="vo.paymentCycleValue != null">
                and base.payment_cycle_value = #{vo.paymentCycleValue}
            </if>
            <if test="'' != vo.cashPledgeCode and vo.cashPledgeCode != null">
                and base.cash_pledge_code = #{vo.cashPledgeCode}
            </if>
            <if test="'' != vo.cashPledgeValue and vo.cashPledgeValue != null">
                and base.cash_pledge_value = #{vo.cashPledgeValue}
            </if>
            <if test="vo.rentTaxRate != null">
                and base.rent_tax_rate = #{vo.rentTaxRate}
            </if>
            <if test="'' != vo.rentFreePeriodType and vo.rentFreePeriodType != null">
                and base.rent_free_period_type = #{vo.rentFreePeriodType}
            </if>
            <if test="'' != vo.rentFpFixedDate and vo.rentFpFixedDate != null">
                and base.rent_fp_fixed_date = #{vo.rentFpFixedDate}
            </if>
            <if test="vo.rentFpFixedValue != null">
                and base.rent_fp_fixed_value = #{vo.rentFpFixedValue}
            </if>
            <if test="'' != vo.rentIncrementalFlag and vo.rentIncrementalFlag != null">
                and base.rent_incremental_flag = #{vo.rentIncrementalFlag}
            </if>
            <if test="'' != vo.rentIncrementalType and vo.rentIncrementalType != null">
                and base.rent_incremental_type = #{vo.rentIncrementalType}
            </if>
            <if test="vo.propTaxRate != null">
                and base.prop_tax_rate = #{vo.propTaxRate}
            </if>
            <if test="'' != vo.propFreePeriodType and vo.propFreePeriodType != null">
                and base.prop_free_period_type = #{vo.propFreePeriodType}
            </if>
            <if test="'' != vo.propFpFixedDate and vo.propFpFixedDate != null">
                and base.prop_fp_fixed_date = #{vo.propFpFixedDate}
            </if>
            <if test="vo.propFpFixedValue != null">
                and base.prop_fp_fixed_value = #{vo.propFpFixedValue}
            </if>
            <if test="'' != vo.propIncrementalFlag and vo.propIncrementalFlag != null">
                and base.prop_incremental_flag = #{vo.propIncrementalFlag}
            </if>
            <if test="'' != vo.propIncrementalType and vo.propIncrementalType != null">
                and base.prop_incremental_type = #{vo.propIncrementalType}
            </if>
            <if test="'' != vo.signStatus and vo.signStatus != null">
                and base.sign_status = #{vo.signStatus}
            </if>
            <if test="'' != vo.firstBankNameCode and vo.firstBankNameCode != null">
                and base.first_bank_name_code = #{vo.firstBankNameCode}
            </if>
            <if test="'' != vo.firstAccountName and vo.firstAccountName != null">
                and base.first_account_name = #{vo.firstAccountName}
            </if>
            <if test="'' != vo.firstBankName and vo.firstBankName != null">
                and base.first_bank_name = #{vo.firstBankName}
            </if>
            <if test="'' != vo.firstAccountId and vo.firstAccountId != null">
                and base.first_account_id = #{vo.firstAccountId}
            </if>
            <if test="'' != vo.contractWatermark and vo.contractWatermark != null">
                and base.contract_watermark = #{vo.contractWatermark}
            </if>
            <if test="'' != vo.secondWartmarkName and vo.secondWartmarkName != null">
                and base.second_wartmark_name = #{vo.secondWartmarkName}
            </if>
            <if test="'' != vo.secondWartmarkLogoId and vo.secondWartmarkLogoId != null">
                and base.second_wartmark_logo_id = #{vo.secondWartmarkLogoId}
            </if>
            <if test="'' != vo.secondWartmarkLogoName and vo.secondWartmarkLogoName != null">
                and base.second_wartmark_logo_name = #{vo.secondWartmarkLogoName}
            </if>
            <if test="'' != vo.secondWartmarkLogoUrl and vo.secondWartmarkLogoUrl != null">
                and base.second_wartmark_logo_url = #{vo.secondWartmarkLogoUrl}
            </if>
            <if test="'' != vo.invoicingBeforePayment and vo.invoicingBeforePayment != null">
                and base.invoicing_before_payment = #{vo.invoicingBeforePayment}
            </if>
            <if test="'' != vo.systemStationMessageTemplateGroupId and vo.systemStationMessageTemplateGroupId != null">
                and base.system_station_message_template_group_id = #{vo.systemStationMessageTemplateGroupId}
            </if>
            <if test="'' != vo.mobileMessageTemplateGroupId and vo.mobileMessageTemplateGroupId != null">
                and base.mobile_message_template_group_id = #{vo.mobileMessageTemplateGroupId}
            </if>
            <if test="'' != vo.uploadFileNote and vo.uploadFileNote != null">
                and base.upload_file_note = #{vo.uploadFileNote}
            </if>
            <if test="'' != vo.transactorType and vo.transactorType != null">
                and base.transactor_type = #{vo.transactorType}
            </if>
            <if test="'' != vo.relationContract and vo.relationContract != null">
                and base.relation_contract = #{vo.relationContract}
            </if>
            <if test="'' != vo.relationContractCode and vo.relationContractCode != null">
                and base.relation_contract_code = #{vo.relationContractCode}
            </if>
            <if test="'' != vo.contractIsEffective and vo.contractIsEffective != null">
                and base.contract_is_effective = #{vo.contractIsEffective}
            </if>
            <if test="'' != vo.depositReturnWay and vo.depositReturnWay != null">
                and base.deposit_return_way = #{vo.depositReturnWay}
            </if>
            <if test="vo.depositReturnAmount != null">
                and base.deposit_return_amount = #{vo.depositReturnAmount}
            </if>
            <if test="'' != vo.rentDepositReturnWay and vo.rentDepositReturnWay != null">
                and base.rent_deposit_return_way = #{vo.rentDepositReturnWay}
            </if>
            <if test="vo.rentDepositReturnAmount != null">
                and base.rent_deposit_return_amount = #{vo.rentDepositReturnAmount}
            </if>
            <if test="'' != vo.areaType and vo.areaType != null">
                and base.area_type = #{vo.areaType}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
    <select id="selectAppCustomByPage" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsAppSignInfoPageResultVo">
        SELECT
            sign_id AS signId,
            transaction_type AS transactionType,
            contract_code AS contractCode,
            sign_type AS signType,
            group_concat( p.product_name ) AS productName,
            group_concat( p.project_name ) AS projectName,
            group_concat( p.community_region ) AS communityRegion,
            group_concat( p.project_id ) AS projectId,
            sign_status AS signStatus,
            contract_watermark AS contractWatermark,
            '' AS projectIdFr,
            s.sign_time as signTime
        FROM v_bbs_sign_info s
            INNER JOIN v_bbs_result_relation r ON r.sign_info_id = s.sign_id
            INNER JOIN v_bbs_result_customer c ON c.rr_id = r.rr_id AND c.customer_type = '00'
            INNER JOIN v_bbs_result_product p ON p.rr_id = r.rr_id
        WHERE s.sign_status = '2'
          AND s.del_flag = 1
          AND (( sign_type = '2' AND deposit_return_way = '1' AND rent_deposit_return_way = '1' ) OR sign_type = '1' )
          <if test='vo.customerId != "1"'>
            and c.customer_no = #{vo.customerId}
          </if>
        GROUP BY sign_id,transaction_type,contract_code,sign_type,sign_status,contract_watermark,s.sign_time
        order by s.sign_time desc
    </select>

    <select id="selectAppSignByPage" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsAppSignInfoPageResultVo">
        SELECT
            sign_id AS signId,
            transaction_type AS transactionType,
            contract_code AS contractCode,
            sign_type AS signType,
            group_concat( p.product_name ) AS productName,
            group_concat( p.project_name ) AS projectName,
            group_concat( p.community_region ) AS communityRegion,
            group_concat( p.project_id ) AS projectId,
            group_concat( p.community_Building_Name ) AS communityBuildingName,
            sign_status AS signStatus,
            contract_watermark AS contractWatermark,
            '' AS projectIdFr,
            f.create_time AS signTime,
            s.create_time
        FROM v_bbs_sign_info s
                 INNER JOIN v_bbs_result_relation r ON r.sign_info_id = s.sign_id
                 INNER JOIN v_bbs_result_customer c ON c.rr_id = r.rr_id AND c.customer_type = '00'
                 INNER JOIN v_bbs_result_product p ON p.rr_id = r.rr_id
                 left join (select * from bbs_approve_info union all select * from bbs_renewal_approve_info) f on f.parent_id = s.sign_id and f.approve_type = '2' and f.del_flag = '1'
        WHERE s.del_flag = 1 and IFNULL(s.sign_type ,'') !='3'
          and s.sign_status in('3','4','6','7','8')
        <if test='vo.customerId != "1"'>
            and c.customer_no = #{vo.customerId}
        </if>
        GROUP BY sign_id,transaction_type,contract_code,sign_type,sign_status,contract_watermark,f.create_time,s.create_time
        order by s.create_time desc
    </select>

    <select id="selectByPage" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsRenewalInfoPageListResultVo">
        SELECT
        s.sign_id AS signId,
        p.community_building_name AS communityBuildingName,
        GROUP_CONCAT(p.product_name) AS productName,
        c.customer_name AS customerName,
        CASE c.customer_type WHEN '00' THEN '个人' when '01' then '企业' END AS customerType,
        s.business_format_name AS businessFormatName,
        sum(cast(
        CASE
        s.area_type
        WHEN '1' THEN p.house_struct_area
        WHEN '2' THEN p.inner_sleeve_area
        END
        AS DECIMAL(18,2))) AS houseStructArea,
        s.area_type AS areaType,
        s.contract_begin_time AS contractStartTime,
        s.contract_end_time AS contractEndTime,
        GROUP_CONCAT(CONCAT( p.rent_standard, '(', p.rent_standard_unit_name, ')' )) AS rentName,
        CASE s.cash_pledge_code WHEN '9' THEN s.cash_pledge_value ELSE e.meaning END AS cashPledge,
        s.payment_cycle_code AS paymentCycle,
        d.meaning  AS paymentCycleName,
        s.rent_free_period_type AS rentFreePeriodType,
        s.rent_fp_fixed_value AS rentFpFixedValue,
        c.customer_tel AS customerTel,
        s.sign_time AS signTime,
        s.sign_status AS signStatus,
        s.contract_watermark as contractWatermark,
        CASE s.sign_status WHEN '1' THEN '暂存' WHEN '2' THEN '待签约' WHEN '3' THEN '已签约' WHEN '4' THEN '未签约' WHEN '5' THEN '待审核' WHEN '6' THEN '未通过' WHEN '7' THEN '租户已签' when '8' then '终止' END AS signStatusName,
        (SELECT
        CASE b.approve_status WHEN '0' THEN '暂存' WHEN '1' THEN '已通过' WHEN '2' THEN '未通过' WHEN '3' THEN '审批中' WHEN '4' THEN '撤回' ELSE '--' END
        FROM
        (select bad2.parent_id, bad2.approve_type, bad2.approve_status, bad2.create_time, bad2.del_flag from bbs_renewal_approve_detail_info  bad2) b
        WHERE
        b.parent_id = s.sign_id
        AND b.approve_type = '1'
        AND b.del_flag = '1'
        ORDER BY b.create_time desc LIMIT 1
        ) AS contractSQ,
        (SELECT
        CASE a.approve_status WHEN '1' THEN '已通过' WHEN '2' THEN '未通过' WHEN '3' THEN '审批中' WHEN '4' THEN '撤回' WHEN '5' THEN '未发起' ELSE '--' END
        FROM
        ( select bad1.parent_id, bad1.approve_status, bad1.approve_type, bad1.create_time, bad1.del_flag from bbs_renewal_approve_detail_info bad1)  a
        WHERE
        a.parent_id = s.sign_id
        AND a.approve_type = '2'
        AND a.del_flag = '1'
        ORDER BY a.create_time desc LIMIT 1
        ) AS contractSH,
        s.create_user AS createUser,
        s.create_user_name AS createUserName,
        s.create_time AS createTime,
        s.contract_code AS contractCode,
        s.contract_type AS contractType
        FROM
        bbs_renewal_info s
        left join bbs_dict d on s.payment_cycle_code=d.code and d.type_code='PAYMENT_CYCLE_CODE'
        left join bbs_dict e on s.cash_pledge_code=e.code and e.type_code='CASH_PLEDGE_CODE'
        LEFT JOIN bbs_renewal_relation r ON r.sign_info_id = s.sign_id AND r.del_flag = '1'
        LEFT JOIN bbs_renewal_product p ON p.rr_id = r.rr_id AND p.del_flag = '1'
        LEFT JOIN bbs_renewal_customer c ON c.rr_id = r.rr_id AND c.del_flag = '1'
        WHERE
        s.del_flag = '1'
        <if test="vo.type != null and vo.type !=''">
            and s.sign_status=#{vo.type}
        </if>
        <if test="vo.communityBuildingName != null and vo.communityBuildingName !=''">
            and p.community_building_name like concat('%',#{vo.communityBuildingName},'%')
        </if>
        <if test="vo.productName != null and vo.productName !=''">
            and p.product_name like concat('%',#{vo.productName},'%')
        </if>
        <if test="vo.communityName != null and vo.communityName !=''">
            and p.community_building_no =#{vo.communityName}
        </if>
        <if test="vo.groupName != null and vo.groupName !=''">
            and p.group_no =#{vo.groupName}
        </if>
        <if test="vo.buildingName != null and vo.buildingName !=''">
            and p.building_no =#{vo.buildingName}
        </if>
        <if test="vo.unitName != null and vo.unitName !=''">
            and p.unit_no =#{vo.unitName}
        </if>
        <if test="vo.customerName != null and vo.customerName !=''">
            and c.customer_name like concat('%',#{vo.customerName},'%')
        </if>
        <if test="vo.customerTel != null and vo.customerTel !=''">
            and c.customer_tel =#{vo.customerTel}
        </if>
        <if test="vo.businessFormat !=null and vo.businessFormat !=''">
            and s.business_format=#{vo.businessFormat}
        </if>
        <if test="'' != vo.businessFormatName and vo.businessFormatName != null">
            and s.business_format_name like concat('%',#{vo.businessFormatName},'%')
        </if>
        <if test="vo.customerType != null and vo.customerType !=''">
            and c.customer_type =#{vo.customerType}
        </if>
        <if test="vo.contractCode != null and vo.contractCode !=''">
            and s.contract_code=#{vo.contractCode}
        </if>
        <if test="vo.createUser != null and vo.createUser !=''">
            and s.create_user_name like concat('%',#{vo.createUser},'%')
        </if>
        <if test="vo.signStartTime != null and vo.signStartTime !=''">
            and s.sign_time &gt;= concat(#{vo.signStartTime},' 00:00:00') and s.sign_time &lt;= concat(#{vo.signEndTime},' 23:59:59')
        </if>
        <if test="vo.createStartTime != null and vo.createStartTime !='' ">
            and s.create_time &gt;= concat(#{vo.createStartTime},' 00:00:00') and s.create_time &lt;= concat(#{vo.createEndTime},' 23:59:59')
        </if>
        GROUP BY s.sign_id order by s.create_time desc
    </select>


    <select id="selectByPageContact" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsRenewalInfoPageListResultVo">
        SELECT
        *
        FROM
        (
        SELECT
        b.sign_id,
        b.contract_url,
        b.sign_time,
        b.contract_code
        FROM
        bbs_sign_info b UNION ALL
        SELECT
        a.sign_id,
        a.contract_url,
        a.sign_time,
        a.contract_code
        FROM
        bbs_renewal_info a
        ) a
        where exists(select 1 from bbs_renewal_info b where b.parent_contract_code= a.contract_code and  b.sign_id = #{vo.signId} )
    </select>


    <select id="selectBySignIds" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsRenewalInfoPageListResultExcelVo">
        SELECT
        s.sign_id AS signId,
        p.community_building_name AS communityBuildingName,
        GROUP_CONCAT(p.product_name) AS productName,
        c.customer_name AS customerName,
        CASE c.customer_type WHEN '00' THEN '个人' when '01' then '企业' END AS customerType,
        s.business_format_name AS businessFormatName,
        sum(cast(
        CASE
        s.area_type
        WHEN '1' THEN p.house_struct_area
        WHEN '2' THEN p.inner_sleeve_area
        END
        AS DECIMAL(18,2))) AS houseStructArea,
        s.area_type AS areaType,
        s.contract_begin_time as contractStartTime,
        s.contract_end_time as contractEndTime,
        GROUP_CONCAT(CONCAT( p.rent_standard, '(', p.rent_standard_unit_name, ')' )) AS rentName,
        CASE s.cash_pledge_code WHEN '9' THEN s.cash_pledge_value ELSE e.meaning END AS cashPledge,
        s.payment_cycle_code  AS paymentCycle,
        d.meaning  AS paymentCycleName,
        s.rent_free_period_type as rentFreePeriodType,
        s.rent_fp_fixed_value as rentFpFixedValue,
        c.customer_tel AS customerTel,
        s.sign_time AS signTime,
        s.sign_status AS signStatus,
        CASE max(s.sign_status ) WHEN '1' THEN '暂存' WHEN '2' THEN '待签约' WHEN '3' THEN '已签约' WHEN '4' THEN '未签约' WHEN '5' THEN '待审核' WHEN '6' THEN '未通过' WHEN '7' THEN '租户已签' when '8' then '终止' END AS signStatusName,
        (SELECT
        CASE b.approve_status WHEN '1' THEN '已通过' WHEN '2' THEN '未通过' WHEN '3' THEN '待审核' WHEN '4' THEN '撤回' ELSE '--' END
        FROM
        (select bad2.parent_id, bad2.approve_type, bad2.approve_status, bad2.create_time, bad2.del_flag from bbs_renewal_approve_detail_info  bad2) b
        WHERE
        b.parent_id = s.sign_id
        AND b.approve_type = '1'
        AND b.del_flag = '1'
        ORDER BY b.create_time desc LIMIT 1
        ) AS contractSQ,
        (SELECT
        CASE a.approve_status WHEN '1' THEN '已通过' WHEN '2' THEN '未通过' WHEN '3' THEN '待审核' WHEN '4' THEN '撤回' ELSE '--' END
        FROM
        ( select bad1.parent_id, bad1.approve_status, bad1.approve_type, bad1.create_time, bad1.del_flag from bbs_renewal_approve_detail_info bad1)  a
        WHERE
        a.parent_id = s.sign_id
        AND a.approve_type = '2'
        AND a.del_flag = '1'
        ORDER BY a.create_time desc LIMIT 1
        ) AS contractSH,
        s.create_user AS createUser,
        s.create_user_name AS createUserName,
        s.create_time AS createTime,
        s.contract_code AS contractCode,
        case s.contract_type when '1' then '标准合同' when '2' then '非标合同' end AS contractType
        FROM bbs_renewal_info s
        left join bbs_dict d on s.payment_cycle_code=d.code and d.type_code='PAYMENT_CYCLE_CODE'
        left join bbs_dict e on s.cash_pledge_code=e.code and e.type_code='CASH_PLEDGE_CODE'
        LEFT JOIN bbs_renewal_relation r ON r.sign_info_id = s.sign_id AND r.del_flag = '1'
        LEFT JOIN bbs_renewal_product p ON p.rr_id = r.rr_id AND p.del_flag = '1'
        LEFT JOIN bbs_renewal_customer c ON c.rr_id = r.rr_id AND c.del_flag = '1'
        WHERE s.del_flag = '1' and s.sign_id in
        <foreach collection="signIds" item="signId" open="(" close=")" separator=",">
            #{signId}
        </foreach>
        GROUP BY s.sign_id order by s.create_time desc
    </select>
    <select id="selectApproveTimeNodeBySignId" resultType="com.bonc.ioc.bzf.busisigning.vo.BscIntentionTimeNodeVo">
        SELECT '签约审核' as "name",
               CASE a.approve_status WHEN '1' THEN	'已通过' WHEN '2' THEN	'未通过' WHEN '3' THEN	'待审核' WHEN '4' THEN '撤回' ELSE '--'	END "status",
               a.sgin_time "timeNode"
        FROM (SELECT bad1.parent_id,
                    bad1.approve_status,
                    bad1.approve_type,
                    bad1.create_time,
                    bad1.create_time sgin_time,
                    bad1.del_flag
                FROM bbs_renewal_approve_detail_info bad1
            ) a
        WHERE a.parent_id = #{signId}
          AND a.approve_type = '2'
          AND a.del_flag = '1'
        ORDER BY a.create_time
    </select>
</mapper>
