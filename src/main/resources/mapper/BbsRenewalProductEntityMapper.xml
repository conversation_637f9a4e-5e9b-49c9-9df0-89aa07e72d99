<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsRenewalProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsRenewalProductEntity">
                            <id column="rp_id" property="rpId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="rr_id" property="rrId" javaType="String"/>
                            <result column="product_name" property="productName" javaType="String"/>
                            <result column="product_no" property="productNo" javaType="String"/>
                            <result column="project_id" property="projectId" javaType="String"/>
                            <result column="project_no" property="projectNo" javaType="String"/>
                            <result column="project_name" property="projectName" javaType="String"/>
                            <result column="rent_standard_unit" property="rentStandardUnit" javaType="String"/>
                            <result column="rent_standard_unit_name" property="rentStandardUnitName" javaType="String"/>
                            <result column="rent_standard" property="rentStandard" javaType="Double"/>
                            <result column="rent_standard_no" property="rentStandardNo" javaType="String"/>
                            <result column="rent_standard_name" property="rentStandardName" javaType="String"/>
                            <result column="rent" property="rent" javaType="Double"/>
                            <result column="prop_standard_unit" property="propStandardUnit" javaType="String"/>
                            <result column="prop_standard_unit_name" property="propStandardUnitName" javaType="String"/>
                            <result column="prop_standard" property="propStandard" javaType="Double"/>
                            <result column="house_selection_channels_no" property="houseSelectionChannelsNo" javaType="String"/>
                            <result column="house_selection_channels_name" property="houseSelectionChannelsName" javaType="String"/>
                            <result column="community_building_no" property="communityBuildingNo" javaType="String"/>
                            <result column="community_building_name" property="communityBuildingName" javaType="String"/>
                            <result column="group_no" property="groupNo" javaType="String"/>
                            <result column="group_name" property="groupName" javaType="String"/>
                            <result column="building_no" property="buildingNo" javaType="String"/>
                            <result column="building_name" property="buildingName" javaType="String"/>
                            <result column="unit_no" property="unitNo" javaType="String"/>
                            <result column="unit_name" property="unitName" javaType="String"/>
                            <result column="house_type_no" property="houseTypeNo" javaType="String"/>
                            <result column="house_type_name" property="houseTypeName" javaType="String"/>
                            <result column="room_no" property="roomNo" javaType="String"/>
                            <result column="house_struct_area" property="houseStructArea" javaType="String"/>
                            <result column="inner_sleeve_area" property="innerSleeveArea" javaType="String"/>
                            <result column="jacketed_code" property="jacketedCode" javaType="String"/>
                            <result column="jacketed" property="jacketed" javaType="String"/>
                            <result column="room_name" property="roomName" javaType="String"/>
                            <result column="community_address" property="communityAddress" javaType="String"/>
                            <result column="community_region" property="communityRegion" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="current_floor_no" property="currentFloorNo" javaType="String"/>
                            <result column="total_floor_no" property="totalFloorNo" javaType="String"/>
                            <result column="house_orientation" property="houseOrientation" javaType="String"/>
                            <result column="project_short_name" property="projectShortName" javaType="String"/>
                            <result column="operate_entity_type" property="operateEntityType" javaType="String"/>
                            <result column="operate_entity_name" property="operateEntityName" javaType="String"/>
                            <result column="operate_unit_business_no" property="operateUnitBusinessNo" javaType="String"/>
                            <result column="operate_unit_no" property="operateUnitNo" javaType="String"/>
                            <result column="operate_unit_name" property="operateUnitName" javaType="String"/>
                            <result column="project_area_business_no" property="projectAreaBusinessNo" javaType="String"/>
                            <result column="project_area_no" property="projectAreaNo" javaType="String"/>
                            <result column="project_area_name" property="projectAreaName" javaType="String"/>
                            <result column="project_format" property="projectFormat" javaType="String"/>
                            <result column="project_estate" property="projectEstate" javaType="String"/>
                            <result column="house_no_ncc" property="houseNoNcc" javaType="String"/>
                            <result column="project_no_ncc" property="projectNoNcc" javaType="String"/>
                            <result column="house_hire_type" property="houseHireType" javaType="String"/>
                            <result column="house_no" property="houseNo" javaType="String"/>
                            <result column="bed_no" property="bedNo" javaType="String"/>
                            <result column="lease_mode" property="leaseMode" javaType="String"/>
                            <result column="history_state" property="historyState" javaType="String"/>
                            <result column="house_code" property="houseCode" javaType="String"/>
                            <result column="bed_room" property="bedRoom" javaType="String"/>
                            <result column="source_node" property="sourceNode" javaType="String"/>
                            <result column="price_side_ratio_no" property="priceSideRatioNo" javaType="String"/>
                            <result column="price_side_ratio_name" property="priceSideRatioName" javaType="String"/>
                            <result column="monthly_rent_rules_no" property="monthlyRentRulesNo" javaType="String"/>
                            <result column="monthly_rent_rules_name" property="monthlyRentRulesName" javaType="String"/>
                            <result column="public_rent_standard" property="publicRentStandard" javaType="Double"/>
                            <result column="market_rent_standard" property="marketRentStandard" javaType="Double"/>
                            <result column="talent_rent_standard" property="talentRentStandard" javaType="Double"/>
                            <result column="rent_unit" property="rentUnit" javaType="String"/>
                            <result column="plann_business_format_name" property="plannBusinessFormatName" javaType="String"/>
                            <result column="lowest_price" property="lowestPrice" javaType="BigDecimal"/>
                            <result column="monthly_prop_rules_no" property="monthlyPropRulesNo" javaType="String"/>
                            <result column="monthly_prop_rules_name" property="monthlyPropRulesName" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsRenewalProductPageResultVo">
                        <result column="rr_id" property="rrId" javaType="String"/>
                        <result column="product_name" property="productName" javaType="String"/>
                        <result column="product_no" property="productNo" javaType="String"/>
                        <result column="project_id" property="projectId" javaType="String"/>
                        <result column="project_no" property="projectNo" javaType="String"/>
                        <result column="project_name" property="projectName" javaType="String"/>
                        <result column="rent_standard_unit" property="rentStandardUnit" javaType="String"/>
                        <result column="rent_standard_unit_name" property="rentStandardUnitName" javaType="String"/>
                        <result column="rent_standard" property="rentStandard" javaType="Double"/>
                        <result column="rent_standard_no" property="rentStandardNo" javaType="String"/>
                        <result column="rent_standard_name" property="rentStandardName" javaType="String"/>
                        <result column="rent" property="rent" javaType="Double"/>
                        <result column="prop_standard_unit" property="propStandardUnit" javaType="String"/>
                        <result column="prop_standard_unit_name" property="propStandardUnitName" javaType="String"/>
                        <result column="prop_standard" property="propStandard" javaType="Double"/>
                        <result column="house_selection_channels_no" property="houseSelectionChannelsNo" javaType="String"/>
                        <result column="house_selection_channels_name" property="houseSelectionChannelsName" javaType="String"/>
                        <result column="community_building_no" property="communityBuildingNo" javaType="String"/>
                        <result column="community_building_name" property="communityBuildingName" javaType="String"/>
                        <result column="group_no" property="groupNo" javaType="String"/>
                        <result column="group_name" property="groupName" javaType="String"/>
                        <result column="building_no" property="buildingNo" javaType="String"/>
                        <result column="building_name" property="buildingName" javaType="String"/>
                        <result column="unit_no" property="unitNo" javaType="String"/>
                        <result column="unit_name" property="unitName" javaType="String"/>
                        <result column="house_type_no" property="houseTypeNo" javaType="String"/>
                        <result column="house_type_name" property="houseTypeName" javaType="String"/>
                        <result column="room_no" property="roomNo" javaType="String"/>
                        <result column="house_struct_area" property="houseStructArea" javaType="String"/>
                        <result column="inner_sleeve_area" property="innerSleeveArea" javaType="String"/>
                        <result column="jacketed_code" property="jacketedCode" javaType="String"/>
                        <result column="jacketed" property="jacketed" javaType="String"/>
                        <result column="room_name" property="roomName" javaType="String"/>
                        <result column="community_address" property="communityAddress" javaType="String"/>
                        <result column="community_region" property="communityRegion" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="current_floor_no" property="currentFloorNo" javaType="String"/>
                        <result column="total_floor_no" property="totalFloorNo" javaType="String"/>
                        <result column="house_orientation" property="houseOrientation" javaType="String"/>
                        <result column="project_short_name" property="projectShortName" javaType="String"/>
                        <result column="operate_entity_type" property="operateEntityType" javaType="String"/>
                        <result column="operate_entity_name" property="operateEntityName" javaType="String"/>
                        <result column="operate_unit_business_no" property="operateUnitBusinessNo" javaType="String"/>
                        <result column="operate_unit_no" property="operateUnitNo" javaType="String"/>
                        <result column="operate_unit_name" property="operateUnitName" javaType="String"/>
                        <result column="project_area_business_no" property="projectAreaBusinessNo" javaType="String"/>
                        <result column="project_area_no" property="projectAreaNo" javaType="String"/>
                        <result column="project_area_name" property="projectAreaName" javaType="String"/>
                        <result column="project_format" property="projectFormat" javaType="String"/>
                        <result column="project_estate" property="projectEstate" javaType="String"/>
                        <result column="house_no_ncc" property="houseNoNcc" javaType="String"/>
                        <result column="project_no_ncc" property="projectNoNcc" javaType="String"/>
                        <result column="house_hire_type" property="houseHireType" javaType="String"/>
                        <result column="house_no" property="houseNo" javaType="String"/>
                        <result column="bed_no" property="bedNo" javaType="String"/>
                        <result column="lease_mode" property="leaseMode" javaType="String"/>
                        <result column="history_state" property="historyState" javaType="String"/>
                        <result column="house_code" property="houseCode" javaType="String"/>
                        <result column="bed_room" property="bedRoom" javaType="String"/>
                        <result column="source_node" property="sourceNode" javaType="String"/>
                        <result column="price_side_ratio_no" property="priceSideRatioNo" javaType="String"/>
                        <result column="price_side_ratio_name" property="priceSideRatioName" javaType="String"/>
                        <result column="monthly_rent_rules_no" property="monthlyRentRulesNo" javaType="String"/>
                        <result column="monthly_rent_rules_name" property="monthlyRentRulesName" javaType="String"/>
                        <result column="public_rent_standard" property="publicRentStandard" javaType="Double"/>
                        <result column="market_rent_standard" property="marketRentStandard" javaType="Double"/>
                        <result column="talent_rent_standard" property="talentRentStandard" javaType="Double"/>
                        <result column="rent_unit" property="rentUnit" javaType="String"/>
                        <result column="plann_business_format_name" property="plannBusinessFormatName" javaType="String"/>
                        <result column="lowest_price" property="lowestPrice" javaType="BigDecimal"/>
                        <result column="monthly_prop_rules_no" property="monthlyPropRulesNo" javaType="String"/>
                        <result column="monthly_prop_rules_name" property="monthlyPropRulesName" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.rp_id
        ,base.rr_id
        ,base.product_name
        ,base.product_no
        ,base.project_id
        ,base.project_no
        ,base.project_name
        ,base.rent_standard_unit
        ,base.rent_standard_unit_name
        ,base.rent_standard
        ,base.rent_standard_no
        ,base.rent_standard_name
        ,base.rent
        ,base.prop_standard_unit
        ,base.prop_standard_unit_name
        ,base.prop_standard
        ,base.house_selection_channels_no
        ,base.house_selection_channels_name
        ,base.community_building_no
        ,base.community_building_name
        ,base.group_no
        ,base.group_name
        ,base.building_no
        ,base.building_name
        ,base.unit_no
        ,base.unit_name
        ,base.house_type_no
        ,base.house_type_name
        ,base.room_no
        ,base.house_struct_area
        ,base.inner_sleeve_area
        ,base.jacketed_code
        ,base.jacketed
        ,base.room_name
        ,base.community_address
        ,base.community_region
        ,base.del_flag
        ,base.current_floor_no
        ,base.total_floor_no
        ,base.house_orientation
        ,base.project_short_name
        ,base.operate_entity_type
        ,base.operate_entity_name
        ,base.operate_unit_business_no
        ,base.operate_unit_no
        ,base.operate_unit_name
        ,base.project_area_business_no
        ,base.project_area_no
        ,base.project_area_name
        ,base.project_format
        ,base.project_estate
        ,base.house_no_ncc
        ,base.project_no_ncc
        ,base.house_hire_type
        ,base.house_no
        ,base.bed_no
        ,base.lease_mode
        ,base.history_state
        ,base.house_code
        ,base.bed_room
        ,base.source_node
        ,base.price_side_ratio_no
        ,base.price_side_ratio_name
        ,base.monthly_rent_rules_no
        ,base.monthly_rent_rules_name
        ,base.public_rent_standard
        ,base.market_rent_standard
        ,base.talent_rent_standard
        ,base.rent_unit
        ,base.plann_business_format_name
        ,base.lowest_price
        ,base.monthly_prop_rules_no
        ,base.monthly_prop_rules_name
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_renewal_product base
        <where>
            <if test="'' != vo.rpId and vo.rpId != null">
                and base.rp_id = #{vo.rpId}
            </if>
            <if test="'' != vo.rrId and vo.rrId != null">
                and base.rr_id = #{vo.rrId}
            </if>
            <if test="'' != vo.productName and vo.productName != null">
                and base.product_name = #{vo.productName}
            </if>
            <if test="'' != vo.productNo and vo.productNo != null">
                and base.product_no = #{vo.productNo}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.projectNo and vo.projectNo != null">
                and base.project_no = #{vo.projectNo}
            </if>
            <if test="'' != vo.projectName and vo.projectName != null">
                and base.project_name = #{vo.projectName}
            </if>
            <if test="'' != vo.rentStandardUnit and vo.rentStandardUnit != null">
                and base.rent_standard_unit = #{vo.rentStandardUnit}
            </if>
            <if test="'' != vo.rentStandardUnitName and vo.rentStandardUnitName != null">
                and base.rent_standard_unit_name = #{vo.rentStandardUnitName}
            </if>
            <if test="vo.rentStandard != null">
                and base.rent_standard = #{vo.rentStandard}
            </if>
            <if test="'' != vo.rentStandardNo and vo.rentStandardNo != null">
                and base.rent_standard_no = #{vo.rentStandardNo}
            </if>
            <if test="'' != vo.rentStandardName and vo.rentStandardName != null">
                and base.rent_standard_name = #{vo.rentStandardName}
            </if>
            <if test="vo.rent != null">
                and base.rent = #{vo.rent}
            </if>
            <if test="'' != vo.propStandardUnit and vo.propStandardUnit != null">
                and base.prop_standard_unit = #{vo.propStandardUnit}
            </if>
            <if test="'' != vo.propStandardUnitName and vo.propStandardUnitName != null">
                and base.prop_standard_unit_name = #{vo.propStandardUnitName}
            </if>
            <if test="vo.propStandard != null">
                and base.prop_standard = #{vo.propStandard}
            </if>
            <if test="'' != vo.houseSelectionChannelsNo and vo.houseSelectionChannelsNo != null">
                and base.house_selection_channels_no = #{vo.houseSelectionChannelsNo}
            </if>
            <if test="'' != vo.houseSelectionChannelsName and vo.houseSelectionChannelsName != null">
                and base.house_selection_channels_name = #{vo.houseSelectionChannelsName}
            </if>
            <if test="'' != vo.communityBuildingNo and vo.communityBuildingNo != null">
                and base.community_building_no = #{vo.communityBuildingNo}
            </if>
            <if test="'' != vo.communityBuildingName and vo.communityBuildingName != null">
                and base.community_building_name = #{vo.communityBuildingName}
            </if>
            <if test="'' != vo.groupNo and vo.groupNo != null">
                and base.group_no = #{vo.groupNo}
            </if>
            <if test="'' != vo.groupName and vo.groupName != null">
                and base.group_name = #{vo.groupName}
            </if>
            <if test="'' != vo.buildingNo and vo.buildingNo != null">
                and base.building_no = #{vo.buildingNo}
            </if>
            <if test="'' != vo.buildingName and vo.buildingName != null">
                and base.building_name = #{vo.buildingName}
            </if>
            <if test="'' != vo.unitNo and vo.unitNo != null">
                and base.unit_no = #{vo.unitNo}
            </if>
            <if test="'' != vo.unitName and vo.unitName != null">
                and base.unit_name = #{vo.unitName}
            </if>
            <if test="'' != vo.houseTypeNo and vo.houseTypeNo != null">
                and base.house_type_no = #{vo.houseTypeNo}
            </if>
            <if test="'' != vo.houseTypeName and vo.houseTypeName != null">
                and base.house_type_name = #{vo.houseTypeName}
            </if>
            <if test="'' != vo.roomNo and vo.roomNo != null">
                and base.room_no = #{vo.roomNo}
            </if>
            <if test="'' != vo.houseStructArea and vo.houseStructArea != null">
                and base.house_struct_area = #{vo.houseStructArea}
            </if>
            <if test="'' != vo.innerSleeveArea and vo.innerSleeveArea != null">
                and base.inner_sleeve_area = #{vo.innerSleeveArea}
            </if>
            <if test="'' != vo.jacketedCode and vo.jacketedCode != null">
                and base.jacketed_code = #{vo.jacketedCode}
            </if>
            <if test="'' != vo.jacketed and vo.jacketed != null">
                and base.jacketed = #{vo.jacketed}
            </if>
            <if test="'' != vo.roomName and vo.roomName != null">
                and base.room_name = #{vo.roomName}
            </if>
            <if test="'' != vo.communityAddress and vo.communityAddress != null">
                and base.community_address = #{vo.communityAddress}
            </if>
            <if test="'' != vo.communityRegion and vo.communityRegion != null">
                and base.community_region = #{vo.communityRegion}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.currentFloorNo and vo.currentFloorNo != null">
                and base.current_floor_no = #{vo.currentFloorNo}
            </if>
            <if test="'' != vo.totalFloorNo and vo.totalFloorNo != null">
                and base.total_floor_no = #{vo.totalFloorNo}
            </if>
            <if test="'' != vo.houseOrientation and vo.houseOrientation != null">
                and base.house_orientation = #{vo.houseOrientation}
            </if>
            <if test="'' != vo.projectShortName and vo.projectShortName != null">
                and base.project_short_name = #{vo.projectShortName}
            </if>
            <if test="'' != vo.operateEntityType and vo.operateEntityType != null">
                and base.operate_entity_type = #{vo.operateEntityType}
            </if>
            <if test="'' != vo.operateEntityName and vo.operateEntityName != null">
                and base.operate_entity_name = #{vo.operateEntityName}
            </if>
            <if test="'' != vo.operateUnitBusinessNo and vo.operateUnitBusinessNo != null">
                and base.operate_unit_business_no = #{vo.operateUnitBusinessNo}
            </if>
            <if test="'' != vo.operateUnitNo and vo.operateUnitNo != null">
                and base.operate_unit_no = #{vo.operateUnitNo}
            </if>
            <if test="'' != vo.operateUnitName and vo.operateUnitName != null">
                and base.operate_unit_name = #{vo.operateUnitName}
            </if>
            <if test="'' != vo.projectAreaBusinessNo and vo.projectAreaBusinessNo != null">
                and base.project_area_business_no = #{vo.projectAreaBusinessNo}
            </if>
            <if test="'' != vo.projectAreaNo and vo.projectAreaNo != null">
                and base.project_area_no = #{vo.projectAreaNo}
            </if>
            <if test="'' != vo.projectAreaName and vo.projectAreaName != null">
                and base.project_area_name = #{vo.projectAreaName}
            </if>
            <if test="'' != vo.projectFormat and vo.projectFormat != null">
                and base.project_format = #{vo.projectFormat}
            </if>
            <if test="'' != vo.projectEstate and vo.projectEstate != null">
                and base.project_estate = #{vo.projectEstate}
            </if>
            <if test="'' != vo.houseNoNcc and vo.houseNoNcc != null">
                and base.house_no_ncc = #{vo.houseNoNcc}
            </if>
            <if test="'' != vo.projectNoNcc and vo.projectNoNcc != null">
                and base.project_no_ncc = #{vo.projectNoNcc}
            </if>
            <if test="'' != vo.houseHireType and vo.houseHireType != null">
                and base.house_hire_type = #{vo.houseHireType}
            </if>
            <if test="'' != vo.houseNo and vo.houseNo != null">
                and base.house_no = #{vo.houseNo}
            </if>
            <if test="'' != vo.bedNo and vo.bedNo != null">
                and base.bed_no = #{vo.bedNo}
            </if>
            <if test="'' != vo.leaseMode and vo.leaseMode != null">
                and base.lease_mode = #{vo.leaseMode}
            </if>
            <if test="'' != vo.historyState and vo.historyState != null">
                and base.history_state = #{vo.historyState}
            </if>
            <if test="'' != vo.houseCode and vo.houseCode != null">
                and base.house_code = #{vo.houseCode}
            </if>
            <if test="'' != vo.bedRoom and vo.bedRoom != null">
                and base.bed_room = #{vo.bedRoom}
            </if>
            <if test="'' != vo.sourceNode and vo.sourceNode != null">
                and base.source_node = #{vo.sourceNode}
            </if>
            <if test="'' != vo.priceSideRatioNo and vo.priceSideRatioNo != null">
                and base.price_side_ratio_no = #{vo.priceSideRatioNo}
            </if>
            <if test="'' != vo.priceSideRatioName and vo.priceSideRatioName != null">
                and base.price_side_ratio_name = #{vo.priceSideRatioName}
            </if>
            <if test="'' != vo.monthlyRentRulesNo and vo.monthlyRentRulesNo != null">
                and base.monthly_rent_rules_no = #{vo.monthlyRentRulesNo}
            </if>
            <if test="'' != vo.monthlyRentRulesName and vo.monthlyRentRulesName != null">
                and base.monthly_rent_rules_name = #{vo.monthlyRentRulesName}
            </if>
            <if test="vo.publicRentStandard != null">
                and base.public_rent_standard = #{vo.publicRentStandard}
            </if>
            <if test="vo.marketRentStandard != null">
                and base.market_rent_standard = #{vo.marketRentStandard}
            </if>
            <if test="vo.talentRentStandard != null">
                and base.talent_rent_standard = #{vo.talentRentStandard}
            </if>
            <if test="'' != vo.rentUnit and vo.rentUnit != null">
                and base.rent_unit = #{vo.rentUnit}
            </if>
            <if test="'' != vo.plannBusinessFormatName and vo.plannBusinessFormatName != null">
                and base.plann_business_format_name = #{vo.plannBusinessFormatName}
            </if>
            <if test="vo.lowestPrice != null">
                and base.lowest_price = #{vo.lowestPrice}
            </if>
            <if test="'' != vo.monthlyPropRulesNo and vo.monthlyPropRulesNo != null">
                and base.monthly_prop_rules_no = #{vo.monthlyPropRulesNo}
            </if>
            <if test="'' != vo.monthlyPropRulesName and vo.monthlyPropRulesName != null">
                and base.monthly_prop_rules_name = #{vo.monthlyPropRulesName}
            </if>
        </where>
    </select>
    <select id="selectBySignId" resultMap="BaseResultMap">
        select rp_id,rr_id,product_name,product_no,project_id,project_no,project_name from bbs_renewal_product where rr_Id in(SELECT rr_id FROM bbs_renewal_relation where sign_info_id = #{signId})
    </select>
</mapper>
