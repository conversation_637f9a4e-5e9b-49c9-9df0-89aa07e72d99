<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApplyInfoProductMapper">

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApplyInfoProductResultVo">
        <result column="renewal_apply_info_id" property="renewalApplyInfoId" javaType="String"/>
        <result column="community_building_no" property="communityBuildingNo" javaType="String"/>
        <result column="community_building_name" property="communityBuildingName" javaType="String"/>
        <result column="group_no" property="groupNo" javaType="String"/>
        <result column="group_name" property="groupName" javaType="String"/>
        <result column="building_no" property="buildingNo" javaType="String"/>
        <result column="building_name" property="buildingName" javaType="String"/>
        <result column="unit_no" property="unitNo" javaType="String"/>
        <result column="unit_name" property="unitName" javaType="String"/>
        <result column="house_no" property="houseNo" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.renewal_apply_info_id
        ,base.community_building_no
        ,base.community_building_name
        ,base.group_no
        ,base.group_name
        ,base.building_no
        ,base.building_name
        ,base.unit_no
        ,base.unit_name
        ,base.house_no
        ,base.del_flag
    </sql>

    <select id="selectByRateRecord" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_renewal_apply_info_product base
        <where>
            <if test="'' != vo.communityBuildingNo and vo.communityBuildingNo != null">
                and base.community_building_no = #{vo.communityBuildingNo}
            </if>
            <if test="'' != vo.communityBuildingName and vo.communityBuildingName != null">
                and base.community_building_name = #{vo.communityBuildingName}
            </if>
            <if test="'' != vo.groupNo and vo.groupNo != null">
                and base.group_no = #{vo.groupNo}
            </if>
            <if test="'' != vo.groupName and vo.groupName != null">
                and base.group_name = #{vo.groupName}
            </if>
            <if test="'' != vo.buildingNo and vo.buildingNo != null">
                and base.building_no = #{vo.buildingNo}
            </if>
            <if test="'' != vo.buildingName and vo.buildingName != null">
                and base.building_name = #{vo.buildingName}
            </if>
            <if test="'' != vo.unitNo and vo.unitNo != null">
                and base.unit_no = #{vo.unitNo}
            </if>
            <if test="'' != vo.unitName and vo.unitName != null">
                and base.unit_name = #{vo.unitName}
            </if>
            <if test="'' != vo.houseNo and vo.houseNo != null">
                and base.house_no = #{vo.houseNo}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>

</mapper>
