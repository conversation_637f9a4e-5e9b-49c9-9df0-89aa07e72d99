<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsChangeCalculatedProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsChangeCalculatedProductEntity">
                            <id column="pdct_id" property="pdctId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="cc_id" property="ccId" javaType="String"/>
                            <result column="product_name" property="productName" javaType="String"/>
                            <result column="product_no" property="productNo" javaType="String"/>
                            <result column="house_struct_area_old" property="houseStructAreaOld" javaType="String"/>
                            <result column="house_struct_area" property="houseStructArea" javaType="String"/>
                            <result column="rent_standard_unit" property="rentStandardUnit" javaType="String"/>
                            <result column="rent_standard_unit_name" property="rentStandardUnitName" javaType="String"/>
                            <result column="rent_standard" property="rentStandard" javaType="Double"/>
                            <result column="rp_id" property="rpId" javaType="String"/>
                            <result column="rr_id" property="rrId" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangeCalculatedProductPageResultVo">
                        <result column="cc_id" property="ccId" javaType="String"/>
                        <result column="product_name" property="productName" javaType="String"/>
                        <result column="product_no" property="productNo" javaType="String"/>
                        <result column="house_struct_area_old" property="houseStructAreaOld" javaType="String"/>
                        <result column="house_struct_area" property="houseStructArea" javaType="String"/>
                        <result column="rent_standard_unit" property="rentStandardUnit" javaType="String"/>
                        <result column="rent_standard_unit_name" property="rentStandardUnitName" javaType="String"/>
                        <result column="rent_standard" property="rentStandard" javaType="Double"/>
                        <result column="rp_id" property="rpId" javaType="String"/>
                        <result column="rr_id" property="rrId" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <resultMap id="voMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangeCalculatedProductVo">
        <result column="cc_id" property="ccId" javaType="String"/>
        <result column="product_name" property="productName" javaType="String"/>
        <result column="product_no" property="productNo" javaType="String"/>
        <result column="house_struct_area_old" property="houseStructAreaOld" javaType="String"/>
        <result column="house_struct_area" property="houseStructArea" javaType="String"/>
        <result column="rent_standard_unit" property="rentStandardUnit" javaType="String"/>
        <result column="rent_standard_unit_name" property="rentStandardUnitName" javaType="String"/>
        <result column="rent_standard" property="rentStandard" javaType="Double"/>
        <result column="rp_id" property="rpId" javaType="String"/>
        <result column="rr_id" property="rrId" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.pdct_id
        ,base.cc_id
        ,base.product_name
        ,base.product_no
        ,base.house_struct_area_old
        ,base.house_struct_area
        ,base.rent_standard_unit
        ,base.rent_standard_unit_name
        ,base.rent_standard
        ,base.rp_id
        ,base.rr_id
        ,base.del_flag
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_calculated_product base
        <where>
            <if test="'' != vo.pdctId and vo.pdctId != null">
                and base.pdct_id = #{vo.pdctId}
            </if>
            <if test="'' != vo.ccId and vo.ccId != null">
                and base.cc_id = #{vo.ccId}
            </if>
            <if test="'' != vo.productName and vo.productName != null">
                and base.product_name = #{vo.productName}
            </if>
            <if test="'' != vo.productNo and vo.productNo != null">
                and base.product_no = #{vo.productNo}
            </if>
            <if test="'' != vo.houseStructAreaOld and vo.houseStructAreaOld != null">
                and base.house_struct_area_old = #{vo.houseStructAreaOld}
            </if>
            <if test="'' != vo.houseStructArea and vo.houseStructArea != null">
                and base.house_struct_area = #{vo.houseStructArea}
            </if>
            <if test="'' != vo.rentStandardUnit and vo.rentStandardUnit != null">
                and base.rent_standard_unit = #{vo.rentStandardUnit}
            </if>
            <if test="'' != vo.rentStandardUnitName and vo.rentStandardUnitName != null">
                and base.rent_standard_unit_name = #{vo.rentStandardUnitName}
            </if>
            <if test="vo.rentStandard != null">
                and base.rent_standard = #{vo.rentStandard}
            </if>
            <if test="'' != vo.rpId and vo.rpId != null">
                and base.rp_id = #{vo.rpId}
            </if>
            <if test="'' != vo.rrId and vo.rrId != null">
                and base.rr_id = #{vo.rrId}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>

    <select id="selectByCcId" resultMap="voMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_calculated_product base
        where base.cc_id = #{ccId}
    </select>

    <delete id="deleteByCcId">
        DELETE FROM bbs_change_calculated_product
        WHERE cc_id = #{ccId};
    </delete>
</mapper>
