<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsChangeDetermineDeductionPeriodMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsChangeDetermineDeductionPeriodEntity">
        <id column="period_id" property="periodId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="cc_id" property="ccId" javaType="String"/>
        <result column="bill_id" property="billId" javaType="String"/>
        <result column="type" property="type" javaType="String"/>
        <result column="deduction_amount" property="deductionAmount" javaType="String"/>
        <result column="paid_in_money" property="paidInMoney" javaType="String"/>
        <result column="charge_period" property="chargePeriod" javaType="String"/>
        <result column="charge_start_date" property="chargeStartDate" javaType="Date"/>
        <result column="charge_end_date" property="chargeEndDate" javaType="Date"/>
        <result column="paramprice" property="paramprice" javaType="String"/>
        <result column="paramarea" property="paramarea" javaType="String"/>
        <result column="months" property="months" javaType="String"/>
        <result column="days" property="days" javaType="String"/>
        <result column="payable_money" property="payableMoney" javaType="String"/>
        <result column="tax_rate" property="taxRate" javaType="String"/>
        <result column="excluding_rate_money" property="excludingRateMoney" javaType="String"/>
        <result column="rate_money" property="rateMoney" javaType="String"/>
        <result column="day_money" property="dayMoney" javaType="String"/>
        <result column="house_name" property="houseName" javaType="String"/>
        <result column="house_id" property="houseId" javaType="String"/>
        <result column="payable_date" property="payableDate" javaType="Date"/>
        <result column="to_be_paid_money" property="toBePaidMoney" javaType="String"/>
        <result column="charge_cycle_str" property="chargeCycleStr" javaType="String"/>
        <result column="payment_status_str" property="paymentStatusStr" javaType="String"/>
        <result column="charge_status_str" property="chargeStatusStr" javaType="String"/>
        <result column="status_str" property="statusStr" javaType="String"/>
        <result column="month_amount" property="monthAmount" javaType="String"/>
        <result column="notes" property="notes" javaType="String"/>
        <result column="charge_subject_no" property="chargeSubjectNo" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangeDetermineDeductionPeriodPageResultVo">
        <result column="cc_id" property="ccId" javaType="String"/>
        <result column="bill_id" property="billId" javaType="String"/>
        <result column="type" property="type" javaType="String"/>
        <result column="deduction_amount" property="deductionAmount" javaType="String"/>
        <result column="paid_in_money" property="paidInMoney" javaType="String"/>
        <result column="charge_period" property="chargePeriod" javaType="String"/>
        <result column="charge_start_date" property="chargeStartDate" javaType="Date"/>
        <result column="charge_end_date" property="chargeEndDate" javaType="Date"/>
        <result column="paramprice" property="paramprice" javaType="String"/>
        <result column="paramarea" property="paramarea" javaType="String"/>
        <result column="months" property="months" javaType="String"/>
        <result column="days" property="days" javaType="String"/>
        <result column="payable_money" property="payableMoney" javaType="String"/>
        <result column="tax_rate" property="taxRate" javaType="String"/>
        <result column="excluding_rate_money" property="excludingRateMoney" javaType="String"/>
        <result column="rate_money" property="rateMoney" javaType="String"/>
        <result column="day_money" property="dayMoney" javaType="String"/>
        <result column="house_name" property="houseName" javaType="String"/>
        <result column="house_id" property="houseId" javaType="String"/>
        <result column="payable_date" property="payableDate" javaType="Date"/>
        <result column="to_be_paid_money" property="toBePaidMoney" javaType="String"/>
        <result column="charge_cycle_str" property="chargeCycleStr" javaType="String"/>
        <result column="payment_status_str" property="paymentStatusStr" javaType="String"/>
        <result column="charge_status_str" property="chargeStatusStr" javaType="String"/>
        <result column="status_str" property="statusStr" javaType="String"/>
        <result column="month_amount" property="monthAmount" javaType="String"/>
        <result column="notes" property="notes" javaType="String"/>
        <result column="charge_subject_no" property="chargeSubjectNo" javaType="String"/>
    </resultMap>

    <resultMap id="voMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangeDetermineDeductionPeriodVo">
        <result column="cc_id" property="ccId" javaType="String"/>
        <result column="bill_id" property="billId" javaType="String"/>
        <result column="type" property="type" javaType="String"/>
        <result column="deduction_amount" property="deductionAmount" javaType="String"/>
        <result column="paid_in_money" property="paidInMoney" javaType="String"/>
        <result column="charge_period" property="chargePeriod" javaType="String"/>
        <result column="charge_start_date" property="chargeStartDate" javaType="Date"/>
        <result column="charge_end_date" property="chargeEndDate" javaType="Date"/>
        <result column="paramprice" property="paramprice" javaType="String"/>
        <result column="paramarea" property="paramarea" javaType="String"/>
        <result column="months" property="months" javaType="String"/>
        <result column="days" property="days" javaType="String"/>
        <result column="payable_money" property="payableMoney" javaType="String"/>
        <result column="tax_rate" property="taxRate" javaType="String"/>
        <result column="excluding_rate_money" property="excludingRateMoney" javaType="String"/>
        <result column="rate_money" property="rateMoney" javaType="String"/>
        <result column="day_money" property="dayMoney" javaType="String"/>
        <result column="house_name" property="houseName" javaType="String"/>
        <result column="house_id" property="houseId" javaType="String"/>
        <result column="payable_date" property="payableDate" javaType="Date"/>
        <result column="to_be_paid_money" property="toBePaidMoney" javaType="String"/>
        <result column="charge_cycle_str" property="chargeCycleStr" javaType="String"/>
        <result column="payment_status_str" property="paymentStatusStr" javaType="String"/>
        <result column="charge_status_str" property="chargeStatusStr" javaType="String"/>
        <result column="status_str" property="statusStr" javaType="String"/>
        <result column="month_amount" property="monthAmount" javaType="String"/>
        <result column="notes" property="notes" javaType="String"/>
        <result column="charge_subject_no" property="chargeSubjectNo" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.period_id
        ,base.cc_id
        ,base.bill_id
        ,base.type
        ,base.deduction_amount
        ,base.paid_in_money
        ,base.charge_period
        ,base.charge_start_date
        ,base.charge_end_date
        ,base.paramprice
        ,base.paramarea
        ,base.months
        ,base.days
        ,base.payable_money
        ,base.tax_rate
        ,base.excluding_rate_money
        ,base.rate_money
        ,base.day_money
        ,base.house_name
        ,base.house_id
        ,base.payable_date
        ,base.to_be_paid_money
        ,base.charge_cycle_str
        ,base.payment_status_str
        ,base.charge_status_str
        ,base.status_str
        ,base.month_amount
        ,base.notes
        ,base.charge_subject_no
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_determine_deduction_period base
        <where>
            <if test="'' != vo.periodId and vo.periodId != null">
                and base.period_id = #{vo.periodId}
            </if>
            <if test="'' != vo.ccId and vo.ccId != null">
                and base.cc_id = #{vo.ccId}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.type and vo.type != null">
                and base.type = #{vo.type}
            </if>
            <if test="'' != vo.deductionAmount and vo.deductionAmount != null">
                and base.deduction_amount = #{vo.deductionAmount}
            </if>
            <if test="'' != vo.paidInMoney and vo.paidInMoney != null">
                and base.paid_in_money = #{vo.paidInMoney}
            </if>
            <if test="'' != vo.chargePeriod and vo.chargePeriod != null">
                and base.charge_period = #{vo.chargePeriod}
            </if>
            <if test="vo.chargeStartDate != null">
                and base.charge_start_date = #{vo.chargeStartDate}
            </if>
            <if test="vo.chargeEndDate != null">
                and base.charge_end_date = #{vo.chargeEndDate}
            </if>
            <if test="'' != vo.paramprice and vo.paramprice != null">
                and base.paramprice = #{vo.paramprice}
            </if>
            <if test="'' != vo.paramarea and vo.paramarea != null">
                and base.paramarea = #{vo.paramarea}
            </if>
            <if test="'' != vo.months and vo.months != null">
                and base.months = #{vo.months}
            </if>
            <if test="'' != vo.days and vo.days != null">
                and base.days = #{vo.days}
            </if>
            <if test="'' != vo.payableMoney and vo.payableMoney != null">
                and base.payable_money = #{vo.payableMoney}
            </if>
            <if test="'' != vo.taxRate and vo.taxRate != null">
                and base.tax_rate = #{vo.taxRate}
            </if>
            <if test="'' != vo.excludingRateMoney and vo.excludingRateMoney != null">
                and base.excluding_rate_money = #{vo.excludingRateMoney}
            </if>
            <if test="'' != vo.rateMoney and vo.rateMoney != null">
                and base.rate_money = #{vo.rateMoney}
            </if>
            <if test="'' != vo.dayMoney and vo.dayMoney != null">
                and base.day_money = #{vo.dayMoney}
            </if>
            <if test="'' != vo.houseName and vo.houseName != null">
                and base.house_name = #{vo.houseName}
            </if>
            <if test="'' != vo.houseId and vo.houseId != null">
                and base.house_id = #{vo.houseId}
            </if>
            <if test="vo.payableDate != null">
                and base.payable_date = #{vo.payableDate}
            </if>
            <if test="'' != vo.toBePaidMoney and vo.toBePaidMoney != null">
                and base.to_be_paid_money = #{vo.toBePaidMoney}
            </if>
            <if test="'' != vo.chargeCycleStr and vo.chargeCycleStr != null">
                and base.charge_cycle_str = #{vo.chargeCycleStr}
            </if>
            <if test="'' != vo.paymentStatusStr and vo.paymentStatusStr != null">
                and base.payment_status_str = #{vo.paymentStatusStr}
            </if>
            <if test="'' != vo.chargeStatusStr and vo.chargeStatusStr != null">
                and base.charge_status_str = #{vo.chargeStatusStr}
            </if>
            <if test="'' != vo.statusStr and vo.statusStr != null">
                and base.status_str = #{vo.statusStr}
            </if>
        </where>
    </select>


    <select id="selectByCcId" resultMap="voMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_determine_deduction_period base
        where base.cc_id = #{ccId}
        <if test="'' != type and type != null">
            <choose>
                <when test="type == '3'.toString()">
                    and base.type = '3'
                </when>
                <otherwise>
                    and (base.type = '1' or base.type is null)
                </otherwise>
            </choose>
        </if>
        ORDER BY CAST(charge_period AS UNSIGNED) ASC,house_name ASC
    </select>


    <delete id="deleteByCcId">
        DELETE FROM bbs_change_determine_deduction_period
        WHERE cc_id = #{ccId};
    </delete>
</mapper>
