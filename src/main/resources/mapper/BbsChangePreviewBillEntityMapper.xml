<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsChangePreviewBillMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsChangePreviewBillEntity">
                            <id column="bill_id" property="billId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="bill_type" property="billType" javaType="String"/>
                            <result column="cc_id" property="ccId" javaType="String"/>
                            <result column="charge_period" property="chargePeriod" javaType="Integer"/>
                            <result column="charge_start_date" property="chargeStartDate" javaType="Date"/>
                            <result column="charge_end_date" property="chargeEndDate" javaType="Date"/>
                            <result column="paramprice" property="paramprice" javaType="String"/>
                            <result column="paramarea" property="paramarea" javaType="String"/>
                            <result column="months" property="months" javaType="String"/>
                            <result column="days" property="days" javaType="String"/>
                            <result column="payable_money" property="payableMoney" javaType="String"/>
                            <result column="tax_rate" property="taxRate" javaType="String"/>
                            <result column="excluding_rate_money" property="excludingRateMoney" javaType="String"/>
                            <result column="rate_money" property="rateMoney" javaType="String"/>
                            <result column="day_money" property="dayMoney" javaType="String"/>
                            <result column="house_id" property="houseId" javaType="String"/>
                            <result column="house_name" property="houseName" javaType="String"/>
                            <result column="payable_date" property="payableDate" javaType="Date"/>
                            <result column="to_be_paid_money" property="toBePaidMoney" javaType="String"/>
                            <result column="charge_cycle_str" property="chargeCycleStr" javaType="String"/>
                            <result column="payment_status_str" property="paymentStatusStr" javaType="String"/>
                            <result column="charge_status_str" property="chargeStatusStr" javaType="String"/>
                            <result column="status_str" property="statusStr" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="create_user_name" property="createUserName" javaType="String"/>

                            <result column="month_amount" property="monthAmount" javaType="String"/>
                            <result column="notes" property="notes" javaType="String"/>
                            <result column="paid_in_money" property="paidInMoney" javaType="String"/>
                            <result column="charge_subject_no" property="chargeSubjectNo" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangePreviewBillPageResultVo">
                        <result column="bill_type" property="billType" javaType="String"/>
                        <result column="cc_id" property="ccId" javaType="String"/>
                        <result column="charge_period" property="chargePeriod" javaType="Integer"/>
                        <result column="charge_start_date" property="chargeStartDate" javaType="Date"/>
                        <result column="charge_end_date" property="chargeEndDate" javaType="Date"/>
                        <result column="paramprice" property="paramprice" javaType="String"/>
                        <result column="paramarea" property="paramarea" javaType="String"/>
                        <result column="months" property="months" javaType="String"/>
                        <result column="days" property="days" javaType="String"/>
                        <result column="payable_money" property="payableMoney" javaType="String"/>
                        <result column="tax_rate" property="taxRate" javaType="String"/>
                        <result column="excluding_rate_money" property="excludingRateMoney" javaType="String"/>
                        <result column="rate_money" property="rateMoney" javaType="String"/>
                        <result column="day_money" property="dayMoney" javaType="String"/>
                        <result column="house_id" property="houseId" javaType="String"/>
                        <result column="house_name" property="houseName" javaType="String"/>
                        <result column="payable_date" property="payableDate" javaType="Date"/>
                        <result column="to_be_paid_money" property="toBePaidMoney" javaType="String"/>
                        <result column="charge_cycle_str" property="chargeCycleStr" javaType="String"/>
                        <result column="payment_status_str" property="paymentStatusStr" javaType="String"/>
                        <result column="charge_status_str" property="chargeStatusStr" javaType="String"/>
                        <result column="status_str" property="statusStr" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="create_user_name" property="createUserName" javaType="String"/>

                        <result column="month_amount" property="monthAmount" javaType="String"/>
                        <result column="notes" property="notes" javaType="String"/>
                        <result column="paid_in_money" property="paidInMoney" javaType="String"/>
                        <result column="charge_subject_no" property="chargeSubjectNo" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.bill_id
        ,base.bill_type
        ,base.cc_id
        ,base.charge_period
        ,base.charge_start_date
        ,base.charge_end_date
        ,base.paramprice
        ,base.paramarea
        ,base.months
        ,base.days
        ,base.payable_money
        ,base.tax_rate
        ,base.excluding_rate_money
        ,base.rate_money
        ,base.day_money
        ,base.house_id
        ,base.house_name
        ,base.payable_date
        ,base.to_be_paid_money
        ,base.charge_cycle_str
        ,base.payment_status_str
        ,base.charge_status_str
        ,base.status_str
        ,base.del_flag
        ,base.create_user_name
        ,base.month_amount
        ,base.notes
        ,base.paid_in_money
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_preview_bill base
        <where>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.billType and vo.billType != null">
                and base.bill_type = #{vo.billType}
            </if>
            <if test="'' != vo.ccId and vo.ccId != null">
                and base.cc_id = #{vo.ccId}
            </if>
            <if test="vo.chargePeriod != null">
                and base.charge_period = #{vo.chargePeriod}
            </if>
            <if test="vo.chargeStartDate != null">
                and base.charge_start_date = #{vo.chargeStartDate}
            </if>
            <if test="vo.chargeEndDate != null">
                and base.charge_end_date = #{vo.chargeEndDate}
            </if>
            <if test="'' != vo.paramprice and vo.paramprice != null">
                and base.paramprice = #{vo.paramprice}
            </if>
            <if test="'' != vo.paramarea and vo.paramarea != null">
                and base.paramarea = #{vo.paramarea}
            </if>
            <if test="'' != vo.months and vo.months != null">
                and base.months = #{vo.months}
            </if>
            <if test="'' != vo.days and vo.days != null">
                and base.days = #{vo.days}
            </if>
            <if test="'' != vo.payableMoney and vo.payableMoney != null">
                and base.payable_money = #{vo.payableMoney}
            </if>
            <if test="'' != vo.taxRate and vo.taxRate != null">
                and base.tax_rate = #{vo.taxRate}
            </if>
            <if test="'' != vo.excludingRateMoney and vo.excludingRateMoney != null">
                and base.excluding_rate_money = #{vo.excludingRateMoney}
            </if>
            <if test="'' != vo.rateMoney and vo.rateMoney != null">
                and base.rate_money = #{vo.rateMoney}
            </if>
            <if test="'' != vo.dayMoney and vo.dayMoney != null">
                and base.day_money = #{vo.dayMoney}
            </if>
            <if test="'' != vo.houseId and vo.houseId != null">
                and base.house_id = #{vo.houseId}
            </if>
            <if test="'' != vo.houseName and vo.houseName != null">
                and base.house_name = #{vo.houseName}
            </if>
            <if test="vo.payableDate != null">
                and base.payable_date = #{vo.payableDate}
            </if>
            <if test="'' != vo.toBePaidMoney and vo.toBePaidMoney != null">
                and base.to_be_paid_money = #{vo.toBePaidMoney}
            </if>
            <if test="'' != vo.chargeCycleStr and vo.chargeCycleStr != null">
                and base.charge_cycle_str = #{vo.chargeCycleStr}
            </if>
            <if test="'' != vo.paymentStatusStr and vo.paymentStatusStr != null">
                and base.payment_status_str = #{vo.paymentStatusStr}
            </if>
            <if test="'' != vo.chargeStatusStr and vo.chargeStatusStr != null">
                and base.charge_status_str = #{vo.chargeStatusStr}
            </if>
            <if test="'' != vo.statusStr and vo.statusStr != null">
                and base.status_str = #{vo.statusStr}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.createUserName and vo.createUserName != null">
                and base.create_user_name = #{vo.createUserName}
            </if>
        </where>
    </select>
    <select id="selectByCcIdAndBillType" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsChangePreviewBillVo">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_preview_bill base
        where
        base.del_flag = 1
        and base.cc_id = #{ccId}
        and base.bill_type = #{billType}
        order by base.charge_period
    </select>
</mapper>
