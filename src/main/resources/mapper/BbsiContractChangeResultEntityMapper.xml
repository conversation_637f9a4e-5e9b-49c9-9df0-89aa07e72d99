<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsiContractChangeResultMapper">
    <insert id="insertOrUpdate" parameterType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeResultVo">
        insert into bbs_contract_change_result(contract_code,customer_no, customer_name, mail_address, bank_name_code,
        bank_name, bank_subbranch_name, bank_subbranch_code,
        bank_card, customer_tel,bank_ncc_category_code)
        values
        (
        #{vo.contractCode},
        #{vo.customerNo},
        #{vo.customerName},
        #{vo.mailAddress},
        #{vo.bankNameCode},
        #{vo.bankName},
        #{vo.bankSubbranchName},
        #{vo.bankSubbranchCode},
        #{vo.bankCard},
        #{vo.customerTel},
        #{vo.bankNccCategoryCode}
        )
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="'' != vo.customerNo and vo.customerNo != null">
                customer_no = #{vo.customerNo},
            </if>
            <if test="'' != vo.customerName and vo.customerName != null">
                customer_name = #{vo.customerName},
            </if>
            <if test="'' != vo.mailAddress and vo.mailAddress != null">
                mail_address = #{vo.mailAddress},
            </if>
            <if test="'' != vo.bankNameCode and vo.bankNameCode != null">
                bank_name_code = #{vo.bankNameCode},
            </if>
            <if test="'' != vo.bankName and vo.bankName != null">
                bank_name = #{vo.bankName},
            </if>
            <if test="'' != vo.bankSubbranchName and vo.bankSubbranchName != null">
                bank_subbranch_name = #{vo.bankSubbranchName},
            </if>
            <if test="'' != vo.bankSubbranchCode and vo.bankSubbranchCode != null">
                bank_subbranch_code = #{vo.bankSubbranchCode},
            </if>
            <if test="'' != vo.bankCard and vo.bankCard != null">
                bank_card = #{vo.bankCard},
            </if>
            <if test="'' != vo.customerTel and vo.customerTel != null">
                customer_tel = #{vo.customerTel},
            </if>
            <if test="'' != vo.bankNccCategoryCode and vo.bankNccCategoryCode != null">
                bank_ncc_category_code = #{vo.bankNccCategoryCode},
            </if>
        </trim>
    </insert>

    <select id="selectByContractCode" resultType="com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeResultVo">
        SELECT
        contract_code,
        customer_no,
        customer_name,
        mail_address,
        bank_name_code,
        bank_name,
        bank_subbranch_name,
        bank_subbranch_code,
        bank_card,
        customer_tel,
        change_num,
        bank_ncc_category_code
        FROM
        bbs_contract_change_result
        WHERE
        contract_code = #{contractCode}
    </select>
    <delete id="deleteByContractCode">
        DELETE
        FROM
        bbs_contract_change_result
        WHERE
        contract_code = #{contractCode}
    </delete>
</mapper>
