<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsSignFpIntervalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsSignFpIntervalEntity">
                            <id column="sfi_id" property="sfiId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="sign_info_id" property="signInfoId" javaType="String"/>
                            <result column="standard_type" property="standardType" javaType="String"/>
                            <result column="start" property="start" javaType="String"/>
                            <result column="end" property="end" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsSignFpIntervalPageResultVo">
                        <result column="sign_info_id" property="signInfoId" javaType="String"/>
                        <result column="standard_type" property="standardType" javaType="String"/>
                        <result column="start" property="start" javaType="String"/>
                        <result column="end" property="end" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.sfi_id
        ,base.sign_info_id
        ,base.standard_type
        ,base.start
        ,base.end
        ,base.del_flag
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_sign_fp_interval base
        <where>
            <if test="'' != vo.sfiId and vo.sfiId != null">
                and base.sfi_id = #{vo.sfiId}
            </if>
            <if test="'' != vo.signInfoId and vo.signInfoId != null">
                and base.sign_info_id = #{vo.signInfoId}
            </if>
            <if test="'' != vo.standardType and vo.standardType != null">
                and base.standard_type = #{vo.standardType}
            </if>
            <if test="'' != vo.start and vo.start != null">
                and base.start = #{vo.start}
            </if>
            <if test="'' != vo.end and vo.end != null">
                and base.end = #{vo.end}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
</mapper>
