<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsChangeShopInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsChangeShopInfoEntity">
                            <id column="shop_info_id" property="shopInfoId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="cc_id" property="ccId" javaType="String"/>
                            <result column="type" property="type" javaType="String"/>
                            <result column="is_split" property="isSplit" javaType="String"/>
                            <result column="product_name" property="productName" javaType="String"/>
                            <result column="product_address" property="productAddress" javaType="String"/>
                            <result column="product_no" property="productNo" javaType="String"/>
                            <result column="area" property="area" javaType="String"/>
                            <result column="area_type" property="areaType" javaType="String"/>
                            <result column="product_no_old" property="productNoOld" javaType="String"/>
        <result column="rent_standard_unit" property="rentStandardUnit" javaType="String"/>
        <result column="rent_standard" property="rentStandard" javaType="Double"/>
        <result column="product_name_old" property="productNameOld" javaType="String"/>

        <result column="property_standard_unit" property="propertyStandardUnit" javaType="String"/>
        <result column="property_standard" property="propertyStandard" javaType="Double"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangeShopInfoPageResultVo">
                        <result column="cc_id" property="ccId" javaType="String"/>
                        <result column="type" property="type" javaType="String"/>
                        <result column="is_split" property="isSplit" javaType="String"/>
                        <result column="product_name" property="productName" javaType="String"/>
                        <result column="product_address" property="productAddress" javaType="String"/>
                        <result column="product_no" property="productNo" javaType="String"/>
                        <result column="area" property="area" javaType="String"/>
                        <result column="area_type" property="areaType" javaType="String"/>
                        <result column="product_no_old" property="productNoOld" javaType="String"/>
        <result column="rent_standard_unit" property="rentStandardUnit" javaType="String"/>
        <result column="rent_standard" property="rentStandard" javaType="Double"/>
        <result column="product_name_old" property="productNameOld" javaType="String"/>

        <result column="property_standard_unit" property="propertyStandardUnit" javaType="String"/>
        <result column="property_standard" property="propertyStandard" javaType="Double"/>
    </resultMap>

    <resultMap id="voMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsChangeShopInfoVo">
        <result column="cc_id" property="ccId" javaType="String"/>
        <result column="type" property="type" javaType="String"/>
        <result column="is_split" property="isSplit" javaType="String"/>
        <result column="product_name" property="productName" javaType="String"/>
        <result column="product_address" property="productAddress" javaType="String"/>
        <result column="product_no" property="productNo" javaType="String"/>
        <result column="area" property="area" javaType="String"/>
        <result column="area_type" property="areaType" javaType="String"/>
        <result column="product_no_old" property="productNoOld" javaType="String"/>
        <result column="rent_standard_unit" property="rentStandardUnit" javaType="String"/>
        <result column="rent_standard" property="rentStandard" javaType="Double"/>
        <result column="product_name_old" property="productNameOld" javaType="String"/>

        <result column="property_standard_unit" property="propertyStandardUnit" javaType="String"/>
        <result column="property_standard" property="propertyStandard" javaType="Double"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.shop_info_id
        ,base.cc_id
        ,base.type
        ,base.is_split
        ,base.product_name
        ,base.product_address
        ,base.product_no
        ,base.area
        ,base.area_type
        ,base.product_no_old
        ,base.rent_standard_unit
        ,base.rent_standard
        ,base.product_name_old
        ,base.property_standard_unit
        ,base.property_standard
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_shop_info base
        <where>
            <if test="'' != vo.shopInfoId and vo.shopInfoId != null">
                and base.shop_info_id = #{vo.shopInfoId}
            </if>
            <if test="'' != vo.ccId and vo.ccId != null">
                and base.cc_id = #{vo.ccId}
            </if>
            <if test="'' != vo.type and vo.type != null">
                and base.type = #{vo.type}
            </if>
            <if test="'' != vo.isSplit and vo.isSplit != null">
                and base.is_split = #{vo.isSplit}
            </if>
            <if test="'' != vo.productName and vo.productName != null">
                and base.product_name = #{vo.productName}
            </if>
            <if test="'' != vo.productAddress and vo.productAddress != null">
                and base.product_address = #{vo.productAddress}
            </if>
            <if test="'' != vo.productNo and vo.productNo != null">
                and base.product_no = #{vo.productNo}
            </if>
            <if test="'' != vo.area and vo.area != null">
                and base.area = #{vo.area}
            </if>
            <if test="'' != vo.areaType and vo.areaType != null">
                and base.area_type = #{vo.areaType}
            </if>
            <if test="'' != vo.productNoOld and vo.productNoOld != null">
                and base.product_no_old = #{vo.productNoOld}
            </if>
        </where>
    </select>


    <select id="selectByCcId" resultMap="voMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_change_shop_info base
        where base.cc_id = #{ccId}
    </select>

    <select id="selectDelProductNoByCcId" resultType="string">
        SELECT
            product_no_old AS product_no
        FROM
            bbs_change_shop_info base
        WHERE is_split = '1' AND base.cc_id = #{ccId}  GROUP BY product_no_old
        UNION
        SELECT
            product_no AS product_no
        FROM
            bbs_change_shop_info base
        WHERE (is_split != '1' or is_split is null)  AND type = '2' AND base.cc_id = #{ccId} GROUP BY product_no
    </select>


    <select id="selectReduceProductNoByCcId" resultType="string">
        SELECT
            product_no AS product_no
        FROM
            bbs_change_shop_info base
        WHERE type = '2' AND base.cc_id = #{ccId} GROUP BY product_no
    </select>



    <delete id="deleteByCcId">
        DELETE FROM bbs_change_shop_info
        WHERE cc_id = #{ccId};
    </delete>
</mapper>
