<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsRenewalCustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsRenewalCustomerEntity">
                            <id column="rc_id" property="rcId" javaType="String"/>
                            <result column="create_user" property="createUser" javaType="String"/>
                            <result column="create_time" property="createTime" javaType="Date"/>
                            <result column="modify_user" property="modifyUser" javaType="String"/>
                            <result column="modify_time" property="modifyTime" javaType="Date"/>
                            <result column="tenant_id" property="tenantId" javaType="Long"/>
                            <result column="cid" property="cid" javaType="Long"/>
                            <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="relation_id" property="relationId" javaType="String"/>
                            <result column="rr_id" property="rrId" javaType="String"/>
                            <result column="type" property="type" javaType="String"/>
                            <result column="customer_no" property="customerNo" javaType="String"/>
                            <result column="customer_name" property="customerName" javaType="String"/>
                            <result column="customer_type" property="customerType" javaType="String"/>
                            <result column="customer_supplier_no" property="customerSupplierNo" javaType="String"/>
                            <result column="customer_ratio" property="customerRatio" javaType="Float"/>
                            <result column="customer_credit_code" property="customerCreditCode" javaType="String"/>
                            <result column="customer_id_type" property="customerIdType" javaType="String"/>
                            <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
                            <result column="customer_gender" property="customerGender" javaType="String"/>
                            <result column="customer_work_tel" property="customerWorkTel" javaType="String"/>
                            <result column="contact_name" property="contactName" javaType="String"/>
                            <result column="customer_tel" property="customerTel" javaType="String"/>
                            <result column="customer_public_record_no" property="customerPublicRecordNo" javaType="String"/>
                            <result column="customer_house_address" property="customerHouseAddress" javaType="String"/>
                            <result column="bank_name_code" property="bankNameCode" javaType="String"/>
                            <result column="bank_name" property="bankName" javaType="String"/>
                            <result column="bank_card" property="bankCard" javaType="String"/>
                            <result column="bank_phone" property="bankPhone" javaType="String"/>
                            <result column="bank_is_authentication" property="bankIsAuthentication" javaType="String"/>
                            <result column="bank_is_agreement" property="bankIsAgreement" javaType="String"/>
                            <result column="bank_agreement_no" property="bankAgreementNo" javaType="String"/>
                            <result column="bank_agreement_type" property="bankAgreementType" javaType="String"/>
                            <result column="bank_subbranch_code" property="bankSubbranchCode" javaType="String"/>
                            <result column="bank_subbranch_name" property="bankSubbranchName" javaType="String"/>
                            <result column="bank_ncc_category_code" property="bankNccCategoryCode" javaType="String"/>
                            <result column="bank_user_name" property="bankUserName" javaType="String"/>
                            <result column="bank_data_urls" property="bankDataUrls" javaType="String"/>
                            <result column="email_address" property="emailAddress" javaType="String"/>
                            <result column="mail_address" property="mailAddress" javaType="String"/>
                            <result column="registered_address" property="registeredAddress" javaType="String"/>
                            <result column="postal_code" property="postalCode" javaType="String"/>
                            <result column="legal_name" property="legalName" javaType="String"/>
                            <result column="legal_mobile" property="legalMobile" javaType="String"/>
                            <result column="consignor_name" property="consignorName" javaType="String"/>
                            <result column="consignor_mobile" property="consignorMobile" javaType="String"/>
                            <result column="consignor_signatory_relation_type" property="consignorSignatoryRelationType" javaType="String"/>
                            <result column="consignor_certification_type" property="consignorCertificationType" javaType="String"/>
                            <result column="consignor_identity_card" property="consignorIdentityCard" javaType="String"/>
                            <result column="consignor_data_urls" property="consignorDataUrls" javaType="String"/>
                            <result column="consignor_identity_card_photo_url" property="consignorIdentityCardPhotoUrl" javaType="String"/>
                            <result column="transactor_identity_card_photo_url" property="transactorIdentityCardPhotoUrl" javaType="String"/>
                            <result column="consignor_identity_card_front_photo_url" property="consignorIdentityCardFrontPhotoUrl" javaType="String"/>
                            <result column="consignor_identity_card_back_photo_url" property="consignorIdentityCardBackPhotoUrl" javaType="String"/>
                            <result column="transactor_identity_card_front_photo_url" property="transactorIdentityCardFrontPhotoUrl" javaType="String"/>
                            <result column="transactor_identity_card_back_photo_url" property="transactorIdentityCardBackPhotoUrl" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="tenant_customer_no" property="tenantCustomerNo" javaType="String"/>
                            <result column="tenant_supplier_no" property="tenantSupplierNo" javaType="String"/>
                            <result column="tenant_supplier_name" property="tenantSupplierName" javaType="String"/>
                            <result column="company_supplier_no" property="companySupplierNo" javaType="String"/>
                            <result column="company_supplier_name" property="companySupplierName" javaType="String"/>
                            <result column="company_id_type" property="companyIdType" javaType="String"/>
                            <result column="customer_payment_amount" property="customerPaymentAmount" javaType="String"/>
                            <result column="customer_nationality" property="customerNationality" javaType="String"/>
                            <result column="business_license" property="businessLicense" javaType="String"/>
                            <result column="apply_business_type" property="applyBusinessType" javaType="String"/>
                            <result column="apply_business_type_name" property="applyBusinessTypeName" javaType="String"/>
                            <result column="intention_id" property="intentionId" javaType="String"/>
                            <result column="deposit_return_bank_name_code" property="depositReturnBankNameCode" javaType="String"/>
                            <result column="deposit_return_bank_name" property="depositReturnBankName" javaType="String"/>
                            <result column="deposit_return_province_code" property="depositReturnProvinceCode" javaType="String"/>
                            <result column="deposit_return_province_name" property="depositReturnProvinceName" javaType="String"/>
                            <result column="deposit_return_city_code" property="depositReturnCityCode" javaType="String"/>
                            <result column="deposit_return_city_name" property="depositReturnCityName" javaType="String"/>
                            <result column="deposit_return_bank_subbranch_code" property="depositReturnBankSubbranchCode" javaType="String"/>
                            <result column="deposit_return_bank_subbranch_name" property="depositReturnBankSubbranchName" javaType="String"/>
                            <result column="deposit_return_bank_card" property="depositReturnBankCard" javaType="String"/>
                            <result column="deposit_return_modify_reason" property="depositReturnModifyReason" javaType="String"/>
                            <result column="deposit_return_upload_materials" property="depositReturnUploadMaterials" javaType="String"/>
                            <result column="rent_deposit_return_bank_name_code" property="rentDepositReturnBankNameCode" javaType="String"/>
                            <result column="rent_deposit_return_bank_name" property="rentDepositReturnBankName" javaType="String"/>
                            <result column="rent_deposit_return_province_code" property="rentDepositReturnProvinceCode" javaType="String"/>
                            <result column="rent_deposit_return_province_name" property="rentDepositReturnProvinceName" javaType="String"/>
                            <result column="rent_deposit_return_city_code" property="rentDepositReturnCityCode" javaType="String"/>
                            <result column="rent_deposit_return_city_name" property="rentDepositReturnCityName" javaType="String"/>
                            <result column="rent_deposit_return_bank_subbranch_code" property="rentDepositReturnBankSubbranchCode" javaType="String"/>
                            <result column="rent_deposit_return_bank_subbranch_name" property="rentDepositReturnBankSubbranchName" javaType="String"/>
                            <result column="rent_deposit_return_bank_card" property="rentDepositReturnBankCard" javaType="String"/>
                            <result column="rent_deposit_return_modify_reason" property="rentDepositReturnModifyReason" javaType="String"/>
                            <result column="rent_deposit_return_upload_materials" property="rentDepositReturnUploadMaterials" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsRenewalCustomerPageResultVo">
                        <result column="relation_id" property="relationId" javaType="String"/>
                        <result column="rr_id" property="rrId" javaType="String"/>
                        <result column="type" property="type" javaType="String"/>
                        <result column="customer_no" property="customerNo" javaType="String"/>
                        <result column="customer_name" property="customerName" javaType="String"/>
                        <result column="customer_type" property="customerType" javaType="String"/>
                        <result column="customer_supplier_no" property="customerSupplierNo" javaType="String"/>
                        <result column="customer_ratio" property="customerRatio" javaType="Float"/>
                        <result column="customer_credit_code" property="customerCreditCode" javaType="String"/>
                        <result column="customer_id_type" property="customerIdType" javaType="String"/>
                        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
                        <result column="customer_gender" property="customerGender" javaType="String"/>
                        <result column="customer_work_tel" property="customerWorkTel" javaType="String"/>
                        <result column="customer_tel" property="customerTel" javaType="String"/>
                        <result column="customer_public_record_no" property="customerPublicRecordNo" javaType="String"/>
                        <result column="customer_house_address" property="customerHouseAddress" javaType="String"/>
                        <result column="bank_name_code" property="bankNameCode" javaType="String"/>
                        <result column="bank_name" property="bankName" javaType="String"/>
                        <result column="bank_card" property="bankCard" javaType="String"/>
                        <result column="bank_phone" property="bankPhone" javaType="String"/>
                        <result column="bank_is_authentication" property="bankIsAuthentication" javaType="String"/>
                        <result column="bank_is_agreement" property="bankIsAgreement" javaType="String"/>
                        <result column="bank_agreement_no" property="bankAgreementNo" javaType="String"/>
                        <result column="bank_agreement_type" property="bankAgreementType" javaType="String"/>
                        <result column="bank_subbranch_code" property="bankSubbranchCode" javaType="String"/>
                        <result column="bank_subbranch_name" property="bankSubbranchName" javaType="String"/>
                        <result column="bank_ncc_category_code" property="bankNccCategoryCode" javaType="String"/>
                        <result column="bank_user_name" property="bankUserName" javaType="String"/>
                        <result column="bank_data_urls" property="bankDataUrls" javaType="String"/>
                        <result column="email_address" property="emailAddress" javaType="String"/>
                        <result column="mail_address" property="mailAddress" javaType="String"/>
                        <result column="postal_code" property="postalCode" javaType="String"/>
                        <result column="legal_name" property="legalName" javaType="String"/>
                        <result column="legal_mobile" property="legalMobile" javaType="String"/>
                        <result column="consignor_name" property="consignorName" javaType="String"/>
                        <result column="consignor_mobile" property="consignorMobile" javaType="String"/>
                        <result column="consignor_signatory_relation_type" property="consignorSignatoryRelationType" javaType="String"/>
                        <result column="consignor_certification_type" property="consignorCertificationType" javaType="String"/>
                        <result column="consignor_identity_card" property="consignorIdentityCard" javaType="String"/>
                        <result column="consignor_data_urls" property="consignorDataUrls" javaType="String"/>
                        <result column="consignor_identity_card_photo_url" property="consignorIdentityCardPhotoUrl" javaType="String"/>
                        <result column="transactor_identity_card_photo_url" property="transactorIdentityCardPhotoUrl" javaType="String"/>
                        <result column="consignor_identity_card_front_photo_url" property="consignorIdentityCardFrontPhotoUrl" javaType="String"/>
                        <result column="consignor_identity_card_back_photo_url" property="consignorIdentityCardBackPhotoUrl" javaType="String"/>
                        <result column="transactor_identity_card_front_photo_url" property="transactorIdentityCardFrontPhotoUrl" javaType="String"/>
                        <result column="transactor_identity_card_back_photo_url" property="transactorIdentityCardBackPhotoUrl" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="tenant_customer_no" property="tenantCustomerNo" javaType="String"/>
                        <result column="tenant_supplier_no" property="tenantSupplierNo" javaType="String"/>
                        <result column="tenant_supplier_name" property="tenantSupplierName" javaType="String"/>
                        <result column="company_supplier_no" property="companySupplierNo" javaType="String"/>
                        <result column="company_supplier_name" property="companySupplierName" javaType="String"/>
                        <result column="company_id_type" property="companyIdType" javaType="String"/>
                        <result column="customer_payment_amount" property="customerPaymentAmount" javaType="String"/>
                        <result column="customer_nationality" property="customerNationality" javaType="String"/>
                        <result column="business_license" property="businessLicense" javaType="String"/>
                        <result column="apply_business_type" property="applyBusinessType" javaType="String"/>
                        <result column="apply_business_type_name" property="applyBusinessTypeName" javaType="String"/>
                        <result column="intention_id" property="intentionId" javaType="String"/>
                        <result column="deposit_return_bank_name_code" property="depositReturnBankNameCode" javaType="String"/>
                        <result column="deposit_return_bank_name" property="depositReturnBankName" javaType="String"/>
                        <result column="deposit_return_province_code" property="depositReturnProvinceCode" javaType="String"/>
                        <result column="deposit_return_province_name" property="depositReturnProvinceName" javaType="String"/>
                        <result column="deposit_return_city_code" property="depositReturnCityCode" javaType="String"/>
                        <result column="deposit_return_city_name" property="depositReturnCityName" javaType="String"/>
                        <result column="deposit_return_bank_subbranch_code" property="depositReturnBankSubbranchCode" javaType="String"/>
                        <result column="deposit_return_bank_subbranch_name" property="depositReturnBankSubbranchName" javaType="String"/>
                        <result column="deposit_return_bank_card" property="depositReturnBankCard" javaType="String"/>
                        <result column="deposit_return_modify_reason" property="depositReturnModifyReason" javaType="String"/>
                        <result column="deposit_return_upload_materials" property="depositReturnUploadMaterials" javaType="String"/>
                        <result column="rent_deposit_return_bank_name_code" property="rentDepositReturnBankNameCode" javaType="String"/>
                        <result column="rent_deposit_return_bank_name" property="rentDepositReturnBankName" javaType="String"/>
                        <result column="rent_deposit_return_province_code" property="rentDepositReturnProvinceCode" javaType="String"/>
                        <result column="rent_deposit_return_province_name" property="rentDepositReturnProvinceName" javaType="String"/>
                        <result column="rent_deposit_return_city_code" property="rentDepositReturnCityCode" javaType="String"/>
                        <result column="rent_deposit_return_city_name" property="rentDepositReturnCityName" javaType="String"/>
                        <result column="rent_deposit_return_bank_subbranch_code" property="rentDepositReturnBankSubbranchCode" javaType="String"/>
                        <result column="rent_deposit_return_bank_subbranch_name" property="rentDepositReturnBankSubbranchName" javaType="String"/>
                        <result column="rent_deposit_return_bank_card" property="rentDepositReturnBankCard" javaType="String"/>
                        <result column="rent_deposit_return_modify_reason" property="rentDepositReturnModifyReason" javaType="String"/>
                        <result column="rent_deposit_return_upload_materials" property="rentDepositReturnUploadMaterials" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.rc_id
        ,base.relation_id
        ,base.rr_id
        ,base.type
        ,base.customer_no
        ,base.customer_name
        ,base.customer_type
        ,base.customer_supplier_no
        ,base.customer_ratio
        ,base.customer_credit_code
        ,base.customer_id_type
        ,base.customer_id_number
        ,base.customer_gender
        ,base.customer_work_tel
        ,base.customer_tel
        ,base.customer_public_record_no
        ,base.customer_house_address
        ,base.bank_name_code
        ,base.bank_name
        ,base.bank_card
        ,base.bank_phone
        ,base.bank_is_authentication
        ,base.bank_is_agreement
        ,base.bank_agreement_no
        ,base.bank_agreement_type
        ,base.bank_subbranch_code
        ,base.bank_subbranch_name
        ,base.bank_ncc_category_code
        ,base.bank_user_name
        ,base.bank_data_urls
        ,base.email_address
        ,base.mail_address
        ,base.postal_code
        ,base.legal_name
        ,base.legal_mobile
        ,base.consignor_name
        ,base.consignor_mobile
        ,base.consignor_signatory_relation_type
        ,base.consignor_certification_type
        ,base.consignor_identity_card
        ,base.consignor_data_urls
        ,base.consignor_identity_card_photo_url
        ,base.transactor_identity_card_photo_url
        ,base.consignor_identity_card_front_photo_url
        ,base.consignor_identity_card_back_photo_url
        ,base.transactor_identity_card_front_photo_url
        ,base.transactor_identity_card_back_photo_url
        ,base.del_flag
        ,base.tenant_customer_no
        ,base.tenant_supplier_no
        ,base.tenant_supplier_name
        ,base.company_supplier_no
        ,base.company_supplier_name
        ,base.company_id_type
        ,base.customer_payment_amount
        ,base.customer_nationality
        ,base.business_license
        ,base.apply_business_type
        ,base.apply_business_type_name
        ,base.intention_id
        ,base.deposit_return_bank_name_code
        ,base.deposit_return_bank_name
        ,base.deposit_return_province_code
        ,base.deposit_return_province_name
        ,base.deposit_return_city_code
        ,base.deposit_return_city_name
        ,base.deposit_return_bank_subbranch_code
        ,base.deposit_return_bank_subbranch_name
        ,base.deposit_return_bank_card
        ,base.deposit_return_modify_reason
        ,base.deposit_return_upload_materials
        ,base.rent_deposit_return_bank_name_code
        ,base.rent_deposit_return_bank_name
        ,base.rent_deposit_return_province_code
        ,base.rent_deposit_return_province_name
        ,base.rent_deposit_return_city_code
        ,base.rent_deposit_return_city_name
        ,base.rent_deposit_return_bank_subbranch_code
        ,base.rent_deposit_return_bank_subbranch_name
        ,base.rent_deposit_return_bank_card
        ,base.rent_deposit_return_modify_reason
        ,base.rent_deposit_return_upload_materials
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbs_renewal_customer base
        <where>
            <if test="'' != vo.rcId and vo.rcId != null">
                and base.rc_id = #{vo.rcId}
            </if>
            <if test="'' != vo.relationId and vo.relationId != null">
                and base.relation_id = #{vo.relationId}
            </if>
            <if test="'' != vo.rrId and vo.rrId != null">
                and base.rr_id = #{vo.rrId}
            </if>
            <if test="'' != vo.type and vo.type != null">
                and base.type = #{vo.type}
            </if>
            <if test="'' != vo.customerNo and vo.customerNo != null">
                and base.customer_no = #{vo.customerNo}
            </if>
            <if test="'' != vo.customerName and vo.customerName != null">
                and base.customer_name = #{vo.customerName}
            </if>
            <if test="'' != vo.customerType and vo.customerType != null">
                and base.customer_type = #{vo.customerType}
            </if>
            <if test="'' != vo.customerSupplierNo and vo.customerSupplierNo != null">
                and base.customer_supplier_no = #{vo.customerSupplierNo}
            </if>
            <if test="vo.customerRatio != null">
                and base.customer_ratio = #{vo.customerRatio}
            </if>
            <if test="'' != vo.customerCreditCode and vo.customerCreditCode != null">
                and base.customer_credit_code = #{vo.customerCreditCode}
            </if>
            <if test="'' != vo.customerIdType and vo.customerIdType != null">
                and base.customer_id_type = #{vo.customerIdType}
            </if>
            <if test="'' != vo.customerIdNumber and vo.customerIdNumber != null">
                and base.customer_id_number = #{vo.customerIdNumber}
            </if>
            <if test="'' != vo.customerGender and vo.customerGender != null">
                and base.customer_gender = #{vo.customerGender}
            </if>
            <if test="'' != vo.customerWorkTel and vo.customerWorkTel != null">
                and base.customer_work_tel = #{vo.customerWorkTel}
            </if>
            <if test="'' != vo.customerTel and vo.customerTel != null">
                and base.customer_tel = #{vo.customerTel}
            </if>
            <if test="'' != vo.customerPublicRecordNo and vo.customerPublicRecordNo != null">
                and base.customer_public_record_no = #{vo.customerPublicRecordNo}
            </if>
            <if test="'' != vo.customerHouseAddress and vo.customerHouseAddress != null">
                and base.customer_house_address = #{vo.customerHouseAddress}
            </if>
            <if test="'' != vo.bankNameCode and vo.bankNameCode != null">
                and base.bank_name_code = #{vo.bankNameCode}
            </if>
            <if test="'' != vo.bankName and vo.bankName != null">
                and base.bank_name = #{vo.bankName}
            </if>
            <if test="'' != vo.bankCard and vo.bankCard != null">
                and base.bank_card = #{vo.bankCard}
            </if>
            <if test="'' != vo.bankPhone and vo.bankPhone != null">
                and base.bank_phone = #{vo.bankPhone}
            </if>
            <if test="'' != vo.bankIsAuthentication and vo.bankIsAuthentication != null">
                and base.bank_is_authentication = #{vo.bankIsAuthentication}
            </if>
            <if test="'' != vo.bankIsAgreement and vo.bankIsAgreement != null">
                and base.bank_is_agreement = #{vo.bankIsAgreement}
            </if>
            <if test="'' != vo.bankAgreementNo and vo.bankAgreementNo != null">
                and base.bank_agreement_no = #{vo.bankAgreementNo}
            </if>
            <if test="'' != vo.bankAgreementType and vo.bankAgreementType != null">
                and base.bank_agreement_type = #{vo.bankAgreementType}
            </if>
            <if test="'' != vo.bankSubbranchCode and vo.bankSubbranchCode != null">
                and base.bank_subbranch_code = #{vo.bankSubbranchCode}
            </if>
            <if test="'' != vo.bankSubbranchName and vo.bankSubbranchName != null">
                and base.bank_subbranch_name = #{vo.bankSubbranchName}
            </if>
            <if test="'' != vo.bankNccCategoryCode and vo.bankNccCategoryCode != null">
                and base.bank_ncc_category_code = #{vo.bankNccCategoryCode}
            </if>
            <if test="'' != vo.bankUserName and vo.bankUserName != null">
                and base.bank_user_name = #{vo.bankUserName}
            </if>
            <if test="'' != vo.bankDataUrls and vo.bankDataUrls != null">
                and base.bank_data_urls = #{vo.bankDataUrls}
            </if>
            <if test="'' != vo.emailAddress and vo.emailAddress != null">
                and base.email_address = #{vo.emailAddress}
            </if>
            <if test="'' != vo.mailAddress and vo.mailAddress != null">
                and base.mail_address = #{vo.mailAddress}
            </if>
            <if test="'' != vo.postalCode and vo.postalCode != null">
                and base.postal_code = #{vo.postalCode}
            </if>
            <if test="'' != vo.legalName and vo.legalName != null">
                and base.legal_name = #{vo.legalName}
            </if>
            <if test="'' != vo.legalMobile and vo.legalMobile != null">
                and base.legal_mobile = #{vo.legalMobile}
            </if>
            <if test="'' != vo.consignorName and vo.consignorName != null">
                and base.consignor_name = #{vo.consignorName}
            </if>
            <if test="'' != vo.consignorMobile and vo.consignorMobile != null">
                and base.consignor_mobile = #{vo.consignorMobile}
            </if>
            <if test="'' != vo.consignorSignatoryRelationType and vo.consignorSignatoryRelationType != null">
                and base.consignor_signatory_relation_type = #{vo.consignorSignatoryRelationType}
            </if>
            <if test="'' != vo.consignorCertificationType and vo.consignorCertificationType != null">
                and base.consignor_certification_type = #{vo.consignorCertificationType}
            </if>
            <if test="'' != vo.consignorIdentityCard and vo.consignorIdentityCard != null">
                and base.consignor_identity_card = #{vo.consignorIdentityCard}
            </if>
            <if test="'' != vo.consignorDataUrls and vo.consignorDataUrls != null">
                and base.consignor_data_urls = #{vo.consignorDataUrls}
            </if>
            <if test="'' != vo.consignorIdentityCardPhotoUrl and vo.consignorIdentityCardPhotoUrl != null">
                and base.consignor_identity_card_photo_url = #{vo.consignorIdentityCardPhotoUrl}
            </if>
            <if test="'' != vo.transactorIdentityCardPhotoUrl and vo.transactorIdentityCardPhotoUrl != null">
                and base.transactor_identity_card_photo_url = #{vo.transactorIdentityCardPhotoUrl}
            </if>
            <if test="'' != vo.consignorIdentityCardFrontPhotoUrl and vo.consignorIdentityCardFrontPhotoUrl != null">
                and base.consignor_identity_card_front_photo_url = #{vo.consignorIdentityCardFrontPhotoUrl}
            </if>
            <if test="'' != vo.consignorIdentityCardBackPhotoUrl and vo.consignorIdentityCardBackPhotoUrl != null">
                and base.consignor_identity_card_back_photo_url = #{vo.consignorIdentityCardBackPhotoUrl}
            </if>
            <if test="'' != vo.transactorIdentityCardFrontPhotoUrl and vo.transactorIdentityCardFrontPhotoUrl != null">
                and base.transactor_identity_card_front_photo_url = #{vo.transactorIdentityCardFrontPhotoUrl}
            </if>
            <if test="'' != vo.transactorIdentityCardBackPhotoUrl and vo.transactorIdentityCardBackPhotoUrl != null">
                and base.transactor_identity_card_back_photo_url = #{vo.transactorIdentityCardBackPhotoUrl}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.tenantCustomerNo and vo.tenantCustomerNo != null">
                and base.tenant_customer_no = #{vo.tenantCustomerNo}
            </if>
            <if test="'' != vo.tenantSupplierNo and vo.tenantSupplierNo != null">
                and base.tenant_supplier_no = #{vo.tenantSupplierNo}
            </if>
            <if test="'' != vo.tenantSupplierName and vo.tenantSupplierName != null">
                and base.tenant_supplier_name = #{vo.tenantSupplierName}
            </if>
            <if test="'' != vo.companySupplierNo and vo.companySupplierNo != null">
                and base.company_supplier_no = #{vo.companySupplierNo}
            </if>
            <if test="'' != vo.companySupplierName and vo.companySupplierName != null">
                and base.company_supplier_name = #{vo.companySupplierName}
            </if>
            <if test="'' != vo.companyIdType and vo.companyIdType != null">
                and base.company_id_type = #{vo.companyIdType}
            </if>
            <if test="'' != vo.customerPaymentAmount and vo.customerPaymentAmount != null">
                and base.customer_payment_amount = #{vo.customerPaymentAmount}
            </if>
            <if test="'' != vo.customerNationality and vo.customerNationality != null">
                and base.customer_nationality = #{vo.customerNationality}
            </if>
            <if test="'' != vo.businessLicense and vo.businessLicense != null">
                and base.business_license = #{vo.businessLicense}
            </if>
            <if test="'' != vo.applyBusinessType and vo.applyBusinessType != null">
                and base.apply_business_type = #{vo.applyBusinessType}
            </if>
            <if test="'' != vo.applyBusinessTypeName and vo.applyBusinessTypeName != null">
                and base.apply_business_type_name = #{vo.applyBusinessTypeName}
            </if>
            <if test="'' != vo.intentionId and vo.intentionId != null">
                and base.intention_id = #{vo.intentionId}
            </if>
            <if test="'' != vo.depositReturnBankNameCode and vo.depositReturnBankNameCode != null">
                and base.deposit_return_bank_name_code = #{vo.depositReturnBankNameCode}
            </if>
            <if test="'' != vo.depositReturnBankName and vo.depositReturnBankName != null">
                and base.deposit_return_bank_name = #{vo.depositReturnBankName}
            </if>
            <if test="'' != vo.depositReturnProvinceCode and vo.depositReturnProvinceCode != null">
                and base.deposit_return_province_code = #{vo.depositReturnProvinceCode}
            </if>
            <if test="'' != vo.depositReturnProvinceName and vo.depositReturnProvinceName != null">
                and base.deposit_return_province_name = #{vo.depositReturnProvinceName}
            </if>
            <if test="'' != vo.depositReturnCityCode and vo.depositReturnCityCode != null">
                and base.deposit_return_city_code = #{vo.depositReturnCityCode}
            </if>
            <if test="'' != vo.depositReturnCityName and vo.depositReturnCityName != null">
                and base.deposit_return_city_name = #{vo.depositReturnCityName}
            </if>
            <if test="'' != vo.depositReturnBankSubbranchCode and vo.depositReturnBankSubbranchCode != null">
                and base.deposit_return_bank_subbranch_code = #{vo.depositReturnBankSubbranchCode}
            </if>
            <if test="'' != vo.depositReturnBankSubbranchName and vo.depositReturnBankSubbranchName != null">
                and base.deposit_return_bank_subbranch_name = #{vo.depositReturnBankSubbranchName}
            </if>
            <if test="'' != vo.depositReturnBankCard and vo.depositReturnBankCard != null">
                and base.deposit_return_bank_card = #{vo.depositReturnBankCard}
            </if>
            <if test="'' != vo.depositReturnModifyReason and vo.depositReturnModifyReason != null">
                and base.deposit_return_modify_reason = #{vo.depositReturnModifyReason}
            </if>
            <if test="'' != vo.depositReturnUploadMaterials and vo.depositReturnUploadMaterials != null">
                and base.deposit_return_upload_materials = #{vo.depositReturnUploadMaterials}
            </if>
            <if test="'' != vo.rentDepositReturnBankNameCode and vo.rentDepositReturnBankNameCode != null">
                and base.rent_deposit_return_bank_name_code = #{vo.rentDepositReturnBankNameCode}
            </if>
            <if test="'' != vo.rentDepositReturnBankName and vo.rentDepositReturnBankName != null">
                and base.rent_deposit_return_bank_name = #{vo.rentDepositReturnBankName}
            </if>
            <if test="'' != vo.rentDepositReturnProvinceCode and vo.rentDepositReturnProvinceCode != null">
                and base.rent_deposit_return_province_code = #{vo.rentDepositReturnProvinceCode}
            </if>
            <if test="'' != vo.rentDepositReturnProvinceName and vo.rentDepositReturnProvinceName != null">
                and base.rent_deposit_return_province_name = #{vo.rentDepositReturnProvinceName}
            </if>
            <if test="'' != vo.rentDepositReturnCityCode and vo.rentDepositReturnCityCode != null">
                and base.rent_deposit_return_city_code = #{vo.rentDepositReturnCityCode}
            </if>
            <if test="'' != vo.rentDepositReturnCityName and vo.rentDepositReturnCityName != null">
                and base.rent_deposit_return_city_name = #{vo.rentDepositReturnCityName}
            </if>
            <if test="'' != vo.rentDepositReturnBankSubbranchCode and vo.rentDepositReturnBankSubbranchCode != null">
                and base.rent_deposit_return_bank_subbranch_code = #{vo.rentDepositReturnBankSubbranchCode}
            </if>
            <if test="'' != vo.rentDepositReturnBankSubbranchName and vo.rentDepositReturnBankSubbranchName != null">
                and base.rent_deposit_return_bank_subbranch_name = #{vo.rentDepositReturnBankSubbranchName}
            </if>
            <if test="'' != vo.rentDepositReturnBankCard and vo.rentDepositReturnBankCard != null">
                and base.rent_deposit_return_bank_card = #{vo.rentDepositReturnBankCard}
            </if>
            <if test="'' != vo.rentDepositReturnModifyReason and vo.rentDepositReturnModifyReason != null">
                and base.rent_deposit_return_modify_reason = #{vo.rentDepositReturnModifyReason}
            </if>
            <if test="'' != vo.rentDepositReturnUploadMaterials and vo.rentDepositReturnUploadMaterials != null">
                and base.rent_deposit_return_upload_materials = #{vo.rentDepositReturnUploadMaterials}
            </if>
        </where>
    </select>

    <select id="getCustomerRenewalBasicInfoList"
            resultType="com.bonc.ioc.bzf.busisigning.vo.CustomerRenewalBasicInfoVo">
        SELECT
            c.customer_no,
            c.customer_type,
	        GROUP_CONCAT( p.product_name SEPARATOR  '、') AS "productName",
            s.contract_template_name AS "contractName",
            s.contract_code AS "contractCode",
            s.contract_begin_time as "contractBeginTime",
            s.contract_end_time as "contractEndTime",
            aa.create_time as "applyTime",
            case when a.approve_status='1' then '通过'  when a.approve_status='2' then '未通过' when a.approve_status='3' then '待审核'  else '撤回' end as 'approveResultName'

        FROM
            bbs_renewal_info s
            left join bbs_dict d on s.payment_cycle_code = d.code
            and d.type_code = 'PAYMENT_CYCLE_CODE'
            left join bbs_dict e on s.cash_pledge_code = e.code
            and e.type_code = 'CASH_PLEDGE_CODE'
            LEFT JOIN bbs_renewal_relation r ON r.sign_info_id = s.sign_id
            AND r.del_flag = '1'
            LEFT JOIN bbs_renewal_product p ON p.rr_id = r.rr_id
            AND p.del_flag = '1'
            LEFT JOIN bbs_renewal_customer c ON c.rr_id = r.rr_id
            AND c.del_flag = '1'
            left join (	select ai.* from bbs_renewal_apply_info ai left join bbs_renewal_apply_approve_info aai on ai.renewal_apply_info_id =aai.parent_id
            and aai.del_flag='1' and aai.approve_status='1'
            where ai.del_flag='1' and aai.approve_id is not null) aa on s.parent_contract_code=aa.contract_code
            left join bbs_renewal_approve_info a on s.sign_id=a.parent_id
            and a.del_flag='1' and a.approve_type='2'
        WHERE
            s.del_flag = '1'
            and a.approve_status='1'
            and c.customer_no = #{customerNo}
            and c.customer_type =#{customerType}
        GROUP BY
            r.rr_id
        order by
            s.create_time desc



    </select>


</mapper>
