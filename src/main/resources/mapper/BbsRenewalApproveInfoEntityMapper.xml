<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApproveInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApproveInfoEntity">
                            <id column="approve_id" property="approveId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="parent_id" property="parentId" javaType="String"/>
                            <result column="comment_explanation" property="commentExplanation" javaType="String"/>
                            <result column="approve_type" property="approveType" javaType="String"/>
                            <result column="approve_status" property="approveStatus" javaType="String"/>
                            <result column="submit_time" property="submitTime" javaType="Date"/>
                            <result column="approve_time" property="approveTime" javaType="Date"/>
                            <result column="submit_user_id" property="submitUserId" javaType="String"/>
                            <result column="approver_user_id" property="approverUserId" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="submit_user_name" property="submitUserName" javaType="String"/>
                            <result column="approver_user_name" property="approverUserName" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApproveInfoPageResultVo">
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.approve_id
        ,base.parent_id
        ,base.comment_explanation
        ,base.approve_type
        ,base.approve_status
        ,base.submit_time
        ,base.approve_time
        ,base.submit_user_id
        ,base.approver_user_id
        ,base.del_flag
        ,base.submit_user_name
        ,base.approver_user_name
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        SELECT
        s.sign_id AS signId,
        max(p.community_building_name)   AS communityBuildingName,
        group_concat(p.product_name)   AS productName,
        c.customer_name AS customerName,
        (CASE c.customer_type WHEN '00' THEN '个人' ELSE '企业' END) AS customerType,
        s.business_format AS businessFormat,
        s.business_format_name AS businessFormatName,
        s.sign_status as signStatus,
        i.approve_status as approveStatus,
        i.submit_user_id AS submitUserId,
        i.submit_user_name AS submitUserName,
        i.submit_time AS submit_time,
        i.approve_time as approveTime,
        i.approver_user_id as approverUserId,
        i.approver_user_name as approverUserName,
        s.contract_code AS contractCode,
        s.contract_type AS contractType,
        c.customer_tel           as customerTel,
        c.customer_id_number     as customerIdNumber,
        c.mail_address           as mailAddress,
        c.customer_credit_code   as customerCreditCode,
        c.legal_name             as legalName,
        c.consignor_name         as consignorName,
        c.consignor_mobile       as consignorMobile
        FROM
        <if test="vo.pageStatus != null and vo.pageStatus == '2'.toString()">
            bbs_renewal_approve_detail_info i
        </if>
        <if test="vo.pageStatus != null and vo.pageStatus == '1'.toString()">
            bbs_renewal_approve_info i
        </if>
        left join bbs_renewal_info s on i.parent_id = s.sign_id
        LEFT JOIN bbs_renewal_relation r ON r.sign_info_id = s.sign_id
        LEFT JOIN bbs_renewal_product p ON p.rr_id = r.rr_id
        LEFT JOIN bbs_renewal_customer c ON c.rr_id = r.rr_id
        WHERE s.del_flag = '1'
        AND r.del_flag = '1'
        AND p.del_flag = '1'
        AND c.del_flag = '1'
        and i.del_flag = '1'
        <if test="vo.approveType != null and vo.approveType != ''">
            and i.approve_type = #{vo.approveType}
        </if>
        <if test="vo.approveStatus != null and vo.approveStatus != ''">
            and i.approve_status = #{vo.approveStatus}
        </if>
        <if test="vo.pageStatus != null and vo.pageStatus == '2'.toString()">
            and i.approve_status in ('1', '2')
        </if>
        <if test="vo.communityBuildingName != null and vo.communityBuildingName != ''">
            and p.community_building_name like concat('%',#{vo.communityBuildingName},'%')
        </if>
        <if test="vo.productName  != null and vo.productName != ''">
            and p.product_name like concat('%',#{vo.productName},'%')
        </if>
        <if test="vo.communityName  != null and vo.communityName != ''">
            and p.community_building_no =#{vo.communityName}
        </if>
        <if test="vo.groupName  != null and vo.groupName != ''">
            and p.group_name =#{vo.groupName}
        </if>
        <if test="vo.buildingNo  != null and vo.buildingNo != ''">
            and p.building_no =#{vo.buildingNo}
        </if>
        <if test="vo.unitName  != null and vo.unitName != ''">
            and p.unit_no =#{vo.unitName}
        </if>
        <if test="vo.customerName  != null and vo.customerName != ''">
            and c.customer_name like concat('%',#{vo.customerName},'%')
        </if>
        <if test="vo.customerTel  != null and vo.customerTel != ''">
            and c.customer_tel =#{vo.customerTel}
        </if>
        <if test="vo.businessFormat !=null and vo.businessFormat != ''">
            and s.business_format=#{vo.businessFormat}
        </if>
        <if test="'' != vo.businessFormatName and vo.businessFormatName != null">
            and s.business_format_name like concat('%',#{vo.businessFormatName},'%')
        </if>
        <if test="vo.customerType  != null and vo.customerType != ''">
            and c.customer_type =#{vo.customerType}
        </if>
        <if test="vo.contractCode  != null and vo.contractCode != ''">
            and s.contract_code=#{vo.contractCode}
        </if>
        <if test="vo.submitUserName  != null and vo.submitUserName != ''">
            and i.submit_user_name like concat('%',#{vo.submitUserName},'%')
        </if>
        <if test="vo.approverUserName  != null and vo.approverUserName != ''">
            and i.approver_user_name like concat('%',#{vo.approverUserName},'%')
        </if>
        <if test="vo.approveStartTime  != null and vo.approveStartTime != ''">
            and i.approve_time &gt;= concat(#{vo.approveStartTime},' 00:00:00')
        </if>
        <if test="vo.approveEndTime  != null and vo.approveEndTime != ''">
            and i.approve_time &lt;= concat(#{vo.approveEndTime},' 23:59:59')
        </if>
        <if test="vo.submitStartTime  != null and vo.submitStartTime != ''">
            and i.submit_time &gt;= concat(#{vo.submitStartTime},' 00:00:00')
        </if>
        <if test="vo.submitEndTime  != null and vo.submitEndTime != ''">
            and i.submit_time &lt;= concat(#{vo.submitEndTime},' 23:59:59')
        </if>
        group by i.parent_id,i.modify_time,i.create_time
        order by ifnull(i.approve_time, i.create_time) DESC
    </select>
    <select id="selectMsgInfo" resultType="java.util.Map">
        SELECT s.sign_id       AS signId,
               p.product_name  as "housingAddress",
               c.customer_name as "customerName",
               c.customer_tel as "customerTel",
               g1.template_id  as "websiteMsgTemplateId",
               g2.template_id  as "SMSTemplateId",
               g2.success_template_id as "approveSuccessSMSTemplateId",
               g2.failed_template_id as "approveFailedSMSTemplateId"
        FROM bbs_renewal_info s
                 LEFT JOIN bbs_renewal_relation r ON r.sign_info_id = s.sign_id
                 LEFT JOIN bbs_renewal_product p ON p.rr_id = r.rr_id
                 LEFT JOIN bbs_renewal_customer c ON c.rr_id = r.rr_id
                 left join bbs_renewal_template_group g1 on s.system_station_message_template_group_id = g1.template_group_id
                 left join bbs_renewal_template_group g2 on s.mobile_message_template_group_id = g2.template_group_id
        WHERE s.del_flag = '1'
          and s.sign_id = #{signId}
    </select>
    <select id="addressCascadeQuery" resultType="com.bonc.ioc.bzf.busisigning.vo.AddressCascadeQueryResultVo">
        SELECT distinct
        <if test="vo.level != null and vo.level == '0'.toString()">
            p.community_building_no as "code", p.community_building_name as "value"
        </if>
        <if test="vo.level != null and vo.level == '1'.toString()">
            p.group_name as "code", p.group_name as "value"
        </if>
        <if test="vo.level != null and vo.level == '2'.toString()">
            p.building_no as "code", concat(p.building_name, '楼') as "value"
        </if>
        <if test="vo.level != null and vo.level == '3'.toString()">
            p.unit_no as "code", concat(p.unit_name, '单元')   as "value"
        </if>
        FROM
        <if test="vo.pageStatus != null and vo.pageStatus == '2'.toString()">
            bbs_renewal_approve_detail_info i
        </if>
        <if test="vo.pageStatus != null and vo.pageStatus == '1'.toString()">
            bbs_renewal_approve_info i
        </if>
        left join bbs_renewal_info s on i.parent_id = s.sign_id
        LEFT JOIN bbs_renewal_relation r ON r.sign_info_id = s.sign_id
        LEFT JOIN bbs_renewal_product p ON p.rr_id = r.rr_id
        WHERE s.del_flag = '1'
        AND r.del_flag = '1'
        AND p.del_flag = '1'
        and i.del_flag = '1'
        <if test="vo.approveType != null and vo.approveType != ''">
            and i.approve_type = #{vo.approveType}
        </if>
        <if test="vo.pageStatus != null and vo.pageStatus == '1'.toString()">
            and i.approve_status = '3'
        </if>
        <if test="vo.pageStatus != null and vo.pageStatus == '2'.toString()">
            and i.approve_status in ('1','2')
        </if>
        <if test="vo.level != null and vo.level == '1'.toString()">
            and p.community_building_no = #{vo.code}
        </if>
        <if test="vo.level != null  and vo.level == '2'.toString() and vo.code!=null and vo.code!='' ">
            and p.group_name = #{vo.code}
        </if>
        <if test="vo.level != null  and vo.level == '2'.toString() and (vo.code == null or vo.code=='') ">
            and p.community_building_no = #{vo.communityCode}
        </if>
        <if test="vo.level != null and vo.level == '3'.toString()">
            and p.building_no = #{vo.code}

        </if>
        <if test="vo.level != null and vo.level == '2'.toString()">
           order by p.building_name asc
        </if>
        <if test="vo.level != null and vo.level == '3'.toString()">
            order by p.building_name asc
        </if>
    </select>
    <select id="selectproductNoBySignId" resultType="java.util.Map">
        SELECT s.sign_id    AS "signId",
               p.product_no as "productNo"
        FROM bbs_renewal_info s
                 LEFT JOIN bbs_renewal_relation r ON r.sign_info_id = s.sign_id
                 LEFT JOIN bbs_renewal_product p ON p.rr_id = r.rr_id
        WHERE s.del_flag = '1'
          and s.sign_id = #{signId}
    </select>
    <select id="selectContractCodeAndRantBySignId" resultType="java.util.Map">
        select bsi.contract_code as "contractCode",
                brp.product_no          as "productNo",
               brp.rent          as "rant"
        from bbs_renewal_info bsi
                 left join bbs_renewal_relation brr on bsi.sign_id = brr.sign_info_id
                 left join bbs_renewal_product brp on brr.rr_id = brp.rr_id
        WHERE bsi.del_flag = '1'
          and bsi.sign_id = #{signId}
    </select>

    <select id="selectCustCheckInInfo" resultType="java.util.Map">
        select s.sign_id                 as signId,
               p.building_no             as buildingNo,
               p.building_name           as buildingName,
               p.project_id              as projectId,
               p.product_name            as projectName,
               p.community_building_no   as communityId,
               p.community_building_name as communityName,
               p.unit_no                 as unitNo,
               p.unit_name               as unitName,
               p.product_no              as houseResourceId,
               p.product_name            as houseResource,
               c.customer_no             as customerId,
               c.customer_tel            as customerPhone,
               c.customer_credit_code    as creditCode,
               s.contract_code           as contractCode,
               s.contract_begin_time     as contractBeginTime,
               s.contract_end_time       as contractEndTime,
               c.customer_type           as enterprisePropertyCode
        from bbs_renewal_info s
                 left join bbs_renewal_relation rr
                           on s.sign_id = rr.sign_info_id
                 left join bbs_renewal_product p on rr.rr_id = p.rr_id
                 left join bbs_renewal_customer c on rr.rr_id = c.rr_id
        where s.sign_id = #{signId}
          and s.del_flag = 1
    </select>

    <select id="selectPersonCustomerHouseInfo"
            resultType="com.bonc.ioc.bzf.busisigning.feign.vo.PersonCustomerHouseDataVo">
        SELECT s.sign_id               AS "batchNo",
               p.community_building_no as "communityCode",
               s.contract_code         as "contractCode",
               p.project_no            as "operateProjectId",
               c.customer_no           as "personCustomerId",
               now()                   as "signingDate",
               '02'                    as "businessType",
               '02'                    as "businessTypeCode",
               p.product_no            as "productCode",
               c.customer_type         as "customerType",
               c.customer_type         as "customerTypeCode",
               c.customer_no           as "orgCustomerId"
        FROM bbs_renewal_info s
                 LEFT JOIN bbs_renewal_relation r ON r.sign_info_id = s.sign_id
                 LEFT JOIN bbs_renewal_product p ON p.rr_id = r.rr_id
                 LEFT JOIN bbs_renewal_customer c ON c.rr_id = r.rr_id
        where s.del_flag = '1' and  s.sign_id = #{signId}
    </select>
</mapper>
