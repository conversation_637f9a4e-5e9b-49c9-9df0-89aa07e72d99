spring:
  cloud:
    nacos:
      config:
#        server-addr: **********:18848
        server-addr: *************:8848
        group: DEFAULT_GROUP
        shared-dataids: mcp-common.yaml,bzf-system-busisigning-database.yaml
        namespace: c8114731-2787-44bf-ae34-edae7c95bfed
      discovery:
#        server-addr: **********:18848
        server-addr: *************:8848
        register-enabled: true
        namespace: c8114731-2787-44bf-ae34-edae7c95bfed
    gateway:
      discovery:
        locator:
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  kafka:
    # ָkafka服务器地址，可以指定多个
#    bootstrap-servers: **********:19090
    bootstrap-servers: *************:9092
    #=============== producer生产者配置 =======================
    producer:
      retries: 5
      # 每次批量发送消息的数量
      batch-size: 16384
      # 缓存容量
      buffer-memory: 33554432
      # ָ指定消息key和消息体的编解码方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    #=============== consumer消费者配置  =======================
    consumer:
      # 默认的消费组ID
      group-id: default‐group
      #如果为true，则消费者的偏移量将在后台定期提交，默认值为true
      enable-auto-commit: false
      # 一次poll返回的最多记录数
      max-poll-records: 10
      # 提交offset延时(接收到消息后多久提交offset)
      auto-commit-interval: 100
      #当Kafka中没有初始偏移量或者服务器上不再存在当前偏移量时该怎么办，默认值为latest，表示自动将偏移重置为最新的偏移量
      #可选的值为latest, earliest, none
      # earliest:当各分区下有已提交的offset时，从提交的offset开始消费；无提交的offset时，从头开始消费
      # latest:当各分区下有已提交的offset时，从提交的offset开始消费；无提交的offset时，消费新产生的该分区下的数据
      # none:topic各分区都存在已提交的offset时，从offset后开始消费；只要有一个分区不存在已提交的offset，则抛出异常
      auto-offset-reset: latest
      #值的反序列化器类，实现类实现了接口org.apache.kafka.common.serialization.Deserializer
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      #密钥的反序列化器类，实现类实现了接口org.apache.kafka.common.serialization.Deserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        #侦听器的AckMode,参见https://docs.spring.io/spring-kafka/reference/htmlsingle/#committing-offsets
        #当enable.auto.commit的值设置为false时，该值会生效；为true时不会生效
        #listener:
        #ack-mode: manual_immediate
        # 开启批量监听
        #type: batch
      # 消费线程数
      #concurrency: 10

logging:
  config: classpath:log4j2/log4j2-dev.xml
  #AES加密 key
AES:
  key: v0afWOt3XXlIfNecMSDqRQ==

log:
  topic: LOG_STATE
  send: false
schedule:
  sign:
    status:
      cron: 0 0 0 * * ?
serviceApi:
  #用友支行列表服务地址
  subbankUrl: http://10.0.9.88:8000
bzfsystem:
  #合同导出路径
  exportFilePath: /data/sinova/bzf-system-busisigning/file/
  #合同导出文件名
  exportFileName: contractExport.xlsx
  #续租申请书文件id
  fileId: 11032
  app:
    #app签约测试(1.是 0.否)
    pcTest: 0
  #消息模板id
  messageTemplate:
    #签约审核
    signApprove:
      #签约审核成功站内信息消息模板编码
      systemSuccessTemplateId: a0737f97a3794f51ae018c813dfdd3ac
      #签约审核失败站内信息消息模板编码
      systemFailedTemplateId: f7baf60d41f9489b8688f2151c9b3f46
      #签约审核成功短信息消息模板编码
      mobileSuccessTemplateId: 2bf2b7b3901049c198c2994bdf8628f1
      #签约审核失败短信息消息模板编码
      mobileFailedTemplateId: 918682ce4c204634a0e2df1d6545a576
    #续租申请
    renewalApply:
      #续租申请审核成功站内信息消息模板编码
      systemSuccessTemplateId: b21a80fe6ed548279053bcdbbeeb5b3f
      #续租申请审核失败站内信息消息模板编码
      systemFailedTemplateId: 0c36285621ae49e08905ee1456ea036a
      #续租申请审核成功短信息消息模板编码
      mobileSuccessTemplateId: 043b0902ec334afcb3969f4f7ec2ef49
      #续租申请审核失败短信息消息模板编码
      mobileFailedTemplateId: 7347469ae01d4b89b4d1d33c8c76ac34
    #续租审核
    renewalApprove:
      #续租审核成功站内信息消息模板编码
      systemSuccessTemplateId: f3247a0c02334dd1aeaea47395216c32
      #续租审核失败站内信息消息模板编码
      systemFailedTemplateId: a8cdb6fac0794a1e927e4da40bc44e00
      #续租审核成功短信息消息模板编码
      mobileSuccessTemplateId: e306b4273adb4eb8b9d3e9aa57b26560
      #续租审核失败短信息消息模板编码
      mobileFailedTemplateId: 5d5423ad43434529b9b1144be4567fd3
    #合同变更申请
    contractChangeApplay:
      #合同变更申请审核成功站内信息消息模板编码
      systemSuccessTemplateId: 9cf6343a0fb74b33b4a33a752164521a
      #合同变更申请审核失败站内信息消息模板编码
      systemFailedTemplateId: 22d144bbf9344f40a7c1d4de847ebee8
      #合同变更申请审核成功短信息消息模板编码
      mobileSuccessTemplateId: f6bd823819d34faba9ea1fc3c92e514c
      #合同变更申请审核失败短信息消息模板编码
      mobileFailedTemplateId: bedc5e1b4a434641b1b8d99ee1a9d7a0
      #合同变更申请验证码短信息消息模板编码
      mobileCodeTemplateId: 7ccccc1d7fc147cfb4bbea5aa42d5735
