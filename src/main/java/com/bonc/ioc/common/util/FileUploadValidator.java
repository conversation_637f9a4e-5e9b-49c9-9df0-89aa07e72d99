package com.bonc.ioc.common.util;

import com.bonc.ioc.common.exception.McpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

public class FileUploadValidator {

    private static final Logger log = LoggerFactory.getLogger(FileUploadValidator.class);

    // 最大允许文件大小为500MB
    private static final long MAX_FILE_SIZE = 500 * 1024 * 1024;
    // 文件名非法字符正则表达式
    private static final String ILLEGAL_CHARS_REGEX = ".*[^a-zA-Z0-9\\.\\-_].*";
    // 定义文件名中禁止包含的特殊字符集合，考虑了多种操作系统中的限制
    private static final String SPECIAL_CHARACTERS = "[~#@*+%{}<>\\[\\]|\"^/:*?\"<>|]";
    // 将特殊字符集合编译成正则表达式模式，以提高代码的执行效率
    private static final Pattern SPECIAL_CHARACTERS_PATTERN = Pattern.compile(SPECIAL_CHARACTERS);

    // 支持的文件扩展名集合
    private static final Set<String> ALLOWED_FILE_EXTENSIONS = new HashSet<>();
    // 支持的MIME类型集合
    private static final Set<String> ALLOWED_MIME_TYPES = new HashSet<>();

    static {
        // 初始化支持的文件扩展名
        String[] extensions = {
                "jpg", "jpeg", "png", "gif", "bmp", "tiff", "tif", "pdf", "doc", "docx",
                "xls", "xlsx", "ppt", "pptx", "txt", "csv", "json", "xml", "mp4", "avi",
                "mov", "mp3", "wav", "aac", "zip", "rar", "7z"
        };
        for (String extension : extensions) {
            ALLOWED_FILE_EXTENSIONS.add(extension.toLowerCase());
        }

        // 初始化支持的MIME类型
        String[] mimeTypes = {
                "application/octet-stream",
                // 图像文件
                "image/jpeg", "image/png", "image/gif", "image/bmp", "image/tiff", "image/webp",
                // 文档文件
                "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/vnd.oasis.opendocument.text", "application/vnd.oasis.opendocument.spreadsheet",
                // 文本文件
                "text/plain", "text/csv",
                // 压缩文件
                "application/x-rar-compressed", "application/zip","application/x-zip-compressed",
                // 视频文件
                "video/mp4", "video/x-msvideo", "video/x-flv", "video/quicktime", "video/x-ms-wmv",
                // 音频文件
                "audio/mpeg", "audio/wav", "audio/x-wav", "audio/ogg", "audio/aac", "audio/x-aac"
        };
        for (String mimeType : mimeTypes) {
            ALLOWED_MIME_TYPES.add(mimeType);
        }
    }

    public static void isValidFileUpload(MultipartFile file) {
        if (file.isEmpty()) {
            throw new McpException("文件为空");
        }
        try {
            // 获取文件名
            String fileName = file.getName();
            // 检查文件名是否为空，是否包含非法字符或路径遍历攻击字符
            if (fileName == null || fileName.trim().isEmpty() || fileName.contains("..") || containsIllegalChars(fileName)){
                throw new McpException("Invalid file name");
            }
            String originalFileName = file.getOriginalFilename();
            if (originalFileName == null || originalFileName.trim().isEmpty() || hasSpecialCharacters(originalFileName)) {
                throw new McpException("文件名包含特殊字符");
            }
            if (!isValidFileExtension(originalFileName)) {
                throw new McpException("文件类型不支持");
            }
            // 检查文件大小
            long fileSize = file.getSize();
            if (fileSize > MAX_FILE_SIZE) {
                throw new McpException("文件大小超过限制 " + MAX_FILE_SIZE + " bytes");
            }
            String mimeType = file.getContentType();
            if (!ALLOWED_MIME_TYPES.contains(mimeType)) {
                throw new McpException("文件MIME类型"+mimeType+"不支持");
            }
        } catch (Exception e) {
            logError("文件上传验证过程中发生异常: ", e);
            throw new McpException("文件上传验证过程中发生异常: " + e.getMessage(), e);
        }

    }

    private static void logError(String message, Exception e) {
        log.error(message + e.getMessage(), e);
    }

    public static boolean isValidFileSize(MultipartFile file) {
        return file.getSize() <= MAX_FILE_SIZE;
    }

    public static boolean isValidFileExtension(String originalFileName) {
        String fileExtension = originalFileName.substring(originalFileName.lastIndexOf(".") + 1).toLowerCase();
        return ALLOWED_FILE_EXTENSIONS.contains(fileExtension);
    }

    public static boolean hasSpecialCharacters(String fileName) {
        return SPECIAL_CHARACTERS_PATTERN.matcher(fileName).find();
    }

    private static boolean containsIllegalChars(String fileName) {
        // 定义并实现检查文件名中是否包含非法字符的逻辑
        // 例如：返回fileName.matches(".*[^a-zA-Z0-9\\.\\-_].*"); // 非字母、数字、点、减号或下划线
        return fileName.matches(".*[^a-zA-Z0-9\\.\\-_].*");
    }
}

