package com.bonc.ioc.common.util;
/**
 * @ClassName AppReply
 * @Description 范围报文，增加操作日期
 * @AUTHOR 宋鑫
 * @Date 2023-03-02 14:40
 **/

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bonc.ioc.common.constant.RedirectInfo;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class AppReply<T> implements Serializable {
    private static final long serialVersionUID = 2346043181255743475L;
    public static final String SUCCESS_CODE = "1";
    public static final String SUCCESS_MSG = "请求成功";
    public static final String ERROR_CODE = "0";
    public static final String ERROR_MSG = "请求失败";
    public static final String LOGIN_CODE = "2";
    public static final String LOGIN_MSG = "请登录账号";
    public static final String MULTILOGIN_CODE = "3";
    public static final String MULTILOGIN_MSG = "账号已超时，请重新登录";
    public static final String MULTILOGIN_MSG_1 = "账号已在别处登录，请重新登录";
    public static final String REDIRECT_CODE = "99";
    @ApiModelProperty(
            value = "返回数据",
            required = false
    )
    private T data;
    @ApiModelProperty(
            value = "返回消息",
            required = false
    )
    private String msg;
    @ApiModelProperty(
            value = "traceId",
            required = false
    )
    private String traceId;
    @ApiModelProperty(
            value = "返回编码 0:请求失败 1:请求成功 2:登录失败 3:重新登录",
            required = false
    )
    private String code;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @ApiModelProperty(
            value = "操作日期",
            required = false
    )
    private String date;

    public T getData() {
        return this.data;
    }

    public String getMsg() {
        return this.msg;
    }

    public String getCode() {
        return this.code;
    }

    public void setData(T data) {
        if(data instanceof JSONObject){
            this.data = (T)JSONObject.parseObject(JSON.toJSONString(data));
        }else {
            this.data = data;
        }
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public AppReply(String code, String msg, T data) {
        this.refreshTraceId();
        this.data = data;
        this.msg = msg;
        this.code = code;
        this.date = DateUtil.getDay();
    }

    public AppReply() {
        this.refreshTraceId();
        this.data = null;
        this.msg = null;
        this.code = "1";
        this.date = DateUtil.getDay();
    }

    public static <T> AppReply success(T obj) {
        return new AppReply("1", "请求成功", obj);
    }

    public static AppReply success(String msg) {
        return new AppReply("1", msg, (Object) null);
    }

    public static AppReply success() {
        return new AppReply("1", "请求成功", (Object) null);
    }

    public static <T> AppReply error(T obj) {
        return new AppReply("0", "请求失败", obj);
    }

    public static AppReply error(String msg) {
        return new AppReply("0", msg, (Object) null);
    }

    public static AppReply redirect(String msg, String redirectUrl) {
        RedirectInfo redirectInfo = new RedirectInfo();
        redirectInfo.setErrorMessage(msg);
        redirectInfo.setRedirectUrl(redirectUrl);
        return new AppReply("99", JSONObject.toJSONString(redirectInfo), (Object) null);
    }

    public static AppReply error(String code, String msg) {
        return new AppReply(code, msg, (Object) null);
    }

    public static AppReply error() {
        return new AppReply("0", "请求失败", (Object) null);
    }

    public static AppReply relogin() {
        return new AppReply("3", "账号已超时，请重新登录", (Object) null);
    }

    public static AppReply relogin1() {
        return new AppReply("3", "账号已在别处登录，请重新登录", (Object) null);
    }

    public static AppReply relogin_3() {
        return new AppReply("2", "密码修改成功，请重新登录", (Object) null);
    }

    public static AppReply login() {
        return new AppReply("2", "请登录账号", (Object) null);
    }

    public String toString() {
        String datePattern = "yyyy-MM-dd HH-mm-ss";
        String json = JSONObject.toJSONStringWithDateFormat(this, datePattern, new SerializerFeature[]{SerializerFeature.WriteMapNullValue});
        json = json.replaceAll("null", "\"\"");
        return json;
    }

    public String getTraceId() {
        return this.traceId;
    }

    private void refreshTraceId() {
        this.traceId = TraceUtils.getTraceId();
    }
}
