package com.bonc.ioc.common.util;

/**
 * @ClassName CheckPath
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2024-04-11 10:30
 **/
public class Path {

   /**
    * 检查给定的路径是否合法。
    * 该方法会抛出运行时异常，如果路径中包含".."，因为这可能表示路径尝试访问父目录，可能存在安全风险。
    *
    * @param path 待检查的路径字符串。
    * @throws RuntimeException 如果路径包含".."，抛出此异常。
    */
   public static void checkPath(String path) {
       // 检查路径中是否包含非法的".."字符
       if (path.contains("..")) {
           throw new RuntimeException("路径非法");
       }
   }
    /**
     * @description: 检测路径和文件后缀
     * @author: 宋鑫
     * @date: 2024-04-11 14:19
     * @param: [path]
     * @return: void
     * @since 1.0.0
     **/
    public static void checkPathAndExt(String path) {
        // 检查路径中是否包含非法的".."字符
        if (path.contains("..")) {
            throw new RuntimeException("路径非法");
        }
        path = path.toLowerCase();
        if (!path.endsWith(".pdf")&&!path.endsWith(".docx")&&!path.endsWith(".doc")&&!path.endsWith(".ftl")&&!path.endsWith(".zip")
                &&!path.endsWith(".rar")&&!path.endsWith(".xlsx")) {
            throw new RuntimeException("文件非法");
        }
    }
}
