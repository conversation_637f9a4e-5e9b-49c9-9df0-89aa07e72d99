package com.bonc.ioc.common.filter;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.common.util.AppReply;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName XssFilter
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2023-03-02 14:14
 **/
public class XssFilter implements Filter {
    private static final Logger log = LoggerFactory.getLogger(XssFilter.class);
    private final String encoding = "UTF-8";
    private final String[] legalNames = "content1,ver,historyURL,listURL".split(",");
    private final String[] illegalChars = "|,$,@,\",\\\",<,>,(,),+,\\\",\",\\,http".split(",");
    private Map<String, Set<String>> whiteMap = null;
    private List<String[]> whiteLikeList = null;

    public XssFilter(Map<String, Set<String>> whiteMap) {
        this.whiteMap = whiteMap;
        if (whiteMap != null) {
            Set<String> list = whiteMap.keySet();
            this.whiteLikeList = new ArrayList();
            Iterator var3 = list.iterator();

            while (var3.hasNext()) {
                String item = (String) var3.next();
                if (item.indexOf("*") >= 0) {
                    this.whiteLikeList.add(item.split("/"));
                }
            }
        }

    }

    public void init(FilterConfig filterConfig) throws ServletException {
    }

    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) servletRequest;
        HttpServletResponse res = (HttpServletResponse) servletResponse;
        req.setCharacterEncoding("UTF-8");
        String tempUrl = req.getRequestURI();
        String redirectUrl = req.getContextPath() + "/sys/error/xss";
        Set<String> whiteCharSet = new HashSet();
        if (this.whiteMap != null && this.whiteMap.containsKey(tempUrl)) {
            if (CollectionUtils.isEmpty((Collection) this.whiteMap.get(tempUrl))) {
                log.info("[" + tempUrl + "] request is white list url qualified release");
                filterChain.doFilter(req, res);
                return;
            }

            whiteCharSet = (Set) this.whiteMap.get(tempUrl);
        } else if (this.whiteMap != null) {
            Iterator var9 = this.whiteLikeList.iterator();

            while (var9.hasNext()) {
                String[] item = (String[]) var9.next();
                String[] urls = tempUrl.split("/");
                boolean flag = true;

                for (int idx = 0; idx < urls.length; ++idx) {
                    if (item.length <= idx) {
                        flag = false;
                        break;
                    }

                    if (!item[idx].equals("*") && !item[idx].equals(urls[idx])) {
                        if (item[idx].equals("**") && item.length - 1 == idx) {
                            flag = true;
                            break;
                        }

                        flag = false;
                        break;
                    }
                }

                if (flag) {
                    if (CollectionUtils.isEmpty((Collection) this.whiteMap.get(StringUtils.join(item, "/")))) {
                        log.info("[" + tempUrl + "] request is white list url qualified release");
                        filterChain.doFilter(req, res);
                        return;
                    }

                    whiteCharSet = (Set) this.whiteMap.get(StringUtils.join(item, "/"));
                }
            }
        }

        if (StringUtils.isNotBlank(tempUrl) && (tempUrl.equals(redirectUrl) || tempUrl.equals(req.getContextPath() + "/doc.html") || tempUrl.equals(req.getContextPath() + "/v2/api-docs-ext") || tempUrl.equals(req.getContextPath() + "/swagger-resources/configuration/ui") || tempUrl.equals(req.getContextPath() + "/swagger-resources") || tempUrl.startsWith(req.getContextPath() + "/webjars/"))) {
            log.info("[" + tempUrl + "] request is xss url qualified release");
            filterChain.doFilter(req, res);
        } else {
            Enumeration params = req.getParameterNames();
            boolean executable = true;
            boolean illegalStatus = false;
            String illegalMsg = null;
            String illegalParamName = null;
            String illegalChar = "";

            while (params.hasMoreElements()) {
                String paramName = (String) params.nextElement();
                executable = true;
                if (paramName.toLowerCase().contains("password")) {
                    executable = false;
                } else {
                    for (int i = 0; i < this.legalNames.length; ++i) {
                        if (this.legalNames[i].equals(paramName)) {
                            executable = false;
                            break;
                        }
                    }
                }

                if (executable) {
                    String[] paramValues = req.getParameterValues(paramName);

                    for (int i = 0; i < paramValues.length; ++i) {
                        String paramValue = paramValues[i];

                        for (int j = 0; j < this.illegalChars.length; ++j) {
                            illegalChar = this.illegalChars[j];
                            if (paramValue.indexOf(illegalChar) != -1 && (CollectionUtils.isEmpty((Collection) whiteCharSet) || !((Set) whiteCharSet).contains(illegalChar))) {
                                log.error(String.format("uri:[%s] paramValue:[%s] illegalChar:[%s] xss is error! redirect to %s", tempUrl, paramValue, illegalChar, "/sys/error/xss"));
                                illegalStatus = true;
                                illegalMsg = paramValue;
                                illegalParamName = paramName;
                                break;
                            }
                        }

                        if (!illegalStatus && this.checkXssAttack(paramValue, (Set) whiteCharSet)) {
                            log.error(String.format("uri:[%s] paramValue:[%s] illegalChar:[%s] xss is error! redirect to %s", tempUrl, paramValue, illegalChar, "/sys/error/xss"));
                            illegalStatus = true;
                            illegalMsg = paramValue;
                            illegalParamName = paramName;
                        }

                        if (illegalStatus) {
                            break;
                        }
                    }
                }

                if (illegalStatus) {
                    break;
                }
            }

            for (int j = 0; j < this.illegalChars.length; ++j) {
                illegalChar = this.illegalChars[j];
                if (tempUrl.indexOf(illegalChar) != -1 && (CollectionUtils.isEmpty((Collection) whiteCharSet) || !((Set) whiteCharSet).contains(illegalChar))) {
                    log.error(String.format("uri:[%s] illegalChar:[%s] xss is error! redirect to %s", tempUrl, illegalChar, "/sys/error/xss"));
                    illegalStatus = true;
                    illegalMsg = tempUrl;
                    illegalParamName = "URL";
                    break;
                }
            }

            if (!illegalStatus && this.checkXssAttack(tempUrl, (Set) whiteCharSet)) {
                log.error(String.format("uri:[%s] illegalChar:[%s] xss is error! redirect to %s", tempUrl, illegalChar, "/sys/error/xss"));
                illegalStatus = true;
                illegalMsg = tempUrl;
                illegalParamName = "URL";
            }

            if (illegalStatus) {
                //RedirectUtil.sendRedirect(req, res, redirectUrl + "?paramName=" + URLEncoder.encode(illegalParamName, "utf-8") + "&illegalMsg=" + URLEncoder.encode(illegalMsg, "utf-8"));
                AppReply reply = new AppReply("0", "请求信息不能包含特殊字符", null);
                HttpServletResponse response = (HttpServletResponse) servletResponse;
                //response.setHeader("Access-Control-Allow-Origin", "*");
                response.setHeader("Cache-Control", "no-cache");
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/json");
                response.getWriter().print(JSONObject.toJSONString(reply));
                response.getWriter().flush();
            } else {
                this.headerSet(req, res);
                filterChain.doFilter(req, res);
            }

        }
    }

    public void headerSet(ServletRequest request, ServletResponse response) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse resp = (HttpServletResponse) response;
        resp.setHeader("Cache-Control", "no-store");
        resp.setHeader("Pragma", "no-cache");
        resp.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        resp.setHeader("Content-Security-Policy", "default-src 'self';script-src 'self' *://wso2.* *://*.wso2.*");
        if (req.getCookies() != null) {
            Cookie[] var5 = req.getCookies();
            int var6 = var5.length;

            for (int var7 = 0; var7 < var6; ++var7) {
                Cookie cookie = var5[var7];
                cookie.setSecure(true);
                cookie.setHttpOnly(true);
                resp.addCookie(cookie);
            }
        }

    }

    public void destroy() {
    }

    public boolean checkXssAttack(String str, Set<String> whiteCharSet) {
        String[] regexArr = new String[]{"<(\\s)*(no)?script", "(no)?script(\\s)*>", "<(\\s)*iframe", "iframe(\\s)*>", "<(\\s)*img", "img(\\s)*>", "src(\\s)*=", "<(\\s)*a", "a(\\s)*>", "href(\\s)*=", "style(\\s)*=", "function\\(", "eval\\(", "expression\\(", "javascript:", "vbscript:", "view-source:", "window[\\s\\S]*location", "window\\.", "\\.location", "document[\\s\\S]*cookie", "document\\.", "alert\\(", ":alert", "window[\\s\\S]*open", "oncontrolselect", "oncopy", "oncut", "ondataavailable", "ondatasetchanged", "ondatasetcomplete", "ondblclick", "ondeactivate", "ondrag", "ondragend", "ondragenter", "ondragleave", "ondragover", "ondragstart", "ondrop", "onerror", "onerroupdate", "onfilterchange", "onfinish", "onfocus", "onfocusin", "onfocusout", "onhelp", "onkeydown", "onkeypress", "onkeyup", "onlayoutcomplete", "onload", "onlosecapture", "onmousedown", "onmouseenter", "onmouseleave", "onmousemove", "onmousout", "onmouseover", "onmouseup", "onmousewheel", "onmove", "onmoveend", "onmovestart", "onabort", "onactivate", "onafterprint", "onafterupdate", "onbefore", "onbeforeactivate", "onbeforecopy", "onbeforecut", "onbeforedeactivate", "onbeforeeditocus", "onbeforepaste", "onbeforeprint", "onbeforeunload", "onbeforeupdate", "onblur", "onbounce", "oncellchange", "onchange", "onclick", "oncontextmenu", "onpaste", "onpropertychange", "onreadystatechange", "onreset", "onresize", "onresizend", "onresizestart", "onrowenter", "onrowexit", "onrowsdelete", "onrowsinserted", "onscroll", "onselect", "onselectionchange", "onselectstart", "onstart", "onstop", "onsubmit", "onunload", "select", "insert", "delete", "from", "count\\(", "drop table", "update", "truncate", "asc\\(", "mid\\(", "char\\(", "xp_cmdshell", "exec", "master", "netlocalgroup administrators", "net user", "or", "and", "\\+", "\""};
        String[] var4 = regexArr;
        int var5 = regexArr.length;

        for (int var6 = 0; var6 < var5; ++var6) {
            String regex = var4[var6];
            if (Pattern.matches("\\w+", regex.trim())) {
                regex = "\\b" + regex.trim() + "\\b";
            }

            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(str);
            if (matcher.find() && (CollectionUtils.isEmpty(whiteCharSet) || !whiteCharSet.contains(regex))) {
                log.error(String.format("matches string:[%s] rule:[%s] checkXssAttack error! redirect to %s", str, regex, "/sys/error/xss"));
                return true;
            }
        }

        return false;
    }
}
