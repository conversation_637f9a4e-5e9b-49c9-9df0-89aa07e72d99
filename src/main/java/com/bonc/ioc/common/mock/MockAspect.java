package com.bonc.ioc.common.mock;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.bonc.ioc.bzf.busisigning.entity.SystemLocaIdEntity;
import com.bonc.ioc.bzf.busisigning.vo.ChargePageVo;
import com.bonc.ioc.bzf.busisigning.vo.ChargeRespondVo;
import com.bonc.ioc.bzf.busisigning.vo.TaxrateResultVo;
import com.bonc.ioc.common.util.AppReply;
import com.sinovatech.saas.base.spec.bean.Response;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Method;
import java.util.List;

@Component
@Aspect
@Slf4j
public class MockAspect {

    @Value("${mock.enabled:false}")
    private Boolean enabled;

    @Value("${mock.url:}")
    private String mockUrl;

    @Around(value = "@annotation(Mock)")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        if (enabled){//执行MOCK
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            String key = method.getAnnotation(Mock.class).key();
            Object result = null;
            if(method.getAnnotation(PostMapping.class)!=null){
               result = this.postMethod(this.mockUrl(method),getResutBean(key));
            }else if(method.getAnnotation(GetMapping.class)!=null){
                result = this.getMethod(this.mockUrl(method),getResutBean(key));
            }
            return result;
        }else{ //不执行MOCK
            return joinPoint.proceed();
        }
    }

    private Object postMethod(String url ,ParameterizedTypeReference typeRef){
        log.info(String.format("mock发送地址：%s",url));
        RestTemplate restTemplate=new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", MediaType.ALL.toString());
        HttpEntity requestEntity = new HttpEntity(new JSONObject(), headers);
        Object o = restTemplate.exchange(url, HttpMethod.POST,requestEntity,typeRef).getBody();
        log.info(String.format("mock返回报文：%s",JSONObject.toJSONString(o)));
        return o;
    }

    private Object getMethod(String url,ParameterizedTypeReference typeRef){
        log.info(String.format("mock发送地址：%s",url));
        RestTemplate restTemplate=new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", MediaType.ALL.toString());
        HttpEntity requestEntity = new HttpEntity(new JSONObject(), headers);
        Object o = restTemplate.exchange(url, HttpMethod.GET,requestEntity,typeRef).getBody();
        log.info(String.format("mock返回报文：%s",JSONObject.toJSONString(o)));
        return o;
    }

    public String mockUrl(Method method){
        String key = method.getAnnotation(Mock.class).key();
        Class clazz = method.getDeclaringClass();
        FeignClient feignClient = (FeignClient)clazz.getAnnotation(FeignClient.class);
        String path = feignClient.path();
        String methodPath = null;
        if(method.getAnnotation(PostMapping.class)!=null){
            methodPath = method.getAnnotation(PostMapping.class).value()[0];
        }else if(method.getAnnotation(GetMapping.class)!=null){
            methodPath = method.getAnnotation(GetMapping.class).value()[0];
        }
        return mockUrl + path + methodPath +"?apipost_id=" + key + "&mockReturnType=" + returnType(method);
    }

    public String returnType(Method method){
        return method.getAnnotation(Mock.class).returnType();
    }


    private ParameterizedTypeReference getResutBean(String key){
        if("4fa4640".equals(key)){ //客商编号
            return new ParameterizedTypeReference<AppReply<String>>() {};
        }else if("3c49639b3cb008".equals(key)||"dca76cc7cb00d".equals(key)){//商业查询房源接口,项目查询小区
            return new ParameterizedTypeReference<AppReply<JSONArray>>() {};
        }else if("4f9c1e2bcb01e".equals(key)){//税率
            return new ParameterizedTypeReference<ChargeRespondVo<ChargePageVo<List<TaxrateResultVo>>>>() {};
        }else if("4fb84cc".equals(key)||"decf096fcb022".equals(key)){//查询项目,查询企业
            return new ParameterizedTypeReference<AppReply<JSONObject>>() {};
        }else if("52ccd41".equals(key)){//更改房源状态
            return new ParameterizedTypeReference<AppReply<Object>>() {};
        }else if("b9ce701bae01f".equals(key)){//获取机构类型
            return new ParameterizedTypeReference<Response<SystemLocaIdEntity>>() {};
        }else if("51c852b".equals(key)||"f150051bcb01a".equals(key)){//获取个人信息,机构信息
            return new ParameterizedTypeReference<Response<JSONObject>>() {};
        }else{
            return new ParameterizedTypeReference<JSONObject>() {};
        }


    }

}
