package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyInfoProductEntity;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApplyInfoProductResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApplyInfoProductVo;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

/**
 * @author: hechengyao
 * @createDate: 2023-09-26
 * @Version 1.0
 **/
public interface IBbsRenewalApplyInfoProductService extends IMcpBaseService<BbsRenewalApplyInfoProductEntity> {


    /**
     * 商铺地址-级联查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 级联查询的数据
     * <AUTHOR>
     * @date 2023-09-26
     * @since 1.0.0
     */
    List<BbsRenewalApplyInfoProductResultVo> selectByRateRecord(BbsRenewalApplyInfoProductVo vo);

}
