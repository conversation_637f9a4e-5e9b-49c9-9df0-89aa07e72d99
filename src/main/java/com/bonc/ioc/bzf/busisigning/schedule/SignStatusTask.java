package com.bonc.ioc.bzf.busisigning.schedule;

import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalInfoExtService;
import com.bonc.ioc.bzf.busisigning.service.IBbsSignInfoExtService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 签约状态 定时类
 *
 * <AUTHOR>
 * @since 2023/11/10
 */
@Component
@Slf4j
public class SignStatusTask {

    @Resource
    private IBbsSignInfoExtService bbsSignInfoExtService;
    @Resource
    private IBbsRenewalInfoExtService bbsRenewalInfoExtService;
    /**
     * 更新签约和续签状态
     */
    @Scheduled(cron = "${schedule.sign.status.cron:0 0 0 * * ?}")
    @Transactional(rollbackFor = {Exception.class})
    public void run() {
        log.info("定时执行开始");
        bbsSignInfoExtService.checkContractBeginTime();
        bbsRenewalInfoExtService.checkContractBeginTime();
        log.info("定时执行结束");
    }
}
