package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApproveDetailInfoMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApproveDetailInfoEntity;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalApproveDetailInfoService;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApproveDetailInfoPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApproveDetailInfoPageVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApproveDetailInfoVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 续签审批明细表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-09-21
 * @change 2023-09-21 by liupengwei for init
 */
@Slf4j
@Service
public class BbsRenewalApproveDetailInfoServiceImpl extends McpBaseServiceImpl<BbsRenewalApproveDetailInfoEntity> implements IBbsRenewalApproveDetailInfoService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalApproveDetailInfoMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsRenewalApproveDetailInfoService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsRenewalApproveDetailInfoVo vo) {
        if(vo == null) {
            return null;
        }

        BbsRenewalApproveDetailInfoEntity entity = new BbsRenewalApproveDetailInfoEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setApproveId(null);
        if(!baseService.insert(entity)) {
            log.error("续签审批明细表新增失败:" + entity.toString());
            throw new McpException("续签审批明细表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getApproveId(),1)) {
                log.error("续签审批明细表新增后保存历史失败:" + entity.toString());
                throw new McpException("续签审批明细表新增后保存历史失败");
            }

            log.debug("续签审批明细表新增成功:"+entity.getApproveId());
            return entity.getApproveId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsRenewalApproveDetailInfoVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsRenewalApproveDetailInfoEntity> entityList = new ArrayList<>();
        for (BbsRenewalApproveDetailInfoVo item:voList) {
            BbsRenewalApproveDetailInfoEntity entity = new BbsRenewalApproveDetailInfoEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsRenewalApproveDetailInfoEntity item:entityList){
            item.setApproveId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("续签审批明细表新增失败");
            throw new McpException("续签审批明细表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsRenewalApproveDetailInfoEntity::getApproveId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("续签审批明细表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("续签审批明细表批量新增后保存历史失败");
            }

            log.debug("续签审批明细表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param approveId 需要删除的审批id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String approveId) {
        if(!StringUtils.isEmpty(approveId)) {
            if(!baseService.saveOperationHisById(approveId,3)) {
                log.error("续签审批明细表删除后保存历史失败:" + approveId);
                throw new McpException("续签审批明细表删除后保存历史失败");
            }

            if(!baseService.removeById(approveId)) {
                log.error("续签审批明细表删除失败");
                throw new McpException("续签审批明细表删除失败"+approveId);
            }
        } else {
            throw new McpException("续签审批明细表删除失败审批id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param approveIdList 需要删除的审批id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> approveIdList) {
        if(!CollectionUtils.isEmpty(approveIdList)) {
            int oldSize = approveIdList.size();
            approveIdList = approveIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(approveIdList) || oldSize != approveIdList.size()) {
                throw new McpException("续签审批明细表批量删除失败 存在主键id为空的记录"+StringUtils.join(approveIdList));
            }

            if(!baseService.saveOperationHisByIds(approveIdList,3)) {
                log.error("续签审批明细表批量删除后保存历史失败:" + StringUtils.join(approveIdList));
                throw new McpException("续签审批明细表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(approveIdList)) {
                log.error("续签审批明细表批量删除失败");
                throw new McpException("续签审批明细表批量删除失败"+StringUtils.join(approveIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的续签审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsRenewalApproveDetailInfoVo vo) {
        if(vo != null) {
            BbsRenewalApproveDetailInfoEntity entity = new BbsRenewalApproveDetailInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getApproveId())) {
                throw new McpException("续签审批明细表更新失败传入审批id为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("续签审批明细表更新失败");
                throw new McpException("续签审批明细表更新失败"+entity.getApproveId());
            } else {
                if(!baseService.saveOperationHisById(entity.getApproveId(),2)) {
                    log.error("续签审批明细表更新后保存历史失败:" + entity.getApproveId());
                    throw new McpException("续签审批明细表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("续签审批明细表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的续签审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsRenewalApproveDetailInfoVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalApproveDetailInfoEntity> entityList = new ArrayList<>();

            for (BbsRenewalApproveDetailInfoVo item:voList){
                BbsRenewalApproveDetailInfoEntity entity = new BbsRenewalApproveDetailInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getApproveId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("续签审批明细表批量更新失败 存在审批id为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("续签审批明细表批量更新失败");
                throw new McpException("续签审批明细表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getApproveId())).map(BbsRenewalApproveDetailInfoEntity::getApproveId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("续签审批明细表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("续签审批明细表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的续签审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsRenewalApproveDetailInfoVo vo) {
        if(vo != null) {
            BbsRenewalApproveDetailInfoEntity entity = new BbsRenewalApproveDetailInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("续签审批明细表保存失败");
                throw new McpException("续签审批明细表保存失败"+entity.getApproveId());
            } else {
                if(!baseService.saveOperationHisById(entity.getApproveId(),4)) {
                    log.error("续签审批明细表保存后保存历史失败:" + entity.getApproveId());
                    throw new McpException("续签审批明细表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("续签审批明细表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的续签审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsRenewalApproveDetailInfoVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalApproveDetailInfoEntity> entityList = new ArrayList<>();

            for (BbsRenewalApproveDetailInfoVo item:voList){
                BbsRenewalApproveDetailInfoEntity entity = new BbsRenewalApproveDetailInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("续签审批明细表批量保存失败");
                throw new McpException("续签审批明细表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getApproveId())).map(BbsRenewalApproveDetailInfoEntity::getApproveId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("续签审批明细表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("续签审批明细表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param approveId 需要查询的审批id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsRenewalApproveDetailInfoVo selectByIdRecord(String approveId) {
        BbsRenewalApproveDetailInfoVo vo = new BbsRenewalApproveDetailInfoVo();

        if(!StringUtils.isEmpty(approveId)) {
            BbsRenewalApproveDetailInfoEntity entity = baseService.selectById(approveId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsRenewalApproveDetailInfoPageResultVo>> selectByPageRecord(BbsRenewalApproveDetailInfoPageVo vo) {
        List<BbsRenewalApproveDetailInfoPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
