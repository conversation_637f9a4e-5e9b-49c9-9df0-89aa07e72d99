package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeCalculatedProductEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更计租面积变更产品表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-27
 * @change 2024-11-27 by tbh for init
 */
public interface IBbsChangeCalculatedProductService extends IMcpBaseService<BbsChangeCalculatedProductEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-11-27
     * @change
     * 2024-11-27 by tbh for init
     */
    String insertRecord(BbsChangeCalculatedProductVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-11-27
     * @change
     * 2024-11-27 by tbh for init
     */
    List<String> insertBatchRecord(List<BbsChangeCalculatedProductVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param pdctId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-11-27
     * @change
     * 2024-11-27 by tbh for init
     */
    void removeByIdRecord(String pdctId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param pdctIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-11-27
     * @change
     * 2024-11-27 by tbh for init
     */
    void removeByIdsRecord(List<String> pdctIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的合同变更计租面积变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-11-27
     * @change
     * 2024-11-27 by tbh for init
     */
    void updateByIdRecord(BbsChangeCalculatedProductVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的合同变更计租面积变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-11-27
     * @change
     * 2024-11-27 by tbh for init
     */
    void updateBatchByIdRecord(List<BbsChangeCalculatedProductVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的合同变更计租面积变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-11-27
     * @change
     * 2024-11-27 by tbh for init
     */
    void saveByIdRecord(BbsChangeCalculatedProductVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的合同变更计租面积变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-11-27
     * @change
     * 2024-11-27 by tbh for init
     */
    void saveBatchByIdRecord(List<BbsChangeCalculatedProductVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param pdctId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-11-27
     * @change
     * 2024-11-27 by tbh for init
     */
    BbsChangeCalculatedProductVo selectByIdRecord(String pdctId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-11-27
     * @change
     * 2024-11-27 by tbh for init
     */
    PageResult<List<BbsChangeCalculatedProductPageResultVo>> selectByPageRecord(BbsChangeCalculatedProductPageVo vo);
}
