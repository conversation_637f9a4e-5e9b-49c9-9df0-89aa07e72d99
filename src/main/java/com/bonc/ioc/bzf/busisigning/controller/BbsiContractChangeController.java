package com.bonc.ioc.bzf.busisigning.controller;

import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeService;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合同变更表 前端控制器
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by fzq for init
 */
@Slf4j
@RestController
@RequestMapping("/contractChange")
@Api(tags = "合同变更")
public class BbsiContractChangeController {
    @Resource
    private IBbsiContractChangeService baseService;

    /**
     * insertRecord 新增
     *
     * @param vo 需要新增的记录
     * @return com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by fzq for init
     * @since 1.0.0
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "fzq")
    @ApiOperation(value = "新增", notes = "新增全表数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "合同变更表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsiContractChangeVo vo) {
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.insertRecord(vo, false));
        return appReply;
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param request 删除请求参数，包含ccId和requestId
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by fzq for init
     * @since 1.0.0
     */
    @PostMapping(value = "/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "pyj")
    @ApiOperation(value = "根据主键删除", notes = "根据主键删除表中信息 物理删除")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdRecord(@ApiParam(value = "删除请求参数") @RequestBody @Validated BbsiContractChangeRemoveRequestVo request) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        // 调用Service层方法，传递requestId参数
        baseService.removeByIdRecord(request.getCcId(), request.getRequestId());
        return appReply;
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的合并变更表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by fzq for init
     */
    @PostMapping(value = "/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "fzq")
    @ApiOperation(value = "根据主键更新", notes = "根据主键更新表中信息 更新全部信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateByIdRecord(@ApiParam(value = "需要更新的合并变更表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbsiContractChangeVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateByIdRecord(vo);
        return appReply;
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param ccId 需要查询的
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by fzq for init
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "fzq")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsiContractChangeVo> selectByIdRecord(@ApiParam(value = "主键") @RequestParam String ccId){
        AppReply<BbsiContractChangeVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByIdRecord(ccId));
        return appReply;
    }
    @GetMapping(value = "/selectByContractCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "刘鹏伟")
    @ApiOperation(value = "根据合同编号查询合同变更信息", notes = "根据合同编号查询合同变更信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsiContractChangeResultVo> selectByContractCode(@ApiParam(value = "合同编号") @RequestParam String contractCode){
        AppReply<BbsiContractChangeResultVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByContractCode(contractCode));
        return appReply;
    }

    @GetMapping(value = "/selectCustomInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "fzq")
    @ApiOperation(value = "根据合同编号查询客户信息", notes = "首次新增合同变更，根据合同编号，查询客户信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsiContractChangeCustomerVo> selectCustomInfo(@ApiParam(value = "合同编号") @RequestParam String contractCode) {
        AppReply<BbsiContractChangeCustomerVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectCustomInfo(contractCode));
        return appReply;
    }

    @GetMapping(value = "/selectByPageRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "fzq")
    @ApiOperation(value = "合同变更列表分页查询", notes = "合同变更列表分页查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<PageResult<List<BbsiContractChangeListPageResultVo>>> selectByPageRecord(BbsiContractChangeListPageResultVo vo) {
        AppReply<PageResult<List<BbsiContractChangeListPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
    }

    @GetMapping(value = "/selectChangeRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "fzq")
    @ApiOperation(value = "合同变更记录分页查询", notes = "合同变更记录分页查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<PageResult<List<BbsiContractChangeRecordPageResultVo>>> selectChangeRecord(BbsiContractChangeRecordPageResultVo vo) {
        AppReply<PageResult<List<BbsiContractChangeRecordPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectChangeRecord(vo));
        return appReply;
    }


    /*
    合同变更审批记录新增
     */
    @PostMapping(value = "/insertCaRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "")
    @ApiOperation(value = "新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "审批表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsiContractChangeApproveInfoVo vo) {
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.insertCaRecord(vo));
        return appReply;
    }


    @GetMapping(value = "/selectContractAuditPageRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "")
    @ApiOperation(value = "合同变更申请审核分页查询", notes = "合同变更申请审核分页查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<PageResult<List<BbsiContractChangeRecordPageResultVo>>> selectContractAuditPageRecord(BbsiContractChangeRecordPageResultVo vo) {
        AppReply<PageResult<List<BbsiContractChangeRecordPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectContractAuditPageRecord(vo));
        return appReply;
    }


    @GetMapping(value = "/checkStatus", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "fzq")
    @ApiOperation(value = "合同变更校验状态", notes = "合同变更校验状态")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<String> checkStatus(@ApiParam(value = "合同编号") @RequestParam String contractCode) {
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.checkStatus(contractCode));
        return appReply;
    }

    /**
     * 预览与下载
     *
     * @param ccId 合同变更id
     * @return 预览与下载结果
     */
    @GetMapping(value = "/previewAndDownload", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "fzq")
    @ApiOperation(value = "预览与下载", notes = "预览与下载")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<FIleResultVo> previewAndDownload(@ApiParam(value = "合同变更id") @RequestParam String ccId) {
        AppReply<FIleResultVo> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.previewAndDownload(ccId));
        return appReply;
    }

    /**
     * 预览与下载
     *
     * @param ccId 合同变更id
     * @return 预览与下载结果
     */
    @PostMapping(value = "/previewAndDownloadV2", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "fzq")
    @ApiOperation(value = "预览与下载V2", notes = "预览与下载V2")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<FIleResultVo> previewAndDownloadV2(@ApiParam(value = "合同变更表", required = false) @RequestBody BbsiContractChangeVo vo) {
        AppReply<FIleResultVo> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.previewAndDownloadV2(vo));
        return appReply;
    }
}

