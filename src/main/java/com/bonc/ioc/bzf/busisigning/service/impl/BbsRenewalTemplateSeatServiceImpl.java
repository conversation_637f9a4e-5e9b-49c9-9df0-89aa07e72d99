package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalTemplateSeatEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalTemplateSeatMapper;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalTemplateSeatService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 模板属性表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
@Slf4j
@Service
public class BbsRenewalTemplateSeatServiceImpl extends McpBaseServiceImpl<BbsRenewalTemplateSeatEntity> implements IBbsRenewalTemplateSeatService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalTemplateSeatMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsRenewalTemplateSeatService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsRenewalTemplateSeatVo vo) {
        if(vo == null) {
            return null;
        }

        BbsRenewalTemplateSeatEntity entity = new BbsRenewalTemplateSeatEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setSeatId(null);
        if(!baseService.insert(entity)) {
            log.error("模板属性表新增失败:" + entity.toString());
            throw new McpException("模板属性表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getSeatId(),1)) {
                log.error("模板属性表新增后保存历史失败:" + entity.toString());
                throw new McpException("模板属性表新增后保存历史失败");
            }

            log.debug("模板属性表新增成功:"+entity.getSeatId());
            return entity.getSeatId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsRenewalTemplateSeatVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsRenewalTemplateSeatEntity> entityList = new ArrayList<>();
        for (BbsRenewalTemplateSeatVo item:voList) {
            BbsRenewalTemplateSeatEntity entity = new BbsRenewalTemplateSeatEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsRenewalTemplateSeatEntity item:entityList){
            item.setSeatId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("模板属性表新增失败");
            throw new McpException("模板属性表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsRenewalTemplateSeatEntity::getSeatId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("模板属性表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("模板属性表批量新增后保存历史失败");
            }

            log.debug("模板属性表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param seatId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String seatId) {
        if(!StringUtils.isEmpty(seatId)) {
            if(!baseService.saveOperationHisById(seatId,3)) {
                log.error("模板属性表删除后保存历史失败:" + seatId);
                throw new McpException("模板属性表删除后保存历史失败");
            }

            if(!baseService.removeById(seatId)) {
                log.error("模板属性表删除失败");
                throw new McpException("模板属性表删除失败"+seatId);
            }
        } else {
            throw new McpException("模板属性表删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param seatIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> seatIdList) {
        if(!CollectionUtils.isEmpty(seatIdList)) {
            int oldSize = seatIdList.size();
            seatIdList = seatIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(seatIdList) || oldSize != seatIdList.size()) {
                throw new McpException("模板属性表批量删除失败 存在主键id为空的记录"+StringUtils.join(seatIdList));
            }

            if(!baseService.saveOperationHisByIds(seatIdList,3)) {
                log.error("模板属性表批量删除后保存历史失败:" + StringUtils.join(seatIdList));
                throw new McpException("模板属性表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(seatIdList)) {
                log.error("模板属性表批量删除失败");
                throw new McpException("模板属性表批量删除失败"+StringUtils.join(seatIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsRenewalTemplateSeatVo vo) {
        if(vo != null) {
            BbsRenewalTemplateSeatEntity entity = new BbsRenewalTemplateSeatEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getSeatId())) {
                throw new McpException("模板属性表更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("模板属性表更新失败");
                throw new McpException("模板属性表更新失败"+entity.getSeatId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSeatId(),2)) {
                    log.error("模板属性表更新后保存历史失败:" + entity.getSeatId());
                    throw new McpException("模板属性表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("模板属性表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsRenewalTemplateSeatVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalTemplateSeatEntity> entityList = new ArrayList<>();

            for (BbsRenewalTemplateSeatVo item:voList){
                BbsRenewalTemplateSeatEntity entity = new BbsRenewalTemplateSeatEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getSeatId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("模板属性表批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("模板属性表批量更新失败");
                throw new McpException("模板属性表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSeatId())).map(BbsRenewalTemplateSeatEntity::getSeatId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("模板属性表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("模板属性表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsRenewalTemplateSeatVo vo) {
        if(vo != null) {
            BbsRenewalTemplateSeatEntity entity = new BbsRenewalTemplateSeatEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("模板属性表保存失败");
                throw new McpException("模板属性表保存失败"+entity.getSeatId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSeatId(),4)) {
                    log.error("模板属性表保存后保存历史失败:" + entity.getSeatId());
                    throw new McpException("模板属性表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("模板属性表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsRenewalTemplateSeatVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalTemplateSeatEntity> entityList = new ArrayList<>();

            for (BbsRenewalTemplateSeatVo item:voList){
                BbsRenewalTemplateSeatEntity entity = new BbsRenewalTemplateSeatEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("模板属性表批量保存失败");
                throw new McpException("模板属性表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSeatId())).map(BbsRenewalTemplateSeatEntity::getSeatId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("模板属性表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("模板属性表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param seatId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsRenewalTemplateSeatVo selectByIdRecord(String seatId) {
        BbsRenewalTemplateSeatVo vo = new BbsRenewalTemplateSeatVo();

        if(!StringUtils.isEmpty(seatId)) {
            BbsRenewalTemplateSeatEntity entity = baseService.selectById(seatId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsRenewalTemplateSeatPageResultVo>> selectByPageRecord(BbsRenewalTemplateSeatPageVo vo) {
        List<BbsRenewalTemplateSeatPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据上级id查询map集
     *
     * @param parentId 上级id
     * @return 模板属性map集
     */
    @Override
    public Map<String, String> selectMapByParentId(String parentId) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BbsRenewalTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode().toString())
                .eq(BbsRenewalTemplateSeatEntity::getParentId, parentId)
                .list().stream()
                .collect(Collectors.toMap(BbsRenewalTemplateSeatEntity::getSeatKey, b -> StrUtil.isNotBlank(b.getValue())
                        ? b.getValue() : b.getDefaultVal() == null || b.getDefaultVal() == "" ? "" : b.getDefaultVal()));
    }
}
