package com.bonc.ioc.bzf.busisigning.workflow.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SaveWorkFlowVo extends McpBaseVo {

    @ApiModelProperty(value = "业务分类编码")
    //@NotBlank(message = "业务分类编码不能为空",groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String businessTypeCode;

    @ApiModelProperty(value = "流程分类Id")
//    @NotBlank(message = "流程分类Id不能为空",groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String workflowId;

    private Integer requestlevel;

    private String projectId;

    private String remark;

}
