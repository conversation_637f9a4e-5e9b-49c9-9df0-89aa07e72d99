package com.bonc.ioc.bzf.busisigning.controller;

import com.bonc.ioc.bzf.busisigning.service.IBbsScriptService;
import com.bonc.ioc.bzf.busisigning.vo.BbsLesseeChangeExcelVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsPaymentDateChangeExcelVo;
import com.bonc.ioc.bzf.utils.common.imports.BbsImportResultVo;
import com.bonc.ioc.bzf.utils.common.imports.ImportExcel;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.util.FileUploadValidator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 脚本相关 前端控制器
 *
 * <AUTHOR>
 * @since 2024/5/15
 */
@RestController
@RequestMapping("/script")
@Api(tags = "合同变更(主表)业务相关")
@Validated
public class BbsScriptController {

    /**
     * 脚本相关 服务实例
     */
    @Resource
    private IBbsScriptService scriptService;

    /**
     * excel导入--主承租人变更
     *
     * @param file    文件信息
     * @param batchNo 批次号
     * @return 导入结果
     */
    @ImportExcel
    @PostMapping(value = "/excelImportRentInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "excel导入--主承租人变更")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsImportResultVo<BbsLesseeChangeExcelVo>> excelImportRentInfo(@RequestPart("file") MultipartFile file,
                                                                                   @RequestPart(value = "batchNo", required = false) String batchNo) {
        FileUploadValidator.isValidFileUpload(file);
        AppReply<BbsImportResultVo<BbsLesseeChangeExcelVo>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(scriptService.excelImportLesseeChange(file, batchNo));
        return appReply;
    }

    /**
     * excel导入--应缴日期变更
     *
     * @param file    文件信息
     * @param batchNo 批次号
     * @return 导入结果
     */
    @ImportExcel
    @PostMapping(value = "/excelImportPaymentDate", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "excel导入--应缴日期变更")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsImportResultVo<BbsPaymentDateChangeExcelVo>> excelImportPaymentDate(@RequestPart("file") MultipartFile file,
                                                                                           @RequestPart(value = "batchNo", required = false) String batchNo) {
        FileUploadValidator.isValidFileUpload(file);
        AppReply<BbsImportResultVo<BbsPaymentDateChangeExcelVo>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(scriptService.excelImportPaymentDate(file, batchNo));
        return appReply;
    }
}
