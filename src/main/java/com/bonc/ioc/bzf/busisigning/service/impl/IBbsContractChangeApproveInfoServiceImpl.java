package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApplyInfoMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsiContractChangeApproveMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsiContractChangeMapper;
import com.bonc.ioc.bzf.busisigning.entity.*;
import com.bonc.ioc.bzf.busisigning.enums.ApproveStatusEnum;
import com.bonc.ioc.bzf.busisigning.enums.ChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.ContractChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.factory.change.AbstractContractChangeFactory;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BzfBusinessMessageFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.WebsiteMsgContentVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.WebsiteMsgVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsContractChangeApproveDetailService;
import com.bonc.ioc.bzf.busisigning.service.IBbsContractChangeApproveInfoService;
import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeService;
import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeV2Service;
import com.bonc.ioc.bzf.busisigning.utils.ResultUtils;
import com.bonc.ioc.bzf.busisigning.utils.UserUtil;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IBbsContractChangeApproveInfoServiceImpl extends McpBaseServiceImpl<BbsiContractChangeApproveEntity> implements IBbsContractChangeApproveInfoService {
    @Resource
    private BbsiContractChangeApproveMapper baseMapper;
    @Resource
    private UserUtil userUtil;
    @Resource
    private Environment env;
    @Resource
    private BzfBusinessMessageFeignClient bzfBusinessMessageFeignClient;
    @Resource
    private IBbsContractChangeApproveDetailService bbsContractChangeApproveDetailService;

    @Value("${bzfsystem.messageTemplate.contractChangeApplay.systemSuccessTemplateId:9cf6343a0fb74b33b4a33a752164521a}")
    private String systemSuccess;

    @Value("${bzfsystem.messageTemplate.contractChangeApplay.systemFailedTemplateId:22d144bbf9344f40a7c1d4de847ebee8}")
    private String systemFailed;

    @Value("${bzfsystem.messageTemplate.contractChangeApplay.mobileSuccessTemplateId:f6bd823819d34faba9ea1fc3c92e514c}")
    private String mobileSuccess;

    @Value("${bzfsystem.messageTemplate.contractChangeApplay.mobileFailedTemplateId:bedc5e1b4a434641b1b8d99ee1a9d7a0}")
    private String mobileFailed;

    @Resource
    private BbsiContractChangeMapper contractChangeMapper;


    @Resource
    private BbctContractFeignClient bbctContractFeignClient;

    @Resource
    private BbsRenewalApplyInfoMapper renewalApplyInfoMapper;
    @Autowired
    private ContractChangeResultServiceImpl contractChangeResultService;
    @Resource
    private IBbsiContractChangeService iBbsiContractChangeService;

    @Resource
    private IBbsiContractChangeV2Service iBbsiContractChangeV2Service;

    @Resource
    private RedissonClient redissonClient;

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertCaRecord(BbsiContractChangeApproveInfoVo vo) {
        if (vo == null) {
            return null;
        }
        BbsiContractChangeApproveEntity entity = new BbsiContractChangeApproveEntity();
        BeanUtils.copyProperties(vo, entity);
        entity.setApproveId(null);
        if (!this.insert(entity)) {
            log.error("合同变更审批表新增失败:" + entity.toString());
            throw new McpException("合同变更审批表新增失败");
        } else {
            if (!this.saveOperationHisById(entity.getApproveId(), 1)) {
                log.error("合同变更审批表新增后保存历史失败:" + entity.toString());
                throw new McpException("合同变更审批表新增后保存历史失败");
            }
            log.debug("合同变更审批表新增成功:" + entity.getApproveId());
            return entity.getApproveId();
        }
    }

    @Override
    public PageResult<List<BbsiContractChangeApproveListPageResultVo>> selectByPageRecord(BbsiContractChangeApproveListPageResultVo vo) {
        List<BbsiContractChangeApproveListPageResultVo> result = baseMapper.selectByPageRecord(vo);
        if(result!=null && result.size()>0){
            for(BbsiContractChangeApproveListPageResultVo bbsiContractChangeApproveListPageResultVo:result){
                String productName = bbsiContractChangeApproveListPageResultVo.getProductName();
                if(StringUtils.isNotBlank(productName) && productName.contains(",")){

                }
            }
        }
        return new PageResult<>(result);
    }

    @Override
    public List<BbsApproveDetailInfoVo> selectByContractCode(String ccId) {
        List<BbsApproveDetailInfoVo> result = baseMapper.selectByContractCode(ccId);
        return result;
    }

    @Override
    public BbsiContractChangeApproveVo selectContractChangeApprove(BbsiContractChangeApproveVo vo) {
        return baseMapper.selectContractChangeApprove(vo);
    }

    @Override
    @Transactional
    public AppReply saveContractChangeApproveResult(BbsiContractChangeApproveInfoVo vo) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("approve_id", vo.getApproveId());
        List<BbsiContractChangeApproveEntity> bbsApproveInfoEntities = baseMapper.selectByMap(queryMap);
        if (bbsApproveInfoEntities.isEmpty()) {
            return new AppReply(AppReply.ERROR_CODE, "未查询到合同变更审核记录", null);
        }

        String approveIdKey = vo.getApproveId();
//        String contractCodeKey = vo.getContractCode();
        String ccIdKey = vo.getCcId();
        String lockKey = "contractChangeApprove:" + approveIdKey;
        RLock disLock = redissonClient.getLock(lockKey);
        if(disLock.tryLock()){
            try {

                //防止重复提交
                if (bbsApproveInfoEntities.size() > 1) {
                    baseMapper.deleteBatchIds(bbsApproveInfoEntities.stream().map(BbsiContractChangeApproveEntity::getApproveId).collect(Collectors.toList()));
                }
                BbsiContractChangeApproveEntity bbsApproveInfoEntity = bbsApproveInfoEntities.get(0);
                //保存明细数据
                saveApproveDetailInfo(vo, 1);
                //在保存最新记录
                bbsApproveInfoEntity.setApproveStatus(vo.getApproveStatus());
                bbsApproveInfoEntity.setApproverUserId(userUtil.getUserId());
                bbsApproveInfoEntity.setApproverUserName(userUtil.getUserName(userUtil.getUserId()));
                bbsApproveInfoEntity.setApproveTime(new Date(System.currentTimeMillis()));
                bbsApproveInfoEntity.setApproveId(IdUtil.fastSimpleUUID());
                bbsApproveInfoEntity.setCreateTime(null);
                bbsApproveInfoEntity.setModifyTime(null);
                baseMapper.insert(bbsApproveInfoEntity);
                //未通过
                if ("2".equals(vo.getApproveStatus())) {
                    updateSignStatus(vo.getApproveId(), ApproveStatusEnum.UNAPPROVED.getCode());

                    //租金变更、缩租面积变更    未通过 开启账单（打开关闭的账单）
                    BbsiContractChangeEntity bbsiContractChangeEntity = contractChangeMapper.selectById(vo.getCcId());
                    BbsiContractChangeVo bbsiContractChangeVo = new BbsiContractChangeVo();
                    BeanUtils.copyProperties(bbsiContractChangeEntity, bbsiContractChangeVo);
                    if(bbsiContractChangeVo.getChangeTypeItem().contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())
                            || bbsiContractChangeVo.getChangeTypeItem().contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())
                            || bbsiContractChangeVo.getChangeTypeItem().contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())){
                        String contractCode = bbsiContractChangeVo.getContractCode();
                        String projectId = "";
                        //获取项目idc
                        AppReply<BbctContractManagementVo> appReply = bbctContractFeignClient.selectByIdNo(contractCode, null);
                        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
                            throw new McpException(ResultUtils.createLog("查询合同信息失败[详情: %s, contractCode: %s]", appReply, contractCode));
                        }
                        BbctContractManagementVo bbctContractManagementVo = appReply.getData();
                        if (bbctContractManagementVo.getSubjectMatterList() != null && bbctContractManagementVo.getSubjectMatterList().size() > 0) {
                            projectId = bbctContractManagementVo.getSubjectMatterList().get(0).getProjectId();
                        }
                        iBbsiContractChangeService.closeOrOpenBillAndBillBranks("1", contractCode,projectId);
                    }
                }
                if ("1".equals(vo.getApproveStatus())) {
                    updateSignStatus(vo.getApproveId(), ApproveStatusEnum.APPROVED.getCode());
                    //审核通过 保存变更结果
                    BbsiContractChangeEntity bbsiContractChangeEntity = contractChangeMapper.selectById(vo.getCcId());
                    BbsiContractChangeVo bbsiContractChangeVo = new BbsiContractChangeVo();
                    BeanUtils.copyProperties(bbsiContractChangeEntity, bbsiContractChangeVo);

                    if(ChangeTypeEnum.OTHER_CHANGE.getCode().equals(bbsiContractChangeVo.getChangeType())
                            || ChangeTypeEnum.LESSEE_CHANGE.getCode().equals(bbsiContractChangeVo.getChangeType())){
                        //生成新的协议编号
                        String agreementCode = getContractCode("4", bbsiContractChangeVo.getContractCode());
                        bbsiContractChangeVo.setAgreementCode(agreementCode);
                        //推签约
                        String signId = AbstractContractChangeFactory.getInstance(bbsiContractChangeVo).pushSignInfo();
                        if(StringUtils.isBlank(signId)){
                            throw new McpException("更新合同变更表signId时signId为空"+vo.getCcId());
                        }
                        //更新合同变更表signId
                        BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
                        entity.setCcId(vo.getCcId());
                        entity.setAgreementCode(agreementCode);
                        entity.setSignId(signId);
                        iBbsiContractChangeService.updateById(entity);

                        BbsiContractChangeVo preNewContractChangeVo = iBbsiContractChangeV2Service.selectChangeInfos(vo.getCcId(),
                                bbsiContractChangeVo.getContractCode(), bbsiContractChangeVo.getChangeTypeItem(), null,"1");
                        //保存试算账单
                        AbstractContractChangeFactory.getInstance(preNewContractChangeVo).insertPreviewBills();
                    }else{
                        //原来逻辑
                        contractChangeResultService.contractChange(bbsiContractChangeVo);
                    }
                }
                AppReply msgAppReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
                //驳回原因
                String rejectReason = vo.getApproveStatus().equals("2") ? vo.getCommentExplanation() : "";
                //审签成功 或者 审签成功/失败 发送信息
                if (Arrays.asList("1", "2").contains(vo.getApproveStatus())) {
                    //发送站内信
                    AppReply website = sendMessage("website", vo.getCcId(), rejectReason);
                    //发送短信
                    AppReply sms = sendMessage("SMS", vo.getCcId(), rejectReason);
                    msgAppReply.setCode(website.getCode().equals(AppReply.SUCCESS_CODE) && sms.getCode().equals(AppReply.SUCCESS_CODE) ? AppReply.SUCCESS_CODE : AppReply.ERROR_CODE);
                    msgAppReply.setMsg(website.getMsg() + ";" + sms.getMsg());
                }
//        //审核通过
//        if ("1".equals(vo.getApproveStatus())) {
//            BbsiContractChangeEntity bbsiContractChangeEntity = contractChangeMapper.selectById(vo.getCcId());
//            // 调用合同中心修改合同状态以及字段
//            List<BbctContractManagementVo> contractManagementVos = new ArrayList<>();
//            BbctContractManagementVo bbctContractManagementVo = new BbctContractManagementVo();
//            bbctContractManagementVo.setContractNo(bbsiContractChangeEntity.getContractCode());
//            List<BbctContractSignerVo> bbctContractSignerVos = new ArrayList<>();
//            BbctContractSignerVo bbctContractSignerVo = new BbctContractSignerVo();
//            bbctContractSignerVo.setContractNo(bbsiContractChangeEntity.getContractCode());
//            if (!Objects.equals(bbsiContractChangeEntity.getCustomerNo(), bbsiContractChangeEntity.getCustomerNoOld())) {
//                bbctContractSignerVo.setCustomerNo(bbsiContractChangeEntity.getCustomerNo());
//            }
//            if (!Objects.equals(bbsiContractChangeEntity.getCustomerName(), bbsiContractChangeEntity.getCustomerNameOld())) {
//                bbctContractSignerVo.setCustomerName(bbsiContractChangeEntity.getCustomerName());
//            }
//            if (!Objects.equals(bbsiContractChangeEntity.getMailAddress(), bbsiContractChangeEntity.getMailAddressOld())) {
//                bbctContractSignerVo.setMailAddress(bbsiContractChangeEntity.getMailAddress());
//            }
//            if (!Objects.equals(bbsiContractChangeEntity.getBankName(), bbsiContractChangeEntity.getBankNameOld())) {
//                bbctContractSignerVo.setBankName(bbsiContractChangeEntity.getBankName());
//            }
//            if (!Objects.equals(bbsiContractChangeEntity.getBankNameCode(), bbsiContractChangeEntity.getBankNameCodeOld())) {
//                bbctContractSignerVo.setBankNameCode(bbsiContractChangeEntity.getBankNameCode());
//            }
//            if (!Objects.equals(bbsiContractChangeEntity.getBankCard(), bbsiContractChangeEntity.getBankCardOld())) {
//                bbctContractSignerVo.setBankCard(bbsiContractChangeEntity.getBankCard());
//            }
//            if (!Objects.equals(bbsiContractChangeEntity.getCustomerTel(), bbsiContractChangeEntity.getCustomerTelOld())) {
//                bbctContractSignerVo.setCustomerTel(bbsiContractChangeEntity.getCustomerTel());
//            }
//            bbctContractSignerVos.add(bbctContractSignerVo);
//            bbctContractManagementVo.setUserList(bbctContractSignerVos);
//
//            contractManagementVos.add(bbctContractManagementVo);
//            if (!Objects.equals(env.getProperty("spring.profiles.active"), "debug")) {
//                log.info("调用合同中心合同变更接口，请求参数【{}】", JSON.toJSONString(contractManagementVos));
//                AppReply appReply = bbctContractFeignClient.changeBatchAddOne(contractManagementVos);
//                if (!AppReply.SUCCESS_CODE.equals(appReply.getCode())) {
//                    log.error("调用合同中心合同变更接口，返回【{}】", appReply);
//                    throw new McpException("合同中心合同变更接口调取失败");
//                }
//            }
//        }
                return msgAppReply;

            }finally {
                if (disLock.isLocked() && disLock.isHeldByCurrentThread()) {
                    disLock.unlock();
                }
            }
        }else{
            log.error("该合同补充协议审签中,请勿重复提交,变更id为"+ccIdKey);
            throw new McpException("该合同补充协议审签中,请勿重复提交,变更id为"+ccIdKey);
        }
    }

    @Override
    @Transactional
    public AppReply saveContractChangeApproveResultV2(String ccId, String approveStatus) {
        String lockKey = "contractChangeApprove:" + ccId;
        RLock disLock = redissonClient.getLock(lockKey);
        if(disLock.tryLock()){
            try{
                //未通过
                if ("2".equals(approveStatus)) {
                    //租金变更、缩租面积变更    未通过 开启账单（打开关闭的账单）
                    BbsiContractChangeEntity bbsiContractChangeEntity = contractChangeMapper.selectById(ccId);
                    BbsiContractChangeVo bbsiContractChangeVo = new BbsiContractChangeVo();
                    BeanUtils.copyProperties(bbsiContractChangeEntity, bbsiContractChangeVo);
                    if(bbsiContractChangeVo.getChangeTypeItem().contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())
                            || bbsiContractChangeVo.getChangeTypeItem().contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())
                            || bbsiContractChangeVo.getChangeTypeItem().contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())
                            || bbsiContractChangeVo.getChangeTypeItem().contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode())
                            || bbsiContractChangeVo.getChangeTypeItem().contains(ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode())){
                        String contractCode = bbsiContractChangeVo.getContractCode();
                        String projectId = "";
                        //获取项目idc
                        AppReply<BbctContractManagementVo> appReply = bbctContractFeignClient.selectByIdNo(contractCode, null);
                        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
                            throw new McpException(ResultUtils.createLog("查询合同信息失败[详情: %s, contractCode: %s]", appReply, contractCode));
                        }
                        BbctContractManagementVo bbctContractManagementVo = appReply.getData();
                        if (bbctContractManagementVo.getSubjectMatterList() != null && bbctContractManagementVo.getSubjectMatterList().size() > 0) {
                            projectId = bbctContractManagementVo.getSubjectMatterList().get(0).getProjectId();
                        }
                        iBbsiContractChangeService.closeOrOpenBillAndBillBranks("1", contractCode,projectId);
                    }
                }
                if ("1".equals(approveStatus)) {
                    //审核通过 保存变更结果
                    BbsiContractChangeEntity bbsiContractChangeEntity = contractChangeMapper.selectById(ccId);
                    BbsiContractChangeVo bbsiContractChangeVo = new BbsiContractChangeVo();
                    BeanUtils.copyProperties(bbsiContractChangeEntity, bbsiContractChangeVo);

                    if(ChangeTypeEnum.OTHER_CHANGE.getCode().equals(bbsiContractChangeVo.getChangeType())
                            || ChangeTypeEnum.LESSEE_CHANGE.getCode().equals(bbsiContractChangeVo.getChangeType())){
                        //生成新的协议编号
                        String agreementCode = getContractCode("4", bbsiContractChangeVo.getContractCode());
                        bbsiContractChangeVo.setAgreementCode(agreementCode);
                        //推签约
                        String signId = AbstractContractChangeFactory.getInstance(bbsiContractChangeVo).pushSignInfo();
                        if(StringUtils.isBlank(signId)){
                            throw new McpException("更新合同变更表signId时signId为空"+ccId);
                        }
                        //更新合同变更表signId
                        BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
                        entity.setCcId(ccId);
                        entity.setAgreementCode(agreementCode);
                        entity.setSignId(signId);
                        iBbsiContractChangeService.updateById(entity);

                        BbsiContractChangeVo preNewContractChangeVo = iBbsiContractChangeV2Service.selectChangeInfos(ccId,
                                bbsiContractChangeVo.getContractCode(), bbsiContractChangeVo.getChangeTypeItem(), null,"1");
                        //保存试算账单
                        AbstractContractChangeFactory.getInstance(preNewContractChangeVo).insertPreviewBills();
                    }else{
                        //原来逻辑
                        contractChangeResultService.contractChange(bbsiContractChangeVo);
                    }
                }

                AppReply msgAppReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
                //驳回原因
                String rejectReason = approveStatus.equals("2") ? "未通过": "";
                //审签成功 或者 审签成功/失败 发送信息
                if (Arrays.asList("1", "2").contains(approveStatus)) {
                    //发送站内信
                    AppReply website = sendMessage("website", ccId, rejectReason);
                    //发送短信
                    AppReply sms = sendMessage("SMS", ccId, rejectReason);
                    msgAppReply.setCode(website.getCode().equals(AppReply.SUCCESS_CODE) && sms.getCode().equals(AppReply.SUCCESS_CODE) ? AppReply.SUCCESS_CODE : AppReply.ERROR_CODE);
                    msgAppReply.setMsg(website.getMsg() + ";" + sms.getMsg());
                }

                return msgAppReply;
            }finally {
                if (disLock.isLocked() && disLock.isHeldByCurrentThread()) {
                    disLock.unlock();
                }
            }
        }else{
            log.error("该合同补充协议审签中,请勿重复提交,变更id为"+ccId);
            throw new McpException("该合同补充协议审签中,请勿重复提交,变更id为"+ccId);
        }
    }

    private String getContractCode(String modeId, String contractCode) {
        AppReply<String> appReply = bbctContractFeignClient.contractCodeGeneration(modeId, contractCode);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || StringUtils.isBlank(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("合同中心生成编号失败[详情: %s, 合同编号: %s]", appReply, contractCode));
        }
        return appReply.getData();
    }

    @Override
    public List<AddressCascadeQueryResultVo> addressCascadeQuery(BbsApproveAddressCascadeQueryVo queryVo) {
        return baseMapper.addressCascadeQuery(queryVo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSignStatus(String approveId, String approveStatus) {
        ChainWrappers.lambdaUpdateChain(baseMapper)
                .eq(BbsiContractChangeApproveEntity::getApproveId, approveId)
                .set(BbsiContractChangeApproveEntity::getApproveStatus, approveStatus)
                .update();
    }

    /**
     * @param type         站内信:website 短信:SMS
     * @param ccId         合同id
     * @param rejectReason 驳回原因
     */
    private AppReply sendMessage(String type, String ccId, String rejectReason) {
        //debug调不通 所以不发送
        /*if (Objects.equals(env.getProperty("spring.profiles.active"), "debug")) {
            return new AppReply<>(AppReply.SUCCESS_CODE, "debug", null);
        }*/
        //查询 客户名称 房源地址
        List<Map> list = baseMapper.selectMsgInfo(ccId);
        if (list.isEmpty()) {
            log.info("未查询到客户名称或联系电话");
            return new AppReply<>(AppReply.ERROR_CODE, "未查询到客户名称或联系电话", null);
        }
        Map map = list.get(0);
        log.error("查询客户信息====={}", JSON.toJSONString(map));
        if (null == map) {
            return new AppReply<>(AppReply.ERROR_CODE, "未查询到客户名称或联系电话", null);
        }
        String housingAddress = Objects.toString(map.get("housingAddress"), "未查询到房源地址");
        String customerName = Objects.toString(map.get("customerName"), "未查询到客户名称");
        String phone = Objects.toString(map.get("customerTel"), "");
        if ("SMS".equals(type) && ObjectUtils.isEmpty(phone)) {
            log.error("发送短信失败====>>>>>未查询到客户手机号码{}", phone);
            return new AppReply<>(AppReply.ERROR_CODE, "发送短信失败,未查询到客户手机号码", null);
        }

        String websiteSuccessMsgTemplateId = Objects.toString(systemSuccess, "未查询到站内信模板id");
        String websiteFailMsgTemplateId = Objects.toString(systemFailed, "未查询到站内信模板id");
        String SMSSuccessTemplateId = Objects.toString(mobileSuccess, "未查询到短信模板id");
        String SMSFailTemplateId = Objects.toString(mobileFailed, "未查询到短信模板id");

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("customerName", customerName);
        paramMap.put("toExamineResult", rejectReason);
        paramMap.put("housingAddress", housingAddress);
        List<WebsiteMsgContentVo> websiteMsgContentVo = Arrays.asList(new WebsiteMsgContentVo(paramMap, phone, ccId));
        //发送信息
        AppReply<JSONObject> appReply = null;
        StringBuilder message = new StringBuilder();
        if ("website".equals(type)) {
            message.append("发送站内信");
            //站内信
            if (StringUtils.isEmpty(rejectReason)) {
                paramMap.remove("toExamineResult");
                WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(websiteSuccessMsgTemplateId, "系统站内信通知", websiteMsgContentVo);
                log.info("发送站内信==={}", JSON.toJSONString(websiteMsgVo));
                appReply = bzfBusinessMessageFeignClient.sendWebsiteMsg(websiteMsgVo);
            } else {
                //未通过
                WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(websiteFailMsgTemplateId, "系统站内信通知", websiteMsgContentVo);
                log.info("发送站内信==={}", JSON.toJSONString(websiteMsgVo));
                appReply = bzfBusinessMessageFeignClient.sendWebsiteMsg(websiteMsgVo);
            }

        }
        if ("SMS".equals(type) && StringUtils.isNotEmpty(phone)) {
            message.append("发送手机短信");
            //手机短信
            if (StringUtils.isEmpty(rejectReason)) {
                //通过
                paramMap.remove("toExamineResult");
                WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(SMSSuccessTemplateId, "", websiteMsgContentVo);
                log.info("发送短信==={}", JSON.toJSONString(websiteMsgVo));
                appReply = bzfBusinessMessageFeignClient.sendSMS(websiteMsgVo);
            } else {
                //未通过
                WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(SMSFailTemplateId, "", websiteMsgContentVo);
                log.info("发送短信==={}", JSON.toJSONString(websiteMsgVo));
                appReply = bzfBusinessMessageFeignClient.sendSMS(websiteMsgVo);
            }
        }

        assert appReply != null;
        log.info("发送站内信/短信成功结果==={}", JSON.toJSONString(appReply));
        if (appReply.getCode().equals("1")) {
            List<Map> data = (List<Map>) appReply.getData().get("detailVos");
            if (null != data && null != data.get(0)) {
                Map detail = data.get(0);
                if (detail.get("code").equals("00")) {
                    log.info("发送站内信/短信成功==={}", JSON.toJSONString(appReply));
                    message.append("成功");
                    return new AppReply<>(AppReply.SUCCESS_CODE, message.toString(), null);
                } else {
                    message.append("失败");
                    log.info("发送站内信/短信失败==={}", JSON.toJSONString(appReply));
                }
            } else {
                message.append("失败");
                log.info("发送站内信/短信失败==={}", JSON.toJSONString(appReply));

            }
        } else {
            message.append("失败");
        }
        return new AppReply<>(AppReply.ERROR_CODE, message.toString(), null);
    }

    /**
     * 根据approveId批量保存明细
     *
     * @param
     */
    @Transactional(rollbackFor = {Exception.class})
    public void saveApproveDetailInfo(BbsiContractChangeApproveInfoVo vo, Integer delFlag) {
        BbsiContractChangeApproveDetailEntity bbsContractChangeApproveDetailEntity = new BbsiContractChangeApproveDetailEntity();
        BeanUtils.copyProperties(vo, bbsContractChangeApproveDetailEntity);
        bbsContractChangeApproveDetailEntity.setApproveId(IdUtil.fastSimpleUUID());
        bbsContractChangeApproveDetailEntity.setCommentExplanation(vo.getCommentExplanation());
        bbsContractChangeApproveDetailEntity.setDelFlag(delFlag);
        bbsContractChangeApproveDetailEntity.setApproveTime(new java.util.Date());
        bbsContractChangeApproveDetailEntity.setApproverUserId(userUtil.getUserId());
        bbsContractChangeApproveDetailEntity.setApproverUserName(userUtil.getUserName(userUtil.getUserId()));
        bbsContractChangeApproveDetailEntity.setCreateTime(null);
        bbsContractChangeApproveDetailEntity.setModifyTime(null);
        bbsContractChangeApproveDetailService.insert(bbsContractChangeApproveDetailEntity);
        //保存完明细 将审核表数据删除
        Map<String, Object> delMap = new HashMap<>();
        delMap.put("cc_id", vo.getCcId());
        baseMapper.deleteByMap(delMap);
    }

    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsiContractChangeApproveInfoVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }
        List<BbsiContractChangeApproveDetailEntity> entityList = new ArrayList<>();
        for (BbsiContractChangeApproveInfoVo item : voList) {
            BbsiContractChangeApproveDetailEntity entity = new BbsiContractChangeApproveDetailEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }
        for (BbsiContractChangeApproveDetailEntity item : entityList) {
            item.setApproveId(null);
        }
        if (!bbsContractChangeApproveDetailService.insertBatch(entityList)) {
            log.error("合同变更审批明细表新增失败");
            throw new McpException("合同变更审批明细表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbsiContractChangeApproveDetailEntity::getApproveId).collect(Collectors.toList());
            if (!bbsContractChangeApproveDetailService.saveOperationHisByIds(kidList, 1)) {
                log.error("合同变更审表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("合同变更审批表批量新增后保存历史失败");
            }
            log.debug("审批明细表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    @Override
    public List<ContractStatusResultVo> selectContractStatus(List<String> contractNos) {
        //查询合同变更状态
        Map<String, Object> contractCode2StatusMap=new HashMap<>();
        List<Map> changApproveInfoMapList=baseMapper.selectContractChangeApproveInfosByContractCodes(contractNos);
        for(Map map:changApproveInfoMapList){
            contractCode2StatusMap.put(String.valueOf(map.get("contractCode")),map.get("approveStatus"));
        }
        //查询续签状态
        LambdaQueryChainWrapper<BbsRenewalApplyInfoEntity> applyWrapper = new LambdaQueryChainWrapper<>(renewalApplyInfoMapper)
                .in(BbsRenewalApplyInfoEntity::getContractCode, contractNos)
                .eq(BbsRenewalApplyInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode());
        List<BbsRenewalApplyInfoEntity> renewalApplyInfoEntities = applyWrapper.list();
        return contractNos.stream().map(s -> {
            ContractStatusResultVo vo = new ContractStatusResultVo();
            vo.setContractNo(s);
            String tempStr=String.valueOf(contractCode2StatusMap.get(s));
            if ("".equals(tempStr.trim())||"null".equalsIgnoreCase(tempStr.trim()) ) {
                vo.setChangeCheckStatus("0");
            } else {
                if ("1".equals(tempStr)) {
                    vo.setChangeCheckStatus("2");
                } else if ("2".equals(tempStr)) {
                    vo.setChangeCheckStatus("3");
                } else if ("3".equals(tempStr)) {
                    vo.setChangeCheckStatus("1");
                }
            }
            if (renewalApplyInfoEntities.stream().anyMatch(f -> f.getContractCode().equals(s))) {
                vo.setIsApplyRenewal("1");
            } else {
                vo.setIsApplyRenewal("0");
            }
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据合同变更id查询
     *
     * @param ccId 合同变更id
     * @return 合同变更审批信息 vo实体
     */
    @Override
    public BbsiContractChangeApproveEntity selectByCcId(String ccId) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BbsiContractChangeApproveEntity::getCcId, ccId)
                .one();
    }
}

