package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApplyInfoProductMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyInfoProductEntity;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalApplyInfoProductService;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApplyInfoProductResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApplyInfoProductVo;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: hechengyao
 * @createDate: 2023-09-26
 * @Version 1.0
 **/

@Slf4j
@Service
public class BbsRenewalApplyInfoProductServiceImpl extends McpBaseServiceImpl<BbsRenewalApplyInfoProductEntity> implements IBbsRenewalApplyInfoProductService {


    /**
     * service 本服务
     */
    @Resource
    private IBbsRenewalApplyInfoProductService baseService;

    @Resource
    private BbsRenewalApplyInfoProductMapper renewalApplyInfoProductMapper;

    /**
     * 商铺地址-级联查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 级联查询的数据
     * <AUTHOR>
     * @date 2023-09-26
     * @since 1.0.0
     */
    @Override
    public List<BbsRenewalApplyInfoProductResultVo> selectByRateRecord(BbsRenewalApplyInfoProductVo vo) {
        List<BbsRenewalApplyInfoProductResultVo> result = renewalApplyInfoProductMapper.selectByRateRecord(vo);
        return result;
    }
}
