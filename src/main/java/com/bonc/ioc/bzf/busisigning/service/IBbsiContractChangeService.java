package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeEntity;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;


/**
 * 合并变更表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by fzq for init
 */
public interface IBbsiContractChangeService extends IMcpBaseService<BbsiContractChangeEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo         需要保存的记录
     * @param onlyInsert 是否只新增
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by fzq for init
     * @since 1.0.0
     */
    String insertRecord(BbsiContractChangeVo vo, boolean onlyInsert);

    String insertRecordV2(BbsiContractChangeVo vo, boolean onlyInsert);

    /**
     * 根据id删除
     *
     * @param ccId 主键id
     */
    void deleteByIdRecord(String ccId);

    /**
     * 合同变更，根据合同id查询客户信息
     *
     * @param contractCode 合同编号
     * @return BbsContractChangeCustomerVo 客户信息
     */
    BbsiContractChangeCustomerVo selectCustomInfo(String contractCode);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    PageResult<List<BbsiContractChangeListPageResultVo>> selectByPageRecord(BbsiContractChangeListPageResultVo vo);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param ccId      需要删除的合同变更表id
     * @param requestId 请求ID，用于日志追踪
     * @return void
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    void removeByIdRecord(String ccId, String requestId);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的合并变更表
     * @return void
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbsiContractChangeVo vo);

    void updateByIdRecordV2(BbsiContractChangeVo vo);

    /**
     * 简单更新合同变更记录字段（不改变状态，不触发工作流）
     * 专门用于保存changeTrial试算结果等场景
     *
     * @param vo 需要更新的合同变更表
     * @return void
     * <AUTHOR>
     * @date 2024-01-01
     * @since 1.0.0
     */
    void updateByIdRecordSimple(BbsiContractChangeVo vo);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param ccId 需要查询的
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    BbsiContractChangeVo selectByIdRecord(String ccId);

    /**
     * 合同编号查询变更记录
     *
     * @param vo 需要查询的条件
     * @return 合同变更记录
     */
    PageResult<List<BbsiContractChangeRecordPageResultVo>> selectChangeRecord(BbsiContractChangeRecordPageResultVo vo);

    /*
    审批表新增
     */
    String insertCaRecord(BbsiContractChangeApproveInfoVo vo);

    PageResult<List<BbsiContractChangeRecordPageResultVo>> selectContractAuditPageRecord(BbsiContractChangeRecordPageResultVo vo);

    /**
     * 合同变更校验状态
     *
     * @param contractCode 合同编号
     * @return 暂存条数==0
     */
    String checkStatus(String contractCode);

    BbsiContractChangeResultVo selectByContractCode(String contractCode);

    /**
     * 获取补充协议数量
     *
     * @param contractCode 合同编号
     * @return 补充协议数量
     */
    int selectAgreementCount(String contractCode);

    /**
     * 预览与下载
     *
     * @param ccId 合同变更id
     * @return 预览与下载结果
     */
    FIleResultVo previewAndDownload(String ccId);

    FIleResultVo previewAndDownloadV2(BbsiContractChangeVo vo);

    /**
     * 根据签约id查询
     *
     * @param signId 签约id
     * @return 合同变更信息 vo实体
     */
    BbsiContractChangeVo selectBySignId(String signId);

    /**
     * 根据签约id获取预览账单信息
     *
     * @param signId 签约id
     * @return 预览账单信息 vo实体
     */
    BbsChangePreviewBillResultVo getPreviewBillBySignId(String signId);
    public BbsChangePreviewBillResultVo getPreviewBillByCcId(String ccId);
    /**
     * 退款时使用的合同变更信息
     *
     * @param signId           签约id
     * @param refundChangeType 退款变更类型
     * @return 退款时使用的合同变更信息 vo实体
     */
    BbctRefundUseChangeInfoVo refundUseChangeInfo(String signId, String refundChangeType);


    void closeOrOpenBillAndBillBranks(String closeOrOpen, String contractCode, String projectId);
}
