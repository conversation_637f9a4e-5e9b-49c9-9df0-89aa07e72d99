package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.dao.BbsSignInfoMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsDictEntity;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbHousingFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsDictPageResultVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsDictPageVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsDictVo;
import com.bonc.ioc.bzf.busisigning.dao.BbsDictMapper;
import com.bonc.ioc.bzf.busisigning.service.IBbsDictService;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-05-05
 * @change 2023-05-05 by pyj for init
 */
@Slf4j
@Service
public class BbsDictServiceImpl extends McpBaseServiceImpl<BbsDictEntity> implements IBbsDictService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsDictMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsDictService baseService;

    @Resource
    private BbHousingFeignClient bbHousingFeignClient;

    @Resource
    private BbsSignInfoMapper signInfoMapper;


    @Value("${tz.dbName.commercial:bzf_system_commercial}")
    private String commercial;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbsDictVo vo) {
        if (vo == null) {
            return null;
        }
        BbsDictEntity entity = new BbsDictEntity();
        BeanUtils.copyProperties(vo, entity);
        entity.setDictId(null);
        // 新增
        if (!baseService.insert(entity)) {
            log.error("字典表新增失败:" + entity.toString());
            throw new McpException("字典表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getDictId(), 1)) {
                log.error("字典表新增后保存历史失败:" + entity.toString());
                throw new McpException("字典表新增后保存历史失败");
            }
            log.debug("字典表新增成功:" + entity.getDictId());
            return entity.getDictId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsDictVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }
        List<BbsDictEntity> entityList = new ArrayList<>();
        for (BbsDictVo item : voList) {
            BbsDictEntity entity = new BbsDictEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }
        for (BbsDictEntity item : entityList) {
            item.setDictId(null);
        }
        // 批量新增
        if (!baseService.insertBatch(entityList)) {
            log.error("字典表新增失败");
            throw new McpException("字典表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbsDictEntity::getDictId).collect(Collectors.toList());
            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("字典表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("字典表批量新增后保存历史失败");
            }
            log.debug("字典表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param dictId 需要删除的字典表主键
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String dictId) {
        if (!StringUtils.isEmpty(dictId)) {
            if (!baseService.saveOperationHisById(dictId, 3)) {
                log.error("字典表删除后保存历史失败:" + dictId);
                throw new McpException("字典表删除后保存历史失败");
            }
            // 删除
            if (!baseService.removeById(dictId)) {
                log.error("字典表删除失败");
                throw new McpException("字典表删除失败" + dictId);
            }
        } else {
            throw new McpException("字典表删除失败字典表主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param dictIdList 需要删除的字典表主键
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> dictIdList) {
        if (!CollectionUtils.isEmpty(dictIdList)) {
            int oldSize = dictIdList.size();
            dictIdList = dictIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dictIdList) || oldSize != dictIdList.size()) {
                throw new McpException("字典表批量删除失败 存在主键id为空的记录" + StringUtils.join(dictIdList));
            }
            if (!baseService.saveOperationHisByIds(dictIdList, 3)) {
                log.error("字典表批量删除后保存历史失败:" + StringUtils.join(dictIdList));
                throw new McpException("字典表批量删除后保存历史失败");
            }
            // 批量删除
            if (!baseService.removeByIds(dictIdList)) {
                log.error("字典表批量删除失败");
                throw new McpException("字典表批量删除失败" + StringUtils.join(dictIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的字典表
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsDictVo vo) {
        if (vo != null) {
            BbsDictEntity entity = new BbsDictEntity();
            BeanUtils.copyProperties(vo, entity);
            if (StringUtils.isEmpty(entity.getDictId())) {
                throw new McpException("字典表更新失败传入字典表主键为空");
            }
            // 更新
            if (!baseService.updateById(entity)) {
                log.error("字典表更新失败");
                throw new McpException("字典表更新失败" + entity.getDictId());
            } else {
                if (!baseService.saveOperationHisById(entity.getDictId(), 2)) {
                    log.error("字典表更新后保存历史失败:" + entity.getDictId());
                    throw new McpException("字典表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("字典表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的字典表
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsDictVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsDictEntity> entityList = new ArrayList<>();
            for (BbsDictVo item : voList) {
                BbsDictEntity entity = new BbsDictEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }
            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getDictId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("字典表批量更新失败 存在字典表主键为空的记录");
            }
            // 批量更新
            if (!baseService.updateBatchById(entityList)) {
                log.error("字典表批量更新失败");
                throw new McpException("字典表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getDictId())).map(BbsDictEntity::getDictId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("字典表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("字典表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的字典表
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsDictVo vo) {
        if (vo != null) {
            BbsDictEntity entity = new BbsDictEntity();
            BeanUtils.copyProperties(vo, entity);
            // 更新或新增
            if (!baseService.saveById(entity)) {
                log.error("字典表保存失败");
                throw new McpException("字典表保存失败" + entity.getDictId());
            } else {
                if (!baseService.saveOperationHisById(entity.getDictId(), 4)) {
                    log.error("字典表保存后保存历史失败:" + entity.getDictId());
                    throw new McpException("字典表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("字典表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的字典表
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsDictVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsDictEntity> entityList = new ArrayList<>();
            for (BbsDictVo item : voList) {
                BbsDictEntity entity = new BbsDictEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }
            // 批量更新或新增
            if (!baseService.saveBatchById(entityList)) {
                log.error("字典表批量保存失败");
                throw new McpException("字典表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getDictId())).map(BbsDictEntity::getDictId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("字典表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("字典表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param dictId 需要查询的字典表主键
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsDictVo selectByIdRecord(String dictId) {
        BbsDictVo vo = new BbsDictVo();

        if (!StringUtils.isEmpty(dictId)) {
            BbsDictEntity entity = baseService.selectById(dictId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsDictPageResultVo>> selectByPageRecord(BbsDictPageVo vo) {
        List<BbsDictPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    @Override
    public List<BbsDictVo> selectBusinessFormat() {
        return this.signInfoMapper.selectBusinessFormat(commercial);
    }
}
