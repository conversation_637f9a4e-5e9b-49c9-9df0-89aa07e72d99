package com.bonc.ioc.bzf.busisigning.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 模板属性表 实体类
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
@TableName("bbs_renewal_template_seat")
@ApiModel(value="BbsRenewalTemplateSeatEntity对象", description="模板属性表")
public class BbsRenewalTemplateSeatEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_SEAT_ID = "seat_id";
    public static final String FIELD_PARENT_ID = "parent_id";
    public static final String FIELD_PARENT_TYPE = "parent_type";
    public static final String FIELD_REVERSE_DISPLAY = "reverse_display";
    public static final String FIELD_SEAT_TITLE = "seat_title";
    public static final String FIELD_SEAT_KEY = "seat_key";
    public static final String FIELD_DEFAULT_VAL = "default_val";
    public static final String FIELD_VALUE = "value";
    public static final String FIELD_IS_SHOW = "is_show";
    public static final String FIELD_EDITABLE = "editable";
    public static final String FIELD_IS_REQUIRED = "is_required";
    public static final String FIELD_MODULE_TYPE = "module_type";
    public static final String FIELD_SORT = "sort";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
                                @TableId(value = "seat_id", type = IdType.ASSIGN_UUID)
                                  private String seatId;

    /**
     * 上级id（签约id）
     */
    @ApiModelProperty(value = "上级id（签约id）")
                            private String parentId;

    /**
     * 上级类型(2.签约)
     */
    @ApiModelProperty(value = "上级类型(2.签约)")
                            private String parentType;

    /**
     * 是否反显默认值(1反显，0不反显)
     */
    @ApiModelProperty(value = "是否反显默认值(1反显，0不反显)")
                            private Integer reverseDisplay;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
                            private String seatTitle;

    /**
     * 字段名
     */
    @ApiModelProperty(value = "字段名")
                            private String seatKey;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
                            private String defaultVal;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
                            private String value;

    /**
     * 是否显示组件(1是，0否)
     */
    @ApiModelProperty(value = "是否显示组件(1是，0否)")
                            private String isShow;

    /**
     * 是否可编辑(1是，0否)
     */
    @ApiModelProperty(value = "是否可编辑(1是，0否)")
                            private String editable;

    /**
     * 是否必填(1是，0否)
     */
    @ApiModelProperty(value = "是否必填(1是，0否)")
                            private String isRequired;

    /**
     * 组件类型
     */
    @ApiModelProperty(value = "组件类型")
                            private String moduleType;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
                            private Integer sort;

    /**
     * 删除标识(1 未删除 0 已删除)
     */
    @ApiModelProperty(value = "删除标识(1 未删除 0 已删除)")
                            private Integer delFlag;

    /**
     * @return 主键
     */
    public String getSeatId() {
        return seatId;
    }

    public void setSeatId(String seatId) {
        this.seatId = seatId;
    }

    /**
     * @return 上级id（签约id）
     */
    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * @return 上级类型(2.签约)
     */
    public String getParentType() {
        return parentType;
    }

    public void setParentType(String parentType) {
        this.parentType = parentType;
    }

    /**
     * @return 是否反显默认值(1反显，0不反显)
     */
    public Integer getReverseDisplay() {
        return reverseDisplay;
    }

    public void setReverseDisplay(Integer reverseDisplay) {
        this.reverseDisplay = reverseDisplay;
    }

    /**
     * @return 标题
     */
    public String getSeatTitle() {
        return seatTitle;
    }

    public void setSeatTitle(String seatTitle) {
        this.seatTitle = seatTitle;
    }

    /**
     * @return 字段名
     */
    public String getSeatKey() {
        return seatKey;
    }

    public void setSeatKey(String seatKey) {
        this.seatKey = seatKey;
    }

    /**
     * @return 默认值
     */
    public String getDefaultVal() {
        return defaultVal;
    }

    public void setDefaultVal(String defaultVal) {
        this.defaultVal = defaultVal;
    }

    /**
     * @return 值
     */
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    /**
     * @return 是否显示组件(1是，0否)
     */
    public String getIsShow() {
        return isShow;
    }

    public void setIsShow(String isShow) {
        this.isShow = isShow;
    }

    /**
     * @return 是否可编辑(1是，0否)
     */
    public String getEditable() {
        return editable;
    }

    public void setEditable(String editable) {
        this.editable = editable;
    }

    /**
     * @return 是否必填(1是，0否)
     */
    public String getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(String isRequired) {
        this.isRequired = isRequired;
    }

    /**
     * @return 组件类型
     */
    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    /**
     * @return 排序
     */
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * @return 删除标识(1 未删除 0 已删除)
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbsRenewalTemplateSeatEntity{" +
            "seatId=" + seatId +
            ", parentId=" + parentId +
            ", parentType=" + parentType +
            ", reverseDisplay=" + reverseDisplay +
            ", seatTitle=" + seatTitle +
            ", seatKey=" + seatKey +
            ", defaultVal=" + defaultVal +
            ", value=" + value +
            ", isShow=" + isShow +
            ", editable=" + editable +
            ", isRequired=" + isRequired +
            ", moduleType=" + moduleType +
            ", sort=" + sort +
            ", delFlag=" + delFlag +
        "}";
    }
}