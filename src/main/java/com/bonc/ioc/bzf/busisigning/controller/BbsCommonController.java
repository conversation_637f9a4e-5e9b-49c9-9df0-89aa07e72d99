package com.bonc.ioc.bzf.busisigning.controller;

import com.bonc.ioc.bzf.busisigning.service.IBbsCommonService;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.util.AppReply;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 通用相关 前端控制器
 *
 * <AUTHOR>
 * @since 2024/8/30
 */
@Slf4j
@RestController
@RequestMapping("/common")
@Api(tags = "签约")
@Validated
public class BbsCommonController extends McpBaseController {

    /**
     * 通用相关 服务实例
     */
    @Resource
    private IBbsCommonService commonService;

    /**
     * 获取省份列表
     *
     * @return 省份列表
     */
    @GetMapping(value = "/selectProvince", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "pyj")
    @ApiOperation(value = "获取省份列表", notes = "获取省份列表")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> selectProvince() {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(commonService.selectProvince());
        return appReply;
    }

    /**
     * 获取市级列表
     *
     * @param proCode 省份编码
     * @return 市级列表
     */
    @GetMapping(value = "/selectCity", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "pyj")
    @ApiOperation(value = "获取市级列表", notes = "获取市级列表")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> selectCity(@ApiParam(value = "省份编码", required = true) @RequestParam() String proCode) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(commonService.selectCity(proCode));
        return appReply;
    }
}
