package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsTemplateSeatEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeEntity;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractTemplateSeatVo;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;


/**
 * 合并变更表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by fzq for init
 */
public interface IBbsiContractChangeV2Service extends IMcpBaseService<BbsiContractChangeEntity> {

    /**
     * 查询变更信息
     * @param ccId
     * @param contractCode
     * @return
     */
    BbsiContractChangeVo selectChangeInfos(String ccId, String contractCode, String changeTypeItem, String isDetails, String isApprove);


    /**
     * 选择变更后的商铺地址
     * @param ccId
     * @param contractCode
     * @return
     */
    List<BbsChangeShopInfoVo> selectChangedShopAddress(String ccId, String contractCode);

    /**
     * 合同变更，根据合同id查询客户信息
     *
     * @param contractCode 合同编号
     * @return BbsContractChangeCustomerVo 客户信息
     */
    BbsiContractChangeCustomerVo findTransactors(String contractCode);

    /**
     * 验证是否有同类型合同变更
     * @param contractCode
     * @param changeTypeItem
     * @return
     */
    String verifySameType(String contractCode,String changeTypeItem,String ccId);

    /**
     * 验证是否欠费
     * @param contractCode
     * @return
     */
    String verifyArrears(String contractCode);

    List<BbctContractTemplateSeatVo> selectContractTemplateSeatByTemplateId(String contractTemplateId);


    List<BillHouseNameGroupVo> payableDateInformations(String ccId);


    ChangeCalculationBillVo getPreviewBillByVo(BbsiContractChangeVo vo);

    List<BbsChangeDetermineDeductionPeriodVo> deductionBillDetermination(ChangeCalculationBillVo vo) ;

    /**
     * 根据抵扣账单动态更新费用汇总列表
     *
     * @param originalFeesSummaryList 原始费用汇总列表
     * @param rentAndBondDeductionBillList 押金和租金抵扣账单列表
     * @param propertyDeductionBillList 物业费抵扣账单列表
     * @param refundPaymentMethod 退/缴方式
     * @return 更新后的费用汇总列表
     */
    List<FeesSummaryVo> updateFeesSummaryWithDeduction(List<FeesSummaryVo> originalFeesSummaryList,
                                                       List<BbsChangeDetermineDeductionPeriodVo> rentAndBondDeductionBillList,
                                                       List<BbsChangeDetermineDeductionPeriodVo> propertyDeductionBillList,
                                                       String refundPaymentMethod);

    List<BbsTemplateSeatEntity> seatInfoVoList(String contractCode,String isShow);

    String verifyChangeCalculated(String contractCode,String changeTypeItem);

    List<BbsChangeCalculatedProductVo> selectByCcId(String ccId);

    AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> selectByPageRecordBill(com.bonc.ioc.bzf.busisigning.feign.vo.BbpmBillManagementPageVo vo);

    /**
     * 计算选择账单的时间范围
     *
     * @param selectedBills 选择的账单列表
     * @return 账单时间范围字符串，格式如：2024-07-25至2026-08-24
     */
    String calculateSelectedBillsDateRange(List<BbpmBillManagementPageResultVo> selectedBills);

    }
