package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.dao.*;
import com.bonc.ioc.bzf.busisigning.entity.*;
import com.bonc.ioc.bzf.busisigning.enums.*;
import com.bonc.ioc.bzf.busisigning.factory.change.AbstractContractChangeFactory;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbCustomerFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctPaymentV2FeignClient;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.service.*;
import com.bonc.ioc.bzf.busisigning.utils.DictTreeToBusinessFormatUtil;
import com.bonc.ioc.bzf.busisigning.utils.JsonToObjectUtil;
import com.bonc.ioc.bzf.busisigning.utils.PaymentCycleBillConverter;
import com.bonc.ioc.bzf.busisigning.utils.ResultUtils;
import com.bonc.ioc.bzf.busisigning.utils.UserUtil;
import com.bonc.ioc.bzf.busisigning.enums.PaymentCycleChangeScopeEnum;
import com.bonc.ioc.bzf.busisigning.enums.ContractChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.vo.JsonVo.IncrementalInfo;
import com.bonc.ioc.bzf.busisigning.vo.JsonVo.IncrementalInfoArray;
import com.bonc.ioc.bzf.busisigning.vo.JsonVo.JsonRootBean;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.ContractOtherInfo;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.StandardVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.CloseOrOpenBillAndBillBranksParamVo;
import com.bonc.ioc.bzf.busisigning.workflow.component.WorkflowCommponent;
import com.bonc.ioc.bzf.busisigning.workflow.enums.BusinessTypeCodeEnum;
import com.bonc.ioc.bzf.busisigning.workflow.service.IFwWorkflowService;
import com.bonc.ioc.bzf.busisigning.workflow.vo.BaseFlowVo;
import com.bonc.ioc.bzf.busisigning.workflow.vo.BbfwRequestBusinessMappingVo;
import com.bonc.ioc.bzf.busisigning.workflow.vo.OaCreateWorkFlowRequestV2Vo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 合同变更表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by fzq for init
 */
@Slf4j
@Service
public class BbsiContractChangeServiceImpl extends McpBaseServiceImpl<BbsiContractChangeEntity> implements IBbsiContractChangeService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsiContractChangeMapper baseMapper;

    @Resource
    private BbsChangePayableDateMapper bbsChangePayableDateMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsiContractChangeService baseService;

    @Resource
    private IBbsContractApproveInfoService contractApproveInfoService;

    /**
     * 获取登录用户
     */
    @Resource
    private UserUtil userUtil;

    @Resource
    private IContractChangeResultService contractChangeResultService;

    /**
     * 签约扩展相关 服务实例
     */
    @Resource
    private IBbsSignInfoExtService signInfoExtService;

    /**
     * 签约相关 服务实例
     */
    @Resource
    private IBbsSignInfoService signInfoService;

    /**
     * 合同变更审批相关 服务实例
     */
    @Resource
    private IBbsContractChangeApproveInfoService contractChangeApproveInfoService;

    /**
     * 合同变更产品信息相关 服务实例
     */
    @Resource
    private IBbsChangeResultProductService changeResultProductService;

    /**
     * 合同变更递增规则信息相关 服务实例
     */
    @Resource
    private IBbsChangeIncrementalConfigService changeIncrementalConfigService;

    @Resource
    private BbsContractChangeApproveInfoMapper bbsContractChangeApproveInfoMapper;

    @Resource
    private BbsContractChangeApproveDetailMapper bbsContractChangeApproveDetailMapper;

    /**
     * 合同中心 feign实例
     */
    @Resource
    private BbctContractFeignClient contractFeignClient;

    /**
     * 客户中心 feign实例
     */
    @Resource
    private BbCustomerFeignClient customerFeignClient;

    @Resource
    private IBbsChangeReturnBankCardService iBbsChangeReturnBankCardService;

    @Resource
    private IBbsChangeResultProductService iBbsChangeResultProductService;

    @Resource
    private IBbsChangeIncrementalConfigService iBbsChangeIncrementalConfigService;

    @Resource
    private BbsChangeReturnBankCardMapper bbsChangeReturnBankCardMapper;

    @Resource
    private BbsChangeResultProductMapper bbsChangeResultProductMapper;

    @Resource
    private BbsChangeIncrementalConfigMapper bbsChangeIncrementalConfigMapper;

    @Resource
    private IBbsChangePayableDateService iBbsChangePayableDateService;

    @Resource
    private IBbsChangeShopInfoService iBbsChangeShopInfoService;

    @Resource
    private BbsChangeShopInfoMapper bbsChangeShopInfoMapper;

    @Resource
    private BbsChangeSubjectMatterMapper bbsChangeSubjectMatterMapper;

    @Resource
    private IBbsChangeSubjectMatterService iBbsChangeSubjectMatterService;

    @Resource
    private IBbsChangeDetermineDeductionPeriodService iBbsChangeDetermineDeductionPeriodService;

    @Resource
    private BbsChangeDetermineDeductionPeriodMapper bbsChangeDetermineDeductionPeriodMapper;

    @Resource
    private BbctPaymentV2FeignClient paymentV2FeignClient;

    @Resource
    private IBbsChangeCalculatedProductService iBbsChangeCalculatedProductService;

    @Resource
    private BbsChangeCalculatedProductMapper bbsChangeCalculatedProductMapper;

    @Resource
    private IFwWorkflowService workflowService;
    @Resource
    private WorkflowCommponent workflowCommponent;
    @Resource
    private IFwWorkflowService iFwWorkflowService;

    /**
     * insertRecord 新增
     *
     * @param vo         需要保存的记录
     * @param onlyInsert 是否只新增
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by fzq for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbsiContractChangeVo vo, boolean onlyInsert) {
        if (vo == null) {
            return null;
        }
        // 1的话是驳回后提交变成待审核
        if("1".equals(vo.getIsnextflow()+"")){
            vo.setChangeStatus(ChangeStatusEnum.WAIT_AUDIT.getCode());
        }else {
            vo.setChangeStatus(ChangeStatusEnum.TEMPORARY.getCode());
        }
        BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
        BeanUtils.copyProperties(vo, entity);
        entity.setDelFlag(DelFlagEnum.UNDELETED.getCode());
        String userId = userUtil.getUserId();
        entity.setChangeUserId(userId);
        String userName = userUtil.getUserName(userId);
        entity.setChangeUserName(userName);
        BbsiContractChangeEntity entityExsit = baseService.selectById(entity.getCcId());
        if (entityExsit != null) {//编辑
            boolean flag = baseService.updateById(entity);
            if (!flag) {
                log.error("合同变更表更新失败:" + entity.toString());
                throw new McpException("合同变更表更新失败");
            }

        } else {//新建
            boolean flag = baseService.insert(entity);
            if (!flag) {
                log.error("合同变更表新增失败:" + entity.toString());
                throw new McpException("合同变更表新增失败");
            }
        }
        String ccId  = entity.getCcId();
        OaCreateWorkFlowRequestV2Vo flowRequestV2Vo = new OaCreateWorkFlowRequestV2Vo();
        flowRequestV2Vo.setBusinessId(ccId);
        flowRequestV2Vo.setWorkflowId(this.workflowService.selectWorkFlowId(BusinessTypeCodeEnum.CODE_003008.getCode()));
        flowRequestV2Vo.setBusinessTypeCode(BusinessTypeCodeEnum.CODE_003008.getCode());
        flowRequestV2Vo.setIsnextflow(vo.getIsnextflow());//暂存
        flowRequestV2Vo.setRequestname(workflowCommponent.getWorkName(entity.getContractCode(),"5"));
        try {
            Long requestId = this.workflowService.doCreateRequest(flowRequestV2Vo);
            log.info("创建流程成功requestId"+requestId);
//            BbfwRequestBusinessMappingVo bbfwRequestBusinessMappingVo = new BbfwRequestBusinessMappingVo();
//            bbfwRequestBusinessMappingVo.setRequestId(requestId.toString());
//            bbfwRequestBusinessMappingVo.setWorkflowId(vo.getWorkflowId());
//            bbfwRequestBusinessMappingVo.setBusinessTypeCode(vo.getBusinessTypeCode());
//            bbfwRequestBusinessMappingVo.setBusinessId(ccId);
//            return AppReply.success(bbfwRequestBusinessMappingVo);
        }catch (Exception e){
            log.error("创建流程失败"+e.getMessage());
            this.deleteByIdRecord(ccId);
            throw new McpException("创建流程失败"+e.getMessage());
//            return AppReply.error(e.getMessage());
        }
//        if (!onlyInsert && "1".equals(vo.getChangeStatus())) {
//            contractChangeResultService.contractChange(vo);
//            Map<String, Object> removeParamMap = new HashMap<>();
//            removeParamMap.put("cc_id", entity.getCcId());
//            bbsContractChangeApproveInfoMapper.deleteByMap(removeParamMap);
//            BbsContractChangeApproveInfoEntity bbsContractChangeApproveInfoEntity = new BbsContractChangeApproveInfoEntity();
//            bbsContractChangeApproveInfoEntity.setApproverUserId(userId);
//            bbsContractChangeApproveInfoEntity.setApproverUserName(userName);
//            bbsContractChangeApproveInfoEntity.setCcId(entity.getCcId());
//            bbsContractChangeApproveInfoEntity.setApproveStatus(ApproveStatusEnum.APPROVED.getCode());
//            bbsContractChangeApproveInfoEntity.setApproveTime(new Date());
//            bbsContractChangeApproveInfoEntity.setContractCode(entity.getContractCode());
//            bbsContractChangeApproveInfoMapper.insert(bbsContractChangeApproveInfoEntity);
//            BbsContractChangeApproveDetailEntity bbsContractChangeApproveDetailEntity = new BbsContractChangeApproveDetailEntity();
//            bbsContractChangeApproveDetailEntity.setApproverUserId(userId);
//            bbsContractChangeApproveDetailEntity.setApproverUserName(userName);
//            bbsContractChangeApproveDetailEntity.setApproveStatus(ApproveStatusEnum.APPROVED.getCode());
//            bbsContractChangeApproveDetailEntity.setApproveTime(new Date());
//            bbsContractChangeApproveDetailEntity.setCcId(entity.getCcId());
//            bbsContractChangeApproveDetailEntity.setDescription("业务人员提交变更申请");
//            bbsContractChangeApproveDetailMapper.insert(bbsContractChangeApproveDetailEntity);
//        }
        log.debug("合同变更表新增成功:" + entity.getCcId());
        return entity.getCcId();
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecordV2(BbsiContractChangeVo vo, boolean onlyInsert) {
        if (vo == null) {
            return null;
        }
      
        //一些字段特殊处理赋值
        specialHandle(vo);

        BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
        BeanUtils.copyProperties(vo, entity);
        entity.setDelFlag(DelFlagEnum.UNDELETED.getCode());
        String userId = userUtil.getUserId();
        entity.setChangeUserId(userId);
        String userName = userUtil.getUserName(userId);
        entity.setChangeUserName(userName);
        BbsiContractChangeEntity entityExsit = baseService.selectById(entity.getCcId());
        if (entityExsit != null) {//编辑
            boolean flag = baseService.updateById(entity);
            if (!flag) {
                log.error("合同变更表更新失败:" + entity.toString());
                throw new McpException("合同变更表更新失败");
            }
        } else {//新建
            boolean flag = baseService.insert(entity);
            if (!flag) {
                log.error("合同变更表新增失败:" + entity.toString());
                throw new McpException("合同变更表新增失败");
            }
        }

        //其他变更
        if(ChangeTypeEnum.OTHER_CHANGE.getCode().equals(vo.getChangeType())){
            handleOtherChange(entity, vo);
        }

        if("1".equals(vo.getIsnextflow()+"")){
//        if (!onlyInsert && "1".equals(vo.getChangeStatus())) {
//            contractChangeResultService.contractChange(vo);

            // Map<String, Object> removeParamMap = new HashMap<>();
            // removeParamMap.put("cc_id", entity.getCcId());
            // bbsContractChangeApproveInfoMapper.deleteByMap(removeParamMap);
            // BbsContractChangeApproveInfoEntity bbsContractChangeApproveInfoEntity = new BbsContractChangeApproveInfoEntity();
            // bbsContractChangeApproveInfoEntity.setApproverUserId(userId);
            // bbsContractChangeApproveInfoEntity.setApproverUserName(userName);
            // bbsContractChangeApproveInfoEntity.setCcId(entity.getCcId());
            // bbsContractChangeApproveInfoEntity.setApproveStatus(ApproveStatusEnum.WAIT_APPROVE.getCode());
            // bbsContractChangeApproveInfoEntity.setApproveTime(new Date());
            // bbsContractChangeApproveInfoEntity.setContractCode(entity.getContractCode());
            // bbsContractChangeApproveInfoMapper.insert(bbsContractChangeApproveInfoEntity);
            // BbsContractChangeApproveDetailEntity bbsContractChangeApproveDetailEntity = new BbsContractChangeApproveDetailEntity();
            // bbsContractChangeApproveDetailEntity.setApproverUserId(userId);
            // bbsContractChangeApproveDetailEntity.setApproverUserName(userName);
            // bbsContractChangeApproveDetailEntity.setApproveStatus(ApproveStatusEnum.WAIT_APPROVE.getCode());
            // bbsContractChangeApproveDetailEntity.setApproveTime(new Date());
            // bbsContractChangeApproveDetailEntity.setCcId(entity.getCcId());
            // bbsContractChangeApproveDetailEntity.setDescription("业务人员提交变更申请");
            // bbsContractChangeApproveDetailMapper.insert(bbsContractChangeApproveDetailEntity);

            //租金变更、缩租面积变更  提交的时候关闭账单
            if(vo.getChangeTypeItem().contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())
                    || vo.getChangeTypeItem().contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())
                    || vo.getChangeTypeItem().contains(ContractChangeTypeEnum.BOND_CHANGE.getCode()) 
                    || vo.getChangeTypeItem().contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode())
                    || vo.getChangeTypeItem().contains(ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode())
                    ){
                baseService.closeOrOpenBillAndBillBranks("0", vo.getContractCode(), vo.getProjectId());
            }else if(vo.getChangeTypeItem().contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())){
                if( vo.getChangeTypeItem().equals(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())
                        && isOnePriceAll(vo.getContractCode()) ){
                    log.info("单独计租面积变更,合同租金和租金都为一口价,不关闭账单");
                }else{
                    baseService.closeOrOpenBillAndBillBranks("0", vo.getContractCode(), vo.getProjectId());
                }
            }
        }

        //提交前再更新变更主表状态安全些
        BbsiContractChangeEntity entity2 = new BbsiContractChangeEntity();
        entity2.setCcId(entity.getCcId());
        if("1".equals(vo.getIsnextflow()+"")){
            entity2.setChangeStatus(ChangeStatusEnum.WAIT_AUDIT.getCode());
        }else {
            entity2.setChangeStatus(ChangeStatusEnum.TEMPORARY.getCode());
        }
        baseService.updateById(entity2);

        //发起流程
        String ccId  = entity.getCcId();
        OaCreateWorkFlowRequestV2Vo flowRequestV2Vo = new OaCreateWorkFlowRequestV2Vo();
        flowRequestV2Vo.setBusinessId(ccId);
        flowRequestV2Vo.setWorkflowId(this.workflowService.selectWorkFlowId(BusinessTypeCodeEnum.CODE_003009.getCode()));
        flowRequestV2Vo.setBusinessTypeCode(BusinessTypeCodeEnum.CODE_003009.getCode());
        flowRequestV2Vo.setIsnextflow(vo.getIsnextflow());//暂存
        flowRequestV2Vo.setRequestname(workflowCommponent.getWorkName(entity.getContractCode(),"1"));
        try {
            Long requestId = this.workflowService.doCreateRequest(flowRequestV2Vo);
            log.info("合同变更表创建流程成功requestId"+requestId);
        }catch (Exception e){
            log.error("合同变更表创建流程失败"+e.getMessage());
            this.deleteByIdRecord(ccId);
            this.deleteAllByCcId(ccId);
            throw new McpException("合同变更表创建流程失败"+e.getMessage());
//            return AppReply.error(e.getMessage());
        }

        log.debug("合同变更表新增成功:" + entity.getCcId());
        return entity.getCcId();
    }

    public boolean isOnePriceAll(String contractCode){
        //产品说的： 单独计租面积变更，租金和物业费是一口价时，不关闭账单
        BbctContractManagementVo bbctContractManagementVo = selectContractByIdNo(contractCode);
        //合同中产品信息
        List<BbctContractSubjectMatterVo> subjectMatterList = bbctContractManagementVo.getSubjectMatterList();
        if (subjectMatterList == null || subjectMatterList.size() == 0) {
            throw new McpException(contractCode + "合同信息中subjectMatterList为空");
        }
        String isProperty = "1-1";
        String isOnePrice = "1";
        // 判断合同是否存在物业费 并存储（从老合同扩展字段取）
        ContractOtherInfo contractOtherInfo = JsonToObjectUtil.jsonToContractOtherInfo(bbctContractManagementVo.getContractExtend());
        if (contractOtherInfo != null) {
            //有物业费
            if("02-08".equals(contractOtherInfo.getContractFees())){
                for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
                    //从产品扩展字段解析物业费标准
                    String productExtend = subjectMatterVo.getProductExtend();
                    if(StringUtils.isNotBlank(productExtend)){
                        StandardVo propStandard = JsonToObjectUtil.jsonToProductExtend(productExtend);
                        if(propStandard != null){
                            String standardUnit = propStandard.getStandardUnit();
                            if("1".equals(standardUnit) || "4".equals(standardUnit)){
                                //不是一口价
                                isProperty="1-0";
                                break;
                            }
                        }
                    }
                }
            }
        }
        // 是否为 租金为一口价（单位是元/月或元/年）
        for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
            String rentUnit = subjectMatterVo.getRentUnit();
            if (StringUtils.isNotBlank(rentUnit)) {
                if (!(rentUnit.contains("元/月") || rentUnit.contains("元/年"))) {
                    isOnePrice= "0";
                    break;
                }
            }
        }

        if("1-1".equals(isProperty) && "1".equals(isOnePrice)){
            return true;
        }

        return false;
    }

    /**
     * 开启或关闭账单
     * @param closeOrOpen
     * @param contractCode
     * @param projectId
     */
    @Override
    public void closeOrOpenBillAndBillBranks(String closeOrOpen, String contractCode, String projectId) {
        //closeOrOpen 0，关闭 1，开启
        try{
            if(StringUtils.isBlank(projectId)){
                throw new RuntimeException("关闭或开启账单需要的projectId为空");
            }

            CloseOrOpenBillAndBillBranksParamVo request = new CloseOrOpenBillAndBillBranksParamVo();
            request.setCloseOrOpen(closeOrOpen);
            request.setContractCode(contractCode);
            request.setProjectId(projectId);

            log.info("开启或者关闭账单请求参数:" + JSONObject.toJSONString(request));
            AppReply<Object> appReply = paymentV2FeignClient.closeOrOpenBillAndBillBranks(request);
            log.info("开启或者关闭账单返回:" + JSONObject.toJSONString(appReply));
            if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
                String result = Objects.isNull(appReply) ? null : appReply.toString();
                log.error("开启失败:" + result);
                throw new McpException("*开启或关闭账单失败,"+ result+","+ JSONObject.toJSONString(request));
            }
        }   catch (Exception e) {
            throw new RuntimeException("*开启或关闭账单失败,失败原因为：" + e.getMessage());
        }
    }

    private void specialHandle(BbsiContractChangeVo vo){
//        // 提前计算并缓存 changeTypeItem 是否包含 LEASE_AREA_REDUCTION_CHANGE
//        boolean isLeaseAreaReductionChange = vo.getChangeTypeItem() != null &&
//                vo.getChangeTypeItem().contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode());
        
        //应收或应退金额处理 这个字段按逗号分割（  租金金额1000,押金金额3000 ）
        if(StringUtils.isNotBlank(vo.getRentAmountReceivable())){
            String[] split1 = vo.getRentAmountReceivable().split(",");
            for (String s : split1) {
                if(s.contains("押金金额")){
                    vo.setRentAmountReceivable(s.split("押金金额")[1]);
                    vo.setReduceAmountReceivable(s.split("押金金额")[1]);
                    vo.setCashAmountReceivable(s.split("押金金额")[1]);
                }else if(s.contains("租金金额")){
                    vo.setRentAmountReceivableLease(s.split("租金金额")[1]);
                    vo.setReduceAmountReceivableLease(s.split("租金金额")[1]);
                }else if(s.contains("物业费")){
                    vo.setPropertyAmountReceivable(s.split("物业费")[1]);
                }
            }
        }
        if(StringUtils.isNotBlank(vo.getPropertyAmountReceivable()) && vo.getPropertyAmountReceivable().contains("物业费")){
            String[] split1 = vo.getPropertyAmountReceivable().split(",");
            for (String s : split1) {
                if(s.contains("物业费")){
                    vo.setPropertyAmountReceivable(s.split("物业费")[1]);
                }
            }
        }
//        //缩租这个金额也处理一下
//        if(StringUtils.isNotBlank(vo.getReduceAmountReceivable())
//                || (StringUtils.isNotBlank(vo.getRentAmountReceivable())&&vo.getChangeTypeItem().contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode()))){
//            String[] split1 = StringUtils.isNotBlank(vo.getReduceAmountReceivable()) ?
//                    vo.getReduceAmountReceivable().split(",") :
//                    vo.getRentAmountReceivable().split(",");
//            for (String s : split1) {
//                if(s.contains("押金金额")){
//                    vo.setReduceAmountReceivable(s.split("押金金额")[1]);
//                }else if(s.contains("租金金额")){
//                    vo.setReduceAmountReceivableLease(s.split("租金金额")[1]);
//                }
//            }
//        }
        // 生效执行日期
        if (StringUtils.isNotBlank(vo.getRentEffectiveExecutionDate())) {
            vo.setReduceEffectiveExecutionDate(vo.getRentEffectiveExecutionDate());
            vo.setCashEffectiveExecutionDate(vo.getRentEffectiveExecutionDate());
        }
        // 退/缴方式
        if (StringUtils.isNotBlank(vo.getRentRefundPaymentMethod())) {
            vo.setReduceRefundPaymentMethod(vo.getRentRefundPaymentMethod());
            vo.setCashRefundPaymentMethod(vo.getRentRefundPaymentMethod());
        }

        //变更银行卡处理
        //租金--变更退回银行卡
        BbsChangeReturnBankCardVo rentReturnBankCardVo = vo.getRentReturnBankCardVo();
        if (rentReturnBankCardVo != null) {
            //缩租--变更退回银行卡
            vo.setReduceReturnBankCardVo(rentReturnBankCardVo);
            //押金--变更退回银行卡
            vo.setCashReturnBankCardVo(rentReturnBankCardVo);
        }

        //业态 转json 和 解析名字 存储
        if(vo.getDictTreeVoList()!=null && vo.getDictTreeVoList().size()>0){
            List<DictTreeVo> dictTreeVoList = vo.getDictTreeVoList();
            List<BusinessFormatVo> bfvList = DictTreeToBusinessFormatUtil.convertDictTreeToBusinessFormat(dictTreeVoList);
            if(bfvList!=null && bfvList.size()>0){
                vo.setBusinessFormat(JSON.toJSONString(bfvList));
            }
            vo.setBusinessFormatName(DictTreeToBusinessFormatUtil.getFormatName(dictTreeVoList));
        }

        // 费用汇总转JSON存储
        if(vo.getChangeCalculationBillVo() != null && vo.getChangeCalculationBillVo().getFeesSummaryList() != null
           && !vo.getChangeCalculationBillVo().getFeesSummaryList().isEmpty()) {
            try {
                List<FeesSummaryVo> feesSummaryListToStore = determineFeesSummaryListToStore(vo);
                String feesSummaryJson = JSON.toJSONString(feesSummaryListToStore);
                vo.setFeesSummaryJson(feesSummaryJson);
                log.debug("费用汇总列表转换为JSON: {}", feesSummaryJson);
            } catch (Exception e) {
                log.error("费用汇总列表转换JSON失败", e);
                throw new McpException("费用汇总列表转换JSON失败: " + e.getMessage());
            }
        }
        
        // 缴费周期变更JSON转换处理
        handlePaymentCycleChange(vo);
        
        // 处理billCycle字段解析：将"2024-07-25至2026-08-24"格式解析为开始和结束日期
        parseBillCycleDateRange(vo);
    }

    /**
     * 解析billCycle字段中的时间范围字符串
     * 将"2024-07-25至2026-08-24"格式解析为开始日期和结束日期
     * 
     * @param vo 合同变更VO
     * @throws McpException 当billCycle格式不符合要求时抛出异常
     */
    private void parseBillCycleDateRange(BbsiContractChangeVo vo) {
        String billCycle = vo.getBillCycle();
        
        // 验证并处理undefined的情况
        if (StringUtils.isNotBlank(billCycle)) {
            // 检查是否包含undefined
            if (billCycle.contains("undefined")) {
                log.error("检测到bill_cycle包含undefined: {}", billCycle);
                throw new McpException(String.format("账单周期数据异常，包含undefined值: [%s]，请重新选择账单周期", billCycle));
            }
            
            // 正常的时间范围解析
            if (billCycle.contains("至")) {
                try {
                    String[] dates = billCycle.split("至");
                    if (dates.length == 2) {
                        String startDateStr = dates[0].trim();
                        String endDateStr = dates[1].trim();
                        
                        // 验证日期字符串不为undefined
                        if (!"undefined".equals(startDateStr) && !"undefined".equals(endDateStr) &&
                            StringUtils.isNotBlank(startDateStr) && StringUtils.isNotBlank(endDateStr)) {
                            
                            // 验证日期格式是否为yyyy-MM-dd
                            validateDateFormat(startDateStr, "开始日期", billCycle);
                            validateDateFormat(endDateStr, "结束日期", billCycle);
                            
                            vo.setBillCycleStart(startDateStr);
                            vo.setBillCycleEnd(endDateStr);
                            log.debug("解析billCycle开始时间: {}, 结束时间: {}", startDateStr, endDateStr);
                            
                            log.info("成功解析billCycle时间范围: {} -> 开始时间: {}, 结束时间: {}", 
                                    billCycle, vo.getBillCycleStart(), vo.getBillCycleEnd());
                        } else {
                            log.error("billCycle包含undefined或空值，无法解析: {}", billCycle);
                            throw new McpException(String.format("账单周期格式错误，包含无效的时间值: [%s]，请提供有效的开始和结束时间", billCycle));
                        }
                    } else {
                        log.error("billCycle格式不正确，分割结果数量异常: {}, 分割结果: {}", billCycle, Arrays.toString(dates));
                        throw new McpException(String.format("账单周期格式错误: [%s]，正确格式应为: 开始日期至结束日期，如: 2024-07-25至2026-08-24", billCycle));
                    }
                } catch (McpException e) {
                    // 重新抛出McpException
                    throw e;
                } catch (Exception e) {
                    log.error("解析billCycle时间范围失败: billCycle={}", billCycle, e);
                    throw new McpException(String.format("账单周期解析失败: [%s]，错误原因: %s", billCycle, e.getMessage()));
                }
            } else {
                log.error("billCycle格式错误，不包含'至'分隔符: {}", billCycle);
                throw new McpException(String.format("账单周期格式错误: [%s]，必须包含'至'分隔符，正确格式如: 2024-07-25至2026-08-24", billCycle));
            }
        } else if (StringUtils.isNotBlank(billCycle)) {
            log.debug("billCycle字段不包含'至'字符，跳过解析: {}", billCycle);
        }
    }

    /**
     * 验证日期格式是否为yyyy-MM-dd
     * 
     * @param dateStr 日期字符串
     * @param dateType 日期类型描述（开始日期/结束日期）
     * @param originalBillCycle 原始billCycle值，用于错误信息
     * @throws McpException 当日期格式不正确时抛出异常
     */
    private void validateDateFormat(String dateStr, String dateType, String originalBillCycle) {
        if (StringUtils.isBlank(dateStr)) {
            log.error("{}为空: {}", dateType, originalBillCycle);
            throw new McpException(String.format("账单周期格式错误，%s为空: [%s]", dateType, originalBillCycle));
        }
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setLenient(false); // 严格模式，不允许非法日期
        
        try {
            sdf.parse(dateStr);
            log.debug("{}格式验证通过: {}", dateType, dateStr);
        } catch (Exception e) {
            log.error("{}格式验证失败: {}, 原始billCycle: {}, 错误: {}", dateType, dateStr, originalBillCycle, e.getMessage());
            throw new McpException(String.format("账单周期格式错误，%s格式不正确: [%s]，正确格式应为: yyyy-MM-dd (如: 2025-08-01)，原始值: [%s]", 
                    dateType, dateStr, originalBillCycle));
        }
    }

    /**
     * 处理缴费周期变更的JSON转换逻辑
     * @param vo 合同变更VO
     */
    private void handlePaymentCycleChange(BbsiContractChangeVo vo) {
        String changeTypeItem = vo.getChangeTypeItem();
        // 缴费周期变更
        if(changeTypeItem.contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode())){
            // 如果是账期缴费周期变更且选择了账单
            if (PaymentCycleChangeScopeEnum.isBillPaymentCycleChange(vo.getChangeScope()) &&
                !CollectionUtils.isEmpty(vo.getSelectedBills())) {
                
                try {
                    // 转换为JSON存储
                    String jsonString = PaymentCycleBillConverter.convertToJson(vo.getSelectedBills());
                    vo.setSelectedBillsJson(jsonString);
                    log.debug("缴费周期变更账单列表转换为JSON: {}", jsonString);
                } catch (Exception e) {
                    log.error("缴费周期变更账单列表转换JSON失败", e);
                    throw new McpException("缴费周期变更账单列表转换JSON失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 其他变更 先把相关的一些子表删除，然后新增
     * @param entity
     * @param vo
     */
    private void handleOtherChange(BbsiContractChangeEntity entity, BbsiContractChangeVo vo) {
        deleteAllByCcId(entity.getCcId());
        insertOtherRecord(vo, entity.getCcId());
    }

    /**
     * 其他变更 新增时，需要插入的其他表，没有哪种变更时，删除某些表
     */
    private void insertOtherRecord(BbsiContractChangeVo vo,String ccId) {
        String changeTypeItem = vo.getChangeTypeItem();
        if(StringUtils.isBlank(changeTypeItem)){
            throw new McpException("changeTypeItem为空");
        }
        //乙方变更
        if(changeTypeItem.contains(ContractChangeTypeEnum.PARTY_B_CHANGE.getCode())){
            //个人变企业  企业变企业，新的类型就是企业
            vo.setCustomerType("01");
        }
        //保证金变更
        if(changeTypeItem.contains(ContractChangeTypeEnum.BOND_CHANGE.getCode())){
            if(RefundMethodEnum.RETURN_BANK_CARD.getCode().equals(vo.getCashRefundPaymentMethod())){
                vo.getCashReturnBankCardVo().setCcId(ccId);
                vo.getCashReturnBankCardVo().setSourceType("01");
                //插入退回银行卡信息
                iBbsChangeReturnBankCardService.insertRecord(vo.getCashReturnBankCardVo());
            }
        }
        //租金变更
        if(changeTypeItem.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())){
            if(RefundMethodEnum.RETURN_BANK_CARD.getCode().equals(vo.getRentRefundPaymentMethod())){
                vo.getRentReturnBankCardVo().setCcId(ccId);
                vo.getRentReturnBankCardVo().setSourceType("02");
                //插入退回银行卡信息
                iBbsChangeReturnBankCardService.insertRecord(vo.getRentReturnBankCardVo());
            }
            //插入租金标准
            vo.getProductVoList().forEach(productVo -> productVo.setCcId(ccId));
            iBbsChangeResultProductService.insertBatchRecord(vo.getProductVoList());
            //插入租金递增  参考"BbsSignInfoExtServiceImpl中saveSignData保存租金递增数据方法"
            List<BbsChangeIncrementalConfigVo> bbsChangeIncrementalConfigVoList = new ArrayList<>();
            JsonRootBean rentStandardJsonOld = vo.getRentStandardJsonOld();
            JsonRootBean rentStandardJson = vo.getRentStandardJson();
            if(rentStandardJsonOld!=null){
                IncrementalInfo incrementalInfoOld = rentStandardJsonOld.getIncrementalInfo();
                List<IncrementalInfoArray> incrementalInfoArrayOld = incrementalInfoOld.getIncrementalInfoArray();
                for (IncrementalInfoArray Z : incrementalInfoArrayOld) {
                    BbsChangeIncrementalConfigVo bbsSignIncrementalConfigVo = new BbsChangeIncrementalConfigVo();
                    BeanUtil.copyProperties(Z, bbsSignIncrementalConfigVo);
                    bbsSignIncrementalConfigVo.setStandardType("rent");
                    Double increase = Double.valueOf(Z.getIncrease());
                    bbsSignIncrementalConfigVo.setIncrease(increase);
//                bbsSignIncrementalConfigVo.setSignInfoId(bbsSignInfoVo.getSignId());
                    bbsSignIncrementalConfigVo.setDelFlag(1);
                    bbsSignIncrementalConfigVo.setCcId(ccId);
                    bbsSignIncrementalConfigVo.setType("01");
                    bbsChangeIncrementalConfigVoList.add(bbsSignIncrementalConfigVo);
                }
            }
            if(rentStandardJson!=null){
                IncrementalInfo incrementalInfo = rentStandardJson.getIncrementalInfo();
                List<IncrementalInfoArray> incrementalInfoArray = incrementalInfo.getIncrementalInfoArray();
                for (IncrementalInfoArray Z : incrementalInfoArray) {
                    BbsChangeIncrementalConfigVo bbsSignIncrementalConfigVo = new BbsChangeIncrementalConfigVo();
                    BeanUtil.copyProperties(Z, bbsSignIncrementalConfigVo);
                    bbsSignIncrementalConfigVo.setStandardType("rent");
                    Double increase = Double.valueOf(Z.getIncrease());
                    bbsSignIncrementalConfigVo.setIncrease(increase);
//                bbsSignIncrementalConfigVo.setSignInfoId(bbsSignInfoVo.getSignId());
                    bbsSignIncrementalConfigVo.setDelFlag(1);
                    bbsSignIncrementalConfigVo.setCcId(ccId);
                    bbsSignIncrementalConfigVo.setType("02");
                    bbsChangeIncrementalConfigVoList.add(bbsSignIncrementalConfigVo);
                }
            }
            if (bbsChangeIncrementalConfigVoList.size() > 0) {
                iBbsChangeIncrementalConfigService.insertBatchRecord(bbsChangeIncrementalConfigVoList);
            }
        }
        //应缴费日期变更
        if(changeTypeItem.contains(ContractChangeTypeEnum.PAYABLE_DATE_CHANGE.getCode())){
            if(vo.getBillPayableDateVoList() == null || vo.getBillPayableDateVoList().size() == 0){
                return;
            }
            vo.getBillPayableDateVoList().forEach(payableDateVo -> payableDateVo.setCcId(ccId));
            //过滤掉vo.getBillPayableDateVoList()中payableDate和payableDateOld一样的数据
            vo.getBillPayableDateVoList().removeIf(payableDateVo -> payableDateVo.getPayableDate().equals(payableDateVo.getPayableDateOld()));
            if(vo.getBillPayableDateVoList().size()==0){
                throw new McpException("应缴费日期变更时，缴费日期未发生变更，请重新选择");
            }
            iBbsChangePayableDateService.insertBatchRecord(vo.getBillPayableDateVoList());
        }
        //缩租面积变更
        if(changeTypeItem.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())){
            if(RefundMethodEnum.RETURN_BANK_CARD.getCode().equals(vo.getReduceRefundPaymentMethod())){
                vo.getRentReturnBankCardVo().setCcId(ccId);
                vo.getRentReturnBankCardVo().setSourceType("03");
                //插入退回银行卡信息
                iBbsChangeReturnBankCardService.insertRecord(vo.getReduceReturnBankCardVo());
            }
            //保存老数据
            if(vo.getSubjectMatterVoOldList()!=null && vo.getSubjectMatterVoOldList().size()>0){
                vo.getSubjectMatterVoOldList().forEach(subjectMatterVo -> subjectMatterVo.setCcId(ccId));
                iBbsChangeSubjectMatterService.insertBatchRecord(vo.getSubjectMatterVoOldList());
            }
            //保存新数据
            if(vo.getShopInfoVoList()!=null && vo.getShopInfoVoList().size()>0){
                vo.getShopInfoVoList().forEach(shopInfoVo -> shopInfoVo.setCcId(ccId));
                iBbsChangeShopInfoService.insertBatchRecord(vo.getShopInfoVoList());
            }
        }
        //计租面积
        if(changeTypeItem.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())){
            //计租面积入库
            if (vo.getCalculatedProductVoList() != null && vo.getCalculatedProductVoList().size() > 0) {
                vo.getCalculatedProductVoList().forEach(calculatedProductVo -> calculatedProductVo.setCcId(ccId));
                iBbsChangeCalculatedProductService.insertBatchRecord(vo.getCalculatedProductVoList());
            }
            //前台把银行卡赋值到租金银行卡里了
            if(RefundMethodEnum.RETURN_BANK_CARD.getCode().equals(vo.getRentRefundPaymentMethod())){
                vo.getRentReturnBankCardVo().setCcId(ccId);
                vo.getRentReturnBankCardVo().setSourceType("04");
                //插入退回银行卡信息
                iBbsChangeReturnBankCardService.insertRecord(vo.getRentReturnBankCardVo());
            }

        }

        //抵扣 押金、租金
        if(changeTypeItem.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode()) || changeTypeItem.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())
                || changeTypeItem.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())
                || changeTypeItem.contains(ContractChangeTypeEnum.BOND_CHANGE.getCode())){
            //抵扣账期
            if("3".equals(vo.getRentRefundPaymentMethod())){
                if(vo.getPeriodVoList()!=null && vo.getPeriodVoList().size()>0){
                    //                vo.getPeriodVoList().forEach(periodVo -> periodVo.setCcId(ccId));
                    vo.getPeriodVoList().forEach(periodVo -> {
                        ////ccId 和 type设置值
                        periodVo.setCcId(ccId);
                        //这个类型 1租金押金抵扣,3物业费抵扣
                        periodVo.setType("1");
                        //抵扣的一些金额保留2位小数
                        periodVo.setDeductionAmount(StringUtils.isNotBlank(periodVo.getDeductionAmount())?new BigDecimal(periodVo.getDeductionAmount()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                        periodVo.setPayableMoney(StringUtils.isNotBlank(periodVo.getPayableMoney())?new BigDecimal(periodVo.getPayableMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                        periodVo.setExcludingRateMoney(StringUtils.isNotBlank(periodVo.getExcludingRateMoney())?new BigDecimal(periodVo.getExcludingRateMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                        periodVo.setRateMoney(StringUtils.isNotBlank(periodVo.getRateMoney())?new BigDecimal(periodVo.getRateMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                        periodVo.setToBePaidMoney(StringUtils.isNotBlank(periodVo.getToBePaidMoney())?new BigDecimal(periodVo.getToBePaidMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                        periodVo.setPaidInMoney(StringUtils.isNotBlank(periodVo.getPaidInMoney())?new BigDecimal(periodVo.getPaidInMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                        periodVo.setDayMoney(StringUtils.isNotBlank(periodVo.getDayMoney())?new BigDecimal(periodVo.getDayMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                    });

                    iBbsChangeDetermineDeductionPeriodService.insertBatchRecord(vo.getPeriodVoList());
                }
            }
        }
        //抵扣 物业费 (缩租 和 计租面积 才有)
        if(changeTypeItem.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode()) || changeTypeItem.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())){
            //抵扣账期
            if("3".equals(vo.getRentRefundPaymentMethod()) && vo.getPropertyVoList()!=null && vo.getPropertyVoList().size()>0){
                //ccId 和 type设置值
                vo.getPropertyVoList().forEach(propertyVo -> {
                    propertyVo.setCcId(ccId);
                    propertyVo.setType("3");
                    //抵扣的一些金额保留2位小数
                    propertyVo.setDeductionAmount(StringUtils.isNotBlank(propertyVo.getDeductionAmount())?new BigDecimal(propertyVo.getDeductionAmount()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                    propertyVo.setPayableMoney(StringUtils.isNotBlank(propertyVo.getPayableMoney())?new BigDecimal(propertyVo.getPayableMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                    propertyVo.setExcludingRateMoney(StringUtils.isNotBlank(propertyVo.getExcludingRateMoney())?new BigDecimal(propertyVo.getExcludingRateMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                    propertyVo.setRateMoney(StringUtils.isNotBlank(propertyVo.getRateMoney())?new BigDecimal(propertyVo.getRateMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                    propertyVo.setToBePaidMoney(StringUtils.isNotBlank(propertyVo.getToBePaidMoney())?new BigDecimal(propertyVo.getToBePaidMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                    propertyVo.setPaidInMoney(StringUtils.isNotBlank(propertyVo.getPaidInMoney())?new BigDecimal(propertyVo.getPaidInMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);
                    propertyVo.setDayMoney(StringUtils.isNotBlank(propertyVo.getDayMoney())?new BigDecimal(propertyVo.getDayMoney()).setScale(2, BigDecimal.ROUND_HALF_UP).toString():null);

                });
                iBbsChangeDetermineDeductionPeriodService.insertBatchRecord(vo.getPropertyVoList());
            }
        }
        
        //免租期变更
        if(changeTypeItem.contains(ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode())){
            if(RefundMethodEnum.RETURN_BANK_CARD.getCode().equals(vo.getFreeRentRefundMethod())){
                vo.getFreeRentReturnBankCardVo().setCcId(ccId);
                vo.getFreeRentReturnBankCardVo().setSourceType("05");
                //插入退回银行卡信息
                iBbsChangeReturnBankCardService.insertRecord(vo.getFreeRentReturnBankCardVo());
            }
        }
    }

    /**
     * 根据id删除
     *
     * @param ccId 主键id
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void deleteByIdRecord(String ccId) {
        if (!StringUtils.isEmpty(ccId)) {
            if (!baseService.removeById(ccId)) {
                log.error("合同变更表删除失败");
                throw new McpException("合同变更表删除失败" + ccId);
            }
        } else {
            throw new McpException("合同变更表删除失败主键为空");
        }
    }

    @Override
    public BbsiContractChangeCustomerVo selectCustomInfo(String contractCode) {
        BbsiContractChangeCustomerVo vo = baseMapper.selectCustomInfo(contractCode);
        return vo;
    }

    @Override
    public PageResult<List<BbsiContractChangeListPageResultVo>> selectByPageRecord(BbsiContractChangeListPageResultVo vo) {
        List<BbsiContractChangeListPageResultVo> result = baseMapper.selectByPageRecord(vo);
        if(result!=null){
            for(BbsiContractChangeListPageResultVo bbsiContractChangeListPageResultVo:result){
                String changeType = bbsiContractChangeListPageResultVo.getChangeType();
                if("其他信息".equals(changeType)){
                    bbsiContractChangeListPageResultVo.setChangeType(bbsiContractChangeListPageResultVo.getChangeTypeItem());
//                    bbsiContractChangeListPageResultVo.setChangeType(changeType+"("+bbsiContractChangeListPageResultVo.getChangeTypeItem()+")");
                }
            }
        }
        return new PageResult(result);
    }


    /**
     * removeByIdRecord 根据主键删除
     *
     * @param ccId 需要删除的
     * @return void
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String ccId, String requestId) {
        if (!StringUtils.isEmpty(ccId)) {
            log.info("开始删除合同变更记录, ccId={}, requestId={}", ccId, requestId);
            
            if (!baseService.saveOperationHisById(ccId, 3)) {
                log.error("合同变更表删除后保存历史失败, ccId={}, requestId={}", ccId, requestId);
                throw new McpException("合同变更表删除后保存历史失败");
            }
            
            BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
            entity.setCcId(ccId);
            entity.setDelFlag(DelFlagEnum.DELETED.getCode());
            
            if (!baseService.updateById(entity)) {
                log.error("合同变更表删除失败, ccId={}, requestId={}", ccId, requestId);
                throw new McpException("合同变更表删除失败" + ccId);
            }

            //删除流程
            iFwWorkflowService.removeByRequestId(requestId);
            
            log.info("成功删除合同变更记录, ccId={}, requestId={}", ccId, requestId);
        } else {
            log.error("合同变更表删除失败，ccId为空, requestId={}", requestId);
            throw new McpException("合同变更表删除失败为空");
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的合同变更表
     * @return void
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsiContractChangeVo vo) {
        log.error("合同变更前端传入数据BbsiContractChangeVo:" + JSONObject.toJSONString(vo));
        // 1的话是驳回后提交变成待审核
        if("1".equals(vo.getIsnextflow()+"")){
            vo.setChangeStatus(ChangeStatusEnum.WAIT_AUDIT.getCode());
        }else {
            vo.setChangeStatus(ChangeStatusEnum.TEMPORARY.getCode());
        }
        if (vo != null) {
            BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getCcId())) {
                throw new McpException("合同变更表更新失败传入为空");
            }
            if (!baseService.updateById(entity)) {
                log.error("合同变更表更新失败");
                throw new McpException("合同变更表更新失败" + entity.getCcId());
            } else {
                if (!baseService.saveOperationHisById(entity.getCcId(), 2)) {
                    log.error("合同变更表更新后保存历史失败:" + entity.getCcId());
                    throw new McpException("合同变更表更新后保存历史失败");
                }
//                if ("1".equals(vo.getChangeStatus())) {
//                    contractChangeResultService.contractChange(vo);
//                }
            }
            // 1的话是驳回后提交
            if("1".equals(vo.getIsnextflow()+"")){
                BaseFlowVo flowVo = new BaseFlowVo();
                if((vo.getRequestId()!=null)) {
                    flowVo.setRequestId(vo.getRequestId().toString());
                }else{
                    throw new McpException("requestId不能为空");
                }
                this.workflowService.submitRequest(flowVo);
            }
        } else {
            throw new McpException("合同变更表更新失败传入为空");
        }
    }

    /**
     * 简单更新合同变更记录字段（不改变状态，不触发工作流）
     * 专门用于保存changeTrial试算结果等场景
     *
     * @param vo 需要更新的合同变更表
     * @return void
     * <AUTHOR>
     * @date 2024-01-01
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecordSimple(BbsiContractChangeVo vo) {
        if (vo != null) {
            BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getCcId())) {
                throw new McpException("合同变更表更新失败，主键ID为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("合同变更表字段更新失败, ccId: {}", entity.getCcId());
                throw new McpException("合同变更表字段更新失败: " + entity.getCcId());
            } else {
                log.info("合同变更表字段更新成功, ccId: {}", entity.getCcId());
            }
        } else {
            throw new McpException("合同变更表更新失败，传入参数为空");
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecordV2(BbsiContractChangeVo vo) {
        if (vo != null) {

            //处理应收/应退金额
            specialHandle(vo);

            BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getCcId())) {
                throw new McpException("合同变更表更新失败传入为空");
            }
            if (!baseService.updateById(entity)) {
                log.error("合同变更表更新失败");
                throw new McpException("合同变更表更新失败" + entity.getCcId());
            } else {
                if (!baseService.saveOperationHisById(entity.getCcId(), 2)) {
                    log.error("合同变更表更新后保存历史失败:" + entity.getCcId());
                    throw new McpException("合同变更表更新后保存历史失败");
                }

                if ("1".equals(vo.getChangeStatus())) {
//                    contractChangeResultService.contractChange(vo);
                }

                //其他变更
                if(ChangeTypeEnum.OTHER_CHANGE.getCode().equals(vo.getChangeType())){
                    handleOtherChange(entity, vo);
                }

            }

            if("1".equals(vo.getIsnextflow()+"")){
                //租金变更、缩租面积变更  提交的时候关闭账单
                if(vo.getChangeTypeItem().contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())
                        || vo.getChangeTypeItem().contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())
                        || vo.getChangeTypeItem().contains(ContractChangeTypeEnum.BOND_CHANGE.getCode()) 
                        || vo.getChangeTypeItem().contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode())
                        || vo.getChangeTypeItem().contains(ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode())
                        ){
                    baseService.closeOrOpenBillAndBillBranks("0", vo.getContractCode(), vo.getProjectId());
                }else if(vo.getChangeTypeItem().contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())){
                    if( vo.getChangeTypeItem().equals(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())
                            && isOnePriceAll(vo.getContractCode()) ){
                        log.info("单独计租面积变更,合同租金和租金都为一口价,不关闭账单");
                    }else{
                        baseService.closeOrOpenBillAndBillBranks("0", vo.getContractCode(), vo.getProjectId());
                    }
                }
            }

            //提交前再更新变更主表状态安全些
            BbsiContractChangeEntity entity2 = new BbsiContractChangeEntity();
            entity2.setCcId(entity.getCcId());
            if("1".equals(vo.getIsnextflow()+"")){
                entity2.setChangeStatus(ChangeStatusEnum.WAIT_AUDIT.getCode());
            }else {
                entity2.setChangeStatus(ChangeStatusEnum.TEMPORARY.getCode());
            }
            baseService.updateById(entity2);

            if("1".equals(vo.getIsnextflow()+"")){
                BaseFlowVo flowVo = new BaseFlowVo();
                if((vo.getRequestId()!=null)) {
                    flowVo.setRequestId(vo.getRequestId().toString());
                }else{
                    throw new McpException("requestId不能为空");
                }
                this.workflowService.submitRequest(flowVo);
            }

        } else {
            throw new McpException("合同变更表更新失败传入为空");
        }
    }

    /**
     * 其他变更删除一些子表
     * @param ccId
     */
    private void deleteAllByCcId(String ccId){
        bbsChangeReturnBankCardMapper.deleteByCcId(ccId);
        bbsChangeResultProductMapper.deleteByCcId(ccId);
        bbsChangeIncrementalConfigMapper.deleteByCcId(ccId);
        bbsChangePayableDateMapper.deleteByCcId(ccId);
        bbsChangeShopInfoMapper.deleteByCcId(ccId);
        bbsChangeSubjectMatterMapper.deleteByCcId(ccId);
        bbsChangeDetermineDeductionPeriodMapper.deleteByCcId(ccId);
        bbsChangeCalculatedProductMapper.deleteByCcId(ccId);
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param ccId 需要查询的
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsiContractChangeVo selectByIdRecord(String ccId) {
        BbsiContractChangeVo vo = new BbsiContractChangeVo();

        if (!StringUtils.isEmpty(ccId)) {
            BbsiContractChangeEntity entity = baseService.selectById(ccId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                
                // 处理缴费周期变更的JSON转换
                convertPaymentCycleJsonToVo(vo);
                
                BbsiContractChangeCustomerVo bbsiContractChangeCustomerVo = selectCustomInfo(vo.getContractCode());
                if (bbsiContractChangeCustomerVo == null) {
                    throw new McpException("客户信息查询为null");
                }
                vo.setCustomerTypeCode(bbsiContractChangeCustomerVo.getCustomerType());
                vo.setProjectId(bbsiContractChangeCustomerVo.getProjectId());
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 将缴费周期变更JSON转换为VO
     * @param vo 合同变更VO
     */
    private void convertPaymentCycleJsonToVo(BbsiContractChangeVo vo) {
        String changeTypeItem = vo.getChangeTypeItem();
        // 缴费周期变更
        if(changeTypeItem.contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode()) && 
            StringUtils.isNotBlank(vo.getSelectedBillsJson())) {
            
            try {
                // 转换JSON为完整的账单信息
                List<BbpmBillManagementPageResultVo> bills = 
                    PaymentCycleBillConverter.convertJsonToFullBillInfo(vo.getSelectedBillsJson());
                vo.setSelectedBills(bills);
                log.debug("缴费周期变更JSON转换为账单列表，数量: {}", bills != null ? bills.size() : 0);
            } catch (Exception e) {
                log.error("缴费周期变更JSON转换为账单列表失败", e);
                // 不抛异常，只记录日志，避免影响其他功能
                vo.setSelectedBills(Collections.emptyList());
            }
        }
        
        // 转换变更前账单周期JSON为List
        if (StringUtils.isNotBlank(vo.getBillCycleOld())) {
            try {
                // 解析JSON字符串为字符串列表
                List<String> billCycleList = JSON.parseArray(vo.getBillCycleOld(), String.class);
                vo.setBillCycleOldList(billCycleList);
                log.debug("账单周期JSON转换成功，数量: {}", billCycleList != null ? billCycleList.size() : 0);
            } catch (Exception e) {
                log.error("账单周期JSON转换失败: billCycleOld={}", vo.getBillCycleOld(), e);
                // 转换失败时设置为空列表
                vo.setBillCycleOldList(Collections.emptyList());
            }
        }
    }

    @Override
    public BbsiContractChangeResultVo selectByContractCode(String contractCode) {
        return baseMapper.selectChangeResultBycontractCode(contractCode);
    }

    /**
     * 获取补充协议数量
     *
     * @param contractCode 合同编号
     * @return 补充协议数量
     */
    @Override
    public int selectAgreementCount(String contractCode) {
        return baseMapper.selectAgreementCount(contractCode);
    }

    @Override
    public PageResult<List<BbsiContractChangeRecordPageResultVo>> selectChangeRecord(BbsiContractChangeRecordPageResultVo vo) {
        List<BbsiContractChangeRecordPageResultVo> result = baseMapper.selectByPageChangeRecord(vo);
        return new PageResult(result);
    }

    @Override
    public PageResult<List<BbsiContractChangeRecordPageResultVo>> selectContractAuditPageRecord(BbsiContractChangeRecordPageResultVo vo) {
        List<BbsiContractChangeRecordPageResultVo> result = baseMapper.selectContractAuditPageRecord(vo);

        return new PageResult(result);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertCaRecord(BbsiContractChangeApproveInfoVo vo) {
        if (vo == null) {
            return null;
        }
        BbsiContractChangeApproveEntity entity = new BbsiContractChangeApproveEntity();
        BeanUtils.copyProperties(vo, entity);
        entity.setApproveId(null);
        if (!contractApproveInfoService.insert(entity)) {
            log.error("合同变更审批表新增失败:" + entity.toString());
            throw new McpException("合同变更审批表新增失败");
        } else {
            if (!contractApproveInfoService.saveOperationHisById(entity.getApproveId(), 1)) {
                log.error("合同变更审批表新增后保存历史失败:" + entity.toString());
                throw new McpException("合同变更审批表新增后保存历史失败");
            }
            log.debug("合同变更审批表新增成功:" + entity.getApproveId());
            return entity.getApproveId();
        }
    }

    @Override
    public String checkStatus(String contractCode) {
        QueryWrapper<BbsiContractChangeEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("contract_code", contractCode);
        wrapper.eq("change_status", "0");
        wrapper.eq("del_flag", "1");

        //后加 只有原来合同基本信息变更时从合同管理进修改和验证
        wrapper.eq("change_type", "1");

        wrapper.select("cc_id as ccId");
        List<Map<String, Object>> list = baseMapper.selectMaps(wrapper);
        if (list == null || list.isEmpty()) {
            return null;
        } else {
            return String.valueOf(list.get(0).get("ccId"));
        }
    }

    /**
     * 预览与下载
     *
     * @param ccId 合同变更id
     * @return 预览与下载结果
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public FIleResultVo previewAndDownload(String ccId) {
        BbsiContractChangeVo contractChangeVo = selectByIdRecord(ccId);
        if ("1".equals(contractChangeVo.getChangeStatus()) && StringUtils.isNotBlank(contractChangeVo.getSignId())) {
            BbsSignInfoVo signInfoVo = signInfoService.selectByIdRecord(contractChangeVo.getSignId());
            if (SignStatusEnum.SIGNED.getCode().equals(signInfoVo.getSignStatus())) {
                return previewContract(contractChangeVo.getAgreementCode());
            } else {
                return signInfoExtService.previewAndDownload(contractChangeVo.getSignId(), null, false);
            }
        } else {
            return previewAndDownloadByContractChange(contractChangeVo);
        }
    }


    /**
     * 合同变更新增时没有ccId，所以重新写一个v2接口
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public FIleResultVo previewAndDownloadV2(BbsiContractChangeVo vo) {
        String ccId = vo.getCcId();
        if(StringUtils.isBlank(ccId) || "0".equals(vo.getChangeStatus())){
            return previewAndDownloadByContractChange(vo);
        }else{
            BbsiContractChangeVo contractChangeVo = selectByIdRecord(ccId);
            if ("1".equals(contractChangeVo.getChangeStatus()) && StringUtils.isNotBlank(contractChangeVo.getSignId())) {
                BbsSignInfoVo signInfoVo = signInfoService.selectByIdRecord(contractChangeVo.getSignId());
                if (SignStatusEnum.SIGNED.getCode().equals(signInfoVo.getSignStatus())) {
                    return previewContract(contractChangeVo.getAgreementCode());
                } else {
                    return signInfoExtService.previewAndDownload(contractChangeVo.getSignId(), null, false);
                }
            }
        }
        return null;
    }

    /**
     * 根据签约id查询
     *
     * @param signId 签约id
     * @return 合同变更信息 vo实体
     */
    @Override
    public BbsiContractChangeVo selectBySignId(String signId) {
        BbsiContractChangeVo vo = new BbsiContractChangeVo();
        if (StringUtils.isNotBlank(signId)) {
            BbsiContractChangeEntity entity = new LambdaQueryChainWrapper<>(baseMapper)
                    .eq(BbsiContractChangeEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsiContractChangeEntity::getSignId, signId)
                    .one();
            if (!Objects.isNull(entity)) {
                BeanUtils.copyProperties(entity, vo);
                
                // 处理缴费周期变更的JSON转换
                convertPaymentCycleJsonToVo(vo);
                
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public BbsiContractChangeEntity selectBySignIdEntity(String signId) {
        BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
        if (StringUtils.isNotBlank(signId)) {
             entity = new LambdaQueryChainWrapper<>(baseMapper)
                    .eq(BbsiContractChangeEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsiContractChangeEntity::getSignId, signId)
                    .one();
        }
        return entity;
    }

    /**
     * 根据签约id获取预览账单信息
     *
     * @param signId 签约id
     * @return 预览账单信息 vo实体
     */
    @Override
    public BbsChangePreviewBillResultVo getPreviewBillBySignId(String signId) {
        BbsiContractChangeVo contractChangeVo = selectBySignId(signId);
        return AbstractContractChangeFactory.getInstance(contractChangeVo).getPreviewBills();
    }

    @Override
    public BbsChangePreviewBillResultVo getPreviewBillByCcId(String ccId) {
        BbsiContractChangeVo contractChangeVo = selectByIdRecord(ccId);
        return AbstractContractChangeFactory.getInstance(contractChangeVo).getPreviewBills();
    }

    /**
     * 退款时使用的合同变更信息
     *
     * @param signId           签约id
     * @param refundChangeType 退款变更类型
     * @return 退款时使用的合同变更信息 vo实体
     */
    @Override
    public BbctRefundUseChangeInfoVo refundUseChangeInfo(String signId, String refundChangeType) {
        BbctRefundUseChangeInfoVo refundUseChangeInfoVo = new BbctRefundUseChangeInfoVo();
//        BbsiContractChangeVo contractChangeVo = selectBySignId(signId);
//        BbsiContractChangeApproveEntity contractChangeApproveEntity = contractChangeApproveInfoService.selectByCcId(contractChangeVo.getCcId());
        BbsiContractChangeEntity entity = selectBySignIdEntity(signId);
        if (RefundChangeTypeEnum.RENT_CHANGE.getCode().equals(refundChangeType)) {
            refundUseChangeInfoVo.setChangeAccountingPeriodType(entity.getRentEffectiveExecutionDate());
            refundUseChangeInfoVo.setRequestDate(entity.getModifyTime());
        } else if (RefundChangeTypeEnum.CASH_PLEDGE_CHANGE.getCode().equals(refundChangeType)) {
            refundUseChangeInfoVo.setChangeAccountingPeriodType(entity.getCashEffectiveExecutionDate());
            refundUseChangeInfoVo.setRequestDate(entity.getModifyTime());
        } else if (RefundChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode().equals(refundChangeType)) {
            refundUseChangeInfoVo.setChangeAccountingPeriodType(entity.getReduceEffectiveExecutionDate());
            refundUseChangeInfoVo.setRequestDate(entity.getModifyTime());
        }
        return refundUseChangeInfoVo;
    }

    /**
     * 根据合同变更预览与下载
     *
     * @param contractChangeVo 合同信息 vo实体
     * @return 预览与下载结果
     */
    private FIleResultVo previewAndDownloadByContractChange(BbsiContractChangeVo contractChangeVo) {
        return AbstractContractChangeFactory.getInstance(contractChangeVo).previewAndDownload();
    }

    /**
     * 合同中心 查询合同信息
     *
     * @param contractCode 合同编号
     * @return 合同信息
     */
    private BbctContractManagementVo selectContractByIdNo(String contractCode) {
        AppReply<BbctContractManagementVo> appReply = contractFeignClient.selectByIdNo(contractCode, null);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("查询合同信息失败[详情: %s, contractCode: %s]", appReply, contractCode));
        }
        return appReply.getData();
    }

    /**
     * 预览临时合同
     *
     * @param contractManagementVo 合同 vo实体
     * @return 临时合同 vo实体
     */
    private FIleResultVo previewTemporaryContract(BbctContractManagementVo contractManagementVo) {
        AppReply<FIleResultVo> appReply = contractFeignClient.createPdf(contractManagementVo);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("合同预览失败[详情: %s, contractCode: %s]", appReply, contractManagementVo.getContractNo()));
        }
        return appReply.getData();
    }

    /**
     * 字符串按符号拆分为列表
     *
     * @param str 字符串
     * @return 列表
     */
    private List<String> splitBySymbol(String str, String symbol) {
        if (StringUtils.isBlank(str)) {
            return Collections.emptyList();
        } else {
            return new ArrayList<>(Arrays.asList(str.split(symbol)));
        }
    }

    /**
     * 获取合同编号
     *
     * @return 合同编号
     */
    private String getContractCode(String modeId, String contractCode) {
        AppReply<String> appReply = contractFeignClient.contractCodeGeneration(modeId, contractCode);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || StringUtils.isBlank(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("合同中心生成编号失败[详情: %s, 合同编号: %s]", appReply, contractCode));
        }
        return appReply.getData();
    }

    /**
     * 获取企业信息
     *
     * @param customerNo 客户编号
     * @return 企业信息
     */
    private JSONObject getCompanyInfo(String customerNo) {
        AppReply<JSONObject> appReply = customerFeignClient.getEnterpriseCustomerByIdOrCreditCode(customerNo);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("客户中心获取企业信息失败[详情: %s, 客户编号: %s]",
                    appReply, customerNo));
        }
        return appReply.getData();
    }

    /**
     * 预览合同文件
     *
     * @param contractCode 合同编号
     * @return 合同文件
     */
    private FIleResultVo previewContract(String contractCode) {
        AppReply<FIleResultVo> appReply = contractFeignClient.getPdfFileByContractNo(contractCode);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("预览失败[详情: %s, 合同编号: %s]", appReply, contractCode));
        }
        return appReply.getData();
    }

    /**
     * 确定要存储的费用汇总列表
     * 1. 免租期变更时，还是按照原来的赋值方式
     * 2. 其他变更时，退/缴方式等于3时，从BbsiContractChangeVo中的feesSummaryListDK取值，其他情况都按原来方式
     *
     * @param vo 合同变更VO
     * @return 要存储的费用汇总列表
     */
    private List<FeesSummaryVo> determineFeesSummaryListToStore(BbsiContractChangeVo vo) {
        ChangeCalculationBillVo changeCalculationBillVo = vo.getChangeCalculationBillVo();
        if (changeCalculationBillVo == null) {
            return new ArrayList<>();
        }

        String changeTypeItem = vo.getChangeTypeItem();

        // 1. 免租期变更时，还是按照原来的赋值方式
        if (changeTypeItem != null && changeTypeItem.contains(ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode())) {
            return changeCalculationBillVo.getFeesSummaryList() != null ?
                   changeCalculationBillVo.getFeesSummaryList() : new ArrayList<>();
        }

        // 2. 其他变更时，根据退/缴方式判断
        // 检查各种退/缴方式是否等于3（抵扣租金）
        boolean isDeductionMethod = "3".equals(vo.getRentRefundPaymentMethod()) ||
                                   "3".equals(vo.getReduceRefundPaymentMethod()) ||
                                   "3".equals(vo.getCashRefundPaymentMethod());

        if (isDeductionMethod && vo.getFeesSummaryListDK() != null) {
            // 退/缴方式为3时，从BbsiContractChangeVo中的feesSummaryListDK取值
            return vo.getFeesSummaryListDK() != null ? vo.getFeesSummaryListDK() : new ArrayList<>();
        } else {
            // 其他情况按原来方式，从changeCalculationBillVo的feesSummaryList取值
            return changeCalculationBillVo.getFeesSummaryList() != null ?
                   changeCalculationBillVo.getFeesSummaryList() : new ArrayList<>();
        }
    }
}
