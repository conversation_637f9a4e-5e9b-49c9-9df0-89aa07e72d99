package com.bonc.ioc.bzf.busisigning.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalProductVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultProductVo;
import com.bonc.ioc.bzf.busisigning.vo.SynResult;
import com.bonc.ioc.bzf.busisigning.vo.tz.TzParamVo;
import com.bonc.ioc.bzf.busisigning.vo.tz.TzResult;
import com.bonc.ioc.common.util.AppReply;
import org.redisson.api.RLock;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface TZService {

    /**
     * 核心控制机类
     * @param multipartFile
     * @return
     */
    TzResult core(MultipartFile multipartFile);


    /**
     * 处理数据
     * @param tzResult
     * @return
     */
     void analysData(TzResult tzResult);

    /**
     * 解下EXECL
     * @param multipartFile
     * @return
     */
    TzResult analysisExecl(MultipartFile multipartFile);

    /**
     * 保存数据
     * @param result
     */
    void saveData(TzResult result);

    /**
     * 从缓存中查询
     * @param key
     * @param productNos
     * @param operateState
     * @return
     */
    AppReply<JSONObject> searchProductsFromRedis(String key, List<String> productNos, String operateState);

    /**
     * 根据房源编号查询房源
     * @param key
     * @param productNo
     * @param operateState
     * @return
     */
    AppReply<JSONObject> searchProductFromRedis(String key,String productNo,String operateState);

    /**
     * 缓存房源数据
     * @param key
     * @param productNos
     */
    void cacheHouse(String key , List<String> productNos);

    /**
     * 删除缓存
     * @param key
     */
    void delRedis(String key);


    /**
     * 去重
     * @param productNo
     * @return
     */
    List<String> distinct(List<String> productNo);


    JSONArray selectHouseProducts(List<String> productNos);

    List<List<String>> splitHouseProducts(List<String> productNos);

    void saveRedis(String key,JSONArray jsonArray);

    /**
     * 按照签约结果删除台账
     * @param signIds
     */
    void delTz(List<String> signIds);

    /**
     * 同步工银
     * @return
     */
    SynResult synPayment();

    /**
     * 生成合同
     * @return
     */
    SynResult synContract();

    /**
     * 获取房源新从房态中心
     * @param paramVos
     * @param lockList
     * @param operateState
     * @return
     */
    List<BbsResultProductVo> createProductFromHouse(List<TzParamVo> paramVos, List<RLock> lockList, String operateState);

    /**
     * 获取房源新从房态中心
     *
     * @param bbsResultProductVoList
     * @param lockList
     * @param operateState
     * @param areaType
     * @return
     */
    List<BbsResultProductVo> createProducts(List<BbsResultProductEntity> bbsResultProductVoList,
                                            List<RLock> lockList,
                                            String operateState,
                                            String areaType);

    /**
     * 创建项目信息
     * @param productVos
     */
    void createProjects(List<BbsResultProductVo> productVos);


    /**
     * 创建房源和项目
     *
     * @param bbsResultProductVoList
     * @param lockList
     * @param operateState
     * @param areaType
     * @return
     */
    List<BbsResultProductVo> createProductProject(List<BbsResultProductEntity> bbsResultProductVoList,
                                                  List<RLock> lockList,
                                                  String operateState,
                                                  String areaType);


    /**
     * 从客户中心获个人取基础信息
     * @param paramVo
     * @return
     */
    BbsResultCustomerVo createPersonFromCustomer(TzParamVo paramVo);

    /**
     * 从客户中心获取企业的基础信息
     * @param tzParamVo
     * @return
     */
    BbsResultCustomerVo createCompanyFromCustomer(TzParamVo tzParamVo);

    /**
     * 从房态重新查询房源
     * @param key
     * @param productNo
     * @param paramVo
     * @param operateState
     * @return
     */
    BbsResultProductVo createProductFromHouse(String key, String productNo,TzParamVo paramVo,String operateState);


    /**
     * 解锁
     * @param lockList
     */
    void unlock(List<RLock> lockList);


    /**
     * 同步修改房源状态到待签约
     * @param productVos
     */
    void synHouseStatus(List<BbsResultProductVo> productVos);


    /**
     * 同步修改房源状态到待签约
     * @param products
     */
    void synHouseEntityStatus(List<BbsResultProductEntity> products);

    /**
     * 税率
     * @param projectId
     * @param userType
     * @param customerId
     * @param chargeItemId
     * @return
     */
    Double taxrate(String projectId, String userType, String customerId, String chargeItemId);

    /**
     * 待签约后同步商业内控
     * @param bbsResultProductVoList
     */
    void updatePushStatusSign(List<BbsResultProductVo> bbsResultProductVoList,String whetherPushSign);

    /**
     * 续租同步商业内控
     * @param list
     */
    void updatPushRewallStatus(List<BbsRenewalProductVo> list);
    /**
     * 续租后同步商业呢空
     * @param bbsResultProductVoList
     */
    void updatePushStatusRenewal(List<BbsRenewalProductVo> bbsResultProductVoList);

    String createRentStandardUnitCode(String rentStandardUnitName);
    /**
     * 查询房源运营状态
     * @param signId
     * @return
     */
    List<BbsResultProductEntity>  selectSignHouseHistoryState(String signId);


    void restHouseHistoryState(List<BbsResultProductEntity> list);

}
