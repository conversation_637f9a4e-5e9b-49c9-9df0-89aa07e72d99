package com.bonc.ioc.bzf.busisigning.controller;

import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeAppService;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.tencentcloudapi.ocr.v20181119.models.BankCardOCRResponse;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合同变更表 前端控制器
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by fzq for init
 */
@RestController
@RequestMapping("/contractChange/app")
@Api(tags = "合同变更(app)")
public class BbsiContractChangeAppController {
    @Resource
    private IBbsiContractChangeAppService baseService;

    /**
     * insertRecord 新增
     *
     * @param vo 需要新增的记录
     * @return com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by fzq for init
     * @since 1.0.0
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "fzq")
    @ApiOperation(value = "新增", notes = "新增全表数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "合同变更表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsiContractChangeVo vo) {
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
    }

    @GetMapping(value = "/selectChangeRecordList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "fzq")
    @ApiOperation(value = "合同变更记录列表查询", notes = "合同变更记录列表查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<List<BbsiContractChangeRecordVo>> selectChangeRecordList(@ApiParam(value = "合同编号") @RequestParam String contractCode) {
        AppReply<List<BbsiContractChangeRecordVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectChangeRecordList(contractCode));
        return appReply;
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param ccId 需要查询的
     * @return com.bonc.ioc.common.util.AppReply 主键查询的数据
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "fzq")
    @ApiOperation(value = "详情根据主键查询表中信息", notes = "详情根据主键查询表中信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsiContractChangeVo> selectByIdRecord(@ApiParam(value = "主键") @RequestParam String ccId) {
        AppReply<BbsiContractChangeVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByIdRecord(ccId));
        return appReply;
    }
    /**
     * selectByIdRecord 根据主键查询
     *
     * @param contractCode 需要查询的合同编码
     * @return com.bonc.ioc.common.util.AppReply 主键查询的数据
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectByContractCodeAndApproveStatus", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "fzq")
    @ApiOperation(value = "合同编码查询合同详情(只查询待审核状态)", notes = "合同编码查询合同详情(只查询待审核状态)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsiContractChangeVo> selectByContractCodeAndApproveStatus(@ApiParam(value = "合同编码") @RequestParam String contractCode) {
        AppReply<BbsiContractChangeVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByContractCodeAndApproveStatus(contractCode));
        return appReply;
    }

    @GetMapping(value = "/selectApproveDetail", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "fzq")
    @ApiOperation(value = "查询审批信息", notes = "根据合同变更id查询审批信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbsContractChangeApproveDetailVo>> selectApproveDetail(@ApiParam(value = "主键") @RequestParam String ccId) {
        AppReply<List<BbsContractChangeApproveDetailVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectApproveDetail(ccId));
        return appReply;
    }

    /**
     * cancelById 根据主键删除
     *
     * @param ccId 需要删除的合同变更表id
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by fzq for init
     * @since 1.0.0
     */
    @PostMapping(value = "/cancelById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "pyj")
    @ApiOperation(value = "取消申请", notes = "根据主键删除表中信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdRecord(@ApiParam(value = "需要删除的合同变更表id") @RequestBody String ccId) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.removeByIdRecord(ccId);
        return appReply;
    }

    /**
     * ocr身份证识别
     *
     * @param imageBase64 imageBase64
     * @return
     */
    @PostMapping(value = "/bankCardOCR", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "fzq")
    @ApiOperation(value = "ocr银行卡识别", notes = "ocr银行卡识别")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply bankCardOCR(@ApiParam(value = "ImageBase64") @RequestBody String imageBase64) {
        AppReply<BankCardOCRResponse> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.bankCardOCR(imageBase64));
        return appReply;
    }

    @PostMapping(value = "/getVerificationCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "fzq")
    @ApiOperation(value = "获取短信验证码", notes = "获取短信验证码")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply getVerificationCode(@ApiParam(value = "vo") @RequestBody @Validated(InsertValidatorGroup.class) VerificationCodeVo vo) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.getVerificationCode(vo);
        return appReply;
    }

    @PostMapping(value = "/checkVerificationCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "fzq")
    @ApiOperation(value = "校验短信验证码", notes = "校验短信验证码")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Boolean> checkVerificationCode(@ApiParam(value = "vo") @RequestBody @Validated(InsertValidatorGroup.class) VerificationCodeVo vo) {
        AppReply<Boolean> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.checkVerificationCode(vo));
        return appReply;
    }
}

