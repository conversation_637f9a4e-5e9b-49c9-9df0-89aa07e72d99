package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 人-产品：产品表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-30
 * @change 2023-08-30 by li<PERSON><PERSON><PERSON> for init
 */
public interface IBbsResultProductService extends IMcpBaseService<BbsResultProductEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by li<PERSON><PERSON><PERSON> for init
     */
    String insertRecord(BbsResultProductVo vo);

    String insertRecord(BbsResultProductEntity entity);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by liwenqiang for init
     */
    List<String> insertBatchRecord(List<BbsResultProductVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param rpId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by liwenqiang for init
     */
    void removeByIdRecord(String rpId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param rpIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by liwenqiang for init
     */
    void removeByIdsRecord(List<String> rpIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的人-产品：产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by liwenqiang for init
     */
    void updateByIdRecord(BbsResultProductVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的人-产品：产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by liwenqiang for init
     */
    void updateBatchByIdRecord(List<BbsResultProductVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的人-产品：产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by liwenqiang for init
     */
    void saveByIdRecord(BbsResultProductVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的人-产品：产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by liwenqiang for init
     */
    void saveBatchByIdRecord(List<BbsResultProductVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param rpId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by liwenqiang for init
     */
    BbsResultProductVo selectByIdRecord(String rpId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by liwenqiang for init
     */
    PageResult<List<BbsResultProductPageResultVo>> selectByPageRecord(BbsResultProductPageVo vo);

    /**
     * 根据人房关系id查询
     *
     * @param rrId 人房关系id
     * @return 产品信息列表
     */
    List<BbsResultProductVo> selectByRrId(String rrId);
}
