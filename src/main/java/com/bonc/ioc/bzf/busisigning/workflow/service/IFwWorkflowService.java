package com.bonc.ioc.bzf.busisigning.workflow.service;


import com.bonc.ioc.bzf.busisigning.workflow.vo.BaseFlowVo;
import com.bonc.ioc.bzf.busisigning.workflow.vo.BbfwRequestBusinessMappingVo;
import com.bonc.ioc.bzf.busisigning.workflow.vo.CallBakParamsVo;
import com.bonc.ioc.bzf.busisigning.workflow.vo.OaCreateWorkFlowRequestV2Vo;
import com.bonc.ioc.common.util.AppReply;
import org.json.JSONObject;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IFwWorkflowService {

    /**
     * requestId 转 业务中台Id
     * @param requestId
     * @return
     */
    String requestId2BusinessId(String requestId);

    /**
     *  业务中台Id 获取 工作流参数
     * @param businessId
     * @param businessTypeCode
     * @return
     */
    BbfwRequestBusinessMappingVo businessId2WorkFlow(String businessId, String businessTypeCode);

    /**
     * 按照 requestId 删除数据
     * @param requestId
     */
    void removeByRequestId(String requestId);


    /**
     * 创建流程
     * @param requestV2Vo
     * @return
     */
    Long doCreateRequest(OaCreateWorkFlowRequestV2Vo requestV2Vo);

    /**
     * 同意
     * @param param
     */
    void submitRequest(BaseFlowVo param);

    /**
     * 强制终止
     * @param requestId
     */
    void  doForceOver(String requestId);

    /**
     * 根据业务分类编号获取workflowId
     * @param businessTypeCode
     * @return
     */
    String selectWorkFlowId(@RequestParam("businessTypeCode") String businessTypeCode);

    /**
     * 签约审签
     * @param vo
     * @return
     */
    AppReply<Object> signApprove(@RequestBody CallBakParamsVo vo);

    /**
     * 新签合同合同
     * @param vo
     * @return
     */
    AppReply signContractApprove(@RequestBody CallBakParamsVo vo);

    /**
     * 续租审签
     * @param vo
     * @return
     */
    AppReply<Object> renewalApprove(@RequestBody CallBakParamsVo vo);

    /**
     * 续租合同审核
     * @param vo
     * @return
     */
    AppReply renewalContractApprove(@RequestBody CallBakParamsVo vo);


    /**
     * 处理退款申请业务审批
     * @param vo
     * @return
     */
    AppReply<Object> dealBusinessApprove(@RequestBody CallBakParamsVo vo);


    AppReply<Object> dealBasicInformationChangeApprove(CallBakParamsVo vo);

    AppReply<Object> dealOtherInformationChangeApprove(CallBakParamsVo vo);

    AppReply<Object> deaUploadResultApprove(CallBakParamsVo vo);

    AppReply<Object> dealAdjustResultApprove(CallBakParamsVo vo);

}
