package com.bonc.ioc.bzf.busisigning.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.consts.SymbolConst;
import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeEntity;
import com.bonc.ioc.bzf.busisigning.enums.*;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbpaymentFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSignerVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsAdjustService;
import com.bonc.ioc.bzf.busisigning.service.IBbsSignInfoExtService;
import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeService;
import com.bonc.ioc.bzf.busisigning.utils.DateUtils;
import com.bonc.ioc.bzf.busisigning.utils.ListUtil;
import com.bonc.ioc.bzf.busisigning.utils.ResultUtils;
import com.bonc.ioc.bzf.busisigning.utils.UserUtil;
import com.bonc.ioc.bzf.busisigning.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbpmBillManagementPageVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo;
import com.bonc.ioc.bzf.busisigning.vo.SaveConsignorInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.SigningSaveVo;
import com.bonc.ioc.bzf.busisigning.vo.adjust.*;
import com.bonc.ioc.bzf.busisigning.workflow.component.WorkflowCommponent;
import com.bonc.ioc.bzf.busisigning.workflow.enums.BusinessTypeCodeEnum;
import com.bonc.ioc.bzf.busisigning.workflow.enums.NodeKeyEnum;
import com.bonc.ioc.bzf.busisigning.workflow.service.IFwWorkflowService;
import com.bonc.ioc.bzf.busisigning.workflow.vo.BaseFlowVo;
import com.bonc.ioc.bzf.busisigning.workflow.vo.CallBakParamsVo;
import com.bonc.ioc.bzf.busisigning.workflow.vo.OaCreateWorkFlowRequestV2Vo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


@Slf4j
@Service
public class BbsAdjustServiceImpl implements IBbsAdjustService {

    @Resource
    private BbpaymentFeignClient bbpaymentFeignClient;

    @Resource
    private BbctContractFeignClient contractFeignClient;

    @Resource
    private IFwWorkflowService workflowService;

    @Resource
    private WorkflowCommponent workflowCommponent;

    /**
     * 获取登录用户
     */
    @Resource
    private UserUtil userUtil;

    @Resource
    private IBbsiContractChangeService changeService;


    /**
     * 签约扩展相关 服务实例
     */
    @Resource
    private IBbsSignInfoExtService signInfoExtService;

    @Override
    public AppReply<PageResult<List<BbpmReceivableAdjustPageResultVo>>> selectAdjustList(BbpmReceivableAdjustPageVo vo) {
        return bbpaymentFeignClient.selectAdjustList(vo);
    }


    @Override
    public AppReply<BbpmReceivableAdjustVo> selectAdjustById(String id) {
        return bbpaymentFeignClient.selectAdjustById(id);
    }

    @Override
    public AppReply<BbpmReceivableAdjustVo> adjustSelectByCcid(String ccid) {
        return bbpaymentFeignClient.adjustSelectByCcid(ccid);
    }

    @Override
    public AppReply<BbpmReceivableAdjustVo> adjustSelectBySignId(String signId) {
        BbsiContractChangeVo changeVo =changeService.selectBySignId(signId);
        return bbpaymentFeignClient.adjustSelectByCcid(changeVo.getCcId());
    }

    @Override
    public AppReply saveAdjustById(BbpmReceivableAdjustVo vo) {
        return bbpaymentFeignClient.saveAdjustById(vo);
    }

    @Override
    public AppReply removeByAdjustId(String id) {
        AppReply appReply = bbpaymentFeignClient.removeByAdjustId(id);
        return appReply;
    }

    @Override
    public AppReply removeByRequestId(String requestId) {
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        String businessId = workflowService.requestId2BusinessId(requestId);
        if (StringUtils.isNotBlank(businessId)) {
            //获取数据
            AppReply<BbpmReceivableAdjustVo> appReplyAdjust = bbpaymentFeignClient.adjustSelectByCcid(businessId);
            if (AppReply.SUCCESS_CODE.equals(appReplyAdjust.getCode())) {
                BbpmReceivableAdjustVo adjustVo = appReplyAdjust.getData();
                BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
                entity.setCcId(adjustVo.getChangeId());
                entity.setDelFlag(DelFlagEnum.DELETED.getCode());
                if (!changeService.updateById(entity)) {
                    log.error("合同变更表删除失败, ccId={}, requestId={}", businessId, requestId);
                    throw new McpException("合同变更表删除失败" + businessId);
                }
                appReply = bbpaymentFeignClient.removeByAdjustId(adjustVo.getId());
                if (AppReply.SUCCESS_CODE.equals(appReply.getCode())) {
                    workflowService.removeByRequestId(requestId);
                }
            }else {
                throw new McpException("查询应收调整失败" + businessId);
            }
        } else {
            appReply = AppReply.error("没有对应的业务数据");
        }
        return appReply;
    }

    @Override
    public AppReply<PageResult<List<BbpmReceivableAdjustContractPageResultVo>>> selectByPageForAdjust(BbpmReceivableAdjustContractPageVo vo) {
        vo.setBusinessTypeCode1("02");
        return contractFeignClient.selectByPageForAdjust(vo);
    }

    @Override
    public AppReply<List<CalculationResultVo>> adjustBillView(CalculationParamVo vo) {
        return bbpaymentFeignClient.adjustBillView(vo);
    }


    @Override
    public AppReply<List<BbpmBillForAdjustPageVo>> selectBillForAdjust(String id) {
        return bbpaymentFeignClient.selectBillForAdjust(id);
    }

    @Override
    public AppReply<String[]> selectContractForAdjustId(String id) {
        return bbpaymentFeignClient.selectContractForAdjustId(id);
    }

    @Override
    public String adjustSaveWorkFlow(BbpmReceivableAdjustVo vo) {
        BbpmReceivableAdjustContractPageVo contractVo = new BbpmReceivableAdjustContractPageVo();
        contractVo.setContractNoForAdjust(vo.getContractNo());
        contractVo.setPageSize(1);
        contractVo.setPageNumber(1);
        contractVo.setBusinessTypeCode1("02");
        String accountHolder = vo.getAccountHolder();
        String bankCard = vo.getBankCard();
        String openingBank = vo.getOpeningBank();
        String openingBankName = vo.getOpeningBankName();
        String bankBranchCode = vo.getBankBranchCode();
        String bankBranchName = vo.getBankBranchName();

        AppReply<PageResult<List<BbpmReceivableAdjustContractPageResultVo>>> contractAppReply = contractFeignClient.selectByPageForAdjust(contractVo);
        BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
        if (AppReply.SUCCESS_CODE.equals(contractAppReply.getCode())) {
            if (contractAppReply.getData() != null && contractAppReply.getData().getTotal() > 0) {
                List<BbpmReceivableAdjustContractPageResultVo> contractList = contractAppReply.getData().getRows();
                if (contractList != null && contractList.size() > 0) {
                    BbpmReceivableAdjustContractPageResultVo contract = contractList.get(0);
                    BeanUtils.copyProperties(contract, vo);

                    //银行信息避免被覆盖
                    vo.setAccountHolder(accountHolder);
                    vo.setBankCard(bankCard);
                    vo.setOpeningBank(openingBank);
                    vo.setOpeningBankName(openingBankName);
                    vo.setBankBranchCode(bankBranchCode);
                    vo.setBankBranchName(bankBranchName);
                    //推送合同变更主表数据
                    BeanUtils.copyProperties(contract, entity);
                    entity.setDelFlag(DelFlagEnum.UNDELETED.getCode());
                    String userId = userUtil.getUserId();
                    entity.setChangeUserId(userId);
                    String userName = userUtil.getUserName(userId);
                    entity.setChangeUserName(userName);
                    if ("0".equals(vo.getAdjustStatus())) {
                        entity.setChangeStatus(ChangeStatusEnum.TEMPORARY.getCode());
                    } else {
                        entity.setChangeStatus(ChangeStatusEnum.WAIT_AUDIT.getCode());
                    }
                    entity.setChangeType(ChangeTypeEnum.OTHER_CHANGE.getCode());
                    entity.setChangeTypeItem(ContractChangeTypeEnum.ADJUST_CHANGE.getCode());
                    entity.setContractCode(contract.getContractNo());
                    entity.setSource("PC");
                    entity.setCustomerNameOld(contract.getCustomerName());
                    BbsiContractChangeEntity entityExsit = changeService.selectById(vo.getChangeId());
                    if (entityExsit != null) {//编辑
                        entity.setCcId(entityExsit.getCcId());
                        boolean flag = changeService.updateById(entity);
                        if (!flag) {
                            log.error("合同变更表更新失败:" + entity.toString());
                            throw new McpException("合同变更表更新失败");
                        }
                    } else {//新建
                        boolean flag = changeService.insert(entity);
                        if (!flag) {
                            log.error("合同变更表新增失败:" + entity.toString());
                            throw new McpException("合同变更表新增失败");
                        }
                    }
                }else {
                    throw new McpException("根据"+vo.getContractNo()+"查询不到合同信息");
                }
            }else {
                throw new McpException("根据"+vo.getContractNo()+"查询不到合同信息");
            }
        }else {
            throw new McpException("调用合同中心失败");
        }

        vo.setChangeId(entity.getCcId());
        AppReply<String> appReply = bbpaymentFeignClient.saveAdjustById(vo);
        if (StringUtils.isBlank(vo.getId())) {
            if (AppReply.SUCCESS_CODE.equals(appReply.getCode())) {
                String adjustId = appReply.getData();
                OaCreateWorkFlowRequestV2Vo requestV2Vo = new OaCreateWorkFlowRequestV2Vo();
                requestV2Vo.setBusinessId(entity.getCcId());
                if ("0".equals(vo.getAdjustStatus())) {
                    requestV2Vo.setIsnextflow(0);
                } else {
                    requestV2Vo.setIsnextflow(1);
                }
                requestV2Vo.setRemark(vo.getRemark());
                requestV2Vo.setBusinessTypeCode(BusinessTypeCodeEnum.CODE_003015.getCode());
                requestV2Vo.setWorkflowId(this.workflowService.selectWorkFlowId(BusinessTypeCodeEnum.CODE_003015.getCode()));
                requestV2Vo.setProjectId(vo.getProjectId());
                requestV2Vo.setRequestname(workflowCommponent.getWorkName(vo.getContractNo(), "1"));
                try {
                    log.error("发起流程：" + JSONObject.toJSONString(requestV2Vo));
                    workflowService.doCreateRequest(requestV2Vo);
                } catch (Exception e) {
                    log.error("发起流程失败：" + e.getMessage());
                    bbpaymentFeignClient.removeByAdjustId(adjustId);
                    changeService.deleteByIdRecord(entity.getCcId());
                    throw new McpException(e.getMessage());
                }
            }
        }else {
            if (!"0".equals(vo.getAdjustStatus())) {
                BaseFlowVo flowVo = new BaseFlowVo();
                if((vo.getRequestId()!=null)) {
                    flowVo.setRequestId(vo.getRequestId().toString());
                }else{
                    throw new McpException("requestId不能为空");
                }
                this.workflowService.submitRequest(flowVo);
            }
        }
        return appReply.getData();
    }

    @Override
    public AppReply<Object> finishAdjust(CallBakParamsVo vo) {
        String busienssId = workflowService.requestId2BusinessId(vo.getRequestId());
        if (StringUtils.isNotBlank(busienssId)) {
            //获取数据
            AppReply<BbpmReceivableAdjustVo> appReply = bbpaymentFeignClient.adjustSelectByCcid(busienssId);
            if (AppReply.SUCCESS_CODE.equals(appReply.getCode())) {
                BbpmReceivableAdjustVo adjustVo = appReply.getData();
                BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
                if(WhetherEnum.YES.getCode().equals(vo.getType())){
                    if(NodeKeyEnum.END.getCode().equalsIgnoreCase(vo.getNodeKey())) {
                        // 同意
                        //生成新的协议编号
                        String agreementCode = getContractCode("4", adjustVo.getContractNo());
                        entity.setCcId(adjustVo.getChangeId());
                        entity.setAgreementCode(agreementCode);
                        entity.setChangeStatus(ChangeStatusEnum.ALREADY_PASSED.getCode());
                        //推送上传结果审核数据
                        String signId = this.pushSignInfo(adjustVo,agreementCode);
                        if(StringUtils.isBlank(signId)){
                            throw new McpException("更新合同变更表signId时signId为空"+signId);
                        }
                    }
                }else if(WhetherEnum.NO.getCode().equals(vo.getType())){
                    // 驳回
                    entity.setCcId(adjustVo.getChangeId());
                    entity.setChangeStatus(ChangeStatusEnum.NO_PASS.getCode());
                }else{
                    throw new McpException("操作类型只能是 1、0"+vo.getType());
                }
                changeService.updateById(entity);
            }
        }else{
            throw new McpException("没有对应的业务数据");
        }
        return AppReply.success(vo.getRequestId());
    }

    @Override
    public String getContractCode(String modeId, String contractCode) {
        AppReply<String> appReply = contractFeignClient.contractCodeGeneration(modeId, contractCode);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || StringUtils.isBlank(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("合同中心生成编号失败[详情: %s, 合同编号: %s]", appReply, contractCode));
        }
        return appReply.getData();
    }

    @Override
    public String pushSignInfo(BbpmReceivableAdjustVo adjustVo,String agreementCode) {
        // 获取上级合同信息
        BbctContractManagementVo parentContractInfoVo = getContractInfoByContractCode(adjustVo.getContractNo());
        // 获取上级合同签约信息
        SigningSaveVo parentSignInfoVo = getSignInfoByContractCode(adjustVo.getContractNo());
        // 赋值签约信息(默认参数)
        SigningSaveVo signingSaveVo = setDefaultSignInfo(parentContractInfoVo, parentSignInfoVo,adjustVo,agreementCode);
        // 根据其他变更信息赋值签约信息
        setSignInfoByOtherChangeInfo(signingSaveVo, parentSignInfoVo,adjustVo);
        // 新增签约信息
        return insertSignInfo(signingSaveVo);
    }

    /**
     * 新增签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     * @return 签约id
     */
    protected String insertSignInfo(SigningSaveVo signingSaveVo) {
        log.info(String.format("合同变更信息推送签约请求报文[报文=%s]", JSON.toJSONString(signingSaveVo)));
        AppReply<String> appReply = signInfoExtService.saveSignDataContractChange(signingSaveVo);
        log.info(String.format("合同变更信息推送签约响应报文[报文=%s]", JSON.toJSONString(appReply)));
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("推送签约失败[详情:%s, 合同编号:%s, 协议编号:%s]", appReply,
                    signingSaveVo.getContractCode(), signingSaveVo.getParentContractCode()));
        }
        return appReply.getData();
    }


    /**
     * 根据其他变更信息赋值签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     * @param signingSaveVo 上级合同签约信息 vo实体
     */
    private void setSignInfoByOtherChangeInfo(SigningSaveVo signingSaveVo,
                                              SigningSaveVo parentSignInfoVo,BbpmReceivableAdjustVo adjustVo) {
        // 赋值合同模板信息
        signingSaveVo.setContractTemplateId(parentSignInfoVo.getContractTemplateId());
        signingSaveVo.setContractTemplateName(parentSignInfoVo.getContractTemplateName());
        // 赋值来源信息
        signingSaveVo.setParentContractCode(adjustVo.getContractNo());
        signingSaveVo.setContractType(ContractTypeEnum.OTHER_CHANGE.getCode());
        signingSaveVo.setProductSourceType(ContractChangeTypeEnum.ADJUST_CHANGE.getCode());
//        // 赋值子变更信息
//        List<AbstractSubChangeFactory> subChangeFactoryList = getSubChangeFactoryList();
//        for (AbstractSubChangeFactory subChangeFactory : subChangeFactoryList) {
//            subChangeFactory.setSignInfoByChangeInfo(signingSaveVo);
//        }
    }

    /**
     * 合同中心 查询合同信息
     *
     * @param contractCode 合同编号
     * @return 合同信息
     */
    protected BbctContractManagementVo getContractInfoByContractCode(String contractCode) {
        AppReply<BbctContractManagementVo> appReply = contractFeignClient.selectByIdNo(contractCode, null);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("合同中心生成编号失败[详情: %s, 合同编号: %s]", appReply, contractCode));
        }
        return appReply.getData();
//        AppReply<BbctContractManagementVo> appReply = feignServiceConfiguration.getContractFeignClient().selectByIdNo(contractCode, null);
//        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
//            throw new McpException(ResultUtils.createLog("查询合同信息失败[详情: %s, contractCode: %s]", appReply, contractCode));
//        }
//        return appReply.getData();
    }

    /**
     * 获取签约信息
     *
     * @param contractCode 合同编号
     * @return 签约信息
     */
    protected SigningSaveVo getSignInfoByContractCode(String contractCode) {
        return signInfoExtService.signView(null, contractCode, false);
    }

    /**
     * 赋值签约信息(默认参数)
     *
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentSignInfoVo     上级合同签约信息 vo实体
     * @return 签约信息
     */
    protected SigningSaveVo setDefaultSignInfo(BbctContractManagementVo parentContractInfoVo,
                                               SigningSaveVo parentSignInfoVo,BbpmReceivableAdjustVo adjustVo,String agreementCode) {
        SigningSaveVo signingSaveVo = new SigningSaveVo();
        // 根据上级合同信息赋值签约信息
        setSignInfoByParentContractInfo(signingSaveVo, parentContractInfoVo);
        // 根据上级合同签约信息赋值签约信息
        setSignInfoByParentSignInfo(signingSaveVo, parentSignInfoVo);
        // 根据合同变更信息赋值签约信息
        setSignInfoByChangeInfo(signingSaveVo,adjustVo,agreementCode);
        return signingSaveVo;
    }

    /**
     * 根据合同变更信息赋值签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     */
    private void setSignInfoByChangeInfo(SigningSaveVo signingSaveVo,BbpmReceivableAdjustVo adjustVo,String agreementCode) {
        signingSaveVo.setCcId(adjustVo.getChangeId());
        signingSaveVo.setContractCode(agreementCode);
        signingSaveVo.setParentContractCode(adjustVo.getContractNo());
        signingSaveVo.setTransactorType("1");
        signingSaveVo.setContractWatermark(WhetherEnum.NO.getCode());
        // 赋值委托代理人信息
        setConsignorInfo(signingSaveVo, "1");
    }

    /**
     * 赋值委托代理人信息
     *
     * @param signingSaveVo      签约信息 vo实体
     * @param currentTransactors 签约办理人类型
     */
    private void setConsignorInfo(SigningSaveVo signingSaveVo,
                                  String currentTransactors) {
        SaveConsignorInfoVo consignorInfoVo = new SaveConsignorInfoVo();
        consignorInfoVo.setType(currentTransactors);
        consignorInfoVo.setTransactorIdentityCardPhotoUrl(ListUtil.splitBySymbol("", SymbolConst.COMMA));
        signingSaveVo.setConsignorInfoVo(consignorInfoVo);
    }


    /**
     * 根据上级合同签约信息赋值签约信息
     *
     * @param signingSaveVo    签约信息 vo实体
     * @param parentSignInfoVo 上级合同签约信息 vo实体
     */
    private void setSignInfoByParentSignInfo(SigningSaveVo signingSaveVo,
                                             SigningSaveVo parentSignInfoVo) {
        signingSaveVo.setContractFees(parentSignInfoVo.getContractFees());
        signingSaveVo.setLeaseCycle(parentSignInfoVo.getLeaseCycle());
        signingSaveVo.setLeaseCycleValue(parentSignInfoVo.getLeaseCycleValue());
        signingSaveVo.setSeatInfoVoList(parentSignInfoVo.getSeatInfoVoList());
        signingSaveVo.setPropertyAddress(parentSignInfoVo.getPropertyAddress());
    }

    /**
     * 根据上级合同信息赋值签约信息
     *
     * @param signingSaveVo        签约信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     */
    private void setSignInfoByParentContractInfo(SigningSaveVo signingSaveVo,
                                                 BbctContractManagementVo parentContractInfoVo) {
        // 基础信息
        if ("9".equals(parentContractInfoVo.getCashPledgeCode())) {
            signingSaveVo.setCashPledgeValue(String.valueOf(parentContractInfoVo.getDeposit()));
        }
        signingSaveVo.setContractBeginTime(DateUtils.formatDateTime(parentContractInfoVo.getContractBeginTime()));
        signingSaveVo.setContractEndTime(DateUtils.formatDateTime(parentContractInfoVo.getContractEndTime()));
        signingSaveVo.setBusinessFormatStr(parentContractInfoVo.getBusinessFormat());
        signingSaveVo.setBusinessFormatName(parentContractInfoVo.getBusinessFormatName());
        signingSaveVo.setPaymentCycleCode(parentContractInfoVo.getPaymentCycleCode());
        signingSaveVo.setCashPledgeCode(parentContractInfoVo.getCashPledgeCode());
        signingSaveVo.setFirstBankNameCode(parentContractInfoVo.getFirstBankNameCode());
        signingSaveVo.setFirstBankName(parentContractInfoVo.getFirstBankName());
        signingSaveVo.setFirstAccountName(parentContractInfoVo.getFirstAccountName());
        signingSaveVo.setFirstAccountId(parentContractInfoVo.getFirstAccountId());
        // 客户信息
        BbctContractSignerVo userVo = parentContractInfoVo.getUserList().get(0);
        signingSaveVo.setSecondBankTypeCode(userVo.getBankNameCode());
        signingSaveVo.setSecondBankTypeName(userVo.getBankName());
        signingSaveVo.setSecondBankNameCode(userVo.getBankSubbranchCode());
        signingSaveVo.setSecondBankName(userVo.getBankSubbranchName());
        signingSaveVo.setSecondAccountId(userVo.getBankCard());
        signingSaveVo.setSecondAccountName(userVo.getBankUserName());
        // 产品信息
        List<BbctContractSubjectMatterVo> parentProductList = parentContractInfoVo.getSubjectMatterList();
        List<BbsResultProductEntity> productList = new ArrayList<>();
        for (BbctContractSubjectMatterVo parentProductInfo : parentProductList) {
            BbsResultProductEntity resultProductEntity = new BbsResultProductEntity();
            resultProductEntity.setProductNo(parentProductInfo.getProductNo());
            productList.add(resultProductEntity);
        }
        signingSaveVo.setPropertyAddress(productList);
    }

    /**
     * selectByPageRecord 账单管理--分页查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    public AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> selectByPageRecord(BbpmBillManagementPageVo vo) {

//        if (StringUtils.isNotBlank(vo.getTenantCode())) {
//            log.info("前台传的用户id" + vo.getTenantCode());
//            vo.setTenantCode(iBbcustomerService.getCustomerIdByUserId());
//        }

        //app请求接口必须有这个参数，不选择也带着这个参数，默认就是0。后台处理
        if (vo.getChargeSubjectPeriod() != null && vo.getChargeSubjectPeriod().intValue() == 0) {
            vo.setChargeSubjectPeriod(null);
        }
//
//        if(StringUtils.isBlank(vo.getHouseName())){
//            vo.setHouseName(vo.getHouseName1());
//        }

        AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> pageResultAppReply = bbpaymentFeignClient.selectByPageRecord(vo);
        return pageResultAppReply;


    }

    @Override
    public void createAdjust(String ccid) {
        //获取数据
        AppReply<BbpmReceivableAdjustVo>  appReply = bbpaymentFeignClient.adjustSelectByCcid(ccid);
        if(AppReply.SUCCESS_CODE.equals(appReply.getCode())){
            BbpmReceivableAdjustVo vo = appReply.getData();
            vo.setAdjustStatus("3");
            //修改数据
            this.bbpaymentFeignClient.saveAdjustById(vo);
        }
    }
}
