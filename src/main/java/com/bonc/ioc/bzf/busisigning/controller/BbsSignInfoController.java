package com.bonc.ioc.bzf.busisigning.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.bonc.ioc.bzf.busisigning.dao.BbsSignInfoMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsSignInfoEntity;
import com.bonc.ioc.bzf.busisigning.feign.feign.*;
import com.bonc.ioc.bzf.busisigning.feign.vo.*;
import com.bonc.ioc.bzf.busisigning.feign.vo.ChargeBankResultVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsResultCustomerService;
import com.bonc.ioc.bzf.busisigning.service.IBbsSignInfoExtService;
import com.bonc.ioc.bzf.busisigning.service.IBbsSignInfoService;
import com.bonc.ioc.bzf.busisigning.utils.DateUtils;
import com.bonc.ioc.bzf.busisigning.utils.ExportExcel;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoPageVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoVo;
import com.bonc.ioc.bzf.busisigning.workflow.vo.BaseFlowVo;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 签约表 前端控制器
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by liwenqiang for init
 */
@Slf4j
@RestController
@RequestMapping("/sign")
@Api(tags = "签约")
@Validated
public class BbsSignInfoController extends McpBaseController {

    @Resource
    private IBbsSignInfoService baseService;
    @Resource
    private BbsSignInfoMapper signInfoMapper;
    @Autowired
    private YecaiFeignClient yecaiFeignClient;
    @Autowired
    private BfipSettlementFeignClient feignClient;
    @Autowired
    private BbHousingFeignClient bbHousingFeignClient;
    @Resource
    private BbctContractFeignClient bbctContractFeignClient;
    @Autowired
    private BzfSubbranchFeignClient bzfSubbranchFeignClient;
    @Resource
    private IBbsSignInfoExtService bbsSignInfoExtService;
    @Autowired
    private BzfBusinessMessageFeignClient bbmessageFeignClient;
    @Autowired
    private BbctPaymentV2FeignClient bbctPaymentV2FeignClient;
    @Autowired
    private IBbsResultCustomerService bbsResultCustomerService;
//    @Autowired
//    private BzfSystemCommercialFeignClient bzfSystemCommercialFeignClient;

    @Autowired
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    /**
     * <p>获取签约统计个数据信息
     *
     * @return
     */
    @GetMapping(value = "/getSignBasicNumInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "刘兴广")
    @ApiOperation(value = "获取签约统计个数据信息", notes = "获取签约统计个数据信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<SignBasicNumInfoVo> getSignBasicNumInfo() {
        AppReply<SignBasicNumInfoVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        SignBasicNumInfoVo vo = bbsSignInfoExtService.getSignBasicNumInfo();
        appReply.setData(vo);
        return appReply;
    }

    /**
     * <p>获取指定年签约业态统计结果(列表)
     *
     * @param year 年份（4位）
     * @return
     */
    @GetMapping(value = "/getSignBusinessFormatStatisticsInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "刘兴广")
    @ApiOperation(value = "获取指定年签约业态统计结果(列表)", notes = "获取指定年签约业态统计结果(列表)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<SignBusinessFormatStatisticsInfoVo>> getSignBusinessFormatStatisticsInfo(@ApiParam(value = "年份（4位）", required = true) @RequestParam() String year) {
        AppReply<List<SignBusinessFormatStatisticsInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        List<SignBusinessFormatStatisticsInfoVo> vo = bbsSignInfoExtService.getSignBusinessFormatStatisticsInfo(year);
        appReply.setData(vo);
        return appReply;
    }

    /**
     * <p>获取指定年签约项目排名结果(列表)
     *
     * @param year 年份（4位）
     * @return
     */
    @GetMapping(value = "/getSignProjectRankingInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "刘兴广")
    @ApiOperation(value = "获取指定年签约项目排名结果(列表)", notes = "获取指定年签约项目排名结果(列表)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<SignProjectRankingInfoVo>> getSignProjectRankingInfo(@ApiParam(value = "年份（4位）", required = true) @RequestParam() String year) {
        AppReply<List<SignProjectRankingInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        List<SignProjectRankingInfoVo> vo = bbsSignInfoExtService.getSignProjectRankingInfo(year);
        appReply.setData(vo);
        return appReply;
    }

    /**
     * insertRecord 新增
     *
     * @param vo 需要新增的记录
     * @return com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "签约表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsSignInfoVo vo) {
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要新增的记录 列表
     * @return com.bonc.ioc.common.util.AppReply<List < String>> 返回新增后的主键 列表
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    @PostMapping(value = "/batch/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "liwenqiang")
    @ApiOperation(value = "批量新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增主键集合")})
    public AppReply<List<String>> insertBatchRecord(@ApiParam(value = "签约表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbsSignInfoVo> voList) {
        AppReply<List<String>> appReply = new AppReply<List<String>>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.insertBatchRecord(voList));
        return appReply;
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param signId 需要删除的签约id
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    @PostMapping(value = "/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "liwenqiang")
    @ApiOperation(value = "根据主键删除", notes = "根据主键删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdRecord(@ApiParam(value = "需要删除的签约id", required = false) @RequestBody String signId) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.removeByIdRecord(signId);
        return appReply;
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param signIdList 需要删除的签约id集合
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    @PostMapping(value = "/batch/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "liwenqiang")
    @ApiOperation(value = "根据主键删除-批量", notes = "根据主键批量删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdsRecord(@ApiParam(value = "需要删除的签约id", required = false) @RequestBody List<String> signIdList) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.removeByIdsRecord(signIdList);
        return appReply;
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的签约表
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    @PostMapping(value = "/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "liwenqiang")
    @ApiOperation(value = "根据主键更新", notes = "根据主键更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateByIdRecord(@ApiParam(value = "需要更新的签约表", required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbsSignInfoVo vo) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.updateByIdRecord(vo);
        return appReply;
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的签约表
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    @PostMapping(value = "/batch/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "liwenqiang")
    @ApiOperation(value = "根据主键更新-批量", notes = "根据主键批量更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateBatchByIdRecord(@ApiParam(value = "需要更新的签约表", required = false) @RequestBody @Validated(UpdateValidatorGroup.class) List<BbsSignInfoVo> voList) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.updateBatchByIdRecord(voList);
        return appReply;
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的签约表
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    @PostMapping(value = "/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "liwenqiang")
    @ApiOperation(value = "根据主键更新或新增", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveByIdRecord(@ApiParam(value = "需要更新或新增的签约表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsSignInfoVo vo) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.saveByIdRecord(vo);
        return appReply;
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的签约表
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    @PostMapping(value = "/batch/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "liwenqiang")
    @ApiOperation(value = "根据主键更新或新增-批量", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveBatchByIdRecord(@ApiParam(value = "需要更新或新增的签约表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbsSignInfoVo> voList) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.saveBatchByIdRecord(voList);
        return appReply;
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param signId 需要查询的签约id
     * @return com.bonc.ioc.common.util.AppReply 主键查询的数据
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "liwenqiang")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsSignInfoVo> selectByIdRecord(@ApiParam(value = "需要查询的签约id", required = false) @RequestParam(required = false) String signId) {
        AppReply<BbsSignInfoVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByIdRecord(signId));
        return appReply;
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "liwenqiang")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsSignInfoPageResultVo>>> selectByPageRecord(BbsSignInfoPageVo vo) {
        AppReply<PageResult<List<BbsSignInfoPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
    }

    /**
     * <p>获取商铺签约基本信息
     *
     * @param productNo 产品编码
     * @return
     */
    @GetMapping(value = "/getProductSignBasicInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "刘鹏伟")
    @ApiOperation(value = "获取商铺签约基本信息", notes = "获取商铺签约基本信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<ProductSignBasicInfoVo> getProductSignBasicInfo(@ApiParam(value = "产品编码", required = true) @RequestParam() String productNo) {
        AppReply<ProductSignBasicInfoVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        ProductSignBasicInfoVo vo = bbsSignInfoExtService.getProductSignBasicInfo(productNo);
        appReply.setData(vo);
        return appReply;
    }

    /**
     * <p>获取商铺签约基本信息(列表)
     *
     * @param productNo 产品编码
     * @return
     */
    @GetMapping(value = "/getProductSignBasicInfoList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 12, author = "刘鹏伟")
    @ApiOperation(value = "获取商铺签约基本信息(列表)", notes = "获取商铺签约基本信息(列表)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<ProductSignBasicInfoVo>> getProductSignBasicInfoList(@ApiParam(value = "产品编码集合(List)", required = true) @RequestParam() List<String> productNo) {
        AppReply<List<ProductSignBasicInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        List<ProductSignBasicInfoVo> vo = bbsSignInfoExtService.getProductSignBasicInfoList(productNo);
        appReply.setData(vo);
        return appReply;
    }

    /**
     * <p>获取客户签约基本信息(列表)
     *
     * @param customerNo   客户编码
     * @param customerType 客户类型（00:个人  01：企业）
     * @return
     */
    @GetMapping(value = "/getCustomerSignBasicInfoList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 13, author = "刘兴广")
    @ApiOperation(value = "获取客户的签约基本信息(列表)", notes = "获取客户的签约基本信息(列表)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<CustomerSignBasicInfoVo>> getCustomerSignBasicInfoList(@ApiParam(value = "客户编码", required = true) @RequestParam() String customerNo, @ApiParam(value = "客户类型（00:个人  01：企业）", required = true) @RequestParam() String customerType) {
        AppReply<List<CustomerSignBasicInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        List<CustomerSignBasicInfoVo> vo = bbsSignInfoExtService.getCustomerSignBasicInfoList(customerNo, customerType);
        appReply.setData(vo);
        return appReply;
    }


    @GetMapping(value = "/selectAllContractTemplate", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 25, author = "weichao")
    @ApiOperation(value = "查询所有合同模板", notes = "查询所有合同模板")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbctContractTemplatePageResultVo>> selectAllContractTemplate(@RequestParam(value = "type", required = false) String type, @RequestParam(value = "isShow", required = false) String isShow) {
        return bbctContractFeignClient.selectAllContractTemplate(type, isShow);
    }

    @GetMapping(value = "/selectContractTemplateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 26, author = "weichao")
    @ApiOperation(value = "根据主键查询合同模板", notes = "根据主键查询合同模板")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbctContractTemplateVo> selectContractTemplateById(@RequestParam(value = "contractTemplateId", required = false) String contractTemplateId) {
        return bbctContractFeignClient.selectContractTemplateById(contractTemplateId);
    }

    @GetMapping(value = "/selectLeftTree", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 27, author = "weichao")
    @ApiOperation(value = "商铺地址-获取左侧树", notes = "商铺地址-获取左侧树(区县-小区-组团-楼栋-单元-楼层)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbhgHouseTreeVo>> selectLeftTree(BbhgHouseTreeParamVo bbhgHouseTreeParamVo) {
        return bbHousingFeignClient.selectLeftTree(bbhgHouseTreeParamVo);
    }

    @GetMapping(value = "/selectBscIntentionInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 28, author = "weichao")
    @ApiOperation(value = "租户-意向登记客户", notes = "租户-意向登记客户")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BscIntentionInfoPageResultVo>>> selectBscIntentionInfo(BscIntentionInfoPageVo bscIntentionInfoPageVo, @RequestParam(value = "signType", defaultValue = "1") String signType) {
        return bbsSignInfoExtService.selectBscIntentionInfo(bscIntentionInfoPageVo, signType);
    }

    @GetMapping(value = "/selectBscIntentionInfoByContractCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 28, author = "weichao")
    @ApiOperation(value = "根据合同编号查询意向登记客户", notes = "根据合同编号查询意向登记客户")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BscIntentionInfoPageResultVo>>> selectBscIntentionInfoByContractCode(BscIntentionInfoPageVo bscIntentionInfoPageVo, @RequestParam(value = "contractCode") String contractCode) {
        List<Map<String, Object>> customerList = bbsResultCustomerService.countByContractCode(contractCode);
        for (Map<String, Object> c : customerList) {
            String signType = (String) c.get("sign_type");
            bscIntentionInfoPageVo.setCustomerId((String) c.get("customer_no"));
            bscIntentionInfoPageVo.setProductCode((String) c.get("product_no"));
            AppReply<PageResult<List<BscIntentionInfoPageResultVo>>> appReply = bbsSignInfoExtService.selectBscIntentionInfo(bscIntentionInfoPageVo, signType);
            if (appReply != null && appReply.getData() != null && appReply.getData().getRows() != null && appReply.getData().getRows().size() > 0) {
                return appReply;
            }
        }
        return AppReply.error("该租户未完成意向审签，不支持续租");
    }

    @PostMapping(value = "/selectBbhgHouseInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 29, author = "weichao")
    @ApiOperation(value = "商铺地址-房源信息", notes = "商铺地址-房源信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbhgHouseInfoPageResultVo>>> selectBbhgHouseInfo(@RequestBody BbhgHouseInfoPageVo bbhgHouseInfoPageVo) {
        return bbsSignInfoExtService.selectBbhgHouseInfo(bbhgHouseInfoPageVo);
    }

    /**
     * <AUTHOR>
     * @date 2023-09-06 15：22
     * @version 1.0
     */
    @PostMapping(value = "/saveSignData", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 30, author = "ysx")
    @ApiOperation(value = "新增", notes = "新增多表数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增签约数据")})
    public AppReply saveSignData(@ApiParam(value = "签约数据", required = false) @RequestBody @Validated(InsertValidatorGroup.class) SigningSaveVo vo, @RequestParam(value = "opeState", required = false) String opeState) {
        log.debug("新增签约数据参数:{}", vo);
        return bbsSignInfoExtService.saveSignData(vo, opeState);
    }

    /**
     * <AUTHOR>
     * @date 2023-09-06 15：22
     * @version 1.0
     */
    @PostMapping(value = "/signDataCommit", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 30, author = "ysx")
    @ApiOperation(value = "保存", notes = "保存签约数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:保存签约数据")})
    public AppReply<String> signDataCommit(@ApiParam(value = "签约数据", required = false) @RequestBody @Validated(InsertValidatorGroup.class) SigningSaveVo vo) {
        //String signId = vo.getSignId();
        return bbsSignInfoExtService.signDataCommit(vo);
    }

    /**
     * 查询所有的未删除的消息模板
     *
     * @param messageType      消息类型
     * @param systemType       系统类型
     * @param templateTypeCode 类型编码
     * @return com.bonc.ioc.common.util.AppReply<java.util.List < com.bonc.ioc.bzf.business.message.message.vo.MessTemplateInfoPageResultVo>>
     * <AUTHOR>
     * @date 2022/08/18 17:15:44
     */
    @GetMapping(value = "/selectAllTemplate", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 31, author = "sqj")
    @ApiOperation(value = "查询消息模板", notes = "pc-查询消息模板", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<MessTemplateInfoPageResultVo>> selectAllTemplate(@ApiParam(value = "消息类型;01-站内信/通知消息；02-短信消息", required = true) @RequestParam String messageType,
                                                                          @ApiParam(value = "系统类型;固定值: signing", required = true) @RequestParam String systemType,
                                                                          @ApiParam(value = "消息模板类型编码 固定值: realSigninghandle") @RequestParam String templateTypeCode) {
        return bbmessageFeignClient.selectAllTemplate(messageType, systemType, templateTypeCode);
    }

    /**
     * 查询消息模板(v3)
     *
     * @param vo 消息模板请求参数 vo实体
     * @return 消息模板信息
     */
    @PostMapping(value = "/selectTemplateInfoByCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 31, author = "pyj")
    @ApiOperation(value = "查询消息模板(v3)", notes = "查询消息模板(v3)", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> selectTemplateInfoByCode(@RequestBody BbctMessageTemplateInfoVo vo) {
        return bbmessageFeignClient.selectTemplateInfoByCode(vo);
    }

    @GetMapping(value = "/prjectBankList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 32, author = "weichao")
    @ApiOperation(value = "项目银行列表", notes = "开户行(甲)列表，选择银行（总行列表，乙）")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public ChargeRespondVo<List<ChargeBankResultVo>> prjectBankList(ChargeBankParamsVo chargeBankParamsVo) {
        BankRequestVo<ChargeBankParamsVo> bankRequestVo = new BankRequestVo<>();
        bankRequestVo.setData(chargeBankParamsVo);
        return feignClient.listByProjectId(bankRequestVo);
    }

    @Resource
    private YongYouAdapter yongYouAdapter;

    @GetMapping(value = "/subbranchList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 33, author = "weichao")
    @ApiOperation(value = "支行列表", notes = "根据总行bankType获取支行列表")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<SubbankResultVo> subbranchList(@ApiParam(value = "支行类型: 取总行选择的值", required = true) @RequestParam String bankType) {
//        List<Map<String, String>> bankParam = ListUtil.of(MapUtil.of("bankType", bankType));
        String json = yongYouAdapter.selectPayingSubBank(bankType);
        log.debug("subbranchList:{}", json);
        return AppReply.success(JSONObject.parseObject(json, SubbankResultVo.class));
    }

    @GetMapping(value = "/getBankBranchCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 33, author = "liuxingguang")
    @ApiOperation(value = "工银支行列表（分页）", notes = "工银支行列表（分页）")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<SubbankResultVo> gySubbranchList(@RequestParam("projectId") String projectId, @ApiParam(value = "bankBranchName", required = false) @RequestParam String bankBranchName, @RequestParam("size") String size, @RequestParam("current") String current) {
        BankRequestVo<ChargeBankBranchParamsVo> bankBranchRequestVo = new BankRequestVo<>();
        ChargeBankBranchParamsVo chargeBankBranchParamsVo = new ChargeBankBranchParamsVo();
        chargeBankBranchParamsVo.setProjectId(projectId);
        chargeBankBranchParamsVo.setBankBranchName(bankBranchName);
        chargeBankBranchParamsVo.setSize(size);
        chargeBankBranchParamsVo.setCurrent(current);
        chargeBankBranchParamsVo.setFullPage("false");
        bankBranchRequestVo.setData(chargeBankBranchParamsVo);
        Long total = 0l;
        List<Map<String, Object>> resultList = new ArrayList<>();
        try {
            log.info("工银支行列表接口参数:{}", JSON.toJSONString(bankBranchRequestVo));
            String listBankBranchCodeReturnStr = bfipSettlementFeignClient.listBankBranchCode(bankBranchRequestVo);
            log.info("工银支行列表接口结果:{}", listBankBranchCodeReturnStr);
            Map<String, Object> returnMap = JSON.parseObject(listBankBranchCodeReturnStr, Map.class);
            if ("00000".equals(String.valueOf(returnMap.get("code")))) {
                Map<String, Object> returnMapData = (Map<String, Object>) returnMap.get("data");

                for (Map returnMapDataRecord : (List<Map>) returnMapData.get("records")) {
//                    Map<String,Object> tempResultItem=new HashMap<>();
//                    tempResultItem.put("code",returnMapDataRecord.get("bankBranchCode"));
//                    tempResultItem.put("name",returnMapDataRecord.get("bankBranchName"));
                    resultList.add(returnMapDataRecord);
                }
                total = Long.parseLong(returnMapData.get("total").toString());
            } else {
                throw new RuntimeException("调用工银支行列表接口失败，返回结果为：" + listBankBranchCodeReturnStr);
            }
        } catch (RuntimeException e) {
            e.printStackTrace();
            throw new RuntimeException("调用工银支行列表接口异常，异常信息为：" + e.getMessage());
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("rows", resultList);
        resultMap.put("total", total);
        return AppReply.success(resultMap);
    }

    @GetMapping(value = "/taxrateQusery", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 34, author = "weichao")
    @ApiOperation(value = "查询税率配置列表", notes = "查询税率配置列表")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public ChargeRespondVo<ChargePageVo<List<TaxrateResultVo>>> taxrateQusery(TaxrateParamVo taxrateParamVo) {
        BankRequestVo<TaxrateParamVo> bankRequestVo = new BankRequestVo<>();
        bankRequestVo.setData(taxrateParamVo);
        log.info("工银查询税率参数:{}", JSON.toJSONString(bankRequestVo));
        ChargeRespondVo<ChargePageVo<List<TaxrateResultVo>>> chargeRespondVo = feignClient.taxrateQusery(bankRequestVo);
        log.info("工银查询税率返回:{}", JSON.toJSONString(chargeRespondVo));
        if (chargeRespondVo.getData() == null || chargeRespondVo.getData().getRecords().size() == 0) {
            throw new McpException("查询税率异常:" + chargeRespondVo.getMessage());
        }
        return chargeRespondVo;
    }

    @GetMapping(value = "/selectHouseInfoByIdOrCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 36, author = "weichao")
    @ApiOperation(value = "根据房源id或房源标识查询房源详情", notes = "根据房源id或房源标识查询房源详情")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbhgHouseInfoPageResultVo> selectHouseInfoByIdOrCode(@ApiParam(value = "房源标识") @RequestParam("houseCode") String houseCode) {
        return baseService.selectBbhgProductHouseInfoByHouseProduct(houseCode, false);
    }

    /**
     * app签署合同
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-09-22
     * @since 1.0.0
     */
    @PostMapping(value = "/app/signContract", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "weichao")
    @ApiOperation(value = "app签署合同", notes = "app签署合同", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> signContract(@RequestBody BbcSignContractVo vo, String pdfFileId) {
        return bbsSignInfoExtService.signContract(vo, pdfFileId);
    }

    /**
     * app签约完成
     *
     * @param signId 签约Id
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-09-22
     * @since 1.0.0
     */
    @PostMapping(value = "/app/signing", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "weichao")
    @ApiOperation(value = "app签约完成", notes = "app签约完成", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> signing(String signId) {
        return AppReply.success(bbsSignInfoExtService.signing(signId));
    }


    /**
     * 根据合同编号查询入住信息
     *

     * @return
     */
    @PostMapping(value = "/app/getCheckinInfoByContractCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liuxingguang")
    @ApiOperation(value = "根据合同编号查询入住信息", notes = "根据合同编号查询入住信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply getCheckinInfoByContractcode(@RequestParam(value = "contractCode") String contractCode, @RequestParam(value = "customerId") String customerId) {
        return bbsSignInfoExtService.getCheckinInfoByContractcode(contractCode, customerId);
    }

    /**
     * selectByPageRecord 分页查询
     */
    @GetMapping(value = "/selectSignByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsSignInfoPageListResultVo>>> selectByPage(SelectByPageVo vo) {
        try {
            AppReply<PageResult<List<BbsSignInfoPageListResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.selectByPage(vo));
            return appReply;
        } catch (Exception e) {
            log.error("签约管理分页查询异常，异常信息为：" + e.getMessage(), e);
            AppReply<PageResult<List<BbsSignInfoPageListResultVo>>> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }

    }

    /**
     * selectCustomer 签约列表获取租户信息（卡片）
     *
     * @param signId 签约ID
     */
    @GetMapping(value = "/selectCustomer", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约列表获取租户信息", notes = "签约列表获取租户信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsResultCustomerVo> selectCustomer(@RequestParam("signId") String signId) {
        try {
            AppReply<BbsResultCustomerVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.selectCustomer(signId));
            return appReply;
        } catch (Exception e) {
            log.error("签约列表获取租户信息异常，异常信息为：" + e.getMessage(), e);
            AppReply<BbsResultCustomerVo> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 商铺地址级联查询
     */
    @GetMapping(value = "/addressCascadeQuery", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "商铺地址级联查询", notes = "商铺地址级联查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<AddressCascadeQueryResultVo>> addressCascadeQuery(AddressCascadeQueryVo queryVo) {
        try {
            AppReply<List<AddressCascadeQueryResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.addressCascadeQuery(queryVo));
            return appReply;
        } catch (Exception e) {
            log.error("签约列表获取租户信息异常，异常信息为：" + e.getMessage(), e);
            AppReply<List<AddressCascadeQueryResultVo>> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 签约终止
     *
     * @param signId 签约ID
     */
    @GetMapping(value = "/signStop", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约终止", notes = "签约终止")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply signStop(@RequestParam("signId") String signId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            bbsSignInfoExtService.signStop(signId);
            return appReply;
        } catch (Exception e) {
            log.error("签约终止异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 签约删除
     *
     * @param signId 签约ID
     */
    @GetMapping(value = "/deleteSign", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约删除", notes = "签约删除")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply deleteSign(@RequestParam("signId") String signId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            bbsSignInfoExtService.deleteSign(signId);
            return appReply;
        } catch (Exception e) {
            log.error("签约删除异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 签约撤回
     *
     * @param signId 签约ID
     */
    @GetMapping(value = "/recallSign", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约撤回", notes = "签约撤回")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply recallSign(@RequestParam("signId") String signId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            bbsSignInfoExtService.recallSign(signId);
            return appReply;
        } catch (Exception e) {
            log.error("签约撤回异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 签约预览/查看 合同信息
     *
     * @param signId   签约ID
     * @param viewType true-预览和单条查看
     */
    @GetMapping(value = "/signView", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约预览/查看 合同信息", notes = "签约预览/查看 合同信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<SigningSaveVo> signView(@RequestParam(value = "signId", required = false) String signId, @RequestParam(value = "contractCode", required = false) String contractCode, @RequestParam("viewType") Boolean viewType) {
        if (StrUtil.isEmpty(signId) && StrUtil.isEmpty(contractCode)) {
            throw new McpException("签约id和合同code不能同时为空");
        }
        return AppReply.success(bbsSignInfoExtService.signView(signId, contractCode, viewType));
    }

    /**
     * 获取合同管理 合同主要信息
     *
     * @param signId       签约id
     * @param contractCode 合同编号
     * @return 合同管理 合同主要信息
     */
    @GetMapping(value = "/getContractInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "pyj")
    @ApiOperation(value = "合同管理 合同主要信息", notes = "合同管理 合同主要信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<SigningSaveVo> getContractInfo(@RequestParam(value = "signId", required = false) String signId,
                                                   @RequestParam(value = "contractCode", required = false) String contractCode) {
        if (StrUtil.isEmpty(contractCode)) {
            throw new McpException("合同code不能同时为空");
        }
        return AppReply.success(bbsSignInfoExtService.getContractInfo(signId, contractCode));
    }

    /**
     * 查询签约类型 1签约 2续约
     *
     * @param signId 签约ID
     */
    @GetMapping(value = "/signType", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "查询签约类型 1签约 2续约", notes = "查询签约类型 1签约 2续约")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> signType(@RequestParam(value = "signId", required = false) String signId, @RequestParam(value = "contractCode", required = false) String contractCode) {
        if (StrUtil.isEmpty(signId) && StrUtil.isEmpty(contractCode)) {
            throw new McpException("签约id和合同code不能同时为空");
        }
        int signCount = ChainWrappers.lambdaQueryChain(signInfoMapper)
                .eq(StrUtil.isNotEmpty(signId), BbsSignInfoEntity::getSignId, signId)
                .eq(StrUtil.isNotEmpty(contractCode), BbsSignInfoEntity::getContractCode, contractCode)
                .list().size();
        if (signCount > 0) {
            return AppReply.success((Object) "1");
        } else {
            return AppReply.success((Object) "2");
        }
    }

    /**
     * 签约预览里 合同模板的预览与下载本地（合同模板）
     *
     * @param signId 签约ID
     * @param type   1-加水印 2-不加水印
     */
    @GetMapping(value = "/previewAndDownload", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "合同模板的预览与下载（合同模板）", notes = "签约预览里 合同模板的预览与下载（合同模板）")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<FIleResultVo> previewAndDownload(@RequestParam("signId") String signId, @RequestParam("type") String type, @RequestParam(value = "appFlag", required = false, defaultValue = "false") boolean appFlag) {
        try {
            AppReply<FIleResultVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.previewAndDownload(signId, type, appFlag));
            return appReply;
        } catch (Exception e) {
            log.error("签约预览里合同模板的预览与下载异常，异常信息为：" + e.getMessage(), e);
            AppReply<FIleResultVo> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 签约阅览里模板合同下载（短信下载）
     */
    @PostMapping(value = "/sendMessageDownTemplateContract", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约阅览里模板合同下载（短信下载）", notes = "签约阅览里模板合同下载（短信下载）")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Boolean> sendMessageDownTemplateContract(@RequestBody DownTemplateVo vo) {
        try {
            return bbsSignInfoExtService.sendMessageDownTemplateContract(vo.getSignId(), vo.getType(), vo.getText());
        } catch (Exception e) {
            log.error("签约阅览里模板合同下载（短信下载）异常，异常信息为：" + e.getMessage(), e);
            return new AppReply<>(AppReply.ERROR_CODE, e.getMessage(), null);
        }
    }

    /**
     * 签约阅览里模板合同下载（邮箱下载）
     */
    @PostMapping(value = "/downloadTemplateContractForEmail", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约阅览里模板合同下载（邮箱下载）", notes = "签约阅览里模板合同下载（邮箱下载）")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Boolean> downloadTemplateContractForEmail(@RequestBody DownTemplateVo vo) {
        try {
            return bbsSignInfoExtService.downloadTemplateContractForEmail(vo.getSignId(), vo.getType(), vo.getText());
        } catch (Exception e) {
            log.error("签约阅览里模板合同下载（邮箱下载）异常，异常信息为：" + e.getMessage(), e);
            return new AppReply<>(AppReply.ERROR_CODE, e.getMessage(), null);
        }

    }

    /**
     * 签约列表下载合同（下载已签字的纸质合同）
     *
     * @param signId 签约ID
     */
    @GetMapping(value = "/downloadContract", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约列表下载合同（下载已签字的纸质合同）", notes = "签约列表下载合同（下载已签字的纸质合同）")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<FIleResultVo> downloadContract(@RequestParam("signId") String signId) {
        try {
            AppReply<FIleResultVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.downloadContract(signId));
            return appReply;
        } catch (Exception e) {
            log.error("签约预览里合同模板的预览与下载异常，异常信息为：" + e.getMessage(), e);
            AppReply<FIleResultVo> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 签约查看 办理人信息
     *
     * @param signId 签约ID
     */
    @GetMapping(value = "/viewTransactors", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约查看 办理人信息", notes = "签约查看 办理人信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<ViewTransactorsVo> viewTransactors(@RequestParam("signId") String signId) {
        try {
            AppReply<ViewTransactorsVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.viewTransactors(signId));
            return appReply;
        } catch (Exception e) {
            log.error("签约查看 办理人信息异常，异常信息为：" + e.getMessage(), e);
            AppReply<ViewTransactorsVo> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 纸质签约本人办理获取本人信息
     */
    @GetMapping(value = "/getPersonInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "纸质签约本人办理获取本人信息", notes = "纸质签约本人办理获取本人信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PersonInfoResultVo> getPersonInfo(@RequestParam("signId") String signId) {
        try {
            AppReply<PersonInfoResultVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.getPersonInfo(signId));
            return appReply;
        } catch (Exception e) {
            log.error("=======================================签约查看 办理人信息异常，异常信息为：" + e.getMessage(), e);
            AppReply<PersonInfoResultVo> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 纸质合同签约 保存当前办理人信息
     */
    @PostMapping(value = "/saveConsignorInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "纸质合同签约 保存当前办理人信息", notes = "纸质合同签约 保存当前办理人信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> saveConsignorInfo(@RequestBody SaveConsignorInfoVo vo) {
        try {
            AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.saveConsignorInfo(vo));
            return appReply;
        } catch (Exception e) {
            log.error("===========================纸质合同签约保存当前办理人信息，异常信息为" + e.getMessage(), e);
            AppReply<String> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 工作流改成保存纸质合同
     */
    @PostMapping(value = "/savePaperContractsSubmit", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "工作流改成保存纸质合同", notes = "工作流改成保存纸质合同")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply savePaperContractsSubmit(@RequestBody SavePaperContractsVo vo) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.savePaperContractsSubmit(vo));
            return appReply;
        } catch (Exception e) {
            log.error("===========================保存纸质合同异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 保存纸质合同
     */
    @PostMapping(value = "/savePaperContracts", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "保存纸质合同", notes = "保存纸质合同")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply savePaperContracts(@RequestBody SavePaperContractsVo vo) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            bbsSignInfoExtService.savePaperContracts(vo);
            return appReply;
        } catch (Exception e) {
            log.error("===========================保存纸质合同异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 纸质合同提交
     *
     * @param signId 签约id
     */
    @GetMapping(value = "/paperContractsSubmit", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "纸质合同提交", notes = "纸质合同提交")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply paperContractsSubmit(@RequestParam("signId") String signId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            bbsSignInfoExtService.paperContractsSubmit(signId);
            return appReply;
        } catch (Exception e) {
            log.error("===========================纸质合同提交异常，异常信息为:" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

//    @GetMapping(value = "/paperContractsSubmitWorkflow", produces = "application/json;charset=UTF-8")
//    @ApiOperationSupport(order = 1, author = "liwenqiang")
//    @ApiOperation(value = "纸质合同提交", notes = "纸质合同提交")
//    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
//    public AppReply paperContractsSubmitWorkflow(BaseFlowVo param) {
//        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
//        bbsSignInfoExtService.paperContractsSubmitWorkflow(param);
//        return appReply;
//    }


    /**
     * 签约列表 导出
     *
     * @param signIds 签约ID集合
     */
    @PostMapping(value = "/exportExcel", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约列表 导出", notes = "签约列表 导出")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public void exportExcel(@RequestBody List<String> signIds, HttpServletResponse response) {
        try {
            String fileName = "签约列表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<BbsSignInfoPageListResultExcelVo> excelVos = bbsSignInfoExtService.exportExcel(signIds);
            new ExportExcel("签约列表", BbsSignInfoPageListResultExcelVo.class).setDataList(excelVos).write(response, fileName).dispose();
        } catch (Exception e) {
            log.error("============================签约列表导出异常，异常信息为：" + e.getMessage(), e);
        }
    }

    /**
     * 对外接口 查询所有项目ID和名称
     */
    @GetMapping(value = "/selectProductProjectInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "获取所有项目id和名称", notes = "获取所有项目id和名称")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<SelectProductProjectInfoVo>> selectProductProjectInfo() {
        try {
            AppReply<List<SelectProductProjectInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.selectProductProjectInfo());
            return appReply;
        } catch (Exception e) {
            log.error("===========================查询所有项目ID和名称异常，异常信息为:" + e.getMessage(), e);
            AppReply<List<SelectProductProjectInfoVo>> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 3.42. 分行银行编码以及名称分页查询接口
     *
     * <AUTHOR>
     * @date 2022-12-12
     * @change 2022-12-12 by ly for init
     * @since 1.0.0
     */
    @GetMapping(value = "/getListBankBranchCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "ly")
    @ApiOperation(value = "根据项目id获取银行-付款银行（工银3.42）", notes = "根据项目id获取银行-付款银行（工银3.42）", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult> getListBankBranchCode(@Validated(UpdateValidatorGroup.class) BankBranchCodeParamsVo bankBranchCodeParamsVo) {
        return bbctPaymentV2FeignClient.getListBankBranchCode(bankBranchCodeParamsVo);
    }
    /**
     * selectByPageRecord 分页查询
     */
    @GetMapping(value = "/selectByPageSupplementAgreementList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "补充协议签署分页查询", notes = "补充协议签署分页查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsSignInfoPageListResultVo>>> selectByPageSupplementAgreementList(SelectByPageVo vo) {
        try {
            AppReply<PageResult<List<BbsSignInfoPageListResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.selectByPageSupplementAgreementList(vo));
            return appReply;
        } catch (Exception e) {
            log.error("补充协议签署分页查询异常，异常信息为：" + e.getMessage(), e);
            AppReply<PageResult<List<BbsSignInfoPageListResultVo>>> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * selectByPageRecord 分页查询
     */
    @GetMapping(value = "/selectByPageUploadResultApproveList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "上传结果分页查询", notes = "上传结果补充协议签署分页查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsSignInfoPageListResultVo>>> selectByPageUploadResultApproveList(SelectByPageVo vo) {
        try {
            AppReply<PageResult<List<BbsSignInfoPageListResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(bbsSignInfoExtService.selectByPageUploadResultApproveList(vo));
            return appReply;
        } catch (Exception e) {
            log.error("上传结果查询异常，异常信息为：" + e.getMessage(), e);
            AppReply<PageResult<List<BbsSignInfoPageListResultVo>>> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * <AUTHOR>
     * @date 2023-09-06 15：22
     * @version 1.0
     */
    @PostMapping(value = "/saveSignDataContractChange", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 30, author = "ysx")
    @ApiOperation(value = "合同变更签约新增", notes = "新增多表数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增签约数据")})
    public AppReply<String> saveSignDataContractChange(@ApiParam(value = "签约数据", required = false) @RequestBody @Validated(InsertValidatorGroup.class) SigningSaveVo vo) {
        log.debug("合同变更签约新增签约数据参数:{}", vo);
        return bbsSignInfoExtService.saveSignDataContractChange(vo);
    }

    /**
     * 签约预览/查看 合同信息
     *
     * @param signId   签约ID
     * @param viewType true-预览和单条查看
     */
    @GetMapping(value = "/contractChangeSignView", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "合同变更签约预览/查看 合同信息", notes = "合同变更签约预览/查看 合同信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<SigningSaveVo> contractChangeSignView(@RequestParam(value = "signId", required = false) String signId, @RequestParam(value = "contractCode", required = false) String contractCode, @RequestParam("viewType") Boolean viewType) {
        if (StrUtil.isEmpty(signId) && StrUtil.isEmpty(contractCode)) {
            throw new McpException("签约id和合同code不能同时为空");
        }
        return AppReply.success(bbsSignInfoExtService.contractChangeSignView(signId, contractCode, viewType));
    }


//
//    /**
//     * 签约终止
//     *
//     * @param signId 签约ID
//     */
//    @GetMapping(value = "/signStopByRequestId", produces = "application/json;charset=UTF-8")
//    @ApiOperationSupport(order = 1, author = "liwenqiang")
//    @ApiOperation(value = "签约终止", notes = "签约终止")
//    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
//    public AppReply signStopByRequestId(@RequestParam("signId") String signId) {
//        try {
//            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
//            bbsSignInfoExtService.signStopByRequestId(signId);
//            return appReply;
//        } catch (Exception e) {
//            log.error("签约终止异常，异常信息为：" + e.getMessage(), e);
//            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
//            return appReply;
//        }
//    }

    /**
     * 签约删除
     *
     * @param
     */
    @GetMapping(value = "/deleteSignByRequestId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约删除", notes = "签约删除")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply deleteSignByRequestId(@RequestParam("requestId") String requestId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            bbsSignInfoExtService.deleteSignByRequestId(requestId);
            return appReply;
        } catch (Exception e) {
            log.error("签约删除异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }


    /**
     * 签约终止
     *
     */
    @GetMapping(value = "/signStopByBusinessId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约终止", notes = "签约终止")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply signStopByBusinessId(@RequestParam("businessId") String businessId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            bbsSignInfoExtService.signStopByBusinessId(businessId);
            return appReply;
        } catch (Exception e) {
            log.error("签约终止异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 合同变更签约终止
     *
     */
    @GetMapping(value = "/contractChangeSignStopByBusinessId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "合同变更签约终止", notes = "合同变更签约终止")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply contractChangeSignStopByBusinessId(@RequestParam("signId") String businessId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            bbsSignInfoExtService.contractChangeSignStopByBusinessId(businessId);
            return appReply;
        } catch (Exception e) {
            log.error("签约终止异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 签约删除
     *
     * @param businessId 签约ID
     */
    @GetMapping(value = "/deleteSignByBusinessId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约删除", notes = "签约删除")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply deleteSignByBusinessId(@RequestParam("businessId") String businessId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            bbsSignInfoExtService.deleteSignByBusinessId(businessId);
            return appReply;
        } catch (Exception e) {
            log.error("签约删除异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

}

