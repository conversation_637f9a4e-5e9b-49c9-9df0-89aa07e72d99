package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRefundInfoEntity;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctApproveVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSignerVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctRefundAmountDetailVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctStatusVo;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.workflow.vo.BbfwRequestBusinessMappingVo;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 退款申请表 服务类
 *
 * <AUTHOR>
 * @date 2024-08-28
 * @change 2024-08-28 by pyj for init
 */
public interface IBbsRefundInfoService extends IMcpBaseService<BbsRefundInfoEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbsRefundInfoVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbsRefundInfoVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param refundId 需要删除的退款id
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String refundId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param refundIdList 需要删除的退款id
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> refundIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的退款申请表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbsRefundInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的退款申请表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbsRefundInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的退款申请表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbsRefundInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的退款申请表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbsRefundInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param refundId 需要查询的退款id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    BbsRefundInfoVo selectByIdRecord(String refundId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbsRefundInfoPageResultVo>> selectByPageRecord(BbsRefundInfoPageVo vo);

    /**
     * 根据合同编号查询房源列表
     *
     * @param contractCode 合同编号
     * @return 房源列表
     */
    List<BbctContractSubjectMatterVo> selectProductListByContractCode(String contractCode);

    /**
     * 根据合同编号查询承租人信息
     *
     * @param contractCode 合同编号
     * @return 房源列表
     */
    BbctContractSignerVo selectCustomerByContractCode(String contractCode);

    /**
     * 根据主键查询退款金额明细
     *
     * @param refundId 退款id
     * @return 退款金额明细 vo实体
     */
    List<BbctRefundAmountDetailVo> selectRefundAmountListById(String refundId);

    /**
     * 根据签约id删除
     *
     * @param signId 签约id
     */
    void removeBySignId(String signId);

    /**
     * 退款申请提交
     *
     * @param idVo 主键id vo实体
     */
    BbfwRequestBusinessMappingVo refundApplySubmit(BbsIdVo idVo);

    /**
     * 处理退款申请业务审批
     *
     * @param approveVo 审批 vo实体
     */
    void dealBusinessApprove(BbctApproveVo approveVo);

    /**
     * 更新退款进度
     *
     * @param statusVo 状态 vo实体
     */
    void updateRefundProcess(BbctStatusVo statusVo);

    /**
     * 结算中心更新退款进度
     *
     * @param statusVo 状态 vo实体
     */
    void chargeUpdateRefundProcess(BbctStatusVo statusVo);
}
