package com.bonc.ioc.bzf.busisigning.factory.change.otherchange;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.consts.SymbolConst;
import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsSignFpIntervalEntity;
import com.bonc.ioc.bzf.busisigning.enums.BeforeAndAfterEnum;
import com.bonc.ioc.bzf.busisigning.enums.ContractChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.ReductionProductTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.StandardTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.WhetherEnum;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctChargeRuleSubParamsBigVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.utils.DateUtils;
import com.bonc.ioc.bzf.busisigning.utils.JsonToObjectUtil;
import com.bonc.ioc.bzf.busisigning.utils.ListUtil;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.vo.JsonVo.IncrementalInfoArray;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.*;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.ContractOtherInfo;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.FreeSectionVo;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.FreeVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillParamsRoomChargeSubjectVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillparamsRoomChargeIncreaseRule;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillparamsRoomChargePreferentRule;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsParamsRoomVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsParamsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 缩租面积变更 工厂类
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
@Slf4j
public class LeaseAreaReductionChangeFactory extends AbstractSubChangeFactory {

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param contractChangeVo             合同变更信息 vo实体
     */
    public LeaseAreaReductionChangeFactory(FeignServiceConfiguration feignServiceConfiguration,
                                           BusinessServiceConfiguration businessServiceConfiguration,
                                           BbsiContractChangeVo contractChangeVo) {
        super(feignServiceConfiguration,
                businessServiceConfiguration,
                contractChangeVo);
    }

    /**
     * 赋值试算账单请求参数
     *
     * @param previewBillsParamsVo 试算账单请求参数 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentSignInfoVo     上级合同签约信息 vo实体
     */
    @Override
    public void setPreviewBillsParams(PreviewBillsParamsVo previewBillsParamsVo,
                                      BbctContractManagementVo parentContractInfoVo,
                                      SigningSaveVo parentSignInfoVo) {
        previewBillsParamsVo.setChangeAccountingPeriodType(contractChangeVo.getReduceEffectiveExecutionDate());
        previewBillsParamsVo.setCutShortRoomList(createCutShortRoomList());
        setRoomList(previewBillsParamsVo, parentContractInfoVo);
    }

    /**
     * 赋值预览信息
     *
     * @param previewInfoParamsVo 预览信息参数 vo实体
     */
    @Override
    public void setPreviewInfo(BbctPreviewInfoParamsVo previewInfoParamsVo) {
        return;
    }

    /**
     * 根据变更信息赋值签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     */
    @Override
    public void setSignInfoByChangeInfo(SigningSaveVo signingSaveVo) {
        List<BbsResultProductEntity> resultList = new ArrayList<>();
        List<BbsResultProductEntity> productList = signingSaveVo.getPropertyAddress();
        // 处理缩租的产品
        deleteReturnedProductToProductInfoByChangeInfo(resultList, productList);
        // 处理拆分后已选的产品
        setSplitAndChooseProductInfoByChangeInfo(resultList);
        signingSaveVo.setPropertyAddress(resultList);
    }

    /**
     * 根据变更信息从产品信息排除已退产品
     *
     * @param resultList  处理后的产品信息列表
     * @param productList 处理前产品信息列表
     */
    private void deleteReturnedProductToProductInfoByChangeInfo(List<BbsResultProductEntity> resultList,
                                                                List<BbsResultProductEntity> productList) {
        // 已退产品
        List<BbsChangeShopInfoVo> returnedProductList = businessServiceConfiguration
                .getChangeShopInfoService()
                .selectByCcIdAndType(contractChangeVo.getCcId(), ReductionProductTypeEnum.RETURNED.getCode());
        Set<String> excludeSet = new HashSet<>();
        for (BbsChangeShopInfoVo returnedProductInfo : returnedProductList) {
            excludeSet.add(returnedProductInfo.getProductNo());
        }
        // 已拆分产品
        List<BbsChangeShopInfoVo> splitProductList = businessServiceConfiguration
                .getChangeShopInfoService()
                .selectSplitProductByCcId(contractChangeVo.getCcId());
        for (BbsChangeShopInfoVo splitProductInfo : splitProductList) {
            excludeSet.add(splitProductInfo.getProductNoOld());
        }
        // 排除掉要排除的产品
        for (BbsResultProductEntity productInfo : productList) {
            if (!excludeSet.contains(productInfo.getProductNo())) {
                resultList.add(productInfo);
            }
        }
    }

    /**
     * 根据变更信息赋值拆分并且已选的产品信息
     *
     * @param resultList 处理后的产品信息列表
     */
    private void setSplitAndChooseProductInfoByChangeInfo(List<BbsResultProductEntity> resultList) {
        List<BbsChangeShopInfoVo> splitAndChooseProductList = businessServiceConfiguration
                .getChangeShopInfoService()
                .selectSplitProductByCcIdAndType(contractChangeVo.getCcId(), ReductionProductTypeEnum.CHOOSE.getCode());
        for (BbsChangeShopInfoVo splitAndChooseProductInfo : splitAndChooseProductList) {
            BbsResultProductEntity resultProductEntity = new BbsResultProductEntity();
            resultProductEntity.setProductNo(splitAndChooseProductInfo.getProductNo());
            resultProductEntity.setContractChangeType(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode());
            resultList.add(resultProductEntity);
        }
    }

    /**
     * 生成缩减产品列表
     *
     * @return 缩减产品列表
     */
    private List<String> createCutShortRoomList() {
        Set<String> productNoSet;
        if (Objects.isNull(contractChangeVo.getShopInfoVoList())) {
            productNoSet = businessServiceConfiguration
                    .getChangeShopInfoService()
                    .selectReturnedProductNoByCcId(contractChangeVo.getCcId());
        } else {
            productNoSet = new HashSet<>();
            List<BbsChangeShopInfoVo> shopInfoList = contractChangeVo.getShopInfoVoList();
            for (BbsChangeShopInfoVo shopInfoVo : shopInfoList) {
                if (WhetherEnum.YES.getCode().equals(shopInfoVo.getIsSplit())) {
                    productNoSet.add(shopInfoVo.getProductNoOld());
                } else {
                    if(ReductionProductTypeEnum.RETURNED.getCode().equals(shopInfoVo.getType())){
                        productNoSet.add(shopInfoVo.getProductNo());
                    }
                }
            }
        }
        return new ArrayList<>(productNoSet);
    }

    /**
     * 赋值试算需要的产品列表
     *
     * @param previewBillsParamsVo 试算账单请求参数 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     */
    private void setRoomList(PreviewBillsParamsVo previewBillsParamsVo,
                             BbctContractManagementVo parentContractInfoVo) {
        List<PreviewBillsParamsRoomVo> roomList;
        if (Objects.isNull(previewBillsParamsVo.getRoomList())) {
            roomList = new ArrayList<>();
        } else {
            roomList = previewBillsParamsVo.getRoomList();
        }
        // 获取已选的拆分类型的产品列表
        List<BbsChangeShopInfoVo> changeProductList = getChangeProductList();
        Map<String, BbctSizeVo> parentProductMap = new HashMap<>();
        for (BbsChangeShopInfoVo changeProductVo : changeProductList) {
            setParentProductMap(parentProductMap, changeProductVo);
            roomList.add(createRoomInfo(changeProductVo, parentContractInfoVo, parentProductMap));
        }
        previewBillsParamsVo.setRoomList(roomList);
    }

    /**
     * 获取已选的拆分类型的产品列表
     *
     * @return 已选的拆分类型的产品列表
     */
    private List<BbsChangeShopInfoVo> getChangeProductList() {
        if (Objects.isNull(contractChangeVo.getShopInfoVoList())) {
            return businessServiceConfiguration
                    .getChangeShopInfoService()
                    .selectSplitProductByCcIdAndType(contractChangeVo.getCcId(), ReductionProductTypeEnum.CHOOSE.getCode());
        } else {
            List<BbsChangeShopInfoVo> resultList = new ArrayList<>();
            List<BbsChangeShopInfoVo> shopInfoList = contractChangeVo.getShopInfoVoList();
            for (BbsChangeShopInfoVo shopInfoVo : shopInfoList) {
                if (WhetherEnum.YES.getCode().equals(shopInfoVo.getIsSplit()) &&
                        ReductionProductTypeEnum.CHOOSE.getCode().equals(shopInfoVo.getType())) {
                    resultList.add(shopInfoVo);
                }
            }
            return resultList;
        }
    }

    /**
     * 赋值上级产品信息map集
     *
     * @param parentProductMap 上级产品信息map集
     * @param changeProductVo  变更的产品信息 vo实体
     */
    private void setParentProductMap(Map<String, BbctSizeVo> parentProductMap,
                                     BbsChangeShopInfoVo changeProductVo) {
        if (parentProductMap.containsKey(changeProductVo.getProductNoOld())) {
            BbctSizeVo sizeVo = parentProductMap.get(changeProductVo.getProductNoOld());
            sizeVo.setCurrentSize(sizeVo.getCurrentSize() + 1);
            parentProductMap.put(changeProductVo.getProductNoOld(), sizeVo);
        } else {
            int splitTotalSize = businessServiceConfiguration
                    .getChangeShopInfoService()
                    .getSplitSizeByCcIdAndParentProduct(contractChangeVo.getCcId(), changeProductVo.getProductNoOld());
            BbctSizeVo sizeVo = new BbctSizeVo();
            sizeVo.setCurrentSize(1);
            sizeVo.setTotalSize(splitTotalSize);
            parentProductMap.put(changeProductVo.getProductNoOld(), sizeVo);
        }
    }

    /**
     * 生成试算的产品信息
     *
     * @param changeProductVo      变更的产品信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentProductMap     上级产品信息map集
     * @return 试算的产品信息
     */
    private PreviewBillsParamsRoomVo createRoomInfo(BbsChangeShopInfoVo changeProductVo,
                                                    BbctContractManagementVo parentContractInfoVo,
                                                    Map<String, BbctSizeVo> parentProductMap) {
        PreviewBillsParamsRoomVo roomVo = new PreviewBillsParamsRoomVo();
        roomVo.setHouseId(changeProductVo.getProductNo());
        roomVo.setHouseName(changeProductVo.getProductName());
        roomVo.setBeforeHouseId(changeProductVo.getProductNoOld());
        List<PreviewBillParamsRoomChargeSubjectVo> chargeSubjectList = new ArrayList<>();
        // 租金计费规则
        chargeSubjectList.add(createRentChargeSubject(changeProductVo, parentContractInfoVo));
        // 保证金计费规则
        chargeSubjectList.add(createCashPledgeChargeSubject(changeProductVo, parentContractInfoVo, parentProductMap));
        // 物业费计费规则
        // 判断合同是否存在物业费 并存储（从老合同扩展字段取）
        ContractOtherInfo contractOtherInfo = JsonToObjectUtil.jsonToContractOtherInfo(parentContractInfoVo.getContractExtend());
        log.info("合同信息扩展为："+parentContractInfoVo.getContractExtend());
        if (contractOtherInfo != null) {
            //有物业费
            if("02-08".equals(contractOtherInfo.getContractFees())){
                //从产品扩展字段解析物业费标准
                for (BbctContractSubjectMatterVo subVo : parentContractInfoVo.getSubjectMatterList()) {
                    if (changeProductVo.getProductNoOld().equals(subVo.getProductNo())) {
                        //从产品扩展字段解析物业费标准
                        log.info("老房子物业费json："+subVo.getProductExtend());
                        if(StringUtils.isNotBlank(subVo.getProductExtend())){
                            StandardVo propStandardVo = JsonToObjectUtil.jsonToProductExtend(subVo.getProductExtend());
                            if(propStandardVo != null){
                                chargeSubjectList.add(createPropertyChargeSubject(changeProductVo, parentContractInfoVo,propStandardVo.getStandardUnit(),propStandardVo.getStandard()));
                            }
                        }
                    }
                }
            }
        }
        roomVo.setChargeSubjectList(chargeSubjectList);
        return roomVo;
    }

    /**
     * 生成租金计费规则
     *
     * @param changeProductVo      变更后的产品信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @return 租金计费规则
     */
    private PreviewBillParamsRoomChargeSubjectVo createRentChargeSubject(BbsChangeShopInfoVo changeProductVo,
                                                                         BbctContractManagementVo parentContractInfoVo) {
        PreviewBillParamsRoomChargeSubjectVo rentChargeSubjectVo = new PreviewBillParamsRoomChargeSubjectVo();
        rentChargeSubjectVo.setChargeSubjectNo("01");
        rentChargeSubjectVo.setCyclicOrSingle("01");
        rentChargeSubjectVo.setChargeSubjectPeriod(Integer.valueOf(parentContractInfoVo.getPaymentCycleCode()));
        rentChargeSubjectVo.setAmountType("0" + changeProductVo.getRentStandardUnit());
        rentChargeSubjectVo.setParamList((JSONObject) JSON.toJSON(new BbctChargeRuleSubParamsBigVo()));
        BbctChargeRuleSubParamsBigVo chargeRuleSubParamsVo = new BbctChargeRuleSubParamsBigVo();
        chargeRuleSubParamsVo.setPARAMPRICE(String.valueOf(changeProductVo.getRentStandard()));
        chargeRuleSubParamsVo.setPARAMAREA(changeProductVo.getArea());
        rentChargeSubjectVo.setParamValueList((JSONObject) JSON.toJSON(chargeRuleSubParamsVo));
        ContractOtherInfo contractOtherInfo = JsonToObjectUtil.jsonToContractOtherInfo(parentContractInfoVo.getContractExtend());
        if (!Objects.isNull(contractOtherInfo)) {
            rentChargeSubjectVo.setTaxRate(BigDecimal.valueOf(contractOtherInfo
                            .getRentTaxRate())
                    .divide(BigDecimal.valueOf(100)));
        }
        rentChargeSubjectVo.setIncreaseRules(getChangeIncreaseRuleList(parentContractInfoVo));
        rentChargeSubjectVo.setPreferentRules(getParentPreferentRuleList(contractOtherInfo));
        return rentChargeSubjectVo;
    }

    /**
     * 生成保证金计费规则
     *
     * @param changeProductVo      变更后的产品信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentProductMap     上级产品信息map集
     * @return 保证金计费规则
     */
    private PreviewBillParamsRoomChargeSubjectVo createCashPledgeChargeSubject(BbsChangeShopInfoVo changeProductVo,
                                                                               BbctContractManagementVo parentContractInfoVo,
                                                                               Map<String, BbctSizeVo> parentProductMap) {
        PreviewBillParamsRoomChargeSubjectVo cashPledgeChargeSubjectVo = new PreviewBillParamsRoomChargeSubjectVo();
        cashPledgeChargeSubjectVo.setChargeSubjectNo("02");
        cashPledgeChargeSubjectVo.setCyclicOrSingle("02");
        cashPledgeChargeSubjectVo.setChargeSubjectPeriod(Integer.valueOf(parentContractInfoVo.getPaymentCycleCode()));
        if ("9".equals(parentContractInfoVo.getCashPledgeCode())) {
//            cashPledgeChargeSubjectVo.setDepositProportion(new BigDecimal(1));
            cashPledgeChargeSubjectVo.setChargeSubjectAmount(getSplitProductCashPledge(changeProductVo, parentContractInfoVo, parentProductMap));
        } else {
            cashPledgeChargeSubjectVo.setDepositProportion(new BigDecimal(parentContractInfoVo.getCashPledgeCode()));
            cashPledgeChargeSubjectVo.setParamList((JSONObject) JSON.toJSON(new BbctChargeRuleSubParamsBigVo()));
            BbctChargeRuleSubParamsBigVo chargeRuleSubParamsVo = new BbctChargeRuleSubParamsBigVo();
            chargeRuleSubParamsVo.setPARAMPRICE(String.valueOf(changeProductVo.getRentStandard()));
            chargeRuleSubParamsVo.setPARAMAREA(changeProductVo.getArea());
            cashPledgeChargeSubjectVo.setParamValueList((JSONObject) JSON.toJSON(chargeRuleSubParamsVo));
        }
        cashPledgeChargeSubjectVo.setTaxRate(BigDecimal.valueOf(0.00));
        return cashPledgeChargeSubjectVo;
    }

    /**
     * 获取拆分产品的保证金金额
     *
     * @param changeProductVo      变更后的产品信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentProductMap     上级产品信息map集
     * @return 拆分产品的保证金金额
     */
    private BigDecimal getSplitProductCashPledge(BbsChangeShopInfoVo changeProductVo,
                                                 BbctContractManagementVo parentContractInfoVo,
                                                 Map<String, BbctSizeVo> parentProductMap) {
        BbctSizeVo sizeVo = parentProductMap.get(changeProductVo.getProductNoOld());
        int splitSize = sizeVo.getTotalSize();
        BigDecimal parentProductCashPledge = getCashPledgeByBill(parentContractInfoVo.getContractNo(),
                parentContractInfoVo.getSubjectMatterList().get(0).getProjectId(),
                changeProductVo.getProductNameOld());
        if (splitSize < 2 || 0 == parentProductCashPledge.intValue()) {
            return parentProductCashPledge;
        }
        double splitProductCashPledge = parentProductCashPledge.doubleValue() / splitSize;
        if (0 == parentProductCashPledge.doubleValue() % splitSize) {
            return BigDecimal.valueOf(splitProductCashPledge);
        } else {
            if (sizeVo.getCurrentSize() < splitSize) {
                //向下取整
                return BigDecimal.valueOf(Math.floor(splitProductCashPledge));
            } else {
                //向上取整
                return BigDecimal.valueOf(Math.ceil(splitProductCashPledge));
            }
        }
    }

    /**
     * 获取变更后的递增规则
     *
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @return 变更后的递增规则
     */
    private List<PreviewBillparamsRoomChargeIncreaseRule> getChangeIncreaseRuleList(BbctContractManagementVo parentContractInfoVo) {
        List<PreviewBillparamsRoomChargeIncreaseRule> increaseRuleList = new ArrayList<>();
        List<String> changeTypeItemList = ListUtil.splitBySymbol(contractChangeVo.getChangeTypeItem(), SymbolConst.COMMA);
        if (changeTypeItemList.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())) {
            // 如果有租金变更，使用租金变更配置的递增规则
            addChangeIncreaseRule(increaseRuleList);
        } else {
            // 使用上级合同的递增规则
            addParentContractIncreaseRule(increaseRuleList, parentContractInfoVo);
        }
        return increaseRuleList;
    }

    /**
     * 添加合同变更后的递增规则
     *
     * @param increaseRuleList 递增规则列表
     */
    private void addChangeIncreaseRule(List<PreviewBillparamsRoomChargeIncreaseRule> increaseRuleList) {
        List<BbsChangeIncrementalConfigVo> changeIncrementalList = getChangeIncrementalConfigList();
        AtomicInteger count = new AtomicInteger(1);
        changeIncrementalList.forEach(incremental -> {
            PreviewBillparamsRoomChargeIncreaseRule increaseRules = new PreviewBillparamsRoomChargeIncreaseRule();
            increaseRules.setIncreaseRuleId(count.getAndIncrement());
            increaseRules.setIncreaseProportion(Convert.toBigDecimal(incremental.getIncrease()).divide(Convert.toBigDecimal(100)));
            increaseRules.setIncreasePeriod("year".equals(incremental.getUnit()) ? "04" : "01");
            increaseRules.setIncreaseType("appoint".equals(incremental.getAdjustmentPoint()) ? "02" : "01");
            increaseRules.setIncreaseOrder(incremental.getTimePoint());
            increaseRuleList.add(increaseRules);
        });
    }

    /**
     * 获取合同变更递增规则列表
     *
     * @return 合同变更递增规则列表
     */
    private List<BbsChangeIncrementalConfigVo> getChangeIncrementalConfigList() {
        if (Objects.isNull(contractChangeVo.getRentStandardJson())) {
            return businessServiceConfiguration
                    .getChangeIncrementalConfigService()
                    .selectByCcIdAndType(contractChangeVo.getCcId(), BeforeAndAfterEnum.AFTER.getCode(), StandardTypeEnum.RENT.getCode());
        } else {
            List<BbsChangeIncrementalConfigVo> resultList = new ArrayList<>();
            List<IncrementalInfoArray> incrementalInfoList = contractChangeVo.getRentStandardJson()
                    .getIncrementalInfo()
                    .getIncrementalInfoArray();
            for (IncrementalInfoArray incrementalInfo : incrementalInfoList) {
                BbsChangeIncrementalConfigVo changeIncrementalConfigVo = new BbsChangeIncrementalConfigVo();
                BeanUtils.copyProperties(incrementalInfo, changeIncrementalConfigVo);
                resultList.add(changeIncrementalConfigVo);
            }
            return resultList;
        }
    }

    /**
     * 添加合同变更后的递增规则
     *
     * @param increaseRuleList     递增规则列表
     * @param parentContractInfoVo 上级合同信息 vo实体
     */
    private void addParentContractIncreaseRule(List<PreviewBillparamsRoomChargeIncreaseRule> increaseRuleList,
                                               BbctContractManagementVo parentContractInfoVo) {
        IncrementExtendVo incrementExtendVo = JsonToObjectUtil.jsonToIncrementExtendVo(parentContractInfoVo.getIncreaseJson());
        if (!Objects.isNull(incrementExtendVo) &&
                !Objects.isNull(incrementExtendVo.getRentIncrementVo()) &&
                CollectionUtils.isNotEmpty(incrementExtendVo.getRentIncrementVo().getIncrementList())) {
            List<BbsSignIncrementalConfigVo> incrementalInfoList = incrementExtendVo.getRentIncrementVo().getIncrementList();
            AtomicInteger count = new AtomicInteger(1);
            for (BbsSignIncrementalConfigVo incrementalInfo : incrementalInfoList) {
                PreviewBillparamsRoomChargeIncreaseRule increaseRules = new PreviewBillparamsRoomChargeIncreaseRule();
                increaseRules.setIncreaseRuleId(count.getAndIncrement());
                increaseRules.setIncreaseProportion(Convert.toBigDecimal(incrementalInfo.getIncrease()).divide(Convert.toBigDecimal(100)));
                increaseRules.setIncreasePeriod("year".equals(incrementalInfo.getUnit()) ? "04" : "01");
                increaseRules.setIncreaseType("appoint".equals(incrementalInfo.getAdjustmentPoint()) ? "02" : "01");
                increaseRules.setIncreaseOrder(incrementalInfo.getTimePoint());
                increaseRuleList.add(increaseRules);
            }
        }
    }

    /**
     * 获取上级合同签约的优惠规则
     *
     * @param contractOtherInfo 合同扩展字段信息
     * @return 上级合同签约的优惠规则
     */
    private List<PreviewBillparamsRoomChargePreferentRule> getParentPreferentRuleList(ContractOtherInfo contractOtherInfo) {
        List<PreviewBillparamsRoomChargePreferentRule> preferentRuleList = new ArrayList<>();
        if (Objects.isNull(contractOtherInfo) || Objects.isNull(contractOtherInfo.getPropFreeVo())) {
            return preferentRuleList;
        }
        FreeVo freeVo = contractOtherInfo.getPropFreeVo();
        if ("0".equals(freeVo.getFreePeriodType())) {
            return preferentRuleList;
        } else if ("2".equals(freeVo.getFreePeriodType())) {
            PreviewBillparamsRoomChargePreferentRule preferentRules = new PreviewBillparamsRoomChargePreferentRule();
            preferentRules.setPreferentRuleId(1);
            preferentRules.setPreferentialType("01");
            preferentRules.setPreferentialBeginDate(freeVo.getFpFixedDate());
            preferentRules.setPreferentialEndDate(DateUtils.getDateAddDaySubtraction(freeVo.getFpFixedDate(), freeVo.getFpFixedValue()));
            preferentRuleList.add(preferentRules);
        } else if ("1".equals(freeVo.getFreePeriodType())) {
            List<FreeSectionVo> freeSectionList = freeVo.getFreeSections();
            AtomicInteger count = new AtomicInteger(1);
            freeSectionList.forEach(freeSection -> {
                PreviewBillparamsRoomChargePreferentRule preferentRules = new PreviewBillparamsRoomChargePreferentRule();
                preferentRules.setPreferentRuleId(count.getAndIncrement());
                preferentRules.setPreferentialType("01");
                preferentRules.setPreferentialBeginDate(freeSection.getStart());
                preferentRules.setPreferentialEndDate(freeSection.getEnd());
                preferentRuleList.add(preferentRules);
            });
        }
        return preferentRuleList;
    }

    /**
     * 生成租金计费规则--物业费
     *
     * @param changeProductVo      变更后的产品信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @return 物业费计费规则
     */
    private PreviewBillParamsRoomChargeSubjectVo createPropertyChargeSubject(BbsChangeShopInfoVo changeProductVo,
                                                                             BbctContractManagementVo parentContractInfoVo,
                                                                             String propStandardUnit, Double propStandard ) {
        //获取老合同物业费递增配置
        String increaseJson = parentContractInfoVo.getIncreaseJson();
        IncrementVo popVo = JsonToObjectUtil.jsonToPropIncrementVo(increaseJson);
        //获取老合同其他信息项
        String contractExtendJson = parentContractInfoVo.getContractExtend();
        ContractOtherInfo extendVo = JsonToObjectUtil.jsonToContractOtherInfo(contractExtendJson);
        PreviewBillParamsRoomChargeSubjectVo rentChargeSubjectVo = new PreviewBillParamsRoomChargeSubjectVo();
        rentChargeSubjectVo.setChargeSubjectNo("07");
        rentChargeSubjectVo.setCyclicOrSingle("01");
        rentChargeSubjectVo.setChargeSubjectPeriod(Integer.valueOf(parentContractInfoVo.getPaymentCycleCode()));
        rentChargeSubjectVo.setAmountType("0" + propStandardUnit);
        rentChargeSubjectVo.setParamList((JSONObject) JSON.toJSON(new BbctChargeRuleSubParamsBigVo()));
        BbctChargeRuleSubParamsBigVo chargeRuleSubParamsVo = new BbctChargeRuleSubParamsBigVo();
        chargeRuleSubParamsVo.setPARAMPRICE(String.valueOf(propStandard));
        chargeRuleSubParamsVo.setPARAMAREA(changeProductVo.getArea());
        rentChargeSubjectVo.setParamValueList((JSONObject) JSON.toJSON(chargeRuleSubParamsVo));
        rentChargeSubjectVo.setTaxRate(BigDecimal.valueOf(extendVo
                        .getPropTaxRate())
                .divide(BigDecimal.valueOf(100)));
        rentChargeSubjectVo.setIncreaseRules(getPropChangeIncreaseRuleList(popVo));
        rentChargeSubjectVo.setPreferentRules(getParentPropPreferentRuleList(extendVo.getPropFreeVo()));
        return rentChargeSubjectVo;
    }

    private List<PreviewBillparamsRoomChargeIncreaseRule> getPropChangeIncreaseRuleList(IncrementVo popVo) {
        List<PreviewBillparamsRoomChargeIncreaseRule> increaseRuleList = new ArrayList<>();
        List<BbsChangeIncrementalConfigVo> changeIncrementalList = getPropChangeIncrementalConfigList(popVo);
        AtomicInteger count = new AtomicInteger(1);
        changeIncrementalList.forEach(incremental -> {
            PreviewBillparamsRoomChargeIncreaseRule increaseRules = new PreviewBillparamsRoomChargeIncreaseRule();
            increaseRules.setIncreaseRuleId(count.getAndIncrement());
            increaseRules.setIncreaseProportion(Convert.toBigDecimal(incremental.getIncrease()).divide(Convert.toBigDecimal(100)));//递增比例
            increaseRules.setIncreasePeriod("year".equals(incremental.getUnit()) ? "04" : "01");//递增周期
            increaseRules.setIncreaseType("appoint".equals(incremental.getAdjustmentPoint()) ? "02" : "01");
            increaseRules.setIncreaseOrder(incremental.getTimePoint());
            increaseRuleList.add(increaseRules);
        });

        return increaseRuleList;
    }

    /**
     * 获取上级合同签约的优惠规则
     *
     * @param popFree 物业费免租期
     * @return 上级合同签约的优惠规则
     */
    private List<PreviewBillparamsRoomChargePreferentRule> getParentPropPreferentRuleList(FreeVo popFree) {
        List<PreviewBillparamsRoomChargePreferentRule> preferentRuleList = new ArrayList<>();
        if ("0".equals(popFree.getFreePeriodType())) {//物业费免租期分类
            return preferentRuleList;
        } else if ("2".equals(popFree.getFreePeriodType())) {//物业费免租期分类
            PreviewBillparamsRoomChargePreferentRule preferentRules = new PreviewBillparamsRoomChargePreferentRule();
            preferentRules.setPreferentRuleId(1);
            preferentRules.setPreferentialType("01");
            preferentRules.setPreferentialBeginDate(popFree.getFpFixedDate());//物业费免租期固定日期,当租金免租期分类为2时有效
            preferentRules.setPreferentialEndDate(DateUtils.getDateAddDaySubtraction(popFree.getFpFixedDate(), popFree.getFpFixedValue()));
            preferentRuleList.add(preferentRules);
        } else if ("1".equals(popFree.getFreePeriodType())) {//物业费免租期分类
            List<FreeSectionVo> fpIntervalEntities = popFree.getFreeSections();//免租期信息列表
            AtomicInteger count = new AtomicInteger(1);
            fpIntervalEntities.forEach(fpIntervalEntity -> {
                PreviewBillparamsRoomChargePreferentRule preferentRules = new PreviewBillparamsRoomChargePreferentRule();
                preferentRules.setPreferentRuleId(count.getAndIncrement());
                preferentRules.setPreferentialType("01");
                preferentRules.setPreferentialBeginDate(fpIntervalEntity.getStart());
                preferentRules.setPreferentialEndDate(fpIntervalEntity.getEnd());
                preferentRuleList.add(preferentRules);
            });
        }
        return preferentRuleList;
    }

    /**
     * 获取合同变更递增规则列表----物业费
     *
     * @return 合同变更递增规则列表
     */
    private List<BbsChangeIncrementalConfigVo> getPropChangeIncrementalConfigList(IncrementVo popVo) {
        List<BbsChangeIncrementalConfigVo> resultList = new ArrayList<>();
        // 修改为获取老合同物业费
        if(popVo.getIncrementalFlag().equals("1")){
            List<BbsSignIncrementalConfigVo> incrementalInfoList = popVo.getIncrementList();
            for (BbsSignIncrementalConfigVo incrementalInfo : incrementalInfoList) {
                BbsChangeIncrementalConfigVo changeIncrementalConfigVo = new BbsChangeIncrementalConfigVo();
                BeanUtils.copyProperties(incrementalInfo, changeIncrementalConfigVo);
                resultList.add(changeIncrementalConfigVo);
            }
        }
        return resultList;
    }
}
