package com.bonc.ioc.bzf.busisigning.service;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalInfoEntity;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 签约表 服务类
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
public interface IBbsRenewalInfoService extends IMcpBaseService<BbsRenewalInfoEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    String insertRecord(BbsRenewalInfoVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    List<String> insertBatchRecord(List<BbsRenewalInfoVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param signId 需要删除的签约id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void removeByIdRecord(String signId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param signIdList 需要删除的签约id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void removeByIdsRecord(List<String> signIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的签约表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void updateByIdRecord(BbsRenewalInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的签约表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void updateBatchByIdRecord(List<BbsRenewalInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的签约表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void saveByIdRecord(BbsRenewalInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的签约表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void saveBatchByIdRecord(List<BbsRenewalInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param signId 需要查询的签约id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    BbsRenewalInfoVo selectByIdRecord(String signId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    PageResult<List<BbsRenewalInfoPageListResultExcelVo>> selectByPageRecord(SelectByPageVo vo);

    PageResult<List<BbsRenewalInfoPageListResultVo>> selectByPage(SelectByPageVo vo);

    PageResult<List<BbsRenewalInfoPageListResultVo>> selectByPageContact(SelectByPageVo vo);

}
