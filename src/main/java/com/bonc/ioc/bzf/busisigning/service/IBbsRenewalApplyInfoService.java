package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyInfoEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import java.util.Map;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 续签申请信息表 服务类
 *
 * <AUTHOR>
 * @date 2023-09-21
 * @change 2023-09-21 by King-Y for init
 */
public interface IBbsRenewalApplyInfoService extends IMcpBaseService<BbsRenewalApplyInfoEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    String insertRecord(BbsRenewalApplyInfoVo vo);

    /**
     * 获取租户信息
     * @param contractNo
     * @return
     */
    Map<String, Object> getUserMsg(String contractNo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    List<String> insertBatchRecord(List<BbsRenewalApplyInfoVo> voList);

    /**
     * removeByIdRecord 取消申请
     * @param renewalApplyInfoId 申请信息id
     * @return  void
     * @since 1.0.0selectByIdRecord
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    void cancelApplyById(String renewalApplyInfoId);

    /**
     * selectByIdRecord 根据合同编号查询
     * @param contractNo 需要查询的合同编号
     * @return  根据合同编号查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    BbsRenewalApplyInfoEntity selectByContractNo(String contractNo);

    /**
     * removeByIdRecord 获取续租申请书文件id
     * @return  String
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    String getApplyFileId();

    /**
     * removeByIdRecord 根据主键删除
     * @param renewalApplyInfoId 需要删除的申请信息id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    void removeByIdRecord(String renewalApplyInfoId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param renewalApplyInfoIdList 需要删除的申请信息id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    void removeByIdsRecord(List<String> renewalApplyInfoIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的续签申请信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    void updateByIdRecord(BbsRenewalApplyInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的续签申请信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    void updateBatchByIdRecord(List<BbsRenewalApplyInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的续签申请信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    void saveByIdRecord(BbsRenewalApplyInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的续签申请信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    void saveBatchByIdRecord(List<BbsRenewalApplyInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param renewalApplyInfoId 需要查询的申请信息id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    BbsRenewalApplyInfoVo selectByIdRecord(String renewalApplyInfoId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    PageResult<List<BbsRenewalApplyInfoPageResultVo>> selectByPageRecord(BbsRenewalApplyInfoPageVo vo);
}
