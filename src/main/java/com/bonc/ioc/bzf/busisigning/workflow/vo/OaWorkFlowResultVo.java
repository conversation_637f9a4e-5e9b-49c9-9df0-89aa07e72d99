package com.bonc.ioc.bzf.busisigning.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: caijun
 * @date: 2024/05/23
 * @change: 2024/05/23 by <EMAIL> for init
 */
@Data
public class OaWorkFlowResultVo implements Serializable {

    /**
     * 返回码
     */
    @ApiModelProperty(value = "返回码")
    private String errcode;

    /**
     * 返回信息
     */
    @ApiModelProperty(value = "返回信息")
    private String errmsg;

    /**
     * 审批ID
     */
    @ApiModelProperty(value = "审批ID")
    private Long requestId;


}
