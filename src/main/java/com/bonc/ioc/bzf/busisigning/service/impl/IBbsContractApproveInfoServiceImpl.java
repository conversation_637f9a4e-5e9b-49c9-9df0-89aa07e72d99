package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.entity.BbsApproveInfoEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeApproveEntity;
import com.bonc.ioc.bzf.busisigning.service.IBbsContractApproveInfoService;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeApproveInfoVo;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class IBbsContractApproveInfoServiceImpl extends McpBaseServiceImpl<BbsiContractChangeApproveEntity> implements IBbsContractApproveInfoService {


}

