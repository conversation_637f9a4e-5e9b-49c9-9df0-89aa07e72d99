package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRefundApproveInfoEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 退款审批表 服务类
 *
 * <AUTHOR>
 * @date 2024-08-28
 * @change 2024-08-28 by pyj for init
 */
public interface IBbsRefundApproveInfoService extends IMcpBaseService<BbsRefundApproveInfoEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbsRefundApproveInfoVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbsRefundApproveInfoVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param approveId 需要删除的审批id
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String approveId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param approveIdList 需要删除的审批id
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> approveIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的退款审批表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbsRefundApproveInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的退款审批表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbsRefundApproveInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的退款审批表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbsRefundApproveInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的退款审批表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbsRefundApproveInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param approveId 需要查询的审批id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    BbsRefundApproveInfoVo selectByIdRecord(String approveId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbsRefundApproveInfoPageResultVo>> selectByPageRecord(BbsRefundApproveInfoPageVo vo);

    /**
     * 退款审核分页查询
     *
     * @param vo 退款审核分页请求参数
     * @return 退款审核分页结果
     */
    PageResult<List<BbsRefundApplyPageResultVo>> selectRefundApplyApproveByPage(BbsRefundApplyPageVo vo);

    /**
     * 退款申请提交
     *
     * @param refundId 退款id
     */
    void submit(String refundId);

    /**
     * 退款申请通过
     *
     * @param refundId 退款id
     * @param remark   说明
     */
    void approve(String refundId, String remark);

    /**
     * 退款申请未通过
     *
     * @param refundId 退款id
     * @param remark   说明
     */
    void unApprove(String refundId, String remark);

    /**
     * 根据signId批量保存明细
     *
     * @param refundId 退款id
     * @return 最新审批信息
     */
    BbsRefundApproveInfoEntity saveApproveDetailInfo(String refundId, Integer delFlag);

    /**
     * 获取退款审核操作记录
     *
     * @param refundId 退款id
     * @return 退款审核操作记录
     */
    List<BbsRefundApproveDetailInfoVo> selectDetailInfo(String refundId);
}
