package com.bonc.ioc.bzf.busisigning.factory.change.otherchange;

import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.utils.DateUtils;
import com.bonc.ioc.bzf.busisigning.vo.BbctPreviewInfoParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo;
import com.bonc.ioc.bzf.busisigning.vo.SigningSaveVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsParamsVo;
import lombok.extern.slf4j.Slf4j;

/**
 * 租赁期变更 工厂类
 *
 * <AUTHOR>
 * @since 2024/10/24
 */
@Slf4j
public class LeaseDateChangeFactory extends AbstractSubChangeFactory {

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param contractChangeVo             合同变更信息 vo实体
     */
    public LeaseDateChangeFactory(FeignServiceConfiguration feignServiceConfiguration,
                                  BusinessServiceConfiguration businessServiceConfiguration,
                                  BbsiContractChangeVo contractChangeVo) {
        super(feignServiceConfiguration,
                businessServiceConfiguration,
                contractChangeVo);
    }

    /**
     * 赋值试算账单请求参数
     *
     * @param previewBillsParamsVo 试算账单请求参数 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentSignInfoVo     上级合同签约信息 vo实体
     */
    @Override
    public void setPreviewBillsParams(PreviewBillsParamsVo previewBillsParamsVo,
                                      BbctContractManagementVo parentContractInfoVo,
                                      SigningSaveVo parentSignInfoVo) {
        return;
    }

    /**
     * 赋值预览信息
     *
     * @param previewInfoParamsVo 预览信息参数 vo实体
     */
    @Override
    public void setPreviewInfo(BbctPreviewInfoParamsVo previewInfoParamsVo) {
        return;
    }

    /**
     * 根据变更信息赋值签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     */
    @Override
    public void setSignInfoByChangeInfo(SigningSaveVo signingSaveVo) {
        signingSaveVo.setContractBeginTime(DateUtils.formatDateTime(contractChangeVo.getContractBeginTime()));
        signingSaveVo.setContractEndTime(DateUtils.formatDateTime(contractChangeVo.getContractEndTime()));
    }
}
