package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApplyApproveDetailInfoMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyApproveDetailInfoEntity;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalApplyApproveDetailInfoService;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveDetailInfoResultVo;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveDetailInfoVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 续签申请信息接口
 *
 * @author: hechengyao
 * @createDate: 2023-09-21
 * @Version 1.0
 **/
@Slf4j
@Service
public class BbsRenewalApplyApproveDetailInfoServiceImpl extends McpBaseServiceImpl<BbsRenewalApplyApproveDetailInfoEntity> implements IBbsRenewalApplyApproveDetailInfoService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalApplyApproveDetailInfoMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsRenewalApplyApproveDetailInfoService baseService;

    @Override
    public String insertRecord(RenewalApplyApproveDetailInfoVo vo) {
        if (vo == null) {
            return null;
        }
        BbsRenewalApplyApproveDetailInfoEntity entity = new BbsRenewalApplyApproveDetailInfoEntity();
        BeanUtils.copyProperties(vo, entity);

        if (!baseService.insert(entity)) {
            log.error("续签申请审核信息表新增失败:" + entity.toString());
            throw new McpException("续签申请审核信息表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getApproveId(), 1)) {
                log.error("续签申请信息表新增后保存历史失败:" + entity.toString());
                throw new McpException("续签申请信息表新增后保存历史失败");
            }

            log.debug("续签申请信息表新增后保存历史成功:" + entity.getApproveId());
            return entity.getApproveId();
        }
    }

    /**
     * 续签申请审核
     *
     * @param approveDetailId 审批明细id
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @Override
    public BbsRenewalApplyApproveDetailInfoEntity queryRenewalApplyApproveInfo(String approveDetailId) {
        return null;
    }

    /**
     * 续签申请审核-查看续签申请审核记录
     *
     * @param parentId 申请信息id
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @Override
    public PageResult<List<RenewalApplyApproveDetailInfoResultVo>> selectByListRecord(String parentId) {
        List<RenewalApplyApproveDetailInfoResultVo> result = baseMapper.selectByListRecord(parentId);
        result.forEach(t ->{
            if ("3".equals(t.getApproveStatus())) {
                t.setApprovDesc("租户续租申请提交");
            }
            if ("1".equals(t.getApproveStatus()) || "2".equals(t.getApproveStatus())) {
                t.setApprovDesc("申请审核");
            }
        });
        return new PageResult<>(result);
    }

}
