package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.vo.BbsLesseeChangeExcelVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsPaymentDateChangeExcelVo;
import com.bonc.ioc.bzf.utils.common.imports.BbsImportResultVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 脚本相关 interface
 *
 * <AUTHOR>
 * @since 2024/5/15
 */
public interface IBbsScriptService {

    /**
     * excel导入--主承租人变更
     *
     * @param file    文件信息
     * @param batchNo 批次号
     * @return 导入结果
     */
    BbsImportResultVo<BbsLesseeChangeExcelVo> excelImportLesseeChange(MultipartFile file, String batchNo);

    /**
     * excel导入--应缴日期变更
     *
     * @param file    文件信息
     * @param batchNo 批次号
     * @return 导入结果
     */
    BbsImportResultVo<BbsPaymentDateChangeExcelVo> excelImportPaymentDate(MultipartFile file, String batchNo);
}
