package com.bonc.ioc.bzf.busisigning.service;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalCustomerEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsSignInfoEntity;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementPageResultVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementPageVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractStatusNumVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctMessageDownContractVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbpmBillManagementPageVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.EmailDownloadVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.TreeParamVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.TreeVo;
import com.bonc.ioc.bzf.busisigning.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbpmCashPledgeVo;
import com.bonc.ioc.bzf.busisigning.vo.ContractingProgressVo;
import com.bonc.ioc.bzf.busisigning.vo.RenewalingSaveVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 合同 服务类
 *
 * <AUTHOR>
 * @date 2023-08-30
 */
public interface BbctContractService {

    /**
     * generateContract 纸质合同签约提交时调用合同中心生成合同
     *
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    AppReply<String> generateContract(String signId);

    /**
     * 台账生成合同
     * @param signId
     * @return
     */
    AppReply<String> generateContractTz(String signId);

    /**
     * modifyContractStatus 合同审核提交时调用合同中心调整合同状态为生效
     * @param signId  签约id
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    AppReply modifyContractStatus(String signId);
    /**
     * modifyContractStatus 合同审核提交时调用接口生成帐单
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    AppReply<Object> generateBill(String signId);

    /**
     * 合同审核提交时调用接口生成帐单
     * @param signId
     * @param dataSource 1新签2续租
     * @return
     */
    AppReply<Object> generateBill(String signId,String dataSource);

    /**
     * 合同审核提交时调用接口退租
     * @param contractCode
     * @param renewalingSaveVo
     * @return
     */
    AppReply<Object> exitFormForCreateByContract(String contractCode,RenewalingSaveVo renewalingSaveVo);

    /**
     * selectTree 树型房子信息查询
     *
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    AppReply<List<TreeVo>> selectTree(TreeParamVo vo);

    /**
     * selectTree 查询合同状态分类统计数量
     *
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    AppReply<BbctContractStatusNumVo> selectContractStatusNum(String contractTypeCode);

    /**
     * findContractByPage 合同管理-合同列表查询
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     * @param vo
     * @return
     */
    AppReply<PageResult<List<BbctContractManagementPageResultVo>>> findContractByPage(BbctContractManagementPageVo vo);

    /**
     * contractBatchExport 合同管理-合同批量导出
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    void contractBatchExport(HttpServletResponse response, BbctContractManagementPageVo vo) throws Exception;

    /**
     * contractBatchExport 合同管理-合同批量导出
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    void exportContractList(BbctContractManagementPageVo vo);

    /**
     * downloadContractForEmail 合同下载(下载到邮箱)
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    AppReply<Boolean> downloadContractForEmail(EmailDownloadVo vo);

    /**
     * downloadContractForEmail 合同下载(短信下载)
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    AppReply<Boolean> sendMessageDownContract(BbctMessageDownContractVo vo);

    /**
     * getAssociationContractMessage 合同管理-关联信息-账单查询及保证金查询（计费科目编号传 02 ）
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> getBill(BbpmBillManagementPageVo vo);

    /**
     * modifyContractStatus 通过账单id查询账单文件id（保证金预览及下载）
     * @param billId  账单id
     * @param projectId  项目id
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    AppReply<BbpmCashPledgeVo> getFileIdByBillId(String billId, String projectId);

    /**
     * generateRenewalContract 纸质合同签约提交时调用合同中心生成合同-续租
     *
     * <AUTHOR>
     * @date 2023-09-25
     * @since 1.0.0
     */
    AppReply<String> generateRenewalContract(String signId);

    /**
     * generateRenewalBill 合同审核提交时调用接口生成续租帐单-续租
     * <AUTHOR>
     * @date 2023-09-25
     * @since 1.0.0
     */
    AppReply<Object> generateRenewalBill(String signId);

    /**
     * getHouseByHouseCode 通过房源编码查询房源信息
     * @param houseCodes  房源编码
     * <AUTHOR>
     * @date 2023-08-30
     * @since 1.0.0
     */
    AppReply<List<JSONObject>> getHousesByHouseCodes(List<String> houseCodes);

    AppReply<ContractingProgressVo> getContractingProgress(String contractCode, String signType);

    AppReply<String> checkIntention(String contractNo);

    String createProductExtend(BbsResultProductEntity productEntity);

    String createContractOtherInfo(BbsSignInfoEntity signInfo);

    String createIncrement(BbsSignInfoEntity signInfo);

    PageResult<List<BbctContractManagementPageResultVo>> selectByPageAgreementList(BbctContractManagementPageVo vo);
}
