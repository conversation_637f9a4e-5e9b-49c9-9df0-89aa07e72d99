package com.bonc.ioc.bzf.busisigning.workflow.enums;

/**
 * 商业签约业务分类
 *
 * <AUTHOR>
 * @since 2023/5/6
 */
public enum NodeKeyEnum {


    START("A", "开始节点"),



    END("Z", "结束节点（最后一个审核节点）");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    NodeKeyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    NodeKeyEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        NodeKeyEnum[] enums = values();
        for (NodeKeyEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
