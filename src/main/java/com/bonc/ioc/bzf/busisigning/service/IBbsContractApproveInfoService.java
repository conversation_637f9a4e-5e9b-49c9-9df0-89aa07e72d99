package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsApproveInfoEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeApproveEntity;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;

/**
 * 审批表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by 刘鹏伟 for init
 */
public interface IBbsContractApproveInfoService extends IMcpBaseService<BbsiContractChangeApproveEntity> {


}
