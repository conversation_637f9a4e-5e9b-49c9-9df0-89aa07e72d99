package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.dao.BbsiContractChangeResultMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeResultEntity;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSignerVo;
import com.bonc.ioc.bzf.busisigning.service.IContractChangeResultService;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class ContractChangeResultServiceImpl extends McpBaseServiceImpl<BbsiContractChangeResultEntity> implements IContractChangeResultService {
    @Resource
    private BbctContractFeignClient bbctContractFeignClient;

    @Resource
    private Environment env;
    /**
     * service 本服务
     */
    @Resource
    private BbsiContractChangeResultMapper mapper;

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public void contractChange(BbsiContractChangeVo vo) {
        // 调用合同中心修改合同状态以及字段
        List<BbctContractManagementVo> contractManagementVos = new ArrayList<>();
        BbctContractManagementVo bbctContractManagementVo = new BbctContractManagementVo();
        bbctContractManagementVo.setContractNo(vo.getContractCode());

        //bbctContractManagementVo.setChangeCheckStatus("2");
        List<BbctContractSignerVo> bbctContractSignerVos = new ArrayList<>();

        BbctContractSignerVo bbctContractSignerVo = new BbctContractSignerVo();
        bbctContractSignerVo.setContractNo(vo.getContractCode());
        bbctContractSignerVo.setCustomerNo(StringUtils.isBlank(vo.getCustomerNo()) ? vo.getCustomerNoOld() : vo.getCustomerNo());
        //bbctContractSignerVo.setSignerId(bbsiContractChangeCustomerVo.getSignId());
        if (StringUtils.isNotBlank(vo.getCustomerName())) {
            bbctContractSignerVo.setCustomerName(vo.getCustomerName());
        }

        if (StringUtils.isNotBlank(vo.getMailAddress())) {
            bbctContractSignerVo.setMailAddress(vo.getMailAddress());
        }
        if (StringUtils.isNotBlank(vo.getBankName())) {
            bbctContractSignerVo.setBankName(vo.getBankName());
        }
        if (StringUtils.isNotBlank(vo.getBankNameCode())) {
            bbctContractSignerVo.setBankNameCode(vo.getBankNameCode());
        }
        if (StringUtils.isNotBlank(vo.getBankCard())) {
            bbctContractSignerVo.setBankCard(vo.getBankCard());
        }
        if (StringUtils.isNotBlank(vo.getCustomerTel())) {
            bbctContractSignerVo.setCustomerTel(vo.getCustomerTel());
            bbctContractSignerVo.setConsignorMobile(vo.getCustomerTel());
        }
        bbctContractSignerVos.add(bbctContractSignerVo);
        bbctContractManagementVo.setUserList(bbctContractSignerVos);

        contractManagementVos.add(bbctContractManagementVo);
        if (!Objects.equals(env.getProperty("spring.profiles.active"), "debug")) {
            AppReply appReply = bbctContractFeignClient.changeBatchAddOne(contractManagementVos);
            if (!AppReply.SUCCESS_CODE.equals(appReply.getCode())) {
                log.error("调用合同中心合同变更接口，返回【{}】", appReply);
                throw new McpException("合同中心合同变更接口调取失败");
            }
        }
        //todo 调用工银接口对接
        BbsiContractChangeResultVo bbsiContractChangeResultVo = new BbsiContractChangeResultVo();
        BeanUtils.copyProperties(vo, bbsiContractChangeResultVo);

        mapper.insertOrUpdate(bbsiContractChangeResultVo);
    }

    /**
     * 新增或更新
     *
     * @param vo 合同变更结果 vo实体
     */
    @Override
    public void insertOrUpdate(BbsiContractChangeResultVo vo) {
        mapper.insertOrUpdate(vo);
    }

    /**
     * 根据合同编号获取合同变更结果
     *
     * @param contractCode 合同编号
     * @return 合同变更结果
     */
    @Override
    public BbsiContractChangeResultVo selectByContractCode(String contractCode) {
        return mapper.selectByContractCode(contractCode);
    }

    /**
     * 根据合同编号删除
     *
     * @param contractCode 合同编号
     */
    @Override
    public void deleteByContractCode(String contractCode) {
        mapper.deleteByContractCode(contractCode);
    }
}
