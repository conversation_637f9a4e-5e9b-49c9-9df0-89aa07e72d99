package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeReturnBankCardEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsChangeReturnBankCardMapper;
import com.bonc.ioc.bzf.busisigning.service.IBbsChangeReturnBankCardService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更退回银行卡信息 服务类实现
 *
 * <AUTHOR>
 * @date 2024-09-06
 * @change 2024-09-06 by King-Y for init
 */
@Slf4j
@Service
public class BbsChangeReturnBankCardServiceImpl extends McpBaseServiceImpl<BbsChangeReturnBankCardEntity> implements IBbsChangeReturnBankCardService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsChangeReturnBankCardMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsChangeReturnBankCardService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsChangeReturnBankCardVo vo) {
        if(vo == null) {
            return null;
        }

        BbsChangeReturnBankCardEntity entity = new BbsChangeReturnBankCardEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setCardId(null);
        if(!baseService.insert(entity)) {
            log.error("合同变更退回银行卡信息新增失败:" + entity.toString());
            throw new McpException("合同变更退回银行卡信息新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getCardId(),1)) {
                log.error("合同变更退回银行卡信息新增后保存历史失败:" + entity.toString());
                throw new McpException("合同变更退回银行卡信息新增后保存历史失败");
            }

            log.debug("合同变更退回银行卡信息新增成功:"+entity.getCardId());
            return entity.getCardId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsChangeReturnBankCardVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsChangeReturnBankCardEntity> entityList = new ArrayList<>();
        for (BbsChangeReturnBankCardVo item:voList) {
            BbsChangeReturnBankCardEntity entity = new BbsChangeReturnBankCardEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsChangeReturnBankCardEntity item:entityList){
            item.setCardId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("合同变更退回银行卡信息新增失败");
            throw new McpException("合同变更退回银行卡信息新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsChangeReturnBankCardEntity::getCardId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("合同变更退回银行卡信息批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("合同变更退回银行卡信息批量新增后保存历史失败");
            }

            log.debug("合同变更退回银行卡信息新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param cardId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String cardId) {
        if(!StringUtils.isEmpty(cardId)) {
            if(!baseService.saveOperationHisById(cardId,3)) {
                log.error("合同变更退回银行卡信息删除后保存历史失败:" + cardId);
                throw new McpException("合同变更退回银行卡信息删除后保存历史失败");
            }

            if(!baseService.removeById(cardId)) {
                log.error("合同变更退回银行卡信息删除失败");
                throw new McpException("合同变更退回银行卡信息删除失败"+cardId);
            }
        } else {
            throw new McpException("合同变更退回银行卡信息删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param cardIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> cardIdList) {
        if(!CollectionUtils.isEmpty(cardIdList)) {
            int oldSize = cardIdList.size();
            cardIdList = cardIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(cardIdList) || oldSize != cardIdList.size()) {
                throw new McpException("合同变更退回银行卡信息批量删除失败 存在主键id为空的记录"+StringUtils.join(cardIdList));
            }

            if(!baseService.saveOperationHisByIds(cardIdList,3)) {
                log.error("合同变更退回银行卡信息批量删除后保存历史失败:" + StringUtils.join(cardIdList));
                throw new McpException("合同变更退回银行卡信息批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(cardIdList)) {
                log.error("合同变更退回银行卡信息批量删除失败");
                throw new McpException("合同变更退回银行卡信息批量删除失败"+StringUtils.join(cardIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的合同变更退回银行卡信息
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsChangeReturnBankCardVo vo) {
        if(vo != null) {
            BbsChangeReturnBankCardEntity entity = new BbsChangeReturnBankCardEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getCardId())) {
                throw new McpException("合同变更退回银行卡信息更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("合同变更退回银行卡信息更新失败");
                throw new McpException("合同变更退回银行卡信息更新失败"+entity.getCardId());
            } else {
                if(!baseService.saveOperationHisById(entity.getCardId(),2)) {
                    log.error("合同变更退回银行卡信息更新后保存历史失败:" + entity.getCardId());
                    throw new McpException("合同变更退回银行卡信息更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("合同变更退回银行卡信息更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的合同变更退回银行卡信息
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsChangeReturnBankCardVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsChangeReturnBankCardEntity> entityList = new ArrayList<>();

            for (BbsChangeReturnBankCardVo item:voList){
                BbsChangeReturnBankCardEntity entity = new BbsChangeReturnBankCardEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getCardId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("合同变更退回银行卡信息批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("合同变更退回银行卡信息批量更新失败");
                throw new McpException("合同变更退回银行卡信息批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getCardId())).map(BbsChangeReturnBankCardEntity::getCardId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("合同变更退回银行卡信息批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("合同变更退回银行卡信息批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的合同变更退回银行卡信息
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsChangeReturnBankCardVo vo) {
        if(vo != null) {
            BbsChangeReturnBankCardEntity entity = new BbsChangeReturnBankCardEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("合同变更退回银行卡信息保存失败");
                throw new McpException("合同变更退回银行卡信息保存失败"+entity.getCardId());
            } else {
                if(!baseService.saveOperationHisById(entity.getCardId(),4)) {
                    log.error("合同变更退回银行卡信息保存后保存历史失败:" + entity.getCardId());
                    throw new McpException("合同变更退回银行卡信息保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("合同变更退回银行卡信息保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的合同变更退回银行卡信息
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsChangeReturnBankCardVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsChangeReturnBankCardEntity> entityList = new ArrayList<>();

            for (BbsChangeReturnBankCardVo item:voList){
                BbsChangeReturnBankCardEntity entity = new BbsChangeReturnBankCardEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("合同变更退回银行卡信息批量保存失败");
                throw new McpException("合同变更退回银行卡信息批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getCardId())).map(BbsChangeReturnBankCardEntity::getCardId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("合同变更退回银行卡信息批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("合同变更退回银行卡信息批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param cardId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsChangeReturnBankCardVo selectByIdRecord(String cardId) {
        BbsChangeReturnBankCardVo vo = new BbsChangeReturnBankCardVo();

        if(!StringUtils.isEmpty(cardId)) {
            BbsChangeReturnBankCardEntity entity = baseService.selectById(cardId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsChangeReturnBankCardPageResultVo>> selectByPageRecord(BbsChangeReturnBankCardPageVo vo) {
        List<BbsChangeReturnBankCardPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
