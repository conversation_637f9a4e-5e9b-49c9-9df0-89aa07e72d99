package com.bonc.ioc.bzf.busisigning.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalIncrementalConfigEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalIncrementalConfigMapper;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalIncrementalConfigService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 签约-递增设置（当租金/物业费是否递增标识为1时有效） 服务类实现
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
@Slf4j
@Service
public class BbsRenewalIncrementalConfigServiceImpl extends McpBaseServiceImpl<BbsRenewalIncrementalConfigEntity> implements IBbsRenewalIncrementalConfigService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalIncrementalConfigMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsRenewalIncrementalConfigService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsRenewalIncrementalConfigVo vo) {
        if(vo == null) {
            return null;
        }

        BbsRenewalIncrementalConfigEntity entity = new BbsRenewalIncrementalConfigEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setSicId(null);
        if(!baseService.insert(entity)) {
            log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）新增失败:" + entity.toString());
            throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getSicId(),1)) {
                log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）新增后保存历史失败:" + entity.toString());
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）新增后保存历史失败");
            }

            log.debug("签约-递增设置（当租金/物业费是否递增标识为1时有效）新增成功:"+entity.getSicId());
            return entity.getSicId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsRenewalIncrementalConfigVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsRenewalIncrementalConfigEntity> entityList = new ArrayList<>();
        for (BbsRenewalIncrementalConfigVo item:voList) {
            BbsRenewalIncrementalConfigEntity entity = new BbsRenewalIncrementalConfigEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsRenewalIncrementalConfigEntity item:entityList){
            item.setSicId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）新增失败");
            throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsRenewalIncrementalConfigEntity::getSicId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量新增后保存历史失败");
            }

            log.debug("签约-递增设置（当租金/物业费是否递增标识为1时有效）新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param sicId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String sicId) {
        if(!StringUtils.isEmpty(sicId)) {
            if(!baseService.saveOperationHisById(sicId,3)) {
                log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）删除后保存历史失败:" + sicId);
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）删除后保存历史失败");
            }

            if(!baseService.removeById(sicId)) {
                log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）删除失败");
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）删除失败"+sicId);
            }
        } else {
            throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param sicIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> sicIdList) {
        if(!CollectionUtils.isEmpty(sicIdList)) {
            int oldSize = sicIdList.size();
            sicIdList = sicIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(sicIdList) || oldSize != sicIdList.size()) {
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量删除失败 存在主键id为空的记录"+StringUtils.join(sicIdList));
            }

            if(!baseService.saveOperationHisByIds(sicIdList,3)) {
                log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量删除后保存历史失败:" + StringUtils.join(sicIdList));
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(sicIdList)) {
                log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量删除失败");
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量删除失败"+StringUtils.join(sicIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的签约-递增设置（当租金/物业费是否递增标识为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsRenewalIncrementalConfigVo vo) {
        if(vo != null) {
            BbsRenewalIncrementalConfigEntity entity = new BbsRenewalIncrementalConfigEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getSicId())) {
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）更新失败");
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）更新失败"+entity.getSicId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSicId(),2)) {
                    log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）更新后保存历史失败:" + entity.getSicId());
                    throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的签约-递增设置（当租金/物业费是否递增标识为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsRenewalIncrementalConfigVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalIncrementalConfigEntity> entityList = new ArrayList<>();

            for (BbsRenewalIncrementalConfigVo item:voList){
                BbsRenewalIncrementalConfigEntity entity = new BbsRenewalIncrementalConfigEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getSicId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量更新失败");
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSicId())).map(BbsRenewalIncrementalConfigEntity::getSicId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的签约-递增设置（当租金/物业费是否递增标识为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsRenewalIncrementalConfigVo vo) {
        if(vo != null) {
            BbsRenewalIncrementalConfigEntity entity = new BbsRenewalIncrementalConfigEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）保存失败");
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）保存失败"+entity.getSicId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSicId(),4)) {
                    log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）保存后保存历史失败:" + entity.getSicId());
                    throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的签约-递增设置（当租金/物业费是否递增标识为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsRenewalIncrementalConfigVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalIncrementalConfigEntity> entityList = new ArrayList<>();

            for (BbsRenewalIncrementalConfigVo item:voList){
                BbsRenewalIncrementalConfigEntity entity = new BbsRenewalIncrementalConfigEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量保存失败");
                throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSicId())).map(BbsRenewalIncrementalConfigEntity::getSicId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("签约-递增设置（当租金/物业费是否递增标识为1时有效）批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param sicId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsRenewalIncrementalConfigVo selectByIdRecord(String sicId) {
        BbsRenewalIncrementalConfigVo vo = new BbsRenewalIncrementalConfigVo();

        if(!StringUtils.isEmpty(sicId)) {
            BbsRenewalIncrementalConfigEntity entity = baseService.selectById(sicId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsRenewalIncrementalConfigPageResultVo>> selectByPageRecord(BbsRenewalIncrementalConfigPageVo vo) {
        List<BbsRenewalIncrementalConfigPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据签约id和标准类型查询
     *
     * @param signId       签约id
     * @param standardType 标准类型
     * @return 递增设置列表
     */
    @Override
    public List<BbsRenewalIncrementalConfigVo> selectBySignIdAndStandardType(String signId, String standardType) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BbsRenewalIncrementalConfigEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalIncrementalConfigEntity::getSignInfoId, signId)
                .eq(BbsRenewalIncrementalConfigEntity::getStandardType, standardType)
                .list()
                .stream()
                .map(item -> {
                    BbsRenewalIncrementalConfigVo vo = new BbsRenewalIncrementalConfigVo();
                    BeanUtils.copyProperties(item, vo);
                    return vo;
                })
                .collect(Collectors.toList());
    }
}
