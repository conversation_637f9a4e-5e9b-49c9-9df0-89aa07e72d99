package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsResultProductMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsSignInfoMapper;
import com.bonc.ioc.bzf.busisigning.entity.*;
import com.bonc.ioc.bzf.busisigning.enums.AreaTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.enums.SourceNodeEnum;
import com.bonc.ioc.bzf.busisigning.feign.feign.*;
import com.bonc.ioc.bzf.busisigning.feign.vo.*;
import com.bonc.ioc.bzf.busisigning.feign.vo.ProjectVo;
import com.bonc.ioc.bzf.busisigning.service.*;
import com.bonc.ioc.bzf.busisigning.utils.*;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultProductVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.tz.TzParamVo;
import com.bonc.ioc.bzf.busisigning.vo.tz.TzResult;
import com.bonc.ioc.bzf.utils.common.convert.CopyFieldUtil;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;

import com.bonc.ioc.common.util.SpringContextUtils;
import com.bonc.ioc.common.utils.CurrentUtil;
import com.bonc.ioc.common.utils.ValidateUtils;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.sinovatech.rd.bms.api.location.vo.BmsLocationRpcServiceGetOneReq;
import com.sinovatech.saas.base.spec.bean.Request;
import com.sinovatech.saas.base.spec.bean.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TZServiceImpl implements TZService {

    @Resource
    private IBbsDictService baseService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private BbHousingFeignClient bbHousingFeignClient;
    @Resource
    private BbCustomerFeignClient bbcustomerFeignClient;

    @Resource
    private BbprojectFeignClient bbprojectFeignClient;
    @Resource
    private BmsFeignClient bmsFeignClient;

    @Resource
    private IBbsSignInfoService iBbsSignInfoService;
    @Resource
    private IBbsSignIncrementalConfigService incrementalConfigService;
    @Resource
    private BfipSettlementFeignClient settlementFeignClient;
    @Resource
    private IBbsResultRelationService resultRelationService;
    @Resource
    private IBbsResultProductService resultProductService;
    @Resource
    private IBbsResultCustomerService resultCustomerService;
    @Resource
    private  BbctContractService bbctContractService;

    @Resource
    private BbsSignInfoMapper signInfoMapper;
    @Resource
    private IBbsApproveInfoService approveInfoService;
    @Resource
    private IBbsTemplateSeatService templateSeatService;
    @Resource
    private IBbsSignFpIntervalService iBbsSignFpIntervalService;

    /**
     * 缓存房源有效时间
     */
    @Value("${cache.house.expireTime:1800}")
    private Integer houseExpireTime;
    @Resource
    private ExcelUtil excelUtil;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private BzfSystemCommercialFeignClient bzfSystemCommercialFeignClient;

    @Resource
    private BbsResultProductMapper bbsResultProductMapper;

    /**
     * 核心控制机类
     *
     * @param multipartFile
     * @return
     */
    @Override
    public TzResult core(MultipartFile multipartFile) {
        /**
         * 解下EXECL
         */
        TzResult result = this.analysisExecl(multipartFile);

        /**
         * 处理数据
         */
        this.analysData(result);

        return result;
    }

    /**
     * 处理数据
     *
     * @param result
     * @return
     */
    @Transactional
    @Override
    public void analysData(TzResult result) {
        List<RLock> lockList = new ArrayList<>();
        try {
            /**
             * 处理房源
             */
            houseCore(result, lockList,null);//暂无计划,",0,"

            /**
             * 处理项目
             */
            projectCore(result);

            /**
             * 处理客户
             */
            customerCore(result);

            /**
             * signCore
             */
            signCore(result);

            /**
             * 保存数据
             */
            saveData(result);

            /**
             * 修改房源状态到待签约
             */
            //synHouseStatus(result.getProductVos());
        }catch (Exception e){
            log.error(e.getMessage());
            throw new McpException("导入台账签约数据失败："+e.getMessage());
        }finally {
            //解锁
            unlock(lockList);
        }
    }

    /**
     * 解下EXECL
     *
     * @param multipartFile
     * @return
     */
    @Override
    public TzResult analysisExecl(MultipartFile multipartFile) {
        List<TzParamVo> list = excelUtil.importExcel(multipartFile,TzParamVo.class,null,null);
        TzResult result = new TzResult();
        result.setParamVos(list);
        return result;
    }

    /**
     * 保存数据
     *
     * @param result
     */
    @Override
    public void saveData(TzResult result) {
        /**
         * 签约结果
         */
        String signId = iBbsSignInfoService.insertRecord(result.getSignInfoVo());
        /**
         * 递增
         */
        for(BbsSignIncrementalConfigVo vo:result.getSignInfoVo().getIncrementalConfigVoList()){
            vo.setSignInfoId(signId);
            this.incrementalConfigService.insertRecord(vo);
        }
        /**
         * 免租期-租金、物业费
         */
        for(BbsSignFpIntervalVo vo:result.getSignInfoVo().getBbsSignFpIntervalVoList()){
            vo.setSignInfoId(signId);
            this.iBbsSignFpIntervalService.insertRecord(vo);
        }
        /**
         *  BbsResultRelation
         */
        BbsResultRelationVo resultRelationEntity = new BbsResultRelationVo();
        resultRelationEntity.setDelFlag(1);
        resultRelationEntity.setSignInfoId(signId);
        String rrId = this.resultRelationService.insertRecord(resultRelationEntity);
        /**
         * 客户
          */
        result.getCustomerVo().setRrId(rrId);
        this.resultCustomerService.insertRecord(result.getCustomerVo());
        /**
         * 房源
         */
        for(BbsResultProductVo pvo: result.getProductVos()){
            pvo.setRrId(rrId);
        }
        this.resultProductService.insertBatchRecord(result.getProductVos());


        /**
         * 合同审核
         */
        BbsApproveInfoEntity approveDetailInfoEntity = new BbsApproveInfoEntity();
        approveDetailInfoEntity.setParentId(signId);
        approveDetailInfoEntity.setApproverUserId(currentUserId());
        approveDetailInfoEntity.setApproverUserName(currentUserName());
        approveDetailInfoEntity.setSubmitUserId(currentUserId());
        approveDetailInfoEntity.setSubmitUserName(currentUserName());
        approveDetailInfoEntity.setApproveType("2");
        approveDetailInfoEntity.setApproveStatus("1");
        java.util.Date now = new Date();
        approveDetailInfoEntity.setSubmitTime(now);
        approveDetailInfoEntity.setApproveTime(now);
        this.approveInfoService.insert(approveDetailInfoEntity);

        /**
         * 占位符处理，产权
         */
        BbsTemplateSeatEntity templateSeatEntity = new BbsTemplateSeatEntity();
        templateSeatEntity.setParentId(signId);
        templateSeatEntity.setParentType("2");
        templateSeatEntity.setReverseDisplay(1);
        templateSeatEntity.setSeatTitle("出租人（单位）名称");
        templateSeatEntity.setSeatKey("lessorName");
        templateSeatEntity.setDefaultVal(result.getParamVos().get(0).getFirstName());
        templateSeatEntity.setValue(result.getParamVos().get(0).getFirstName());
        templateSeatEntity.setIsShow("1");
        templateSeatEntity.setEditable("0");
        templateSeatEntity.setIsRequired("1");
        templateSeatEntity.setModuleType("input");
        templateSeatEntity.setDelFlag(DelFlagEnum.UNDELETED.getCode());
        templateSeatService.insert(templateSeatEntity);

        /**
         * 站位付,统一社会信用代码
         */
        BbsTemplateSeatEntity templateSeatEntityCode = new BbsTemplateSeatEntity();
        templateSeatEntityCode.setParentId(signId);
        templateSeatEntityCode.setParentType("2");
        templateSeatEntityCode.setReverseDisplay(1);
        templateSeatEntityCode.setSeatTitle("统一社会信用代码");
        templateSeatEntityCode.setSeatKey("unifiedCreditCode");
        templateSeatEntityCode.setDefaultVal(result.getParamVos().get(0).getUnifiedCreditCode());
        templateSeatEntityCode.setValue(result.getParamVos().get(0).getUnifiedCreditCode());
        templateSeatEntityCode.setIsShow("1");
        templateSeatEntityCode.setEditable("0");
        templateSeatEntity.setIsRequired("1");
        templateSeatEntity.setModuleType("input");
        templateSeatEntity.setDelFlag(DelFlagEnum.UNDELETED.getCode());
        templateSeatService.insert(templateSeatEntityCode);
        log.info("处理完成");
    }

    /**
     * 从缓存中查询
     *
     * @param key
     * @param productNos
     * @param operateState
     * @return
     */
    @Override
    public AppReply<JSONObject> searchProductsFromRedis(String key, List<String> productNos, String operateState) {
        log.info("------单个获取房源开始---------");
        AppReply<JSONObject> appReply = new AppReply<>(AppReply.SUCCESS_CODE,null,null);
        JSONObject result = new JSONObject();
        JSONArray rows = new JSONArray();
        Object o = redisUtil.get(key);
        if(o!=null){
            JSONArray jsonArray = JSONArray.parseArray(o.toString());
            List<JSONObject> list = JSONArray.parseArray(jsonArray.toJSONString(),JSONObject.class);
            for(String productNo:productNos){
                java.util.Optional<JSONObject> data = list.stream().filter(item->productNo.equals(item.getString("productHouseCode"))).findFirst();
                if(data.isPresent()){
                    JSONObject product= data.get();
                    if(StringUtils.isBlank(operateState)){
                        rows.add(product);
                    }else{
                        if(!(operateState).contains("," + product.getString("operationStatus") + ",")){
                            throw new McpException("【"+productNo+"】【"+product.getString("productHouseAddress")+"】当前状态‘"+product.getString("operationStatusName")+"’不支持签约");
                        }else{
                            rows.add(product);
                        }
                    }
                }else{
                    throw new McpException("【"+productNo+"】不存在");
                }
            }
        }else{
            throw new McpException("请重新操作");
        }
        result.put("rows",rows);
        appReply.setData(result);
        log.info("-----单个获取房源结束-------");
        return appReply;
    }

    /**
     * 根据房源编号查询房源
     *
     * @param key
     * @param productNo
     * @param operateState
     * @return
     */
    @Override
    public AppReply<JSONObject> searchProductFromRedis(String key, String productNo, String operateState) {
        List<String> productCodeList = new ArrayList<>();
        productCodeList.add(productNo);
        return this.searchProductsFromRedis(key,productCodeList,operateState);
    }

    /**
     * 缓存房源数据
     *
     * @param key
     * @param productNos
     */
    @Override
    public void cacheHouse(String key, List<String> productNos) {
        JSONArray jsonArray = new JSONArray();
        long start = System.currentTimeMillis();
        /**
         * 房源重复验证
         */
        distinct(productNos);

        /**
         * 拆分房源
         */
        List<List<String>> split = this.splitHouseProducts(productNos);

        /**
         * 根据拆分的数据，向房态统一查询数据
         */
        split.forEach(item -> {
            JSONArray array = this.selectHouseProducts(item);
            if(array!=null)
                jsonArray.addAll(array);
        });

        long end = System.currentTimeMillis();
        log.info(String.format("查询【%s】个房源，整体耗时【%s】", productNos.size(), (end - start) / 1000.0));
        /**
         * 缓存数据
         */
        if(!jsonArray.isEmpty()) {
            this.saveRedis(key, jsonArray);
        }else{
            throw new McpException("本批次查询的房源都不存在，请检查后重新处理");
        }
    }

    /**
     * 删除缓存
     *
     * @param key
     */
    @Override
    public void delRedis(String key) {
        log.info(String.format("删除Redis,key:%s",key));
        redisUtil.delete(key);
    }

    /**
     * 去重
     *
     * @param productNo
     * @return
     */
    @Override
    public List<String> distinct(List<String> productNo) {
        List<String> disticnctList = productNo.stream().distinct().collect(Collectors.toList());
        if(productNo.size()!=disticnctList.size()){
            throw new McpException("请检查房源信息，存在重复的房源产品编号");
        }
        return disticnctList;
    }

    @Override
    public JSONArray selectHouseProducts(List<String> productNos) {
//        String nos = org.apache.commons.lang3.StringUtils.join(productNos,",");
        AppReply<JSONArray> appReply = bbHousingFeignClient.selectInfoByCodes(productNos);
        //AppReply<JSONArray> appReply = bbHousingFeignClient.selectByPageHouseDetails(nos,1,productNos.size(),null,null);
        log.info("批量查询房源返回："+ JSON.toJSONString(appReply));
        JSONArray productInfoJsonArray = null;
        if (Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            productInfoJsonArray = appReply.getData();
        }else{
            throw new McpException("获取房源失败，请重新尝试");
        }
        return productInfoJsonArray;
    }

    @Override
    public List<List<String>> splitHouseProducts(List<String> productNos) {
        Integer housePageSize = 80;
        List<List<String>> list = null;
        log.info(String.format("缓存houseSize:%s",housePageSize));
        if(productNos.size() > housePageSize) {
            list = ListUtils.partition(productNos, housePageSize);
        }else{
            list = ListUtils.partition(productNos,productNos.size());
        }
        return list;
    }

    @Override
    public void saveRedis(String key, JSONArray jsonArray) {
        log.info(String.format("保存Redis,key:%s，有效期%s秒",key,houseExpireTime));
        redisUtil.set(key, jsonArray.toJSONString(),houseExpireTime);
        log.info(String.format("保存Redis,key:%s，有效期%s秒",key,houseExpireTime));
    }

    /**
     * 按照签约结果删除台账
     *
     * @param signIds
     */
    @Transactional
    @Override
    public void delTz(List<String> signIds) {
        this.signInfoMapper.delCustomerBySignIds(signIds);
        this.signInfoMapper.delProductBySignIds(signIds);
        this.signInfoMapper.deleteRelationBySignIds(signIds);
        this.signInfoMapper.delIncrementalConfig(signIds);
        this.signInfoMapper.deleteSignInfoByIds(signIds);
        this.signInfoMapper.delApproveInfo(signIds);
        this.signInfoMapper.delBbsTemplateSeat(signIds);
    }

    /**
     * 同步工银
     *
     * @return
     */
    @Override
    public SynResult synPayment() {
        SynResult result = new SynResult();
        List<SynVo> successList = new ArrayList<>();
        List<SynVo> failList = new ArrayList<>();
        List<BbsSignInfoEntity> signInfoEntities = new LambdaQueryChainWrapper<>(signInfoMapper)
                .eq(BbsSignInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsSignInfoEntity::getSyncPayment, "2")
                .list();
        for(BbsSignInfoEntity bbsSignInfo : signInfoEntities){
            try {
                AppReply<Object>  appReply = this.bbctContractService.generateBill(bbsSignInfo.getSignId());
                if(AppReply.SUCCESS_CODE.equals(appReply.getCode())) {
                    bbsSignInfo.setSyncPayment("1");
                    signInfoMapper.updateById(bbsSignInfo);
                    result.setSucessSize(result.getSucessSize() + 1);
                    SynVo vo = new SynVo();
                    vo.setContractCode(bbsSignInfo.getContractCode());
                    vo.setSignId(bbsSignInfo.getSignId());
                    successList.add(vo);
                }else{
                    throw new Exception(appReply.getMsg());
                }
            }catch (Exception e){
                log.error("生产账单失败："+e.getMessage());
                result.setFailSize(result.getFailSize()+1);
                SynVo vo = new SynVo();
                vo.setContractCode(bbsSignInfo.getContractCode());
                vo.setSignId(bbsSignInfo.getSignId());
                vo.setMsg(e.getMessage());
                failList.add(vo);
            }
        }
        result.setFailList(failList);
        result.setSuccessList(successList);
        return result;
    }

    /**
     * 同步合同
     *
     * @return
     */
    @Override
    public SynResult synContract() {
        SynResult result = new SynResult();
        List<SynVo> successList = new ArrayList<>();
        List<SynVo> failList = new ArrayList<>();
        List<BbsSignInfoEntity> signInfoEntities = new LambdaQueryChainWrapper<>(signInfoMapper)
                .eq(BbsSignInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsSignInfoEntity::getSyncPayment, "0")
                .list();
        for(BbsSignInfoEntity bbsSignInfo : signInfoEntities){
            try {
                AppReply<String> appReply = this.bbctContractService.generateContractTz(bbsSignInfo.getSignId());
                if(AppReply.SUCCESS_CODE.equals(appReply.getCode())) {
                    bbsSignInfo.setSyncPayment("2");
                    signInfoMapper.updateById(bbsSignInfo);
                    result.setSucessSize(result.getSucessSize() + 1);
                    SynVo vo = new SynVo();
                    vo.setContractCode(bbsSignInfo.getContractCode());
                    vo.setSignId(bbsSignInfo.getSignId());
                    successList.add(vo);
                }else{
                    throw new Exception(appReply.getMsg());
                }
            }catch (Exception e){
                log.error("生产合同失败："+e.getMessage());
                result.setFailSize(result.getFailSize()+1);
                SynVo vo = new SynVo();
                vo.setContractCode(bbsSignInfo.getContractCode());
                vo.setSignId(bbsSignInfo.getSignId());
                vo.setMsg(e.getMessage());
                failList.add(vo);
            }
        }
        result.setFailList(failList);
        result.setSuccessList(successList);
        return result;
    }

    /**
     * 获取房源新从房态中心
     *
     * @param paramVos
     * @param lockList
     * @param operateState
     * @return
     */
    @Override
    public List<BbsResultProductVo> createProductFromHouse(List<TzParamVo> paramVos, List<RLock> lockList, String operateState) {
        List<BbsResultProductVo> resultProductVos = new ArrayList<>();
        List<String> productNos = this.getProductNos(paramVos);
        //添加锁
        this.addLock(productNos,lockList);
        String key = UuidUtil.generateUuid();
        this.cacheHouse(key,productNos);
        try {
            for (int i = 1; i <= paramVos.size(); i++) {
                TzParamVo tzParamVo = paramVos.get(i - 1);
                String productNo = tzParamVo.getProductNo();
                BbsResultProductVo productVo = createProductVo(key, productNo,tzParamVo,operateState);
                try{
                    ValidatorUtils.validator(productVo,InsertValidatorGroup.class);
                }catch (Exception e){
                    throw new McpException("["+productNo+"]["+productVo.getProductName()+"]"+e.getMessage());
                }
                productVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
                productVo.setSourceNode(createHouseSourceNode(tzParamVo));//房源来源
                resultProductVos.add(productVo);
            }
        }catch (McpException e){
            throw new McpException(e.getMessage());
        }finally {
            this.delRedis(key);
        }
        return resultProductVos;
    }

    /**
     * 获取房源新从房态中心
     *
     * @param bbsResultProductVoList
     * @param lockList
     * @param operateState
     * @param areaType
     * @return
     */
    @Override
    public List<BbsResultProductVo> createProducts(List<BbsResultProductEntity> bbsResultProductVoList,
                                                   List<RLock> lockList,
                                                   String operateState,
                                                   String areaType) {
        //处理房源编号
        List<TzParamVo> tzParamVos = new ArrayList<>();
        for (BbsResultProductEntity vo : bbsResultProductVoList) {
            TzParamVo tzParamVo = new TzParamVo();
            tzParamVo.setProductNo(vo.getProductNo());
            tzParamVo.setSourceNode(vo.getSourceNode());//数据来源需要特殊处理
            tzParamVo.setAreaType(areaType);
            tzParamVos.add(tzParamVo);
        }
        return this.createProductFromHouse(tzParamVos, lockList, operateState);
    }

    /**
     * 创建项目信息
     *
     * @param productVos
     */
    @Override
    public void createProjects(List<BbsResultProductVo> productVos) {
        for(int i=1;i<=productVos.size();i++){
            BbsResultProductVo productVo = productVos.get(i-1);
            this.createProjectBase(productVos.get(i-1));
            try {
                ValidatorUtils.validator(productVo, UpdateValidatorGroup.class);
            }catch (Exception e){
                throw new McpException("["+productVo.getProductName()+"]["+productVo.getProductName()+"]["+productVo.getProjectId()+"]["+productVo.getProjectName()+"]"+e.getMessage());
            }
            productVo.setDelFlag(1);
        }
    }

    /**
     * 创建房源和项目
     *
     * @param bbsResultProductVoList
     * @param lockList
     * @param operateState
     * @param areaType
     * @return
     */
    @Override
    public List<BbsResultProductVo> createProductProject(List<BbsResultProductEntity> bbsResultProductVoList,
                                                         List<RLock> lockList,
                                                         String operateState,
                                                         String areaType) {
        List<BbsResultProductVo> productVos = this.createProducts(bbsResultProductVoList,
                lockList,
                operateState,
                areaType);
        createProjects(productVos);
        return productVos;
    }


    @Override
    public BbsResultCustomerVo createPersonFromCustomer(TzParamVo tzParamVo) {
        AppReply<JSONObject> customerAppReply = null;
        if(StringUtils.isNotBlank(tzParamVo.getCustomerNumberCode())&&StringUtils.isNotBlank(tzParamVo.getCustomerIdType())){
           customerAppReply =  bbcustomerFeignClient.selectPersonCustomerAll(tzParamVo.getCustomerNumberCode(),tzParamVo.getCustomerIdType());
        }else if(StringUtils.isNotBlank(tzParamVo.getCustomerNo())){
            customerAppReply = bbcustomerFeignClient.getPersonCustomerById(tzParamVo.getCustomerNo());
        }else{
            throw new McpException("查询客户失败：无客户ID或无证件类型和证件号码");
        }
        BbsResultCustomerVo bbsResultCustomerVo = new BbsResultCustomerVo();
        DwdCustomerVo customerVo = new DwdCustomerVo();
        try {
            if(AppReply.SUCCESS_CODE.equals(customerAppReply.getCode())&& customerAppReply.getData()!=null) {
                CopyFieldUtil.transJSONObject(customerVo, customerAppReply.getData(), CopyFieldUtil.TYPE_CUSTOMER);
                customerVo.setTenantSupplierNo(this.getNccCode("PERSON", customerVo.getCustomerNo()));//NCC
                try{
                    ValidatorUtils.validator(customerVo,InsertValidatorGroup.class);
                }catch (Exception e){
                    throw new McpException("["+bbsResultCustomerVo.getCustomerName()+"]"+e.getMessage());
                }
                log.info("客户内容" + JSON.toJSONString(customerVo));
                BeanUtils.copyProperties(customerVo, bbsResultCustomerVo);
                log.info("copy后的内容" + JSON.toJSONString(bbsResultCustomerVo));
                log.info("客户Id：" + bbsResultCustomerVo.getCustomerNo());
                bbsResultCustomerVo.setCustomerType("00");
                bbsResultCustomerVo.setType("个人");
                bbsResultCustomerVo.setCustomerRatio(1f);
                bbsResultCustomerVo.setTenantCustomerNo(customerVo.getCustomerNo());//客商编号
                if (customerAppReply.getData().getJSONObject("extendInfo") != null && customerAppReply.getData().getJSONObject("extendInfo").getString("residenceFullAddress") != null) {
                    bbsResultCustomerVo.setMailAddress(customerAppReply.getData().getJSONObject("extendInfo").getString("residenceFullAddress"));
                } else {
                    bbsResultCustomerVo.setMailAddress("无");
                }
            }else{
                throw new McpException("没有查到个人信息");
            }
        }catch (Exception e){
            throw new McpException("处理个人信息失败："+e.getMessage());
        }
        bbsResultCustomerVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
        return bbsResultCustomerVo;
    }

    /**
     * 从客户中心获取企业的基础信息
     *
     * @param tzParamVo
     * @return
     */
    @Override
    public BbsResultCustomerVo createCompanyFromCustomer(TzParamVo tzParamVo) {
        log.info("tzParamVo:{}",JSONObject.toJSON(tzParamVo));
        log.info("查询企业参数{}：",tzParamVo.getCustomerNumberCode());
        AppReply<JSONObject> customerAppReply =  bbcustomerFeignClient.getEnterpriseCustomerByIdOrCreditCode(tzParamVo.getCustomerNumberCode());
        BbsResultCustomerVo bbsResultCustomerVo = new BbsResultCustomerVo();
        try {
            log.info("客户中心企业查询结果{}",JSONObject.toJSON(customerAppReply));
            if(AppReply.SUCCESS_CODE.equals(customerAppReply.getCode())
                    &&customerAppReply.getData()!=null){
                CopyFieldUtil.transJSONObject(bbsResultCustomerVo, customerAppReply.getData(), CopyFieldUtil.TYPE_CUSTOMER);
                log.info("COPY后：{}",JSONObject.toJSON(bbsResultCustomerVo));
                bbsResultCustomerVo.setCompanySupplierNo(this.getNccCode("ORG",bbsResultCustomerVo.getCustomerNo()));
                bbsResultCustomerVo.setCompanyIdType("01");
                bbsResultCustomerVo.setCustomerType("01");
                bbsResultCustomerVo.setType("企业");
            }else{
                throw new McpException("没有获取到企业信息："+tzParamVo.getCustomerNumberCode());
            }
            try{
                ValidatorUtils.validator(bbsResultCustomerVo, InsertValidatorGroup.class);
            }catch (Exception e){
                throw new McpException("["+bbsResultCustomerVo.getCustomerName()+"]"+e.getMessage());
            }
        }catch (Exception e){
            throw new McpException("处理企业信息失败："+e.getMessage());
        }

        if(customerAppReply.getData().getJSONObject("currentAgentInfo")!=null&&customerAppReply.getData().getJSONObject("currentAgentInfo").getString("agentName")!=null){
            bbsResultCustomerVo.setContactName(customerAppReply.getData().getJSONObject("currentAgentInfo").getString("agentName"));
        }
        if(customerAppReply.getData().getJSONObject("currentAgentInfo")!=null && customerAppReply.getData().getJSONObject("currentAgentInfo").getString("agentPhone")!=null){
            bbsResultCustomerVo.setCustomerTel(customerAppReply.getData().getJSONObject("currentAgentInfo").getString("agentPhone"));
        }
        if(customerAppReply.getData().getJSONObject("currentAgentInfo")!=null&&customerAppReply.getData().getJSONObject("currentAgentInfo").getString("email")!=null){
            bbsResultCustomerVo.setEmailAddress(customerAppReply.getData().getJSONObject("currentAgentInfo").getString("email"));
        }else{
            bbsResultCustomerVo.setEmailAddress("无");
        }
        //bbsResultCustomerVo.setMailAddress(customerAppReply.getData().getString("storeLocatedAddress"));
        bbsResultCustomerVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
        bbsResultCustomerVo.setCustomerRatio(1f);
        bbsResultCustomerVo.setCustomerType("01");
        bbsResultCustomerVo.setBankIsAgreement("0");//不鉴权
        return  bbsResultCustomerVo;
    }

    /**
     * 从房态重新查询房源
     *
     * @param key
     * @param productNo
     * @param paramVo
     * @param operateState
     * @return
     */
    @Override
    public BbsResultProductVo createProductFromHouse(String key, String productNo, TzParamVo paramVo, String operateState) {
        AppReply<JSONObject> objectAppReply = searchProductFromRedis(key,productNo,operateState);
        if (Boolean.TRUE.equals(ResultUtils.checkAppReply(objectAppReply))) {
            JSONObject object = objectAppReply.getData();
            BbsResultProductVo productVo = new BbsResultProductVo();
            try {
                CopyFieldUtil.transJSONObject(productVo, object.getJSONArray("rows").getJSONObject(0), CopyFieldUtil.TYPE_HOUSE);
                String errorKey = "";
                if (org.apache.commons.lang3.StringUtils.isNotBlank(productVo.getCommunityRegion())) {
                    // 房源所在区 区字去掉 东城区->东城
                    productVo.setCommunityRegion(productVo.getCommunityRegion().replaceAll("区", ""));
                }
                if(StringUtils.isBlank(productVo.getHousePurposeName())){//实际用途如果没有值，用取得用途
                    productVo.setHousePurposeName(productVo.getHouseObtainPurposeName());
                }

                if(StringUtils.isBlank(productVo.getHouseOrientation())){
                    productVo.setHouseOrientation("无");
                }

                //验证面积类型
                if(AreaTypeEnum.BUILD_AREA_TYPE.getCode().equals(paramVo.getAreaType())){
                    if(StringUtils.isBlank(productVo.getHouseStructArea())){
                        throw  new McpException(errorKey+"建筑面积不能为空");
                    }
                }else if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(paramVo.getAreaType())){
                    if(StringUtils.isBlank(productVo.getInnerSleeveArea())){
                        throw  new McpException(errorKey+"套内建筑面积不能为空");
                    }
                }else{
                    throw  new McpException(errorKey+"错误的面积类型");
                }
                return productVo;
            } catch (Exception e) {
                throw new McpException("请求房源信息接口异常:"+e.getMessage());
            }
        }else{
            throw new McpException("获取房源失败，请重新尝试");
        }
    }


    /**
     * 获取字典信息
     * @param typeCode
     * @param meaning
     * @return
     */
    private BbsDictEntity getDictMessage(String typeCode, String meaning){
        LambdaQueryWrapper<BbsDictEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BbsDictEntity::getTypeCode, typeCode);
        queryWrapper.eq(BbsDictEntity::getMeaning,meaning);
        List<BbsDictEntity> list = baseService.select(queryWrapper);
        if(CollectionUtils.isNotEmpty(list)){
            BbsDictEntity entity = list.get(0);
            return entity;
        }else{
            throw new McpException(String.format("类型%s，名称：%s在字典里不存在",typeCode,meaning));
        }
    }

    /**
     * 创建房源实体
     * @param key
     * @param productNo
     * @return
     */
    private BbsResultProductVo createProductVo(String key, String productNo,TzParamVo paramVo,String operateState){
        BbsResultProductVo productVo = this.createProductFromHouse(key,productNo,paramVo,operateState);
        if("1".equals(paramVo.getType())) {//台账方式需要处理
            //处理租金标准
            productVo.setRentStandardUnitName(paramVo.getRentStandardUnitName());
            productVo.setRentStandardUnit(this.createRentStandardUnitCode(paramVo.getRentStandardUnitName()));
            productVo.setRentStandard(paramVo.getRentStandard());
            //处理物业费标准
            productVo.setPropStandardUnitName(paramVo.getPropStandardUnitName());
            productVo.setPropStandardUnit(this.createPropStandardUnitCode(paramVo.getPropStandardUnitName()));
            productVo.setPropStandard(paramVo.getPropStandard());
        }
        return productVo;
    }

    /**
     * 获取房源编号
     * @param paramVos
     * @return
     */
    private List<String> getProductNos(List<TzParamVo> paramVos){
        List<String> productNos = new ArrayList<>();
        paramVos.forEach(item->{
            productNos.add(item.getProductNo());
        });
        return productNos;
    }

    /**
     * 房源
     * @param result
     */
    private void houseCore(TzResult result,List<RLock> lockList,String operateState){
        for(TzParamVo vo:result.getParamVos()){
            vo.setType("1");//台账方式
            vo.setSourceNode("2");//数据来源房态
        }
        result.setProductVos(createProductFromHouse(result.getParamVos(),lockList,operateState));
    }


    /**
     * 客户
     * @param result
     */
    private void customerCore(TzResult result){
        TzParamVo firstParam = result.getParamVos().get(0);
        BbsResultCustomerVo customerVo = null;
        if("个人".equals(firstParam.getCustomerType())){
            customerVo = this.createPerson(firstParam);
        }else if("企业".equals(firstParam.getCustomerType())){
            customerVo = this.createCompany(firstParam);
        }else{
            throw new McpException("错误的客户分类");
        }
        customerVo.setContactName(firstParam.getContactName());
        customerVo.setCustomerRatio(1f);
        result.setCustomerVo(customerVo);
    }

    /**
     * 创建个人
     * @param tzParamVo

     */
    private BbsResultCustomerVo createPerson(TzParamVo tzParamVo){
        BbsResultCustomerVo bbsResultCustomerVo = this.createPersonFromCustomer(tzParamVo);
        bbsResultCustomerVo.setCustomerType("00");
        //bbsResultCustomerVo.setBankNameCode(tzParamVo.getCustomerBankCode());//总行编号
        //bbsResultCustomerVo.setBankName(tzParamVo.getCustomerBankName());//总行名称
        //bbsResultCustomerVo.setBankIsAgreement("0");//不鉴权
        //bbsResultCustomerVo.setBankCard(tzParamVo.getCustomerAccountId());
        bbsResultCustomerVo.setConsignorMobile("无");
        bbsResultCustomerVo.setConsignorName("无");
        bbsResultCustomerVo.setIntentionId("-1");//
        return bbsResultCustomerVo;
    }

    /**
     * 创建企业
     * @param tzParamVo
     */
    private BbsResultCustomerVo createCompany(TzParamVo tzParamVo){
        BbsResultCustomerVo bbsResultCustomerVo = createCompanyFromCustomer(tzParamVo);
        //bbsResultCustomerVo.setBankNameCode(tzParamVo.getCustomerBankCode());//总行编号
        //bbsResultCustomerVo.setBankName(tzParamVo.getCustomerBankName());//总行名称
        //bbsResultCustomerVo.setBankCard(tzParamVo.getCustomerAccountId());
        bbsResultCustomerVo.setConsignorMobile("无");
        bbsResultCustomerVo.setConsignorName("无");
        bbsResultCustomerVo.setIntentionId("-1");
        return  bbsResultCustomerVo;
    }

    private void projectCore(TzResult result){
        this.createProjects(result.getProductVos());
    }

    private void createProjectBase(BbsResultProductVo productVo){
        com.bonc.ioc.bzf.busisigning.feign.feign.convervo.ProjectVo pvo = new com.bonc.ioc.bzf.busisigning.feign.feign.convervo.ProjectVo();
        pvo.setProjectId(productVo.getProjectId());
        AppReply<JSONObject> project = bbprojectFeignClient.cacheSelectByProjectId(pvo);
        try {
            CopyFieldUtil.transJSONObject(productVo, project.getData(), CopyFieldUtil.TYPE_PROJECT);
        } catch (Exception e) {
            throw new McpException("转换异常");
        }
        String locationType = this.locationType(productVo);
        log.info("locationType:" + locationType);
        if ("01".equals(locationType)) {
            productVo.setOperateEntityType("01");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(project.getData().getString("affiliatedDepartment")) && org.apache.commons.lang3.StringUtils.isNotBlank(project.getData().getString("affiliatedDepartmentName"))) {
                productVo.setOperateUnitBusinessNo(project.getData().getString("affiliatedDepartment"));//获取部门的id
                productVo.setOperateUnitName(project.getData().getString("affiliatedDepartmentName"));//部门名称
            } else {
                throw new McpException("运营主体为中心的情况:部门编码[affiliatedDepartment]和部门名称[affiliatedDepartmentName]不能为空");
            }
        } else if ("02".equals(locationType)) {
            productVo.setOperateEntityType("02");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(project.getData().getString("affiliatedUnit")) && org.apache.commons.lang3.StringUtils.isNotBlank(project.getData().getString("affiliatedUnitName"))) {
                productVo.setOperateUnitBusinessNo(project.getData().getString("affiliatedUnit"));//单位编码
                productVo.setOperateUnitName(project.getData().getString("affiliatedUnitName"));//单位名称
            } else {
                throw new McpException("运营主体为子公司的情况:部门编码[affiliatedUnit]和部门名称[affiliatedUnitName]不能为空");
            }
        } else {
            throw new McpException("无效的运营主体类型:" + locationType);
        }
        this.operateUnitNo(productVo);
        this.projectAreaNo(productVo);


        if(org.apache.commons.lang3.StringUtils.isBlank(productVo.getProjectShortName())){//项目简称没有值，用项目名称
            productVo.setProjectShortName(productVo.getProjectName());
        }

        if(org.apache.commons.lang3.StringUtils.isBlank(productVo.getOperateUnitNo())){//项目运营主体NCC编码没有值，用项目运营主体编码
            productVo.setOperateUnitNo(productVo.getOperateUnitBusinessNo());
        }
        if(org.apache.commons.lang3.StringUtils.isBlank(productVo.getProjectAreaNo())){//项目运营区域NCC编码没有值，用项目运营区域编码
            productVo.setProjectAreaNo(productVo.getProjectAreaBusinessNo());
        }

        productVo.setProjectFormat("03");
        //处理项目所在小区
        createCommunityInf(productVo);

    }

    private void createCommunityInf(BbsResultProductVo vo){
        JSONArray communityInfoJsonArray = this.getCommunityInfoByHousing(vo.getProjectId());
        StringBuilder builder = new StringBuilder();
        for (int j = 0; j < communityInfoJsonArray.size(); j++) {
            builder.append(communityInfoJsonArray.getJSONObject(j).getString("communityInfoName")).append("，");
        }
        if (builder.length() > 1) {
            String name = builder.substring(0, builder.length() - 1);
            vo.setProjectEstate(name);
        }

    }

    private String locationType(BbsResultProductVo vo) {
        BmsFeignClient client = SpringContextUtils.getBean(BmsFeignClient.class);
        Request<BmsLocationRpcServiceGetOneReq> req = new Request<>();
        BmsLocationRpcServiceGetOneReq rpcServiceGetOneReq = new BmsLocationRpcServiceGetOneReq();
        rpcServiceGetOneReq.setId(vo.getOperateUnitBusinessNo());//affiliatedUnit
        req.setData(rpcServiceGetOneReq);
        Response<JSONObject> project = client.selectOne1(req);
        String locationType = null;
        if (project != null) {
            if ("200".equals(project.getCode())) {
                if (project.getData() != null) {
                    locationType = project.getData().getString("locationType");
                    if (locationType == null) {
                        throw new McpException("用户中心获取locationType 失败，查询项：" + vo.getOperateUnitNo());
                    }

                    if (locationType.indexOf("01") != -1 && locationType.indexOf("02") != -1) {
                        throw new McpException("运营主体类型类型错误：中心和子公司不能同时存在");
                    }
                    if (locationType.indexOf("01") != -1) {
                        locationType = "01";
                    }
                    if (locationType.indexOf("02") != -1) {
                        locationType = "02";
                    }

                }
            } else {
                throw new McpException("用户中心获取locationType失败");
            }
        } else {
            throw new McpException("用户中心获取locationType失败");
        }
        return locationType;
    }

    /**
     * @description: 运营单位编号
     * @author: 宋鑫
     * @date: 2022-12-31 10:15
     * @param: [vo]
     * @return: void
     * @since 1.0.0
     **/
    private void operateUnitNo(BbsResultProductVo vo){
        BmsFeignClient client = SpringContextUtils.getBean(BmsFeignClient.class);
        Request<SystemLocaIdEntity> req = new Request<>();
        SystemLocaIdEntity entity = new SystemLocaIdEntity();
        entity.setSystemName("NCC");
        entity.setLocationId(vo.getOperateUnitBusinessNo());
        req.setData(entity);
        Response<SystemLocaIdEntity> response = client.getSystemLocaIdByLocationId(req);
        if(response!=null){
            if ("200".equals(response.getCode())) {
                try {
                    if (response.getData() != null) {
                        vo.setOperateUnitNo(response.getData().getSystemLocationId());
                    } else {
                        //vo.setOperateUnitNo(null);
                        // vo.setOperateUnitNo("");
                        //throw new McpException("获取运营单位编号返回报文为空，查询项：" + entity.toString() + ",返回报文：" + response.toString());
                    }
                }catch(Exception e ){
                    throw new McpException("运营单位编号获取失败："+e.getMessage());
                }
            } else {
                throw new McpException("运营单位编号获取失败：" + response.getMessage());
            }
        }
    }

    private JSONArray getCommunityInfoByHousing(String projectId) {
        long start = System.currentTimeMillis();
        AppReply<JSONArray> appReply = bbHousingFeignClient.selectInfoNameByProjectId(projectId);
        long end = System.currentTimeMillis();
        double betweenTime = (end - start) / 1000.0;
        log.info(String.format("获取房态小区数据时间[threadName: %s, 耗时: %s秒]", Thread.currentThread().getName(), betweenTime));
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("房态小区数据获取失败[详情: %s, 项目编号: %s]", appReply, projectId));
        }
        JSONArray communityInfoJsonArray = appReply.getData();
        if (CollectionUtils.isEmpty(communityInfoJsonArray)) {
            throw new McpException(String.format("该项目下没有小区[项目编号: %s]", projectId));
        }
        return communityInfoJsonArray;
    }

    /**
     * @description: 获取项目区域编码
     * @author: 宋鑫
     * @date: 2023-03-01 12:00
     * @param: [vo]
     * @return: void
     * @since 1.0.0
     **/
    private void projectAreaNo(BbsResultProductVo vo){
        if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getProjectAreaBusinessNo())) {
            BmsFeignClient client = SpringContextUtils.getBean(BmsFeignClient.class);
            Request<SystemLocaIdEntity> req = new Request<>();
            SystemLocaIdEntity entity = new SystemLocaIdEntity();
            entity.setSystemName("NCC");
            entity.setLocationId(vo.getProjectAreaBusinessNo());
            req.setData(entity);
            Response<SystemLocaIdEntity> response = client.getSystemLocaIdByLocationId(req);
            if (response != null) {
                if ("200".equals(response.getCode())) {
                    try {
                        if (response.getData() != null) {
                            vo.setProjectAreaNo(response.getData().getSystemLocationId());
                        } else {

                        }
                    } catch (Exception e) {
                        log.error("项目区域编码获取失败：" + e.getMessage());
                    }
                } else {
                    throw new McpException("项目区域编码获取失败：" + response.getMessage());
                }
            }
        }
    }

    /**
     * 获取客户NCC编码
     * @param personOrgType
     * @param customerId
     * @return
     */
    private String getNccCode(String personOrgType, String customerId) {
        String code = null;
        if(StringUtils.isBlank(customerId)){
            throw new McpException("获取客户NCC编码，客户ID不能为空");
        }else {
            try {
                AppReply<String> appReply = bbcustomerFeignClient.getOldCode(customerId, personOrgType, "NCC", "1");
                if (AppReply.SUCCESS_CODE.equals(appReply.getCode())) {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(appReply.getData())) {
                        code = appReply.getData();
                        if (org.apache.commons.lang3.StringUtils.isBlank(code)) {
                            log.error("没有获取到客商编号：客户id；" + customerId + ",分类：" + personOrgType);
                            throw new McpException("获取客商编号失败");
                        }
                    } else {
                        log.error("没有获取到客商编号：客户id；" + customerId + ",分类：" + personOrgType);
                        throw new McpException("获取客商编号失败");
                    }
                } else {
                    throw new McpException("获取客商编号失败");
                }
            } catch (Exception e) {
                log.error("获取客户编号失败：" + e.getMessage());
                throw new McpException("获取客商编号失败");
            }
        }
        return code;
    }

    private List<BbsSignIncrementalConfigVo> createBbsSignIncrementalConfigVo(TzParamVo paramVo){
        List<BbsSignIncrementalConfigVo> list = new ArrayList<>();
        //租金处理
        if(StringUtils.isNotBlank(paramVo.getAdjustmentPointName())
                &&paramVo.getTimePoint()!=null
                &&StringUtils.isNotBlank(paramVo.getUnitName())
                &&paramVo.getIncrease()!=null
        ){
            BbsSignIncrementalConfigVo vo = new BbsSignIncrementalConfigVo();
            this.createAdjustmentPoint(paramVo.getAdjustmentPointName(),vo);
            this.createUnit(paramVo.getUnitName(), vo);
            vo.setIncrease(paramVo.getIncrease());
            vo.setTimePoint(paramVo.getTimePoint());
            vo.setStandardType("rent");
            vo.setDelFlag(1);
            list.add(vo);
        }


        //物业费处理
        if(StringUtils.isNotBlank(paramVo.getPropAdjustmentPointName())
                &&paramVo.getPropTimePoint()!=null
                &&StringUtils.isNotBlank(paramVo.getPropUnitName())
                &&paramVo.getPropIncrease()!=null
        ){
            BbsSignIncrementalConfigVo vo = new BbsSignIncrementalConfigVo();
            this.createAdjustmentPoint(paramVo.getPropAdjustmentPointName(),vo);
            this.createUnit(paramVo.getPropUnitName(), vo);
            vo.setIncrease(paramVo.getPropIncrease());
            vo.setTimePoint(paramVo.getPropTimePoint());
            vo.setStandardType("prop");
            vo.setDelFlag(1);
            list.add(vo);
        }
        return list;
    }

    private void createAdjustmentPoint(String adjustmentPointName, BbsSignIncrementalConfigVo vo){
        if(StringUtils.isNotBlank(adjustmentPointName)) {
            if ("每".equals(adjustmentPointName)) {
                vo.setAdjustmentPoint("every");
                vo.setAdjustmentPointName("每");
            } else if ("第".equals(adjustmentPointName)) {
                vo.setAdjustmentPoint("appoint");
                vo.setAdjustmentPointName("第");
            } else {
                throw new McpException("错误参数");
            }
        }
    }

    /**
     * 递增单位
     * @param unitName
     * @param vo
     */
    private void createUnit(String unitName, BbsSignIncrementalConfigVo vo){
        if(StringUtils.isNotBlank(unitName)) {
            if ("年".equals(unitName)) {
                vo.setUnit("year");
                vo.setUnitName("年");
            } else if ("月".equals(unitName)) {
                vo.setUnit("month");
                vo.setUnitName("月");
            } else {
                throw new McpException("错误参数");
            }
        }
    }

    /**
     * 签约结果核心处理
     * @param result
     */
    private void signCore(TzResult result){
        String projectId = result.getProductVos().get(0).getProjectId();
        String customerId = result.getCustomerVo().getCustomerNo();
        BbsSignInfoVo signInfoVo = createSignVo(result.getParamVos(),projectId,customerId);
        result.setSignInfoVo(signInfoVo);
    }

    /**
     * 创建签约结果实体
     * @param paramVos
     * @param projectId
     * @param customerId
     * @return
     */
    private BbsSignInfoVo createSignVo(List<TzParamVo> paramVos,String projectId,String customerId){
        TzParamVo paramVo = paramVos.get(0);
        BbsSignInfoVo signInfoVo = new BbsSignInfoVo();
        //写死相关字段
        signInfoVo.setDelFlag(1);
        signInfoVo.setContractType("1");//普通合同
        signInfoVo.setUploadFileNote("无");
        signInfoVo.setSignType("1");//新签
        signInfoVo.setInvoicingBeforePayment("0"); //是否支持先开票后付款(0.否 1.是)
        signInfoVo.setProductSourceType("99");//台账
        signInfoVo.setSyncPayment("0");//初始化
        signInfoVo.setSignStatus("3");//已签约
        signInfoVo.setTransactorType("2");//纸质签约
        signInfoVo.setContractTemplateId(createContractTemplateId(paramVos));//创建合同模版
        signInfoVo.setCreateUserName(currentUserName());
        signInfoVo.setSignTime(paramVo.getContractBeginTime());//用合同开始日期作为签约时间


        //动态获取
        signInfoVo.setContractFees(createContractFees(paramVo.getContractFees()));//合同费用
        signInfoVo.setContractCode(paramVo.getContractCode());//合同编号
        signInfoVo.setContractBeginTime(paramVo.getContractBeginTime());//合同开始日期
        signInfoVo.setContractEndTime(paramVo.getContractEndTime());//合同结束日期
        signInfoVo.setPaymentCycleCode(createPaymentCycle(paramVo.getPaymentCycleName()));//缴费周期

        signInfoVo.setCashPledgeCode("9");//自定义押金
        signInfoVo.setCashPledgeValue(this.createCashPledge(paramVo.getCachePleadgeValue()));//押金
        signInfoVo.setRentTaxRate(taxrate(projectId,paramVo.getCustomerType(),customerId,"01"));//租金税率
        if(signInfoVo.getContractFees().equals("02-08")){
            signInfoVo.setPropTaxRate(taxrate(projectId,paramVo.getCustomerType(),customerId,"07"));//物业费税率
        }
        this.setFree(paramVo,signInfoVo,"rent");//免租期租金
        this.setFree(paramVo,signInfoVo,"prop");//免租期物业费

        signInfoVo.setFirstBankNameCode(paramVo.getFirstBankNameCode());//甲方总行编号
        signInfoVo.setFirstBankName(paramVo.getFirstBankName());//甲方总行名称
        signInfoVo.setFirstAccountId(paramVo.getFirstAccountId());//甲方总行卡号
        signInfoVo.setFirstAccountName(paramVo.getFirstName());//甲方开户名称，使用运营主体
        this.createBusinessFormat(signInfoVo,paramVo);//业态设置
        signInfoVo.setIncrementalConfigVoList(this.createBbsSignIncrementalConfigVo(paramVo));
        this.setIncrentment(signInfoVo,"rent");//租金递增率标识
        this.setIncrentment(signInfoVo,"prop");//物业费递增率标识


        //验证面积类型
        if(AreaTypeEnum.BUILD_AREA_TYPE.getCode().equals(paramVo.getAreaType())){
            signInfoVo.setAreaType(AreaTypeEnum.BUILD_AREA_TYPE.getCode());
        }else if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(paramVo.getAreaType())){
            signInfoVo.setAreaType(AreaTypeEnum.BUILD_AREA_TYPE.getCode());
        }else{
            throw  new McpException("错误的面积类型");
        }
        return signInfoVo;
    }

    /**
     * 获取缴费周期编码
     * @param paymentCycleName
     * @return
     */
    private String createPaymentCycle(String paymentCycleName){
        BbsDictEntity dict = this.getDictMessage("PAYMENT_CYCLE_CODE",paymentCycleName);
        if(dict!=null){
            return dict.getCode();
        }else{
            throw new McpException("无法识别的缴费周期:"+paymentCycleName);
        }
    }

    /**
     * 合同费用
     * @param contractFees
     * @return
     */
    private String createContractFees(String contractFees){
        BbsDictEntity dict = this.getDictMessage("CONTRACT_FEES",contractFees);
        if(dict!=null){
            return dict.getCode();
        }else{
            throw new McpException("无法识别的合同费用:"+contractFees);
        }
    }

    /**
     * 租金标准码值
     * @param rentStandardUnitName
     * @return
     */
    public String createRentStandardUnitCode(String rentStandardUnitName){
        if(StringUtils.isNotBlank(rentStandardUnitName)) {
            BbsDictEntity dict = this.getDictMessage("COST_UNIT", rentStandardUnitName);
            if (dict != null) {
                return dict.getCode();
            } else {
                throw new McpException("无法识别的租金标准:" + rentStandardUnitName);
            }
        }else{
            return null;
        }
    }

    /**
     * 物业费标准码值
     * @param rentStandardUnitName
     * @return
     */
    private String createPropStandardUnitCode(String rentStandardUnitName){
        if(StringUtils.isNotBlank(rentStandardUnitName)) {
            BbsDictEntity dict = this.getDictMessage("PROP_FREE_UNIT", rentStandardUnitName);
            if (dict != null) {
                return dict.getCode();
            } else {
                throw new McpException("无法识别的物业费标准:" + rentStandardUnitName);
            }
        }else{
            return null;
        }
    }

    /**
     * 获取押金
     * @param cashPledge
     * @return
     */
    private String createCashPledge(Double cashPledge){
        if(cashPledge!=null){
            return String.valueOf(cashPledge);
        }else{
            throw new McpException("押金不能为空");
        }
    }

    private TaxrateParamVo createTaxBean(String projectId, String userType, String customerId, String chargeItemId){
        TaxrateParamVo taxrateParamVo =new TaxrateParamVo();
        taxrateParamVo.setProjectId(projectId);
        taxrateParamVo.setYeTai("03");
        taxrateParamVo.setChargeItemId(chargeItemId);
        if("企业".equals(userType)){
            taxrateParamVo.setTenantry("02");
            taxrateParamVo.setCompanyId(customerId);
        }else if("个人".equals(userType)){
            taxrateParamVo.setTenantry("03");
        }
        taxrateParamVo.setCurrent("1");
        taxrateParamVo.setSize("10");
        return taxrateParamVo;
    }

    /**
     * 获取税率
     * @param projectId
     * @param userType
     * @param customerId
     * @param chargeItemId
     * @return
     */
    public Double taxrate(String projectId, String userType, String customerId, String chargeItemId){
        log.info(String.format("税率-----项目：%s,客户类型：%s,客户id:%s,科目：%s",projectId,userType,customerId,chargeItemId));
        TaxrateParamVo taxrateParamVo = this.createTaxBean(projectId,userType,customerId,chargeItemId);
        BankRequestVo<TaxrateParamVo> bankRequestVo = new BankRequestVo<>();
        bankRequestVo.setData(taxrateParamVo);
        log.info("税率请求：" + JSON.toJSONString(bankRequestVo));
        ChargeRespondVo<ChargePageVo<List<TaxrateResultVo>>> result = this.settlementFeignClient.taxrateQusery(bankRequestVo);
        log.info("税率返回" + JSON.toJSONString(result));
        if("00000".equals(result.getCode())){
            if(result!=null && result.getData()!=null && result.getData().getRecords()!=null && !result.getData().getRecords().isEmpty()){
                TaxrateResultVo taxrateResultVo = result.getData().getRecords().get(0);
                String taxrate = "";
                if("企业".equals(userType)){
                    taxrate = taxrateResultVo.getCompanyTaxRate();
                }else if("个人".equals(userType)){
                    taxrate = taxrateResultVo.getPersonTaxRate();
                }
                if(StringUtils.isNotBlank(taxrate)){
                    taxrate = taxrate.replaceAll("%","");
                    return Double.valueOf(taxrate);
                }else{
                    throw new McpException("税率为空");
                }
            }else{
                throw new McpException("获取税率失败");
            }
        }else{
            throw new McpException("获取税率失败");
        }
    }

    /**
     * 免租期设置
     * @param paramVo
     * @param signInfoVo
     * @param type
     */
    private void setFree(TzParamVo paramVo,BbsSignInfoVo signInfoVo,String type){
        String start;
        String end;
        String[] tmpList;
        java.util.Date startDate = null;
        java.util.Date endDate = null;
        if("rent".equals(type)){
            if(StringUtils.isNotBlank(paramVo.getFreeRent())) {
//                tmp = paramVo.getFreeRent().split("~");
                tmpList = paramVo.getFreeRent().split(",");
                signInfoVo.setRentFreePeriodType("1");//区间
            }else{
                signInfoVo.setRentFreePeriodType("0");//租金无增长
                return;
            }
        }else if("prop".equals(type)){
            if(StringUtils.isNotBlank(paramVo.getFreeProp())) {
//                tmp = paramVo.getFreeProp().split("~");
                tmpList = paramVo.getFreeProp().split(",");
                signInfoVo.setPropFreePeriodType("1");//区间
            }else {
                signInfoVo.setPropFreePeriodType("0");//物业费无增长
                return;
            }
        }else{
            throw new McpException("错误的免租金类型"+type);
        }
        java.text.SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<BbsSignFpIntervalVo> list = new ArrayList<>();
        for (String date : tmpList) {
            String[] tmp = date.split("~");
            start = tmp[0];
            end   = tmp[1];
            try {
                startDate = sdf.parse(start);
            } catch (ParseException e) {
                throw new McpException("开始是日期格式错误："+start);
            }
            try {
                endDate = sdf.parse(end);
            } catch (ParseException e) {
                throw new McpException("结束是日期格式错误："+end);
            }
            Long day = DateUtil.between(startDate,endDate, DateUnit.DAY) + 1;
            BbsSignFpIntervalVo bbsSignFpIntervalVo = new BbsSignFpIntervalVo();
            bbsSignFpIntervalVo.setSignInfoId(signInfoVo.getSignId());
            bbsSignFpIntervalVo.setStandardType(type);
            bbsSignFpIntervalVo.setStart(start);
            bbsSignFpIntervalVo.setEnd(end);
            bbsSignFpIntervalVo.setDelFlag(1);
            list.add(bbsSignFpIntervalVo);
            /*if("rent".equals(type)){
                signInfoVo.setRentFreePeriodType("2");//固定值
                signInfoVo.setRentFpFixedDate(start);//开始日期
                signInfoVo.setRentFpFixedValue(Integer.valueOf(day.toString()));

            }else{
                signInfoVo.setPropFreePeriodType("2");//固定值
                signInfoVo.setPropFpFixedDate(start);//开始日期
                signInfoVo.setPropFpFixedValue(Integer.valueOf(day.toString()));
            }*/
        }
        signInfoVo.setBbsSignFpIntervalVoList(list);
    }

    private boolean filter(List<BbsSignIncrementalConfigVo> incrementalConfigVoList,String type){
        if(incrementalConfigVoList!=null) {
            return incrementalConfigVoList.stream().anyMatch(vo -> type.equals(vo.getStandardType()));
        }else{
            return false;
        }
    }

    /**
     * 增脏标识
     * @param signInfoVo
     * @param type
     */
    private void setIncrentment(BbsSignInfoVo signInfoVo,String type){
        boolean result = this.filter(signInfoVo.getIncrementalConfigVoList(),type);
        if(type.equals("rent")){
            if(result){
                signInfoVo.setRentIncrementalFlag("1");
                signInfoVo.setRentIncrementalType("1");
            }else{
                signInfoVo.setRentIncrementalFlag("0");
            }
        }else{
            if(result){
                signInfoVo.setPropIncrementalFlag("1");
                signInfoVo.setPropIncrementalType("1");
            }else{
                signInfoVo.setPropIncrementalFlag("0");
            }
        }
    }

    /**
     * 获取业态
     * @param signInfoVo
     * @param tzParamVo
     */

//    private void createBusinessFormat(BbsSignInfoVo signInfoVo,TzParamVo tzParamVo){
//        if(StringUtils.isNotBlank(tzParamVo.getBusinessFormatName())){
//            String params[] = tzParamVo.getBusinessFormatName().split("-");
//            if(params.length == 2) {
//                String pname = params[0];//一级名称
//                String cname = params[1];//二级名称
//                List<BbsDictVo> list = this.baseService.selectBusinessFormat();
//                Optional<BbsDictVo> optional = list.stream().filter(item -> item.getMeaning().equals(pname)).findFirst();
//                if (optional.isPresent()) {
//                    BbsDictVo parent = optional.get();
//                    Optional<BbsDictVo> parentOptional = list.stream().filter(item -> item.getParentCode().equals(parent.getCode()) && cname.equals(item.getMeaning())).findFirst();
//                    if (parentOptional.isPresent()) {
//                        BbsDictVo child = parentOptional.get();
//                        signInfoVo.setBusinessFormatName(createBusinessFormatName(parent, child));
//                        signInfoVo.setBusinessFormat(createBusinessFormatCode(parent, child));
//                    } else {
//                        throw new McpException("一级业态"+pname+"下，不存在二级分类业态：" + cname);
//                    }
//
//                } else {
//                    throw new McpException("不存在一级分类业态：" + pname);
//                }
//            }else{
//                throw new McpException("请正确输入业态格式：如：餐饮-餐馆");
//            }
//        }else{
//            throw new McpException("业态不能为空");
//        }
//    }
    private void createBusinessFormat(BbsSignInfoVo signInfoVo,TzParamVo tzParamVo){
        if(StringUtils.isNotBlank(tzParamVo.getBusinessFormatName())){
            List<BbsDictVo>  list = this.baseService.selectBusinessFormat();
            Optional<BbsDictVo> optional = list.stream().filter(item->item.getMeaning().equals(tzParamVo.getBusinessFormatName())).findFirst();
            if(optional.isPresent()){
                BbsDictVo child = optional.get();
                Optional<BbsDictVo> parentOptional = list.stream().filter(item->item.getCode().equals(child.getParentCode())).findFirst();
                if(parentOptional.isPresent()){
                    BbsDictVo parent= parentOptional.get();
                    signInfoVo.setBusinessFormatName(createBusinessFormatName(parent,child));
                    signInfoVo.setBusinessFormat(createBusinessFormatCode(parent,child));
                }else{
                    throw new McpException("不存在一级分类业态："+tzParamVo.getBusinessFormatName());
                }

            }else{
                throw new McpException("不支持的业态："+tzParamVo.getBusinessFormatName());
            }
        }else{
            throw new McpException("业态不能为空");
        }
    }

    /**
     * 获取业态JSON编码
     * @param p
     * @param c
     * @return
     */
    private String createBusinessFormatCode(BbsDictVo p,BbsDictVo c){
        List<BusinessFormatVo> pList = new ArrayList<>();
        BusinessFormatVo pvo = new BusinessFormatVo();
        pvo.setKey(p.getCode());
        pvo.setLabel(p.getMeaning());

        List<BusinessFormatVo> cList = new ArrayList<>();
        BusinessFormatVo cvo = new BusinessFormatVo();
        cvo.setKey(c.getCode());
        cvo.setLabel(c.getMeaning());
        cList.add(cvo);
        pvo.setChild(cList);

        pList.add(pvo);
        return JSON.toJSONString(pList);
    }

    /**
     * 获取业态名称
     * @param p
     * @param c
     * @return
     */
    private String createBusinessFormatName(BbsDictVo p,BbsDictVo c){
        return p.getMeaning()+"-"+c.getMeaning();
    }


    private List<String> addLock(List<String> products, List<RLock> lockList) {
        List<String> productCodeList = new ArrayList<>();
        for (String productNo : products) {
            RLock rLock = redissonClient.getLock("product#" + productNo + "-" + "HouseJob");
            if (rLock.tryLock()) {
                lockList.add(rLock);
                productCodeList.add(productNo);
            } else {
                throw new McpException("选择房源失败，请重新尝试");
            }
            productCodeList.add(productNo);
        }
        return productCodeList;
    }

    public static void main(String[] args) {
        java.util.Date startDate = null;
        java.util.Date endDate = null;
        String start = "2024-11-01";
        String end   = "2025-01-31";
        java.text.SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            startDate = sdf.parse(start);
        } catch (ParseException e) {
            throw new McpException("开始是日期格式错误："+start);
        }
        try {
            endDate = sdf.parse(end);
        } catch (ParseException e) {
            throw new McpException("结束是日期格式错误："+end);
        }
        Long day = DateUtil.between(startDate,endDate, DateUnit.DAY)+1;
        System.out.println(day);
    }

    /**
     * 同步修改房源状态到待签约
     * @param productVos
     */
    public void synHouseStatus(List<BbsResultProductVo> productVos ){
        try {
            /**
             * 更改房态状态
             **/
            List<BbhgOpState> list = new ArrayList<>();
            for(BbsResultProductVo productVo: productVos) {
                BbhgOpState bbhgOpState = new BbhgOpState();
                bbhgOpState.setOperatingState("15");//待签约
                bbhgOpState.setCode(productVo.getProductNo());
                list.add(bbhgOpState);
            }

            BbhgOpStateReq bbhgOpStateReq = new BbhgOpStateReq();
            bbhgOpStateReq.setBbhgOpStates(list);
            log.info("商业房源修改成待签约"+JSON.toJSONString(list));
            syncState(bbhgOpStateReq);
        } catch (Exception e) {
            throw new McpException("更新房源状态失败:" + e.getMessage());
        }
    }

    /**
     * 同步修改房源状态到待签约
     *
     * @param products
     */
    @Override
    public void synHouseEntityStatus(List<BbsResultProductEntity> products) {
        try {
            /**
             * 更改房态状态
             **/
            List<BbhgOpState> list = new ArrayList<>();
            for(BbsResultProductEntity productVo: products) {
                BbhgOpState bbhgOpState = new BbhgOpState();
                bbhgOpState.setOperatingState("15");//待签约
                bbhgOpState.setCode(productVo.getProductNo());
                list.add(bbhgOpState);
            }
            BbhgOpStateReq bbhgOpStateReq = new BbhgOpStateReq();
            bbhgOpStateReq.setBbhgOpStates(list);
            syncState(bbhgOpStateReq);
        } catch (Exception e) {
            throw new McpException("更新房源状态失败:" + e.getMessage());
        }
    }

    /**
     * 解锁
     * @param lockList
     */
    public void unlock(List<RLock> lockList) {
        for (RLock rLock : lockList) {
            try {
                rLock.unlock();
            } catch (Exception e) {
                log.error(String.format("解锁失败[详情: %s]", e.getMessage()), e);
            }
        }
    }
    private void syncState(BbhgOpStateReq bbhgOpStateReq) {
        log.info(JSON.toJSONString(bbhgOpStateReq));
        AppReply appReply = bbHousingFeignClient.syncState(bbhgOpStateReq);
        log.info("调用房态修改房源结果："+JSON.toJSONString(appReply));
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("房态房源状态同步失败[详情: %s, 产品编号: %s]",
                    appReply, bbhgOpStateReq.getBbhgOpStates().get(0).getCode()));
        }
    }

    private void updatePushStatus(UpdatePushStatusVo updatePushStatus){
        log.info(JSON.toJSONString(updatePushStatus));
        AppReply appReply = bzfSystemCommercialFeignClient.updatePushStatus(updatePushStatus);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("商业内控房源状态同步失败[详情: %s, 产品编号: %s]",
                    appReply, updatePushStatus.getProductCode()));
        }
    }

    /**
     * 待签约后同步商业内控
     * @param bbsResultProductVoList
     */
    public void updatePushStatusSign(List<BbsResultProductVo> bbsResultProductVoList,String whetherPushSign){
       try {
           List<BbsResultProductVo> result = bbsResultProductVoList.stream().filter(item -> SourceNodeEnum.BUSINESS.getCode().equals(item.getSourceNode())).collect(Collectors.toList());
           result.forEach(item -> {
               UpdatePushStatusVo updatePushStatusVo = new UpdatePushStatusVo();
              // updatePushStatusVo.setSignType("1");
               updatePushStatusVo.setProductCode(item.getProductNo());
             //  updatePushStatusVo.setAction("1");
               updatePushStatusVo.setWhetherPushSign(whetherPushSign);
               this.updatePushStatus(updatePushStatusVo);
           });
       }catch (Exception e){
           log.error("新签-待签约-同步商业内控失败：{}",e.getMessage());
       }
    }

    /**
     * 续租同步商业内控
     *
     * @param list
     */
    @Override
    public void updatPushRewallStatus(List<BbsRenewalProductVo> list) {
        try {
            list.forEach(item -> {
                UpdatePushStatusVo updatePushStatusVo = new UpdatePushStatusVo();
                updatePushStatusVo.setSignType("2");
                updatePushStatusVo.setProductCode(item.getProductNo());
                updatePushStatusVo.setAction("1");
                this.updatePushStatus(updatePushStatusVo);
            });
        }catch (Exception e){
            log.error("续签-待签约-同步商业内控失败：{}",e.getMessage());
        }
    }

    /**
     * 续租后同步商业呢空
     * @param bbsResultProductVoList
     */
    public void updatePushStatusRenewal(List<BbsRenewalProductVo> bbsResultProductVoList){
        try {
            List<BbsRenewalProductVo> result = bbsResultProductVoList.stream().filter(item -> SourceNodeEnum.BUSINESS.getCode().equals(item.getSourceNode())).collect(Collectors.toList());
            result.forEach(item -> {
                UpdatePushStatusVo updatePushStatusVo = new UpdatePushStatusVo();
                updatePushStatusVo.setSignType("2");
                updatePushStatusVo.setProductCode(item.getProductNo());
                updatePushStatusVo.setAction("1");
                this.updatePushStatus(updatePushStatusVo);
            });
        }catch (Exception e){
            log.error("续签-待签约-同步商业内控失败：{}",e.getMessage());
        }
    }

    /**
     * 查询房源运营装填
     *
     * @param signId
     * @return
     */
    @Override
    public List<BbsResultProductEntity> selectSignHouseHistoryState(String signId) {
        return this.bbsResultProductMapper.selectBySignId(signId);
    }

    @Override
    public void restHouseHistoryState(List<BbsResultProductEntity> houseList) {
        try {
            /**
             * 更改房态状态
             **/
            List<BbhgOpState> list = new ArrayList<>();
            for(BbsResultProductEntity productVo: houseList) {
                BbhgOpState bbhgOpState = new BbhgOpState();
                bbhgOpState.setOperatingState(productVo.getHistoryState());
                bbhgOpState.setCode(productVo.getProductNo());
                list.add(bbhgOpState);
            }
            BbhgOpStateReq bbhgOpStateReq = new BbhgOpStateReq();
            bbhgOpStateReq.setBbhgOpStates(list);
            syncState(bbhgOpStateReq);
        } catch (Exception e) {
            throw new McpException("更新房源状态失败:" + e.getMessage());
        }
    }





    /**
     * 获取合同模版ID
     * @param paramVos
     * @return
     */
    private String createContractTemplateId(List<TzParamVo> paramVos){
        String userType = paramVos.get(0).getCustomerType();
        int houseSize = paramVos.size();
        if("企业".equals(userType)&&houseSize==1){
            return "shangye02-08-01";
        }else if("企业".equals(userType) && houseSize > 1){
            return "shangye02-08-03";
        }else if("个人".equals(userType)&&houseSize==1){
            return "shangye02-08-02";
        }else if("个人".equals(userType) && houseSize > 1){
            return "shangye02-08-04";
        }else{
            return "";
        }
    }

    /**
     * 返货房源数据来源
     * @param paramVo
     * @return
     */
    private String createHouseSourceNode(TzParamVo paramVo){
        if(SourceNodeEnum.BUSINESS.getCode().equals(paramVo.getSourceNode())
                || org.apache.commons.lang3.StringUtils.isBlank(paramVo.getSourceNode())){//来源商业 意向登记
           return SourceNodeEnum.BUSINESS.getCode();
        }else if(SourceNodeEnum.HOUSING.getCode().equals(paramVo.getSourceNode())){ //来源房态
           return SourceNodeEnum.HOUSING.getCode();
        }else {
            throw new McpException("错误的房源数据来源["+paramVo.getSourceNode()+"]");
        }
    }


    private String currentUserId(){
        //return CurrentUtil.getUserId();
        return "-1";
    }

    private String currentUserName(){
        //CurrentUtil.getUserInfo().getAccName();
        return "--";
    }
}
