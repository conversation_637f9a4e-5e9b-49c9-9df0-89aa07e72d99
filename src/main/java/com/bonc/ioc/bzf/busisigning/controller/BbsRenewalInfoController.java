package com.bonc.ioc.bzf.busisigning.controller;

import cn.hutool.core.util.StrUtil;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbcSignContractVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalInfoExtService;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalInfoService;
import com.bonc.ioc.bzf.busisigning.service.IBbsSignInfoExtService;
import com.bonc.ioc.bzf.busisigning.utils.DateUtils;
import com.bonc.ioc.bzf.busisigning.utils.ExportExcel;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.utils.common.convert.UserPoint;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 续约
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
@Slf4j
@RestController
@RequestMapping("/renewal")
@Api(tags = "续约")
@Validated
public class BbsRenewalInfoController {

    @Resource
    private IBbsRenewalInfoService baseService;

    @Resource
    private IBbsSignInfoExtService iBbsSignInfoExtService;

    @Resource
    private IBbsRenewalInfoExtService iBbsRenewalInfoExtService;

    /**
     * 续约预览/查看 合同信息
     *
     * @param signId 续约ID
     * @param viewType true-预览和单条查看
     */
    @GetMapping(value = "/renewalView", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "King-Y")
    @ApiOperation(value = "续约预览/查看 合同信息", notes = "续约预览/查看 合同信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<RenewalingSaveVo> renewalView(@RequestParam(value = "signId",required = false) String signId, @RequestParam(value = "contractCode",required = false) String contractCode, @RequestParam("viewType")Boolean viewType) {
        if (StrUtil.isEmpty(signId) && StrUtil.isEmpty(contractCode)){
            throw new McpException("续约id和合同code不能同时为空");
        }
        return AppReply.success(iBbsRenewalInfoExtService.renewalView(signId, contractCode, viewType));
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @GetMapping(value = "/selectLable", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "King-Y")
    @ApiOperation(value = "列表及条件查询", notes = "列表及条件查询", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsRenewalInfoPageListResultVo>>> selectByPageRecord(SelectByPageVo vo){
        try {
            AppReply<PageResult<List<BbsRenewalInfoPageListResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
            appReply.setData(baseService.selectByPage(vo));
            return appReply;
        } catch (Exception e) {
            log.error("=======================================续约管理分页查询异常，异常信息为：" + e.getMessage(), e);
            AppReply<PageResult<List<BbsRenewalInfoPageListResultVo>>> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
     }

    /**
     * selectByPageRecord 分页查询
     * @param vo 查询合同列表
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-28
     * @change
     * 2023-09-28 by King-Y for init
     */
    @GetMapping(value = "/selectContactLable", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 20, author = "King-Y")
    @ApiOperation(value = "查询合同列表", notes = "查询合同列表", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsRenewalInfoPageListResultVo>>> selectContactLable(SelectByPageVo vo){
        try {
            AppReply<PageResult<List<BbsRenewalInfoPageListResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
            appReply.setData(baseService.selectByPageContact(vo));
            return appReply;
        } catch (Exception e) {
            log.error("=======================================合同列表分页查询异常，异常信息为：" + e.getMessage(), e);
            AppReply<PageResult<List<BbsRenewalInfoPageListResultVo>>> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * selectCustomer 续约列表获取租户信息（卡片）
     *
     * @param signId 续约ID
     */
    @GetMapping(value = "/selectCustomer", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "King-Y")
    @ApiOperation(value = "续约列表获取租户信息", notes = "续约列表获取租户信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsRenewalCustomerVo> selectCustomer(@RequestParam("signId") String signId) {
        try {
            AppReply<BbsRenewalCustomerVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(iBbsRenewalInfoExtService.selectCustomer(signId));
            return appReply;
        } catch (Exception e) {
            log.error("=======================================续约列表获取租户信息异常，异常信息为：" + e.getMessage(), e);
            AppReply<BbsRenewalCustomerVo> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 商铺地址级联查询
     */
    @GetMapping(value = "/addressCascadeQuery", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "King-Y")
    @ApiOperation(value = "商铺地址级联查询", notes = "商铺地址级联查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<AddressCascadeQueryResultVo>> addressCascadeQuery(AddressCascadeQueryVo queryVo) {
        try {
            AppReply<List<AddressCascadeQueryResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(iBbsRenewalInfoExtService.addressCascadeQuery(queryVo));
            return appReply;
        } catch (Exception e) {
            log.error("=======================================续约列表获取租户信息异常，异常信息为：" + e.getMessage(), e);
            AppReply<List<AddressCascadeQueryResultVo>> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 续约终止
     *
     * @param signId 续约ID
     */
    @GetMapping(value = "/signStop", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "King-Y")
    @ApiOperation(value = "续约终止", notes = "续约终止")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply signStop(@RequestParam("signId") String signId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            iBbsRenewalInfoExtService.signStop(signId);
            return appReply;
        } catch (Exception e) {
            log.error("========================签约终止异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 续约删除
     *
     * @param signId 续约ID
     */
    @GetMapping(value = "/deleteSign", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "King-Y")
    @ApiOperation(value = "续约删除", notes = "续约删除")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply deleteSign(@RequestParam("signId") String signId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            iBbsRenewalInfoExtService.deleteSign(signId);
            return appReply;
        } catch (Exception e) {
            log.error("========================续约删除异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 续约删除
     *
     * @param
     */
    @GetMapping(value = "/deleteSignByRequestId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "签约删除", notes = "签约删除")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply deleteSignByRequestId(@RequestParam("requestId") String requestId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            iBbsRenewalInfoExtService.deleteSignByRequestId(requestId);
            return appReply;
        } catch (Exception e) {
            log.error("签约删除异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }










    /**
     * 续约撤回
     *
     * @param signId 续约ID
     */
    @GetMapping(value = "/recallSign", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "King-Y")
    @ApiOperation(value = "续约撤回", notes = "续约撤回")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply recallSign(@RequestParam("signId") String signId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            iBbsRenewalInfoExtService.recallSign(signId);
            return appReply;
        } catch (Exception e) {
            log.error("=======================续约撤回异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 续约预览里 合同模板的预览与下载本地（合同模板）
     *
     * @param signId 续约ID
     * @param type 1-加水印 2-不加水印
     */
    @GetMapping(value = "/previewAndDownload", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "King-Y")
    @ApiOperation(value = "合同模板的预览与下载（合同模板）", notes = "续约预览里 合同模板的预览与下载（合同模板）")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<FIleResultVo> previewAndDownload(@RequestParam("signId") String signId, @RequestParam("type") String type,@RequestParam(value = "appFlag",required = false,defaultValue = "false") boolean appFlag) {
        try {
            AppReply<FIleResultVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(iBbsRenewalInfoExtService.previewAndDownload(signId, type, appFlag));
            return appReply;
        } catch (Exception e) {
            log.error("=======================================续约预览里合同模板的预览与下载异常，异常信息为：" + e.getMessage(), e);
            AppReply<FIleResultVo> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 续约阅览里模板合同下载（短信下载）
     *
     */
    @PostMapping(value = "/sendMessageDownTemplateContract", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "King-Y")
    @ApiOperation(value = "续约阅览里模板合同下载（短信下载）", notes = "续约阅览里模板合同下载（短信下载）")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Boolean> sendMessageDownTemplateContract(@RequestBody DownTemplateVo vo) {
        try {
            return iBbsRenewalInfoExtService.sendMessageDownTemplateContract(vo.getSignId(), vo.getType(), vo.getText());
        } catch (Exception e) {
            log.error("=======================================续约阅览里模板合同下载（短信下载）异常，异常信息为：" + e.getMessage(), e);
            return new AppReply<>(AppReply.ERROR_CODE, e.getMessage(), null);
        }
    }

    /**
     * 续签阅览里模板合同下载（邮箱下载）
     */
    @PostMapping(value = "/downloadTemplateContractForEmail", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "King-Y")
    @ApiOperation(value = "续签阅览里模板合同下载（邮箱下载）", notes = "续签阅览里模板合同下载（邮箱下载）")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Boolean> downloadTemplateContractForEmail(@RequestBody DownTemplateVo vo) {
        try {
            return iBbsRenewalInfoExtService.downloadTemplateContractForEmail(vo.getSignId(), vo.getType(), vo.getText());
        } catch (Exception e) {
            log.error("=======================================续签阅览里模板合同下载（邮箱下载）异常，异常信息为：" + e.getMessage(), e);
            return new AppReply<>(AppReply.ERROR_CODE, e.getMessage(), null);
        }

    }

    /**
     * 续签列表下载合同（下载已签字的纸质合同）
     *
     * @param signId 签约ID
     */
    @GetMapping(value = "/downloadContract", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "King-Y")
    @ApiOperation(value = "续签列表下载合同（下载已签字的纸质合同）", notes = "续签列表下载合同（下载已签字的纸质合同）")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<FIleResultVo> downloadContract(@RequestParam("signId") String signId) {
        try {
            AppReply<FIleResultVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(iBbsRenewalInfoExtService.downloadContract(signId));
            return appReply;
        } catch (Exception e) {
            log.error("=======================================续签预览里合同模板的预览与下载异常，异常信息为：" + e.getMessage(), e);
            AppReply<FIleResultVo> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 续签查看 办理人信息
     *
     * @param signId 签约ID
     */
    @GetMapping(value = "/viewTransactors", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 12, author = "King-Y")
    @ApiOperation(value = "续签查看 办理人信息", notes = "续签查看 办理人信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<ViewTransactorsVo> viewTransactors(@RequestParam("signId") String signId) {
        try {
            AppReply<ViewTransactorsVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(iBbsRenewalInfoExtService.viewTransactors(signId));
            return appReply;
        } catch (Exception e) {
            log.error("=======================================签约查看 办理人信息异常，异常信息为：" + e.getMessage(), e);
            AppReply<ViewTransactorsVo> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 纸质续签本人办理获取本人信息
     * */
    @GetMapping(value = "/getPersonInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 13, author = "King-Y")
    @ApiOperation(value = "纸质续签本人办理获取本人信息", notes = "纸质续签本人办理获取本人信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PersonInfoResultVo> getPersonInfo(@RequestParam("signId")String signId){
        try {
            AppReply<PersonInfoResultVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(iBbsRenewalInfoExtService.getPersonInfo(signId));
            return appReply;
        } catch (Exception e) {
            log.error("=======================================续签查看 办理人信息异常，异常信息为：" + e.getMessage(), e);
            AppReply<PersonInfoResultVo> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 纸质合同续签 保存当前办理人信息
     */
    @PostMapping(value = "/saveConsignorInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 14, author = "King-Y")
    @ApiOperation(value = "纸质合同续签 保存当前办理人信息", notes = "纸质合同续签 保存当前办理人信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> saveConsignorInfo(@RequestBody SaveConsignorInfoVo vo) {
        try {
            AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(iBbsRenewalInfoExtService.saveConsignorInfo(vo));
            return appReply;
        } catch (Exception e) {
            log.error("===========================纸质合同签约保存当前办理人信息，异常信息为" + e.getMessage(), e);
            AppReply<String> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 续签保存纸质合同
     */
    @PostMapping(value = "/savePaperContracts", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 15, author = "King-Y")
    @ApiOperation(value = "续签保存纸质合同", notes = "续签保存纸质合同")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply savePaperContracts(@RequestBody SavePaperContractsVo vo) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            iBbsRenewalInfoExtService.savePaperContracts(vo);
            return appReply;
        } catch (Exception e) {
            log.error("===========================续签保存纸质合同异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 续签纸质合同提交
     *
     * @param signId 签约id
     */
    @GetMapping(value = "/paperContractsSubmit", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 16, author = "King-Y")
    @ApiOperation(value = "续签纸质合同提交", notes = "续签纸质合同提交")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply paperContractsSubmit(@RequestParam("signId") String signId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            iBbsRenewalInfoExtService.paperContractsSubmit(signId);
            return appReply;
        } catch (Exception e) {
            log.error("===========================续签纸质合同提交异常，异常信息为:" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * 续签列表 导出
     *
     * @param signIds 签约ID集合
     */
    @PostMapping(value = "/exportExcel", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 17, author = "King-Y")
    @ApiOperation(value = "续签列表 导出", notes = "续签列表 导出")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public void exportExcel(@RequestBody List<String> signIds, HttpServletResponse response) {
        try {
            String fileName = "续签列表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<BbsRenewalInfoPageListResultExcelVo> excelVos = iBbsRenewalInfoExtService.exportExcel(signIds);
            new ExportExcel("续签列表", BbsRenewalInfoPageListResultExcelVo.class).setDataList(excelVos).write(response, fileName).dispose();
        } catch (Exception e) {
            log.error("===========================续签列表导出异常，异常信息为：" + e.getMessage(), e);
        }
    }

    /**
     * 对外接口 查询所有项目ID和名称
     */
    @GetMapping(value = "/selectProductProjectInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 18, author = "King-Y")
    @ApiOperation(value = "获取所有项目id和名称", notes = "获取所有项目id和名称")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<SelectProductProjectInfoVo>> selectProductProjectInfo() {
        try {
            AppReply<List<SelectProductProjectInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            appReply.setData(iBbsRenewalInfoExtService.selectProductProjectInfo());
            return appReply;
        } catch (Exception e) {
            log.error("===========================查询所有项目ID和名称异常，异常信息为:" + e.getMessage(), e);
            AppReply<List<SelectProductProjectInfoVo>> appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }

    /**
     * <p>获取商铺签约基本信息
     *
     * @param productNo 产品编码
     * @return
     */
    @GetMapping(value = "/getProductSignBasicInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "刘鹏伟")
    @ApiOperation(value = "获取商铺签约基本信息", notes = "获取商铺签约基本信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<ProductSignBasicInfoVo> getProductSignBasicInfo(@ApiParam(value = "产品编码", required = true) @RequestParam() String productNo) {
        AppReply<ProductSignBasicInfoVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        ProductSignBasicInfoVo vo = iBbsSignInfoExtService.getProductSignBasicInfo(productNo);
        appReply.setData(vo);
        return appReply;
    }
    
    /**
     * <p>获取商铺签约基本信息(列表)
     *
     * @param productNo 产品编码
     * @return
     */
    @GetMapping(value = "/getProductSignBasicInfoList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "刘鹏伟")
    @ApiOperation(value = "获取商铺签约基本信息(列表)", notes = "获取商铺签约基本信息(列表)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<ProductSignBasicInfoVo>> getProductSignBasicInfoList(@ApiParam(value = "产品编码集合(List)", required = true) @RequestParam() List<String> productNo) {
        AppReply<List<ProductSignBasicInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        List<ProductSignBasicInfoVo> vo = iBbsSignInfoExtService.getProductSignBasicInfoList(productNo);
        appReply.setData(vo);
        return appReply;
    }

    /**
     * app签约待办分页查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-09-22
     * @since 1.0.0
     */
    @GetMapping(value = "/app/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "weichao")
    @ApiOperation(value = "app签约待办分页查询", notes = "app签约待办分页查询", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsAppSignInfoPageResultVo>>> selectByPageRecord(BbsAppRenewalInfoPageVo vo) {
        return AppReply.success(iBbsRenewalInfoExtService.selectAppByPageRecord(vo));
    }

    /**
     * app签约记录分页查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-09-22
     * @since 1.0.0
     */
    @GetMapping(value = "/app/selectSignByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "weichao")
    @ApiOperation(value = "app签约记录分页查询", notes = "app签约记录分页查询", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsAppSignInfoPageResultVo>>> selectAppSignByPage(BbsAppRenewalInfoPageVo vo) {
        return AppReply.success(iBbsRenewalInfoExtService.selectAppSignByPage(vo));
    }

    /**
     * app签署合同
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-09-22
     * @since 1.0.0
     */
    @PostMapping(value = "/app/signContract", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "weichao")
    @ApiOperation(value = "app签署合同", notes = "app签署合同", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> signContract(@RequestBody BbcSignContractVo vo, String pdfFileId) {
        return iBbsRenewalInfoExtService.signContract(vo,pdfFileId);
    }

    /**
     * app签约完成
     *
     * @param signId 签约Id
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-09-22
     * @since 1.0.0
     */
    @PostMapping(value = "/app/signing", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "weichao")
    @ApiOperation(value = "app签约完成", notes = "app签约完成", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> signing(String signId) {
        return AppReply.success(iBbsRenewalInfoExtService.signing(signId));
    }

    /**
     * <AUTHOR>
     * @date 2023-09-06 15：22
     * @version 1.0
     */
    @PostMapping(value = "/saveRenewalData", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 30, author = "ysx")
    @ApiOperation(value = "新增", notes = "新增多表数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增签约数据")})
    public AppReply saveRenewalData(@ApiParam(value = "签约数据", required = false) @RequestBody @Validated(InsertValidatorGroup.class) RenewalingSaveVo vo, @RequestParam(value = "opeState", required = false) String opeState) {
        log.debug("新增续租签约数据参数:{}", vo);
        return iBbsRenewalInfoExtService.saveRenewalData(vo, opeState);
    }

    /**
     * <AUTHOR>
     * @date 2023-09-06 15：22
     * @version 1.0
     */
    @PostMapping(value = "/renewalDataCommit", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 30, author = "ysx")
    @ApiOperation(value = "保存", notes = "保存签约数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:保存签约数据")})
    public AppReply<String> renewalDataCommit(@ApiParam(value = "签约数据", required = false) @RequestBody @Validated(InsertValidatorGroup.class) RenewalingSaveVo vo) {
        //String signId = vo.getSignId();
        return iBbsRenewalInfoExtService.renewalDataCommit(vo);
    }
    /**
     * <p>获取客户续签基本信息(列表)
     * @param customerNo 客户编码
     * @param customerType 客户类型（00:个人  01：企业）
     * @return
     */
    @GetMapping(value = "/getCustomerRenewalBasicInfoList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 13, author = "刘兴广")
    @ApiOperation(value = "获取客户的续签基本信息(列表)", notes = "获取客户的续签基本信息(列表)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<CustomerRenewalBasicInfoVo>> getCustomerRenewalBasicInfoList(@ApiParam(value = "客户编码", required = true) @RequestParam() String customerNo, @ApiParam(value = "客户类型（00:个人  01：企业）", required = true) @RequestParam() String customerType) {
        AppReply<List<CustomerRenewalBasicInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        List<CustomerRenewalBasicInfoVo> vo = iBbsRenewalInfoExtService.getCustomerRenewalBasicInfoList(customerNo,customerType);
        appReply.setData(vo);
        return appReply;
    }

    @GetMapping(value = "/getCurrentRentDetail", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 13, author = "刘兴广")
    @ApiOperation(value = "获取应退押金和租金金额", notes = "获取应退押金和租金金额")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<CurrentRentDetailVo> getCurrentRentDetail(@ApiParam(value = "上级合同编码", required = true) @RequestParam() String parentContractCode) {
        AppReply<CurrentRentDetailVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        CurrentRentDetailVo vo = iBbsRenewalInfoExtService.getCurrentRentDetail(parentContractCode);
        appReply.setData(vo);
        return appReply;
    }

    /**
     * 续签终止
     *
     */
    @GetMapping(value = "/renewalStopByBusinessId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liwenqiang")
    @ApiOperation(value = "续签终止", notes = "续签终止")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply renewalStopByBusinessId(@RequestParam("businessId") String businessId) {
        try {
            AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
            iBbsRenewalInfoExtService.renewalStopByBusinessId(businessId);
            return appReply;
        } catch (Exception e) {
            log.error("签约终止异常，异常信息为：" + e.getMessage(), e);
            AppReply appReply = new AppReply(AppReply.ERROR_CODE, e.getMessage(), null);
            return appReply;
        }
    }


}

