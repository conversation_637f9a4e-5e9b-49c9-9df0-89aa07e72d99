package com.bonc.ioc.bzf.busisigning.controller;

import com.bonc.ioc.bzf.busisigning.service.TZService;
import com.bonc.ioc.bzf.busisigning.vo.SynResult;
import com.bonc.ioc.bzf.busisigning.vo.tz.TzResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.util.FileUploadValidator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/tz")
@Api(tags = "台账")
@Validated
public class TzController {

    @Resource
    private TZService tzService;

    @PostMapping(value = "/excelImport", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "sx")
    @ApiOperation(value = "导入合同相关数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> excelImport(@RequestPart("file") MultipartFile file) {
        //验证上传文件
        FileUploadValidator.isValidFileUpload(file);
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        this.tzService.core(file);
        return appReply;
    }

    @PostMapping(value = "/test", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "sx")
    @ApiOperation(value = "测试")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<TzResult> test(@RequestBody TzResult tzResult) {
        AppReply<TzResult> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        this.tzService.analysData(tzResult);
        appReply.setData(tzResult);
        return appReply;
    }
    @PostMapping(value = "/delSignTz", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "sx")
    @ApiOperation(value = "删除签约数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply delTz(@RequestBody List<String> signIds){
        AppReply appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        this.tzService.delTz(signIds);
        return appReply;
    }



    @PostMapping(value = "/synPayment", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "sx")
    @ApiOperation(value = "同步工银")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<SynResult> synPayment(){
        AppReply appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(this.tzService.synPayment());
        return appReply;
    }


    @PostMapping(value = "/synContract", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "sx")
    @ApiOperation(value = "同步合同")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<SynResult> synContract(){
        AppReply appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(this.tzService.synContract());
        return appReply;
    }


}
