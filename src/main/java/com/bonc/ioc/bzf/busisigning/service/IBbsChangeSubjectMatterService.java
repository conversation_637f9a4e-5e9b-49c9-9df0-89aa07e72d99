package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeSubjectMatterEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 缩租面积变更历史产品信息表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-25
 * @change 2024-10-25 by tbh for init
 */
public interface IBbsChangeSubjectMatterService extends IMcpBaseService<BbsChangeSubjectMatterEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    String insertRecord(BbsChangeSubjectMatterVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    List<String> insertBatchRecord(List<BbsChangeSubjectMatterVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param subjectMatterId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void removeByIdRecord(String subjectMatterId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param subjectMatterIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void removeByIdsRecord(List<String> subjectMatterIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的缩租面积变更历史产品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void updateByIdRecord(BbsChangeSubjectMatterVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的缩租面积变更历史产品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void updateBatchByIdRecord(List<BbsChangeSubjectMatterVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的缩租面积变更历史产品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void saveByIdRecord(BbsChangeSubjectMatterVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的缩租面积变更历史产品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void saveBatchByIdRecord(List<BbsChangeSubjectMatterVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param subjectMatterId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    BbsChangeSubjectMatterVo selectByIdRecord(String subjectMatterId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    PageResult<List<BbsChangeSubjectMatterPageResultVo>> selectByPageRecord(BbsChangeSubjectMatterPageVo vo);
}
