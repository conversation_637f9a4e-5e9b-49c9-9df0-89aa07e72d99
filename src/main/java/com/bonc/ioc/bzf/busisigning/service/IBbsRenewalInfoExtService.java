package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalInfoEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsSignInfoEntity;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbcSignContractVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date  2023-09-07 09:36
 * @version 1.0
 */
@Service
public interface IBbsRenewalInfoExtService extends IMcpBaseService<BbsRenewalInfoEntity> {

    RenewalingSaveVo renewalView(String signId, String contractCode, Boolean viewType);

    BbsRenewalCustomerVo selectCustomer(String signId);

    List<AddressCascadeQueryResultVo> addressCascadeQuery(AddressCascadeQueryVo queryVo);


    /**
     * 删除签约
     * @param requestId
     */
    void deleteSignByRequestId(String requestId);

    void signStop(String signId);

    void deleteSign(String signId);

    void recallSign(String signId);

    FIleResultVo previewAndDownload(String signId, String type, boolean appFlag);

    AppReply<Boolean> sendMessageDownTemplateContract(String signId, String type, String text);

    AppReply<Boolean> downloadTemplateContractForEmail(String signId, String type, String text);

    FIleResultVo downloadContract(String signId);

    ViewTransactorsVo viewTransactors(String signId);

    PersonInfoResultVo getPersonInfo(String signId);

    String saveConsignorInfo(SaveConsignorInfoVo vo);

    void savePaperContracts(SavePaperContractsVo vo);

    void paperContractsSubmit(String signId);

    List<BbsRenewalInfoPageListResultExcelVo> exportExcel(List<String> signIds);

    List<SelectProductProjectInfoVo> selectProductProjectInfo();

    /** 保存签约数据
     */
    AppReply saveRenewalData(RenewalingSaveVo vo, String opeState);

    /** 保存签约数据
     */
    AppReply<String> renewalDataCommit(String signId);

    /**
     * 提交
     * @param vo
     * @return
     */
    AppReply<String> renewalDataCommit(RenewalingSaveVo vo);


    /**
     * selectByPageRecord app签约待办分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    PageResult<List<BbsAppSignInfoPageResultVo>> selectAppByPageRecord(BbsAppRenewalInfoPageVo vo);

    /**
     * selectByPageRecord app签约记录分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    PageResult<List<BbsAppSignInfoPageResultVo>> selectAppSignByPage(BbsAppRenewalInfoPageVo vo);
    /**
     * app签署合同
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-09-22
     * @since 1.0.0
     */
    AppReply<String> signContract(BbcSignContractVo vo, String pdfFileId);

    /**
     * app签署合同
     *
     * @param signId 签约id
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-09-22
     * @since 1.0.0
     */
    AppReply<String> signing(String signId);


    /**
     * 获取客户的续签基本信息
     * @param customerNo
     * @param customerType
     * @return
     */
    List<CustomerRenewalBasicInfoVo> getCustomerRenewalBasicInfoList(String customerNo, String customerType);


    /**
     * 检查合同开始时间
     */
    void checkContractBeginTime();

    /**
     *获取应退押金和租金金额
     * @param parentContractCode
     * @return
     */
    CurrentRentDetailVo getCurrentRentDetail(String parentContractCode);

    /**
     * 获取工银试算数据
     *
     * @param renewalInfoVo 续签信息 vo实体
     * @return 试算结果
     */
    ChargeRespondVo<PreviewBillsResultVo> getPreviewBills(BbsRenewalInfoVo renewalInfoVo);

    /**
     * 终止签约
     * @param businessId
     */
    void renewalStopByBusinessId(String businessId);
}
