package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsResultCustomerEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsSignInfoEntity;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbcSignContractVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.workflow.vo.BaseFlowVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface IBbsSignInfoExtService {

    SignBasicNumInfoVo getSignBasicNumInfo();

    List<SignBusinessFormatStatisticsInfoVo> getSignBusinessFormatStatisticsInfo(String year);

    List<SignProjectRankingInfoVo> getSignProjectRankingInfo(String year);


    ProductSignBasicInfoVo getProductSignBasicInfo(String productNo);

    List<ProductSignBasicInfoVo> getProductSignBasicInfoList(List<String> productNo);

    /**
     * 获取客户的签约基本信息
     *
     * @param customerNo
     * @param customerType
     * @return
     */
    List<CustomerSignBasicInfoVo> getCustomerSignBasicInfoList(String customerNo, String customerType);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by liwenqiang for init
     * @since 1.0.0
     */
    PageResult<List<BbsSignInfoPageListResultVo>> selectByPage(SelectByPageVo vo);

    BbsResultCustomerVo selectCustomer(String signId);

    List<AddressCascadeQueryResultVo> addressCascadeQuery(AddressCascadeQueryVo queryVo);

    void signStop(String signId);

    void deleteSign(String signId);

    void recallSign(String signId);

    SigningSaveVo signView(String signId, String contractCode, Boolean viewType);

    /**
     * 获取合同管理 合同主要信息
     *
     * @param signId       签约id
     * @param contractCode 合同编号
     * @return 合同管理 合同主要信息
     */
    SigningSaveVo getContractInfo(String signId, String contractCode);

    FIleResultVo previewAndDownload(String signId, String type, boolean appFlag);

    FIleResultVo downloadContract(String signId);

    String saveConsignorInfo(SaveConsignorInfoVo vo);

    ViewTransactorsVo viewTransactors(String signId);

    void savePaperContracts(SavePaperContractsVo vo);

    String savePaperContractsSubmit(SavePaperContractsVo vo);
    void paperContractsSubmit(String signId);
//
//    /**
//     * 合同审核提交
//     * @param param
//     */
//    void paperContractsSubmitWorkflow(BaseFlowVo param);

    List<BbsSignInfoPageListResultExcelVo> exportExcel(List<String> signIds);

    List<SelectProductProjectInfoVo> selectProductProjectInfo();

    void updateSignStatus(String signId, String signStatus);

    AppReply<Boolean> sendMessageDownTemplateContract(String signId, String type, String text);

    AppReply<Boolean> downloadTemplateContractForEmail(String signId, String type, String text);

    PersonInfoResultVo getPersonInfo(String signId);

    /**
     * 保存签约数据
     */
    AppReply saveSignData(SigningSaveVo vo, String opeState);

    AppReply<PageResult<List<BscIntentionInfoPageResultVo>>> selectBscIntentionInfo(BscIntentionInfoPageVo bscIntentionInfoPageVo, String signType);

    AppReply<PageResult<List<BbhgHouseInfoPageResultVo>>> selectBbhgHouseInfo(BbhgHouseInfoPageVo bbhgHouseInfoPageVo);

    /**
     * 保存签约数据
     */
    AppReply<String> signDataCommit(String signId);

    /**
     * 提交
     * @param vo
     * @return
     */
    AppReply<String> signDataCommit(SigningSaveVo vo);

    /**
     * 签署合同
     */
    AppReply<String> signContract(BbcSignContractVo vo, String pdfFileId);

    AppReply getCheckinInfoByContractcode(String contractCode, String customerId);

    /**
     * 提交审批
     */
    AppReply<String> signing(String signId);

    /**
     * 检查合同开始时间
     */
    void checkContractBeginTime();

    AppReply<String> saveSignDataContractChange(SigningSaveVo vo);

    PageResult<List<BbsSignInfoPageListResultVo>> selectByPageSupplementAgreementList(SelectByPageVo vo);

    PageResult<List<BbsSignInfoPageListResultVo>> selectByPageUploadResultApproveList(SelectByPageVo vo);
    SigningSaveVo contractChangeSignView(String signId, String contractCode, Boolean viewType);

    BbctContractManagementVo createContractChangeSignContractManagementVo(BbsSignInfoEntity signInfoEntity,
                                                                          BbsResultCustomerEntity customerEntity,
                                                                          List<BbsResultProductEntity> productEntities,
                                                                          Map<String, String> templateSeatMap);

    /**
     * 获取工银试算数据
     *
     * @param signInfoVo 签约信息 vo实体
     * @return 试算结果
     */
    ChargeRespondVo<PreviewBillsResultVo> getPreviewBills(BbsSignInfoVo signInfoVo);

    /**
     * 终止签约
     * @param requestId
     */
    void signStopByRequestId(String requestId);

    /**
     * 删除签约
     * @param requestId
     */
    void deleteSignByRequestId(String requestId);


    /**
     * 终止签约
     * @param businessId
     */
    void signStopByBusinessId(String businessId);


    /**
     * 合同变更终止签约
     * @param businessId
     */
    void contractChangeSignStopByBusinessId(String businessId);

    /**
     * 删除签约
     * @param businessId
     */
    void deleteSignByBusinessId(String businessId);
}
