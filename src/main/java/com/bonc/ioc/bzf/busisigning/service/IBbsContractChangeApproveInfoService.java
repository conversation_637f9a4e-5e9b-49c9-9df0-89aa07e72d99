package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeApproveEntity;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;

/**
 * 审批表 服务类
 *
 *
 *
 *
 */
public interface IBbsContractChangeApproveInfoService extends IMcpBaseService<BbsiContractChangeApproveEntity> {


    /*
    审批表新增
     */
    String insertCaRecord(BbsiContractChangeApproveInfoVo vo);

    /*
    变更审核记录列表显示
     */
    PageResult<List<BbsiContractChangeApproveListPageResultVo>> selectByPageRecord(BbsiContractChangeApproveListPageResultVo vo);

    List<BbsApproveDetailInfoVo> selectByContractCode(String ccId);

    BbsiContractChangeApproveVo selectContractChangeApprove(BbsiContractChangeApproveVo vo);

    AppReply saveContractChangeApproveResult(BbsiContractChangeApproveInfoVo vo);

    AppReply saveContractChangeApproveResultV2(String ccId, String approveStatus);

    List<AddressCascadeQueryResultVo> addressCascadeQuery(BbsApproveAddressCascadeQueryVo queryVo);

    List<ContractStatusResultVo> selectContractStatus(List<String> contractNos);

    /**
     * 根据合同变更id查询
     *
     * @param ccId 合同变更id
     * @return 合同变更审批信息 vo实体
     */
    BbsiContractChangeApproveEntity selectByCcId(String ccId);
}
