package com.bonc.ioc.bzf.busisigning.controller;

import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalApplyApproveDetailInfoService;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveDetailInfoResultVo;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveDetailInfoVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: hechengya<PERSON>
 * @createDate: 2023-09-20
 * @Version 1.0
 **/
@RestController
@RequestMapping("/renewal/apply/approve/detail/")
@Api(tags = "续签申请审核详细记录")
@Slf4j
public class BbsRenewalApplyApproveDetailInfoController {

    @Resource
    private IBbsRenewalApplyApproveDetailInfoService baseService;

    /**
     * 续签申请审核记录-增
     *
     * @param vo 需要新增的数据
     * @return com.bonc.ioc.common.util.AppReply 新增的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "hcy")
    @ApiOperation(value = "续签申请审核记录-新增", notes = "续签申请审核-新增")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> insertRecord(RenewalApplyApproveDetailInfoVo vo) {
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
    }

    /**
     * 续签申请审核记录-查看续签申请审核记录
     *
     * @param parentId 申请信息id
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @GetMapping(value = "/selectByListRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "hcy")
    @ApiOperation(value = "续签申请审核记录-查看续签申请审核记录", notes = "续签申请审核-查看续签申请审核记录")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<RenewalApplyApproveDetailInfoResultVo>>> selectByListRecord(@RequestParam("parentId") String parentId) {
        AppReply<PageResult<List<RenewalApplyApproveDetailInfoResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByListRecord(parentId));
        return appReply;
    }


}
