package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeResultEntity;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo;
import com.bonc.ioc.common.base.service.IMcpBaseService;

public interface IContractChangeResultService extends IMcpBaseService<BbsiContractChangeResultEntity> {
    void contractChange(BbsiContractChangeVo vo);

    /**
     * 新增或更新
     *
     * @param vo 合同变更结果 vo实体
     */
    void insertOrUpdate(BbsiContractChangeResultVo vo);

    /**
     * 根据合同编号获取合同变更结果
     *
     * @param contractCode 合同编号
     * @return 合同变更结果
     */
    BbsiContractChangeResultVo selectByContractCode(String contractCode);

    /**
     * 根据合同编号删除
     *
     * @param contractCode 合同编号
     */
    void deleteByContractCode(String contractCode);
}
