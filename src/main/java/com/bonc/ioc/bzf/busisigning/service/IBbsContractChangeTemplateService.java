package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsContractChangeTemplateEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更模板表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-25
 * @change 2024-10-25 by pyj for init
 */
public interface IBbsContractChangeTemplateService extends IMcpBaseService<BbsContractChangeTemplateEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbsContractChangeTemplateVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbsContractChangeTemplateVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param relationId 需要删除的关联id
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String relationId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param relationIdList 需要删除的关联id
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> relationIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的合同变更模板表
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbsContractChangeTemplateVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的合同变更模板表
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbsContractChangeTemplateVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的合同变更模板表
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbsContractChangeTemplateVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的合同变更模板表
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbsContractChangeTemplateVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param relationId 需要查询的关联id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    BbsContractChangeTemplateVo selectByIdRecord(String relationId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbsContractChangeTemplatePageResultVo>> selectByPageRecord(BbsContractChangeTemplatePageVo vo);

    /**
     * 根据变更类型查询
     *
     * @param changeType 变更类型
     * @return 合同变更模板信息 vo实体
     */
    BbsContractChangeTemplateVo selectByChangeType(String changeType);
}
