package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApproveInfoEntity;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;

/**
 * 续签审批表 服务类
 *
 * <AUTHOR>
 * @date 2023-09-21
 * @change 2023-09-21 by liupengwei for init
 */
public interface IBbsRenewalApproveInfoService extends IMcpBaseService<BbsRenewalApproveInfoEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    String insertRecord(BbsRenewalApproveInfoVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    List<String> insertBatchRecord(List<BbsRenewalApproveInfoVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param approveId 需要删除的审批id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    void removeByIdRecord(String approveId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param approveIdList 需要删除的审批id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    void removeByIdsRecord(List<String> approveIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的续签审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    void updateByIdRecord(BbsRenewalApproveInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的续签审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    void updateBatchByIdRecord(List<BbsRenewalApproveInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的续签审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    void saveByIdRecord(BbsRenewalApproveInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的续签审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    void saveBatchByIdRecord(List<BbsRenewalApproveInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param approveId 需要查询的审批id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    BbsRenewalApproveInfoVo selectByIdRecord(String approveId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    PageResult<List<BbsRenewalApproveInfoPageResultVo>> selectByPageRecord(BbsRenewalApproveInfoPageVo vo);

    AppReply saveApproveResult(BbsRenewalApproveInfoVo vo);

    List<BbsRenewalApproveInfoVo> selectApproveRecord(String signId, String approveType);

    List<AddressCascadeQueryResultVo> addressCascadeQuery(BbsApproveAddressCascadeQueryVo queryVo);

    void addSigningReviewInfo(String signId);

    void addContractReviewInfo(String signId);

    void withdrawApprove(String signId);

}
