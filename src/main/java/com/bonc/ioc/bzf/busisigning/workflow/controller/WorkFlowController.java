package com.bonc.ioc.bzf.busisigning.workflow.controller;

import com.bonc.ioc.bzf.busisigning.workflow.service.IFwWorkflowService;
import com.bonc.ioc.bzf.busisigning.workflow.vo.CallBakParamsVo;
import com.bonc.ioc.common.util.AppReply;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/workflow")
@Api(tags = "工作流")
@Validated
public class WorkFlowController {
    @Resource
    private IFwWorkflowService workflowService;


    @PostMapping(value = "/sign/approve", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "新签合同审签", notes = "新签合同审签", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<Object> signApprove(@RequestBody @Validated CallBakParamsVo vo){
        return this.workflowService.signApprove(vo);
    }


    @PostMapping(value = "/sign/contract/approve", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "新签合同审签", notes = "新签合同审签", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply signContractApprove(@RequestBody @Validated CallBakParamsVo vo){
         return this.workflowService.signContractApprove(vo);
    }

    @PostMapping(value = "/renewal/approve", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "续签合同审签", notes = "续签合同审签", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<Object> renewalApprove(@RequestBody CallBakParamsVo vo){
        return this.workflowService.renewalApprove(vo);
    }

    @PostMapping(value = "/renewal/contract/approve", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "续签合同审签", notes = "续签合同审签", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply renewalContractApprove(@RequestBody CallBakParamsVo vo){
        return this.workflowService.renewalContractApprove(vo);
    }


    @PostMapping(value = "/refund/dealBusinessApprove", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "处理退款申请业务审批", notes = "处理退款申请业务审批", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<Object> dealBusinessApprove(@RequestBody @Validated CallBakParamsVo vo){
        return this.workflowService.dealBusinessApprove(vo);
    }


    @PostMapping(value = "/contract/dealBasicInformationChangeApprove", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "商业基本信息变更", notes = "处理退款申请业务审批", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<Object> dealBasicInformationChangeApprove(@RequestBody @Validated CallBakParamsVo vo){
        return this.workflowService.dealBasicInformationChangeApprove(vo);
    }

    @PostMapping(value = "/contract/dealOtherInformationChangeApprove", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "商业乙方和其他信息变更", notes = "商业乙方和其他信息变更", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<Object> dealOtherInformationChangeApprove(@RequestBody @Validated CallBakParamsVo vo){
        return this.workflowService.dealOtherInformationChangeApprove(vo);
    }

    @PostMapping(value = "/contract/deaUploadResultApprove", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "商业乙方和其他信息变更上传结果审核", notes = "商业乙方和其他信息变更上传结果审核", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<Object> deaUploadResultApprove(@RequestBody @Validated CallBakParamsVo vo){
        return this.workflowService.deaUploadResultApprove(vo);
    }

    /**
     * 应收调整-工作流结束回调
     * @param vo
     * @return
     */
    @PostMapping(value = "/contract/dealAdjustResultApprove", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "sx")
    @ApiOperation(value = "应收调整-工作流结束回调", notes = "应收调整-工作流结束回调", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> dealAdjustResultApprove(@RequestBody @Validated CallBakParamsVo vo){
        return this.workflowService.dealAdjustResultApprove(vo);
    }


}
