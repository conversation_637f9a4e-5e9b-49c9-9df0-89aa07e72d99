package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeIncrementalConfigEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更租金递增设置 服务类
 *
 * <AUTHOR>
 * @date 2024-09-09
 * @change 2024-09-09 by tbh for init
 */
public interface IBbsChangeIncrementalConfigService extends IMcpBaseService<BbsChangeIncrementalConfigEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    String insertRecord(BbsChangeIncrementalConfigVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    List<String> insertBatchRecord(List<BbsChangeIncrementalConfigVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param sicId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void removeByIdRecord(String sicId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param sicIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void removeByIdsRecord(List<String> sicIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的合同变更租金递增设置
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void updateByIdRecord(BbsChangeIncrementalConfigVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的合同变更租金递增设置
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void updateBatchByIdRecord(List<BbsChangeIncrementalConfigVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的合同变更租金递增设置
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void saveByIdRecord(BbsChangeIncrementalConfigVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的合同变更租金递增设置
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void saveBatchByIdRecord(List<BbsChangeIncrementalConfigVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param sicId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    BbsChangeIncrementalConfigVo selectByIdRecord(String sicId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    PageResult<List<BbsChangeIncrementalConfigPageResultVo>> selectByPageRecord(BbsChangeIncrementalConfigPageVo vo);

    /**
     * 根据合同变更id和类型(变更前变更后类型+标准类型)查询合同变更递增规则列表
     *
     * @param ccId         合同变更id
     * @param type         变更前变更后类型
     * @param standardType 标准类型
     * @return 合同变更递增规则列表
     */
    List<BbsChangeIncrementalConfigVo> selectByCcIdAndType(String ccId, String type, String standardType);
}
