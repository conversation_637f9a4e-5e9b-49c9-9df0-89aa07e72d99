package com.bonc.ioc.bzf.busisigning.factory.change.otherchange;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsSignFpIntervalEntity;
import com.bonc.ioc.bzf.busisigning.enums.BeforeAndAfterEnum;
import com.bonc.ioc.bzf.busisigning.enums.ContractChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.StandardTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.WhetherEnum;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctChargeRuleSubParamsBigVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.utils.DateUtils;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.utils.JsonToObjectUtil;import com.bonc.ioc.bzf.busisigning.vo.JsonVo.IncrementalInfo;
import com.bonc.ioc.bzf.busisigning.vo.JsonVo.IncrementalInfoArray;
import com.bonc.ioc.bzf.busisigning.vo.JsonVo.JsonRootBean;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.*;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.ContractOtherInfo;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.FreeSectionVo;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.FreeVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillParamsRoomChargeSubjectVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillparamsRoomChargeIncreaseRule;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillparamsRoomChargePreferentRule;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsParamsRoomVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsParamsVo;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 租金变更 工厂类
 *
 * <AUTHOR>
 * @since 2024/10/24
 */
@Slf4j
public class RentChangeFactory extends AbstractSubChangeFactory {

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param contractChangeVo             合同变更信息 vo实体
     */
    public RentChangeFactory(FeignServiceConfiguration feignServiceConfiguration,
                             BusinessServiceConfiguration businessServiceConfiguration,
                             BbsiContractChangeVo contractChangeVo) {
        super(feignServiceConfiguration,
                businessServiceConfiguration,
                contractChangeVo);
    }

    /**
     * 赋值试算账单请求参数
     *
     * @param previewBillsParamsVo 试算账单请求参数 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentSignInfoVo     上级合同签约信息 vo实体
     */
    @Override
    public void setPreviewBillsParams(PreviewBillsParamsVo previewBillsParamsVo,
                                      BbctContractManagementVo parentContractInfoVo,
                                      SigningSaveVo parentSignInfoVo) {
        previewBillsParamsVo.setChangeAccountingPeriodType(contractChangeVo.getRentEffectiveExecutionDate());
        previewBillsParamsVo.setCutShortRoomList(Collections.emptyList());
        setRoomList(previewBillsParamsVo, parentContractInfoVo);
    }

    /**
     * 赋值预览信息
     *
     * @param previewInfoParamsVo 预览信息参数 vo实体
     */
    @Override
    public void setPreviewInfo(BbctPreviewInfoParamsVo previewInfoParamsVo) {
        return;
    }

    /**
     * 根据变更信息赋值签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     */
    @Override
    public void setSignInfoByChangeInfo(SigningSaveVo signingSaveVo) {
        // 赋值产品信息
        setProductInfoByChangeInfo(signingSaveVo);
        // 赋值递增规则
        setIncrementInfoByChangeInfo(signingSaveVo);
    }

    /**
     * 根据变更信息赋值产品信息
     *
     * @param signingSaveVo 签约信息 vo实体
     */
    private void setProductInfoByChangeInfo(SigningSaveVo signingSaveVo) {
        List<BbsChangeResultProductVo> changeProductList = businessServiceConfiguration
                .getChangeResultProductService()
                .selectByCcId(contractChangeVo.getCcId());
        List<BbsResultProductEntity> productList = signingSaveVo.getPropertyAddress();
        for (BbsChangeResultProductVo changeProduct : changeProductList) {
            for (BbsResultProductEntity productInfo : productList) {
                if (changeProduct.getProductNo().equals(productInfo.getProductNo())) {
                    productInfo.setRentStandard(changeProduct.getRentStandard());
                    productInfo.setContractChangeType(jointByComma(productInfo.getContractChangeType(),
                            ContractChangeTypeEnum.RENT_CHANGE.getCode()));
                    break;
                }
            }
        }
    }

    /**
     * 根据变更信息赋值信息
     *
     * @param signingSaveVo 签约信息 vo实体
     */
    private void setIncrementInfoByChangeInfo(SigningSaveVo signingSaveVo) {
        List<BbsChangeIncrementalConfigVo> changeIncrementalList = businessServiceConfiguration
                .getChangeIncrementalConfigService()
                .selectByCcIdAndType(contractChangeVo.getCcId(), BeforeAndAfterEnum.AFTER.getCode(), StandardTypeEnum.RENT.getCode());
        JsonRootBean rentStandardJson = new JsonRootBean();
        IncrementalInfo incrementalInfo = new IncrementalInfo();
        if (CollectionUtils.isEmpty(changeIncrementalList)) {
            incrementalInfo.setIncrementalFlag(WhetherEnum.NO.getCode());
        } else {
            incrementalInfo.setIncrementalFlag(WhetherEnum.YES.getCode());
            incrementalInfo.setIncrementalType(WhetherEnum.YES.getCode());
            List<IncrementalInfoArray> resultIncrementalList = new ArrayList<>();
            for (BbsChangeIncrementalConfigVo changeIncrementalInfo : changeIncrementalList) {
                IncrementalInfoArray resultIncrementalInfo = new IncrementalInfoArray();
                BeanUtils.copyProperties(changeIncrementalInfo, resultIncrementalInfo);
                resultIncrementalList.add(resultIncrementalInfo);
            }
            incrementalInfo.setIncrementalInfoArray(resultIncrementalList);
        }
        rentStandardJson.setIncrementalInfo(incrementalInfo);
        signingSaveVo.setRentStandardJson(rentStandardJson);
    }

    /**
     * 赋值试算需要的产品列表
     *
     * @param previewBillsParamsVo 试算账单请求参数 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     */
    private void setRoomList(PreviewBillsParamsVo previewBillsParamsVo,
                             BbctContractManagementVo parentContractInfoVo) {
        List<PreviewBillsParamsRoomVo> roomList;
        if (Objects.isNull(previewBillsParamsVo.getRoomList())) {
            roomList = new ArrayList<>();
        } else {
            roomList = previewBillsParamsVo.getRoomList();
        }
        // 获取变更的产品列表
        List<BbsChangeResultProductVo> changeProductList = getChangeProductListExcludeReturnedProduct();
        // 构建变更的产品信息
        for (BbsChangeResultProductVo changeProductVo : changeProductList) {
            roomList.add(createRoomInfo(changeProductVo, parentContractInfoVo));
        }
        previewBillsParamsVo.setRoomList(roomList);
    }

    /**
     * 获取变更的产品列表(排除已退产品)
     *
     * @return 变更的产品列表
     */
    private List<BbsChangeResultProductVo> getChangeProductListExcludeReturnedProduct() {
        List<BbsChangeResultProductVo> resultList = new ArrayList<>();
        List<BbsChangeResultProductVo> changeProductList = getChangeProductList();
        Set<String> productNoSet = getReturnedProductSet();
        // 租金变更的产品列表中排除缩租的已退产品
        for (BbsChangeResultProductVo changeProductVo : changeProductList) {
            if (!productNoSet.contains(changeProductVo.getProductNo())) {
                resultList.add(changeProductVo);
            }
        }
        return resultList;
    }

    /**
     * 获取变更的产品列表
     *
     * @return 变更的产品列表
     */
    private List<BbsChangeResultProductVo> getChangeProductList() {
        if (Objects.isNull(contractChangeVo.getProductVoList())) {
            return businessServiceConfiguration
                    .getChangeResultProductService()
                    .selectByCcId(contractChangeVo.getCcId());
        } else {
            return contractChangeVo.getProductVoList();
        }
    }

    /**
     * 获取已退产品编号set集
     *
     * @return 已退产品编号set集
     */
    private Set<String> getReturnedProductSet() {
        if (Objects.isNull(contractChangeVo.getShopInfoVoList())) {
            return businessServiceConfiguration
                    .getChangeShopInfoService()
                    .selectReturnedProductNoByCcId(contractChangeVo.getCcId());
        } else {
            Set<String> productNoSet = new HashSet<>();
            List<BbsChangeShopInfoVo> shopInfoList = contractChangeVo.getShopInfoVoList();
            for (BbsChangeShopInfoVo shopInfoVo : shopInfoList) {
                if (WhetherEnum.YES.getCode().equals(shopInfoVo.getIsSplit())) {
                    productNoSet.add(shopInfoVo.getProductNoOld());
                } else {
                    productNoSet.add(shopInfoVo.getProductNo());
                }
            }
            return productNoSet;
        }
    }

    /**
     * 生成试算的产品信息
     *
     * @param changeProductVo      变更的产品信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @return 试算的产品信息
     */
    private PreviewBillsParamsRoomVo createRoomInfo(BbsChangeResultProductVo changeProductVo,
                                                    BbctContractManagementVo parentContractInfoVo) {
        PreviewBillsParamsRoomVo roomVo = new PreviewBillsParamsRoomVo();
        roomVo.setHouseId(changeProductVo.getProductNo());
        roomVo.setHouseName(changeProductVo.getProductName());
        List<PreviewBillParamsRoomChargeSubjectVo> chargeSubjectList = new ArrayList<>();
        // 租金计费规则
        chargeSubjectList.add(createRentChargeSubject(changeProductVo, parentContractInfoVo));
        // 保证金计费规则
        // 非自定义押金
        if (!"9".equals(parentContractInfoVo.getCashPledgeCode())) {
            chargeSubjectList.add(createCashPledgeChargeSubject(changeProductVo, parentContractInfoVo));
        }
//        // 物业费计费规则
//        // 判断合同是否存在物业费 并存储（从老合同扩展字段取）
//        ContractOtherInfo contractOtherInfo = JsonToObjectUtil.jsonToContractOtherInfo(parentContractInfoVo.getContractExtend());
//        log.info("合同信息扩展为："+parentContractInfoVo.getContractExtend());
//        if (contractOtherInfo != null) {
//            //有物业费
//            if("02-08".equals(contractOtherInfo.getContractFees())){
//                //从产品扩展字段解析物业费标准
//                for (BbctContractSubjectMatterVo subVo : parentContractInfoVo.getSubjectMatterList()) {
//                    if (changeProductVo.getProductNo().equals(subVo.getProductNo())) {
//                        //从产品扩展字段解析物业费标准
//                        log.info("老房子物业费json："+subVo.getProductExtend());
//                        if(StringUtils.isNotBlank(subVo.getProductExtend())){
//                            StandardVo propStandardVo = JsonToObjectUtil.jsonToProductExtend(subVo.getProductExtend());
//                            if(propStandardVo != null && ("1".equals(propStandardVo.getStandardUnit()) || "4".equals(propStandardVo.getStandardUnit()))){
//                                chargeSubjectList.add(createPropertyChargeSubject(changeProductVo, parentContractInfoVo,propStandardVo.getStandardUnit(),propStandardVo.getStandard()));
//                            }
//                        }
//                    }
//                }
//            }
//        }
        roomVo.setChargeSubjectList(chargeSubjectList);
        return roomVo;
    }

    /**
     * 生成租金计费规则
     *
     * @param changeProductVo      变更后的产品信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @return 租金计费规则
     */
    private PreviewBillParamsRoomChargeSubjectVo createRentChargeSubject(BbsChangeResultProductVo changeProductVo,
                                                                         BbctContractManagementVo parentContractInfoVo) {
        PreviewBillParamsRoomChargeSubjectVo rentChargeSubjectVo = new PreviewBillParamsRoomChargeSubjectVo();
        rentChargeSubjectVo.setChargeSubjectNo("01");
        rentChargeSubjectVo.setCyclicOrSingle("01");
        rentChargeSubjectVo.setChargeSubjectPeriod(Integer.valueOf(parentContractInfoVo.getPaymentCycleCode()));
        rentChargeSubjectVo.setAmountType("0" + changeProductVo.getRentStandardUnit());
        rentChargeSubjectVo.setParamList((JSONObject) JSON.toJSON(new BbctChargeRuleSubParamsBigVo()));
        BbctChargeRuleSubParamsBigVo chargeRuleSubParamsVo = new BbctChargeRuleSubParamsBigVo();
        chargeRuleSubParamsVo.setPARAMPRICE(String.valueOf(changeProductVo.getRentStandard()));
        chargeRuleSubParamsVo.setPARAMAREA(changeProductVo.getHouseStructArea());
        rentChargeSubjectVo.setParamValueList((JSONObject) JSON.toJSON(chargeRuleSubParamsVo));
        ContractOtherInfo contractOtherInfo = JsonToObjectUtil.jsonToContractOtherInfo(parentContractInfoVo.getContractExtend());
        if (!Objects.isNull(contractOtherInfo)) {
            rentChargeSubjectVo.setTaxRate(BigDecimal.valueOf(contractOtherInfo
                            .getRentTaxRate())
                    .divide(BigDecimal.valueOf(100)));
        }
        rentChargeSubjectVo.setIncreaseRules(getChangeIncreaseRuleList());
        rentChargeSubjectVo.setPreferentRules(getParentPreferentRuleList(contractOtherInfo));
        return rentChargeSubjectVo;
    }


    /**
     * 生成保证金计费规则
     *
     * @param changeProductVo      变更后的产品信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @return 保证金计费规则
     */
    private PreviewBillParamsRoomChargeSubjectVo createCashPledgeChargeSubject(BbsChangeResultProductVo changeProductVo,
                                                                               BbctContractManagementVo parentContractInfoVo) {
        PreviewBillParamsRoomChargeSubjectVo cashPledgeChargeSubjectVo = new PreviewBillParamsRoomChargeSubjectVo();
        cashPledgeChargeSubjectVo.setChargeSubjectNo("02");
        cashPledgeChargeSubjectVo.setCyclicOrSingle("02");
        cashPledgeChargeSubjectVo.setChargeSubjectPeriod(Integer.valueOf(parentContractInfoVo.getPaymentCycleCode()));
        if ("9".equals(parentContractInfoVo.getCashPledgeCode())) {
//            cashPledgeChargeSubjectVo.setDepositProportion(new BigDecimal(1));
            cashPledgeChargeSubjectVo.setChargeSubjectAmount(getCashPledgeByBill(parentContractInfoVo.getContractNo(),
                    parentContractInfoVo.getSubjectMatterList().get(0).getProjectId(),
                    changeProductVo.getProductName()));
        } else {
            cashPledgeChargeSubjectVo.setDepositProportion(new BigDecimal(parentContractInfoVo.getCashPledgeCode()));
            cashPledgeChargeSubjectVo.setParamList((JSONObject) JSON.toJSON(new BbctChargeRuleSubParamsBigVo()));
            BbctChargeRuleSubParamsBigVo chargeRuleSubParamsVo = new BbctChargeRuleSubParamsBigVo();
            chargeRuleSubParamsVo.setPARAMPRICE(String.valueOf(changeProductVo.getRentStandard()));
            chargeRuleSubParamsVo.setPARAMAREA(changeProductVo.getHouseStructArea());
            cashPledgeChargeSubjectVo.setParamValueList((JSONObject) JSON.toJSON(chargeRuleSubParamsVo));
        }
        cashPledgeChargeSubjectVo.setTaxRate(BigDecimal.valueOf(0.00));
        return cashPledgeChargeSubjectVo;
    }

    /**
     * 获取变更后的递增规则
     *
     * @return 变更后的递增规则
     */
    private List<PreviewBillparamsRoomChargeIncreaseRule> getChangeIncreaseRuleList() {
        List<PreviewBillparamsRoomChargeIncreaseRule> increaseRuleList = new ArrayList<>();
        List<BbsChangeIncrementalConfigVo> changeIncrementalList = getChangeIncrementalConfigList();
        AtomicInteger count = new AtomicInteger(1);
        changeIncrementalList.forEach(incremental -> {
            PreviewBillparamsRoomChargeIncreaseRule increaseRules = new PreviewBillparamsRoomChargeIncreaseRule();
            increaseRules.setIncreaseRuleId(count.getAndIncrement());
            increaseRules.setIncreaseProportion(Convert.toBigDecimal(incremental.getIncrease()).divide(Convert.toBigDecimal(100)));//递增比例
            increaseRules.setIncreasePeriod("year".equals(incremental.getUnit()) ? "04" : "01");//递增周期
            increaseRules.setIncreaseType("appoint".equals(incremental.getAdjustmentPoint()) ? "02" : "01");
            increaseRules.setIncreaseOrder(incremental.getTimePoint());
            increaseRuleList.add(increaseRules);
        });
        return increaseRuleList;
    }

    /**
     * 获取合同变更递增规则列表
     *
     * @return 合同变更递增规则列表
     */
    private List<BbsChangeIncrementalConfigVo> getChangeIncrementalConfigList() {
        if (Objects.isNull(contractChangeVo.getRentStandardJson())) {
            return businessServiceConfiguration
                    .getChangeIncrementalConfigService()
                    .selectByCcIdAndType(contractChangeVo.getCcId(), BeforeAndAfterEnum.AFTER.getCode(), StandardTypeEnum.RENT.getCode());
        } else {
            List<BbsChangeIncrementalConfigVo> resultList = new ArrayList<>();
            List<IncrementalInfoArray> incrementalInfoList = contractChangeVo.getRentStandardJson()
                    .getIncrementalInfo()
                    .getIncrementalInfoArray();
            for (IncrementalInfoArray incrementalInfo : incrementalInfoList) {
                BbsChangeIncrementalConfigVo changeIncrementalConfigVo = new BbsChangeIncrementalConfigVo();
                BeanUtils.copyProperties(incrementalInfo, changeIncrementalConfigVo);
                resultList.add(changeIncrementalConfigVo);
            }
            return resultList;
        }
    }

    /**
     * 获取上级合同签约的优惠规则
     *
     * @param contractOtherInfo 合同扩展字段信息
     * @return 上级合同签约的优惠规则
     */
    private List<PreviewBillparamsRoomChargePreferentRule> getParentPreferentRuleList(ContractOtherInfo contractOtherInfo) {
        List<PreviewBillparamsRoomChargePreferentRule> preferentRuleList = new ArrayList<>();
        if (Objects.isNull(contractOtherInfo) || Objects.isNull(contractOtherInfo.getPropFreeVo())) {
            return preferentRuleList;
        }
        FreeVo freeVo = contractOtherInfo.getPropFreeVo();
        if ("0".equals(freeVo.getFreePeriodType())) {
            return preferentRuleList;
        } else if ("2".equals(freeVo.getFreePeriodType())) {
            PreviewBillparamsRoomChargePreferentRule preferentRules = new PreviewBillparamsRoomChargePreferentRule();
            preferentRules.setPreferentRuleId(1);
            preferentRules.setPreferentialType("01");
            preferentRules.setPreferentialBeginDate(freeVo.getFpFixedDate());
            preferentRules.setPreferentialEndDate(DateUtils.getDateAddDaySubtraction(freeVo.getFpFixedDate(), freeVo.getFpFixedValue()));
            preferentRuleList.add(preferentRules);
        } else if ("1".equals(freeVo.getFreePeriodType())) {
            List<FreeSectionVo> freeSectionList = freeVo.getFreeSections();
            AtomicInteger count = new AtomicInteger(1);
            freeSectionList.forEach(freeSection -> {
                PreviewBillparamsRoomChargePreferentRule preferentRules = new PreviewBillparamsRoomChargePreferentRule();
                preferentRules.setPreferentRuleId(count.getAndIncrement());
                preferentRules.setPreferentialType("01");
                preferentRules.setPreferentialBeginDate(freeSection.getStart());
                preferentRules.setPreferentialEndDate(freeSection.getEnd());
                preferentRuleList.add(preferentRules);
            });
        }
        return preferentRuleList;
    }

    /**
     * 生成租金计费规则--物业费
     *
     * @param changeProductVo      变更后的产品信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @return 物业费计费规则
     */
    private PreviewBillParamsRoomChargeSubjectVo createPropertyChargeSubject(BbsChangeResultProductVo changeProductVo,
                                                                             BbctContractManagementVo parentContractInfoVo,
                                                                             String propStandardUnit, Double propStandard ) {
        //获取老合同物业费递增配置
        String increaseJson = parentContractInfoVo.getIncreaseJson();
        IncrementVo popVo = JsonToObjectUtil.jsonToPropIncrementVo(increaseJson);
        //获取老合同其他信息项
        String contractExtendJson = parentContractInfoVo.getContractExtend();
        ContractOtherInfo extendVo = JsonToObjectUtil.jsonToContractOtherInfo(contractExtendJson);
        PreviewBillParamsRoomChargeSubjectVo rentChargeSubjectVo = new PreviewBillParamsRoomChargeSubjectVo();
        rentChargeSubjectVo.setChargeSubjectNo("07");
        rentChargeSubjectVo.setCyclicOrSingle("01");
        rentChargeSubjectVo.setChargeSubjectPeriod(Integer.valueOf(parentContractInfoVo.getPaymentCycleCode()));
        rentChargeSubjectVo.setAmountType("0" + propStandardUnit);
        rentChargeSubjectVo.setParamList((JSONObject) JSON.toJSON(new BbctChargeRuleSubParamsBigVo()));
        BbctChargeRuleSubParamsBigVo chargeRuleSubParamsVo = new BbctChargeRuleSubParamsBigVo();
        chargeRuleSubParamsVo.setPARAMPRICE(String.valueOf(propStandard));
        chargeRuleSubParamsVo.setPARAMAREA(changeProductVo.getHouseStructArea());
        rentChargeSubjectVo.setParamValueList((JSONObject) JSON.toJSON(chargeRuleSubParamsVo));
        rentChargeSubjectVo.setTaxRate(BigDecimal.valueOf(extendVo
                        .getPropTaxRate())
                .divide(BigDecimal.valueOf(100)));
        rentChargeSubjectVo.setIncreaseRules(getPropChangeIncreaseRuleList(popVo));
        rentChargeSubjectVo.setPreferentRules(getParentPropPreferentRuleList(extendVo.getPropFreeVo()));
        return rentChargeSubjectVo;
    }

    private List<PreviewBillparamsRoomChargeIncreaseRule> getPropChangeIncreaseRuleList(IncrementVo popVo) {
        List<PreviewBillparamsRoomChargeIncreaseRule> increaseRuleList = new ArrayList<>();
        List<BbsChangeIncrementalConfigVo> changeIncrementalList = getPropChangeIncrementalConfigList(popVo);
        AtomicInteger count = new AtomicInteger(1);
        changeIncrementalList.forEach(incremental -> {
            PreviewBillparamsRoomChargeIncreaseRule increaseRules = new PreviewBillparamsRoomChargeIncreaseRule();
            increaseRules.setIncreaseRuleId(count.getAndIncrement());
            increaseRules.setIncreaseProportion(Convert.toBigDecimal(incremental.getIncrease()).divide(Convert.toBigDecimal(100)));//递增比例
            increaseRules.setIncreasePeriod("year".equals(incremental.getUnit()) ? "04" : "01");//递增周期
            increaseRules.setIncreaseType("appoint".equals(incremental.getAdjustmentPoint()) ? "02" : "01");
            increaseRules.setIncreaseOrder(incremental.getTimePoint());
            increaseRuleList.add(increaseRules);
        });

        return increaseRuleList;
    }


    /**
     * 获取合同变更递增规则列表----物业费
     *
     * @return 合同变更递增规则列表
     */
    private List<BbsChangeIncrementalConfigVo> getPropChangeIncrementalConfigList(IncrementVo popVo) {
        List<BbsChangeIncrementalConfigVo> resultList = new ArrayList<>();
        // 修改为获取老合同物业费
        if(popVo.getIncrementalFlag().equals("1")){
            List<BbsSignIncrementalConfigVo> incrementalInfoList = popVo.getIncrementList();
            for (BbsSignIncrementalConfigVo incrementalInfo : incrementalInfoList) {
                BbsChangeIncrementalConfigVo changeIncrementalConfigVo = new BbsChangeIncrementalConfigVo();
                BeanUtils.copyProperties(incrementalInfo, changeIncrementalConfigVo);
                resultList.add(changeIncrementalConfigVo);
            }
        }
        return resultList;
    }

    /**
     * 获取上级合同签约的优惠规则
     *
     * @param popFree 物业费免租期
     * @return 上级合同签约的优惠规则--物业费
     */
    private List<PreviewBillparamsRoomChargePreferentRule> getParentPropPreferentRuleList(FreeVo popFree) {
        List<PreviewBillparamsRoomChargePreferentRule> preferentRuleList = new ArrayList<>();
        if ("0".equals(popFree.getFreePeriodType())) {//物业费免租期分类
            return preferentRuleList;
        } else if ("2".equals(popFree.getFreePeriodType())) {//物业费免租期分类
            PreviewBillparamsRoomChargePreferentRule preferentRules = new PreviewBillparamsRoomChargePreferentRule();
            preferentRules.setPreferentRuleId(1);
            preferentRules.setPreferentialType("01");
            preferentRules.setPreferentialBeginDate(popFree.getFpFixedDate());//物业费免租期固定日期,当租金免租期分类为2时有效
            preferentRules.setPreferentialEndDate(DateUtils.getDateAddDaySubtraction(popFree.getFpFixedDate(), popFree.getFpFixedValue()));
            preferentRuleList.add(preferentRules);
        } else if ("1".equals(popFree.getFreePeriodType())) {//物业费免租期分类
            List<FreeSectionVo> fpIntervalEntities = popFree.getFreeSections();//免租期信息列表
            AtomicInteger count = new AtomicInteger(1);
            fpIntervalEntities.forEach(fpIntervalEntity -> {
                PreviewBillparamsRoomChargePreferentRule preferentRules = new PreviewBillparamsRoomChargePreferentRule();
                preferentRules.setPreferentRuleId(count.getAndIncrement());
                preferentRules.setPreferentialType("01");
                preferentRules.setPreferentialBeginDate(fpIntervalEntity.getStart());
                preferentRules.setPreferentialEndDate(fpIntervalEntity.getEnd());
                preferentRuleList.add(preferentRules);
            });
        }
        return preferentRuleList;
    }
}
