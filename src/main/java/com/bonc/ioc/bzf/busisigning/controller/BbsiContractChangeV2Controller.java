package com.bonc.ioc.bzf.busisigning.controller;

import com.bonc.ioc.bzf.busisigning.entity.BbsTemplateSeatEntity;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractTemplateSeatVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractTemplateVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeService;
import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeV2Service;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合同变更表 前端控制器
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by fzq for init
 */
@RestController
@RequestMapping("/contractChangeV2")
@Api(tags = "合同变更V2")
public class BbsiContractChangeV2Controller {
    @Resource
    private IBbsiContractChangeV2Service iBbsiContractChangeV2Service;

    @Resource
    private IBbsiContractChangeService baseService;


    @GetMapping(value = "/findTransactors", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "001-根据合同编号查询办理人", notes = "首次新增合同变更，根据合同编号，查询办理人")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsiContractChangeCustomerVo> findTransactors(@ApiParam(value = "合同编号") @RequestParam String contractCode) {
        AppReply<BbsiContractChangeCustomerVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(iBbsiContractChangeV2Service.findTransactors(contractCode));
        return appReply;
    }


    /**
     * 查询变更信息
     * @param ccId
     * @param contractCode
     * @return
     */
    @GetMapping(value = "/selectChangeInfos", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "tbh")
    @ApiOperation(value = "002-查询变更信息", notes = "新增时传合同编号，修改时传主键ccId,新增或切换类型时changeTypeItem必传,查看详情时isDetails=1.乙方变更信息里有办理人信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsiContractChangeVo> selectChangeInfos(@ApiParam(value = "合同编号", required = false)  @RequestParam(required = false) String contractCode
            ,@ApiParam(value = "主键", required = false)  @RequestParam(required = false) String ccId
            ,@ApiParam(value = "变更类型小项", required = false)  @RequestParam(required = false) String changeTypeItem
            ,@ApiParam(value = "查看详情", required = false)  @RequestParam(required = false) String isDetails){
        AppReply<BbsiContractChangeVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbsiContractChangeV2Service.selectChangeInfos(ccId,contractCode,changeTypeItem,isDetails,null));
        return appReply;
    }


    @GetMapping(value = "/verifySameType", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "tbh")
    @ApiOperation(value = "003-验证是否有同类型合同变更", notes = "有交叉类型的合同变更出现时就抛出异常,无异常返回false字符串")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<String> verifySameType(@ApiParam(value = "合同编号", required = true) @RequestParam String contractCode
            ,@ApiParam(value = "变更类型小项", required = true) @RequestParam String changeTypeItem
            ,@ApiParam(value = "主键", required = false)  @RequestParam(required = false) String ccId) {
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(iBbsiContractChangeV2Service.verifySameType(contractCode,changeTypeItem,ccId));
        return appReply;
    }


    @GetMapping(value = "/verifyArrears", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "tbh")
    @ApiOperation(value = "004-验证是否欠费", notes = "有返回字符串true")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<String> verifyArrears(@ApiParam(value = "合同编号", required = true) @RequestParam String contractCode) {
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(iBbsiContractChangeV2Service.verifyArrears(contractCode));
        return appReply;
    }

    @PostMapping(value = "/insertRecordV2", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "fzq")
    @ApiOperation(value = "005-新增", notes = "新增全表数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增主键")})
//    @LogPoint(system = "bzf-system-busisigning-915",path = "/bzf-system-busisigning-915/contractChangeV2/insertRecordV2")
    public AppReply<String> insertRecordV2(@ApiParam(value = "合同变更表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsiContractChangeVo vo) {
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.insertRecordV2(vo, false));
        return appReply;
    }

    @PostMapping(value = "/updateByIdV2", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "fzq")
    @ApiOperation(value = "006-根据主键更新", notes = "根据主键更新表中信息 更新全部信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
//    @LogPoint(system = "bzf-system-busisigning-915",path = "/bzf-system-busisigning-915/contractChangeV2/updateByIdV2")
    public AppReply updateByIdRecordV2(@ApiParam(value = "需要更新的合并变更表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbsiContractChangeVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateByIdRecordV2(vo);
        return appReply;
    }


    @GetMapping(value = "/selectContractTemplateSeatByTemplateId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 26, author = "tbh")
    @ApiOperation(value = "007-协议其他信息项", notes = "根据模板id查询合同模板占位")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbctContractTemplateSeatVo>> selectContractTemplateSeatByTemplateId(@RequestParam(value = "contractTemplateId", required = false) String contractTemplateId) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbsiContractChangeV2Service.selectContractTemplateSeatByTemplateId(contractTemplateId));
        return appReply;
    }


    /**
     * 选择变更后的商铺地址
     * @param ccId
     * @param contractCode
     * @return
     */
    @GetMapping(value = "/selectChangedShopAddress", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "tbh")
    @ApiOperation(value = "008-选择变更后的商铺地址", notes = "选择变更后的商铺地址")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbsChangeShopInfoVo>> selectChangedShopAddress(@ApiParam(value = "合同编号", required = false)  @RequestParam(required = false) String contractCode
            ,@ApiParam(value = "主键", required = false)  @RequestParam(required = false) String ccId){
        AppReply<List<BbsChangeShopInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbsiContractChangeV2Service.selectChangedShopAddress(ccId,contractCode));
        return appReply;
    }

    @PostMapping(value = "/getPreviewBillByVo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "009-查询试算账单", notes = "查询试算账单")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:试算账单")})
//    @LogPoint(system = "bzf-system-busisigning-915",path = "/bzf-system-busisigning-915/contractChangeV2/getPreviewBillByVo")
    public AppReply<ChangeCalculationBillVo> getPreviewBillByVo(@ApiParam(value = "合同变更表", required = false) @RequestBody BbsiContractChangeVo vo) {
        AppReply<ChangeCalculationBillVo> appReply = new AppReply<ChangeCalculationBillVo>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(iBbsiContractChangeV2Service.getPreviewBillByVo(vo));
        return appReply;
    }

    @PostMapping(value = "/deductionBillDetermination", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "010-抵扣账单-点击确定", notes = "新增验证")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "")})
//    @LogPoint(system = "bzf-system-busisigning-915",path = "/bzf-system-busisigning-915/contractChangeV2/deductionBillDetermination")
    public AppReply<List<BbsChangeDetermineDeductionPeriodVo>> deductionBillDetermination(@ApiParam(value = "抵扣账单", required = false) @RequestBody ChangeCalculationBillVo vo) {
        AppReply<List<BbsChangeDetermineDeductionPeriodVo>> appReply = new AppReply<List<BbsChangeDetermineDeductionPeriodVo>>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(iBbsiContractChangeV2Service.deductionBillDetermination(vo));
        return appReply;
    }

    @PostMapping(value = "/updateFeesSummaryWithDeduction", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "011-根据抵扣账单动态更新费用汇总列表", notes = "根据抵扣账单中的抵扣金额，动态更新费用汇总列表的预计抵扣金额和预计退款金额")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:更新后的费用汇总列表")})
    public AppReply<List<FeesSummaryVo>> updateFeesSummaryWithDeduction(@ApiParam(value = "费用汇总更新请求", required = true) @RequestBody UpdateFeesSummaryRequestVo request) {
        AppReply<List<FeesSummaryVo>> appReply = new AppReply<List<FeesSummaryVo>>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(iBbsiContractChangeV2Service.updateFeesSummaryWithDeduction(
                request.getOriginalFeesSummaryList(),
                request.getRentAndBondDeductionBillList(),
                request.getPropertyDeductionBillList(),
                request.getRefundPaymentMethod()));
        return appReply;
    }


    @GetMapping(value = "/seatInfoVoList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "tbh")
    @ApiOperation(value = "012-协议其他信息项", notes = "传合同编号,seatTitle、value")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbsTemplateSeatEntity>> seatInfoVoList(@ApiParam(value = "合同编号", required = true)  @RequestParam(required = true) String contractCode
            ,@ApiParam(value = "是否显示", required = false)  @RequestParam(required = false) String isShow){
        AppReply<List<BbsTemplateSeatEntity>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbsiContractChangeV2Service.seatInfoVoList(contractCode,isShow));
        return appReply;
    }


    @GetMapping(value = "/verifyChangeCalculated", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "tbh")
    @ApiOperation(value = "013-判断能否能进行计组面积变更", notes = "不能就抛出异常，返回字符串true往下走")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<String> verifyChangeCalculated(@ApiParam(value = "合同编号", required = true) @RequestParam String contractCode
            ,@ApiParam(value = "变更类型小项", required = true) @RequestParam String changeTypeItem
             ) {
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(iBbsiContractChangeV2Service.verifyChangeCalculated(contractCode,changeTypeItem));
        return appReply;
    }

 
    @GetMapping(value = "/bbpmBillManagementEntity/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "014-账单管理--分页查询", notes = "账单管理--分页查询", hidden = false,position = 4)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> selectByPageRecordBill(com.bonc.ioc.bzf.busisigning.feign.vo.BbpmBillManagementPageVo vo){
        return iBbsiContractChangeV2Service.selectByPageRecordBill(vo);
    }

    @PostMapping(value = "/calculateSelectedBillsDateRange", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 15, author = "binghong.tang")
    @ApiOperation(value = "015-计算选择账单时间范围", notes = "计算选择账单周期的最早开始时间和最晚结束时间")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:账单时间范围字符串，如：2024-07-25至2026-08-24")})
    public AppReply<String> calculateSelectedBillsDateRange(@ApiParam(value = "选择的账单列表", required = true) @RequestBody List<BbpmBillManagementPageResultVo> selectedBills) {
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(iBbsiContractChangeV2Service.calculateSelectedBillsDateRange(selectedBills));
        return appReply;
    }

}

