package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsApproveInfoEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsRefundApproveDetailInfoEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsRefundApproveInfoEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsRefundApproveInfoMapper;
import com.bonc.ioc.bzf.busisigning.enums.ApproveStatusEnum;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.service.IBbsRefundApproveDetailInfoService;
import com.bonc.ioc.bzf.busisigning.service.IBbsRefundApproveInfoService;
import com.bonc.ioc.bzf.busisigning.utils.UserUtil;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.Date;
import java.util.Collections;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;

import com.bonc.ioc.common.base.page.PageResult;

/**
 * 退款审批表 服务类实现
 *
 * <AUTHOR>
 * @date 2024-08-28
 * @change 2024-08-28 by pyj for init
 */
@Slf4j
@Service
public class BbsRefundApproveInfoServiceImpl extends McpBaseServiceImpl<BbsRefundApproveInfoEntity> implements IBbsRefundApproveInfoService {

    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRefundApproveInfoMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsRefundApproveInfoService baseService;

    /**
     * 退款审批明细相关 服务实例
     */
    @Resource
    private IBbsRefundApproveDetailInfoService refundApproveDetailInfoService;

    /**
     * 用户相关 工具实例
     */
    @Resource
    private UserUtil userUtil;

    /**
     * 字典 缓存实例
     */
    @Resource
    private McpDictSession mcpDictSession;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbsRefundApproveInfoVo vo) {
        if (vo == null) {
            return null;
        }

        BbsRefundApproveInfoEntity entity = new BbsRefundApproveInfoEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setApproveId(null);
        if (!baseService.insert(entity)) {
            log.error("退款审批表新增失败:" + entity.toString());
            throw new McpException("退款审批表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getApproveId(), 1)) {
                log.error("退款审批表新增后保存历史失败:" + entity.toString());
                throw new McpException("退款审批表新增后保存历史失败");
            }

            log.debug("退款审批表新增成功:" + entity.getApproveId());
            return entity.getApproveId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsRefundApproveInfoVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsRefundApproveInfoEntity> entityList = new ArrayList<>();
        for (BbsRefundApproveInfoVo item : voList) {
            BbsRefundApproveInfoEntity entity = new BbsRefundApproveInfoEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsRefundApproveInfoEntity item : entityList) {
            item.setApproveId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("退款审批表新增失败");
            throw new McpException("退款审批表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbsRefundApproveInfoEntity::getApproveId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("退款审批表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("退款审批表批量新增后保存历史失败");
            }

            log.debug("退款审批表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param approveId 需要删除的审批id
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String approveId) {
        if (!StringUtils.isEmpty(approveId)) {
            if (!baseService.saveOperationHisById(approveId, 3)) {
                log.error("退款审批表删除后保存历史失败:" + approveId);
                throw new McpException("退款审批表删除后保存历史失败");
            }

            if (!baseService.removeById(approveId)) {
                log.error("退款审批表删除失败");
                throw new McpException("退款审批表删除失败" + approveId);
            }
        } else {
            throw new McpException("退款审批表删除失败审批id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param approveIdList 需要删除的审批id
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> approveIdList) {
        if (!CollectionUtils.isEmpty(approveIdList)) {
            int oldSize = approveIdList.size();
            approveIdList = approveIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(approveIdList) || oldSize != approveIdList.size()) {
                throw new McpException("退款审批表批量删除失败 存在主键id为空的记录" + StringUtils.join(approveIdList));
            }

            if (!baseService.saveOperationHisByIds(approveIdList, 3)) {
                log.error("退款审批表批量删除后保存历史失败:" + StringUtils.join(approveIdList));
                throw new McpException("退款审批表批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(approveIdList)) {
                log.error("退款审批表批量删除失败");
                throw new McpException("退款审批表批量删除失败" + StringUtils.join(approveIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的退款审批表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsRefundApproveInfoVo vo) {
        if (vo != null) {
            BbsRefundApproveInfoEntity entity = new BbsRefundApproveInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getApproveId())) {
                throw new McpException("退款审批表更新失败传入审批id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("退款审批表更新失败");
                throw new McpException("退款审批表更新失败" + entity.getApproveId());
            } else {
                if (!baseService.saveOperationHisById(entity.getApproveId(), 2)) {
                    log.error("退款审批表更新后保存历史失败:" + entity.getApproveId());
                    throw new McpException("退款审批表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("退款审批表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的退款审批表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsRefundApproveInfoVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsRefundApproveInfoEntity> entityList = new ArrayList<>();

            for (BbsRefundApproveInfoVo item : voList) {
                BbsRefundApproveInfoEntity entity = new BbsRefundApproveInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getApproveId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("退款审批表批量更新失败 存在审批id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("退款审批表批量更新失败");
                throw new McpException("退款审批表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getApproveId())).map(BbsRefundApproveInfoEntity::getApproveId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("退款审批表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("退款审批表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的退款审批表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsRefundApproveInfoVo vo) {
        if (vo != null) {
            BbsRefundApproveInfoEntity entity = new BbsRefundApproveInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("退款审批表保存失败");
                throw new McpException("退款审批表保存失败" + entity.getApproveId());
            } else {
                if (!baseService.saveOperationHisById(entity.getApproveId(), 4)) {
                    log.error("退款审批表保存后保存历史失败:" + entity.getApproveId());
                    throw new McpException("退款审批表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("退款审批表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的退款审批表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsRefundApproveInfoVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsRefundApproveInfoEntity> entityList = new ArrayList<>();

            for (BbsRefundApproveInfoVo item : voList) {
                BbsRefundApproveInfoEntity entity = new BbsRefundApproveInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("退款审批表批量保存失败");
                throw new McpException("退款审批表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getApproveId())).map(BbsRefundApproveInfoEntity::getApproveId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("退款审批表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("退款审批表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param approveId 需要查询的审批id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsRefundApproveInfoVo selectByIdRecord(String approveId) {
        BbsRefundApproveInfoVo vo = new BbsRefundApproveInfoVo();

        if (!StringUtils.isEmpty(approveId)) {
            BbsRefundApproveInfoEntity entity = baseService.selectById(approveId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsRefundApproveInfoPageResultVo>> selectByPageRecord(BbsRefundApproveInfoPageVo vo) {
        List<BbsRefundApproveInfoPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 退款审核分页查询
     *
     * @param vo 退款审核分页请求参数
     * @return 退款审核分页结果
     */
    @Override
    public PageResult<List<BbsRefundApplyPageResultVo>> selectRefundApplyApproveByPage(BbsRefundApplyPageVo vo) {
        List<BbsRefundApplyPageResultVo> result = baseMapper.selectRefundApplyApproveByPage(vo);
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }

    /**
     * 退款申请提交
     *
     * @param refundId 退款id
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void submit(String refundId) {
        // 保存历史数据
        BbsRefundApproveInfoEntity entity = saveApproveDetailInfo(refundId, 1);
        if (Objects.isNull(entity)) {
            entity = new BbsRefundApproveInfoEntity();
        }
        // 保存最新记录
        entity.setApproveId(IdUtil.fastSimpleUUID());
        entity.setParentId(refundId);
        entity.setApproveType("1");
        entity.setApproveStatus(ApproveStatusEnum.WAIT_APPROVE.getCode());
        entity.setSubmitUserId(userUtil.getUserId());
        entity.setSubmitUserName(userUtil.getUserName(userUtil.getUserId()));
        entity.setSubmitTime(new Date(System.currentTimeMillis()));
        entity.setDelFlag(1);
        baseMapper.insert(entity);
    }

    /**
     * 退款申请通过
     *
     * @param refundId 退款id
     * @param remark   说明
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void approve(String refundId, String remark) {
        // 保存历史数据
        BbsRefundApproveInfoEntity entity = saveApproveDetailInfo(refundId, 1);
        // 保存最新记录
        entity.setApproveId(IdUtil.fastSimpleUUID());
        entity.setParentId(refundId);
        entity.setApproveType("1");
        entity.setApproveStatus(ApproveStatusEnum.APPROVED.getCode());
        entity.setApproverUserId(userUtil.getUserId());
        entity.setApproverUserName(userUtil.getUserName(userUtil.getUserId()));
        entity.setApproveTime(new Date(System.currentTimeMillis()));
        entity.setCommentExplanation(remark);
        entity.setDelFlag(1);
        baseMapper.insert(entity);
    }

    /**
     * 退款申请未通过
     *
     * @param refundId 退款id
     * @param remark   说明
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void unApprove(String refundId, String remark) {
        // 保存历史数据
        BbsRefundApproveInfoEntity entity = saveApproveDetailInfo(refundId, 1);
        // 保存最新记录
        entity.setApproveId(IdUtil.fastSimpleUUID());
        entity.setParentId(refundId);
        entity.setApproveType("1");
        entity.setApproveStatus(ApproveStatusEnum.UNAPPROVED.getCode());
        entity.setApproverUserId(userUtil.getUserId());
        entity.setApproverUserName(userUtil.getUserName(userUtil.getUserId()));
        entity.setApproveTime(new Date(System.currentTimeMillis()));
        entity.setCommentExplanation(remark);
        entity.setDelFlag(1);
        baseMapper.insert(entity);
    }

    /**
     * 根据signId批量保存明细
     *
     * @param refundId 退款id
     * @return 最新审批信息
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsRefundApproveInfoEntity saveApproveDetailInfo(String refundId, Integer delFlag) {
        //用signId查询是否存在记录 存在则保存到历史表中
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("parent_id", refundId);
        queryMap.put("del_flag", 1);
        List<BbsRefundApproveInfoEntity> refundApproveInfoEntities = baseMapper.selectByMap(queryMap);
        if (!refundApproveInfoEntities.isEmpty()) {
            List<BbsRefundApproveDetailInfoVo> detailInfoVos = refundApproveInfoEntities.stream().map(t -> {
                BbsRefundApproveDetailInfoVo approveDetailInfoVo = new BbsRefundApproveDetailInfoVo();
                BeanUtils.copyProperties(t, approveDetailInfoVo);
                approveDetailInfoVo.setApproveDetailId(IdUtil.fastSimpleUUID());
                approveDetailInfoVo.setDelFlag(delFlag);
                return approveDetailInfoVo;
            }).collect(Collectors.toList());
            refundApproveDetailInfoService.insertBatchRecord(detailInfoVos);
            //保存完明细 将审核表数据删除
            baseMapper.deleteBatchIds(refundApproveInfoEntities.stream().map(BbsRefundApproveInfoEntity::getApproveId).collect(Collectors.toList()));
            return refundApproveInfoEntities.get(0);
        }
        return null;
    }

    /**
     * 获取退款审核操作记录
     *
     * @param refundId 退款id
     * @return 退款审核操作记录
     */
    @Override
    public List<BbsRefundApproveDetailInfoVo> selectDetailInfo(String refundId) {
        List<BbsRefundApproveInfoEntity> approveInfoList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BbsRefundApproveInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRefundApproveInfoEntity::getParentId, refundId)
                .orderByDesc(BbsRefundApproveInfoEntity::getCreateTime)
                .list();
        if (CollectionUtils.isEmpty(approveInfoList)) {
            return null;
        }
        List<BbsRefundApproveDetailInfoEntity> approveDetailInfoList = refundApproveDetailInfoService.selectByParentId(refundId);
        List<BbsRefundApproveDetailInfoVo> resultList = new ArrayList<>();
        for (BbsRefundApproveInfoEntity approveInfoEntity : approveInfoList) {
            BbsRefundApproveDetailInfoVo vo = new BbsRefundApproveDetailInfoVo();
            BeanUtils.copyProperties(approveInfoEntity, vo);
            resultList.add(vo);
        }
        if (CollectionUtils.isNotEmpty(approveDetailInfoList)) {
            for (BbsRefundApproveDetailInfoEntity approveDetailInfoEntity : approveDetailInfoList) {
                BbsRefundApproveDetailInfoVo vo = new BbsRefundApproveDetailInfoVo();
                BeanUtils.copyProperties(approveDetailInfoEntity, vo);
                resultList.add(vo);
            }
        }
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(resultList);
        return resultList;
    }
}
