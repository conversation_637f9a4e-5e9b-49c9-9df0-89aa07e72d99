package com.bonc.ioc.bzf.busisigning.controller;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementPageResultVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementPageVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractStatusNumVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctMessageDownContractVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbpmBillManagementPageVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.EmailDownloadVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.TreeParamVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.TreeVo;
import com.bonc.ioc.bzf.busisigning.service.BbctContractService;
import com.bonc.ioc.bzf.busisigning.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbpmCashPledgeVo;
import com.bonc.ioc.bzf.busisigning.vo.ContractingProgressVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: lidongyang
 * @createDate: 2023-08-30
 * @Version 1.0
 **/
@RestController
@RequestMapping("/contract")
@Api(tags = "合同管理")
@Slf4j
public class BbctContractController {

    @Autowired
    private BbctContractService bbctContractService;

    /**
     * 树型房子信息查询
     *
     * @param vo
     * @return
     */
    @GetMapping(value = "/selectTree", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "lidongyang")
    @ApiOperation(value = "树型房子信息查询", notes = "树型房子信息查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<TreeVo>> selectTree(TreeParamVo vo) {
        AppReply<List<TreeVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.selectTree(vo);
        } catch (Exception e) {
            appReply.setData(new ArrayList<TreeVo>());
            log.error("=======================================合同树型房子信息查询异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 合同状态分类统计数量查询
     */
    @GetMapping(value = "/selectContractStatusNum", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "lidongyang")
    @ApiOperation(value = "合同状态分类统计数量查询", notes = "合同状态分类统计数量查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbctContractStatusNumVo> selectContractStatusNum(String contractTypeCode) {
        AppReply<BbctContractStatusNumVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.selectContractStatusNum(contractTypeCode);
        } catch (Exception e) {
            appReply.setData(new BbctContractStatusNumVo());
            log.error("=======================================合同状态分类统计数量查询异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 合同列表查询
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/search", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "lidongyang")
    @ApiOperation(value = "合同列表查询", notes = "合同列表查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbctContractManagementPageResultVo>>> search(@RequestBody BbctContractManagementPageVo vo) {
        AppReply<PageResult<List<BbctContractManagementPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.findContractByPage(vo);
        } catch (Exception e) {
            appReply.setData(new PageResult<>());
            log.error("=======================================合同列表分页查询异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 合同列表导出
     *
     * @param vo
     * @return
     */
    @GetMapping(value = "/export", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "lidongyang")
    @ApiOperation(value = "合同列表导出", notes = "合同列表导出")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> export(HttpServletResponse response, BbctContractManagementPageVo vo) {
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            bbctContractService.contractBatchExport(response, vo);
            appReply.setData("导出成功");
        } catch (Exception e) {
            appReply.setCode(AppReply.ERROR_CODE);
            appReply.setData("导出失败");
            log.error("=======================================合同列表导出异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 合同下载(下载到邮箱)
     */
    @PostMapping(value = "/downloadContractForEmail", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "lidongyang")
    @ApiOperation(value = "合同下载(下载到邮箱)", notes = "合同下载(下载到邮箱)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Boolean> downloadContractForEmail(@RequestBody EmailDownloadVo vo) {
        AppReply<Boolean> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.downloadContractForEmail(vo);
        } catch (Exception e) {
            appReply.setData(false);
            log.error("=======================================合同下载(下载到邮箱)异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 合同下载(短信下载)
     */
    @PostMapping(value = "/sendMessageDownContract", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "lidongyang")
    @ApiOperation(value = "合同下载(短信下载)", notes = "合同下载(短信下载)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Boolean> sendMessageDownContract(@RequestBody BbctMessageDownContractVo vo) {
        AppReply<Boolean> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.sendMessageDownContract(vo);
        } catch (Exception e) {
            appReply.setData(false);
            log.error("=======================================合同下载(短信下载)异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 关联信息-账单及保证金查询
     *
     * @param vo
     * @return
     */
    @GetMapping(value = "/getBill", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "lidongyang")
    @ApiOperation(value = "账单查询及保证金查询", notes = "账单查询及保证金查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> getBill(BbpmBillManagementPageVo vo) {
        AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.getBill(vo);
        } catch (Exception e) {
            appReply.setData(new PageResult<>());
            log.error("=======================================账单查询及保证金查询异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 通过账单id查询账单文件id（保证金预览及下载）
     */
    @GetMapping(value = "/getFileIdByBillId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "lidongyang")
    @ApiOperation(value = "通过账单id查询账单文件id", notes = "通过账单id查询账单文件id")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmCashPledgeVo> getFileIdByBillId(String billId, String projectId) {
        AppReply<BbpmCashPledgeVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.getFileIdByBillId(billId, projectId);
        } catch (Exception e) {
            appReply.setData(null);
            log.error("=======================================异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 通过房源编码查询房源信息
     */
    @GetMapping(value = "/getHousesByHouseCodes", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "lidongyang")
    @ApiOperation(value = "通过房源编码查询房源信息", notes = "通过房源编码查询房源信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<JSONObject>> getHousesByHouseCodes(@RequestParam List<String> houseCodes) {
        AppReply<List<JSONObject>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.getHousesByHouseCodes(houseCodes);
        } catch (Exception e) {
            appReply.setData(null);
            log.error("=======================================异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 签约进度查询
     */
    @GetMapping(value = "/app/getContractingProgress", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "lidongyang")
    @ApiOperation(value = "签约进度查询", notes = "签约进度查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<ContractingProgressVo> getContractingProgress(@ApiParam(value = "合同编码", required = true) @RequestParam String contractCode, @ApiParam(value = "签约类型 1.新签 2.续签", required = true) @RequestParam String signType) {
        return bbctContractService.getContractingProgress(contractCode,signType);
    }

    /**
     * 测试使用
     * @param signId
     * @return
     */
    @GetMapping(value = "/generateContract", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "lidongyang")
    @ApiOperation(value = "合同生成", notes = "合同生成")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> generateContract(String signId) {
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.generateContract(signId);
        } catch (Exception e) {
            appReply.setData("");
            log.error("=======================================异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 测试使用
     * @param signId
     * @return
     */
    @GetMapping(value = "/modifyContractStatus", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "lidongyang")
    @ApiOperation(value = "合同状态修改", notes = "合同状态修改")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply modifyContractStatus(String signId) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.modifyContractStatus(signId);
        } catch (Exception e) {
            appReply.setData("");
            log.error("=======================================异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 测试使用
     * @param signId
     * @return
     */
    @GetMapping(value = "/generateBill", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "lidongyang")
    @ApiOperation(value = "账单生成", notes = "账单生成")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> generateBill(String signId) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.generateBill(signId);
        } catch (Exception e) {
            appReply.setData("");
            log.error("=======================================异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 测试使用
     * @param signId
     * @return
     */
    @GetMapping(value = "/generateRenewalContract", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "lidongyang")
    @ApiOperation(value = "合同生成-续租", notes = "合同生成-续租")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> generateRenewalContract(String signId) {
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.generateRenewalContract(signId);
        } catch (Exception e) {
            appReply.setData("");
            log.error("=======================================异常，异常信息为：", e);
        }
        return appReply;
    }

    /**
     * 测试使用
     * @param signId
     * @return
     */
    @GetMapping(value = "/generateRenewalBill", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "lidongyang")
    @ApiOperation(value = "账单生成-续租", notes = "账单生成-续租")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> generateRenewalBill(String signId) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.generateRenewalBill(signId);
        } catch (Exception e) {
            appReply.setData("");
            log.error("=======================================异常，异常信息为：", e);
        }
        return appReply;
    }

    @GetMapping(value = "/checkIntention", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "刘鹏伟")
    @ApiOperation(value = "查询产品是否意向登记", notes = "账单生成-查询产品是否意向登记,存在:data='1',不存在:data='0'")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> checkIntention(@ApiParam(value = "合同编号" ,required = true) @RequestParam(required = true) String contractNo) {
        AppReply<String> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply = bbctContractService.checkIntention(contractNo);
        } catch (Exception e) {
            appReply.setData("");
            log.error("=======================================异常，异常信息为：", e);
        }
        return appReply;
    }

    @GetMapping(value = "/selectByPageAgreementList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "lidongyang")
    @ApiOperation(value = "商业合同列表根据合同号获取所有生效协议分页查询", notes = "合同分页查询（散租、趸租、管理协议需要传各自的business_type_code2值，01趸租，02散租，03管理协议）", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbctContractManagementPageResultVo>>> selectByPageAgreementList(BbctContractManagementPageVo vo) {
        AppReply<PageResult<List<BbctContractManagementPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(bbctContractService.selectByPageAgreementList(vo));
        return appReply;
    }
}
