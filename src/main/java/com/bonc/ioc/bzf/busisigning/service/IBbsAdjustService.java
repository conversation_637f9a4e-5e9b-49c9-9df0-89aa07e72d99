package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbpmBillManagementPageVo;
import com.bonc.ioc.bzf.busisigning.vo.adjust.*;
import com.bonc.ioc.bzf.busisigning.workflow.vo.CallBakParamsVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.dict.entity.McpDictEntity;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 账单收款单服务类
 *
 * <AUTHOR>
 * @date 2021-08-27
 * @change 2021-08-27 by wtl for init
 */
public interface IBbsAdjustService {




    //++++++++++++++应收调整接口-开始++++++++++++++
    /**
     * 分页查询账单调整接口
     * @param vo
     * @return
     */
    AppReply<PageResult<List<BbpmReceivableAdjustPageResultVo>>> selectAdjustList(BbpmReceivableAdjustPageVo vo);


    /**
     * 根据调整单id查询详情
     * @param id
     * @return
     */
    AppReply<BbpmReceivableAdjustVo> selectAdjustById(String id);

    AppReply<BbpmReceivableAdjustVo> adjustSelectByCcid(String id);

    AppReply<BbpmReceivableAdjustVo> adjustSelectBySignId(String signId);

    /**
     * 根据主键更新或新增调整单
     * @param vo 需要更新的应收调整
     * @return
     */
    AppReply saveAdjustById(BbpmReceivableAdjustVo vo);

    /**
     * 根据主键删除调整单
     * @param id 需要删除的主键
     * @return
     */
    AppReply removeByAdjustId(String id);

    AppReply removeByRequestId(String requestId);

    AppReply<PageResult<List<BbpmReceivableAdjustContractPageResultVo>>> selectByPageForAdjust(BbpmReceivableAdjustContractPageVo vo);

    AppReply<List<CalculationResultVo>> adjustBillView(CalculationParamVo vo);


    AppReply<List<BbpmBillForAdjustPageVo>> selectBillForAdjust(String id);

    AppReply<String[]> selectContractForAdjustId(String id);

    //++++++++++++++应收调整接口-结束++++++++++++++



    String adjustSaveWorkFlow(BbpmReceivableAdjustVo vo);

    AppReply<Object> finishAdjust(CallBakParamsVo vo);

    AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> selectByPageRecord(BbpmBillManagementPageVo vo);

    String getContractCode(String modeId, String contractCode);

    String pushSignInfo(BbpmReceivableAdjustVo adjustVo,String agreementCode);

    void createAdjust(String ccid);
}
