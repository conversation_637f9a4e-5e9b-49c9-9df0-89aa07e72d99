package com.bonc.ioc.bzf.busisigning.workflow.vo;

import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CallBakParamsVo {

    @NotBlank(message = "requestId不能为空",groups = {InsertValidatorGroup.class})
    @ApiModelProperty("requestId")
    private String requestId;

    @NotBlank(message = "操作类型不能为空（同意、驳回）",groups = {InsertValidatorGroup.class})
    @ApiModelProperty("type:1 同意 0 驳回")
    private String type;

    @NotBlank(message = "节点编号不饿能为空",groups = {InsertValidatorGroup.class})
    @ApiModelProperty("节点编号")
    private String nodeKey;

    @ApiModelProperty("意见")
    private String comment;

}
