package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsResultCustomerEntity;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerPageVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 人-产品：客户表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by liwenqiang for init
 */
public interface IBbsResultCustomerService extends IMcpBaseService<BbsResultCustomerEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    String insertRecord(BbsResultCustomerVo vo);

    String insertRecord(BbsResultCustomerEntity entity);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    List<String> insertBatchRecord(List<BbsResultCustomerVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param rcId 需要删除的
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void removeByIdRecord(String rcId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param rcIdList 需要删除的
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void removeByIdsRecord(List<String> rcIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void updateByIdRecord(BbsResultCustomerVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void updateBatchByIdRecord(List<BbsResultCustomerVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void saveByIdRecord(BbsResultCustomerVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void saveBatchByIdRecord(List<BbsResultCustomerVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param rcId 需要查询的
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    BbsResultCustomerVo selectByIdRecord(String rcId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    PageResult<List<BbsResultCustomerPageResultVo>> selectByPageRecord(BbsResultCustomerPageVo vo);

    /**
     * countByProductNoes 查询客户是否存在
     *
     * @param productNoes 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-06
     */
    public List<Map<String,Object>> countByProductNoes(List<String> productNoes);

    /**
     * countByContractCode 查询客户是否存在
     *
     * @param contractCode 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-06
     */
    public List<Map<String,Object>> countByContractCode(String contractCode);

    /**
     * 根据人房关系id查询
     *
     * @param rrId 人房关系id
     * @return 客户信息 vo实体
     */
    BbsResultCustomerVo selectByRrId(String rrId);
}
