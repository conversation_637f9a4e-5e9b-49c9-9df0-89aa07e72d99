package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeReturnBankCardEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更退回银行卡信息 服务类
 *
 * <AUTHOR>
 * @date 2024-09-06
 * @change 2024-09-06 by King-Y for init
 */
public interface IBbsChangeReturnBankCardService extends IMcpBaseService<BbsChangeReturnBankCardEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    String insertRecord(BbsChangeReturnBankCardVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    List<String> insertBatchRecord(List<BbsChangeReturnBankCardVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param cardId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    void removeByIdRecord(String cardId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param cardIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    void removeByIdsRecord(List<String> cardIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的合同变更退回银行卡信息
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    void updateByIdRecord(BbsChangeReturnBankCardVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的合同变更退回银行卡信息
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    void updateBatchByIdRecord(List<BbsChangeReturnBankCardVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的合同变更退回银行卡信息
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    void saveByIdRecord(BbsChangeReturnBankCardVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的合同变更退回银行卡信息
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    void saveBatchByIdRecord(List<BbsChangeReturnBankCardVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param cardId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    BbsChangeReturnBankCardVo selectByIdRecord(String cardId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-06
     * @change
     * 2024-09-06 by King-Y for init
     */
    PageResult<List<BbsChangeReturnBankCardPageResultVo>> selectByPageRecord(BbsChangeReturnBankCardPageVo vo);
}
