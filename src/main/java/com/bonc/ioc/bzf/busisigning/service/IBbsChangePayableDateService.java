package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangePayableDateEntity;
import com.bonc.ioc.bzf.busisigning.vo.BbsChangePayableDatePageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsChangePayableDatePageVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsChangePayableDateVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

/**
 * 应缴费日期变更账单信息表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-23
 * @change 2024-10-23 by binghong.tang for init
 */
public interface IBbsChangePayableDateService extends IMcpBaseService<BbsChangePayableDateEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-23
     * @change
     * 2024-10-23 by binghong.tang for init
     */
    String insertRecord(BbsChangePayableDateVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-23
     * @change
     * 2024-10-23 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbsChangePayableDateVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param dateId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-23
     * @change
     * 2024-10-23 by binghong.tang for init
     */
    void removeByIdRecord(String dateId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param dateIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-23
     * @change
     * 2024-10-23 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> dateIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的应缴费日期变更账单信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-23
     * @change
     * 2024-10-23 by binghong.tang for init
     */
    void updateByIdRecord(BbsChangePayableDateVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的应缴费日期变更账单信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-23
     * @change
     * 2024-10-23 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbsChangePayableDateVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的应缴费日期变更账单信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-23
     * @change
     * 2024-10-23 by binghong.tang for init
     */
    void saveByIdRecord(BbsChangePayableDateVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的应缴费日期变更账单信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-23
     * @change
     * 2024-10-23 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbsChangePayableDateVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param dateId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-23
     * @change
     * 2024-10-23 by binghong.tang for init
     */
    BbsChangePayableDateVo selectByIdRecord(String dateId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-23
     * @change
     * 2024-10-23 by binghong.tang for init
     */
    PageResult<List<BbsChangePayableDatePageResultVo>> selectByPageRecord(BbsChangePayableDatePageVo vo);
}
