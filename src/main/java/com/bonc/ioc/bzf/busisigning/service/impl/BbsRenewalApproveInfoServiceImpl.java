package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApproveInfoMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyInfoEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApproveDetailInfoEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApproveInfoEntity;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.enums.SignStatusEnum;
import com.bonc.ioc.bzf.busisigning.feign.feign.*;
import com.bonc.ioc.bzf.busisigning.feign.vo.*;
import com.bonc.ioc.bzf.busisigning.service.*;
import com.bonc.ioc.bzf.busisigning.utils.BeanListUtils;
import com.bonc.ioc.bzf.busisigning.utils.DateUtils;
import com.bonc.ioc.bzf.busisigning.utils.UserUtil;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 续签审批表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-09-21
 * @change 2023-09-21 by liupengwei for init
 */
@Slf4j
@Service
public class BbsRenewalApproveInfoServiceImpl extends McpBaseServiceImpl<BbsRenewalApproveInfoEntity> implements IBbsRenewalApproveInfoService {
    /**
     * 审核明细Service
     */
    @Resource
    private IBbsRenewalApproveDetailInfoService renewalApproveDetailInfoService;
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalApproveInfoMapper bbsRenewalApproveInfoMapper;


    /**
     * service 本服务
     */
    @Resource
    private IBbsRenewalApproveInfoService baseService;

    /**
     * 获取登录用户
     */
    @Resource
    private UserUtil userUtil;

    /**
     * 签约服务
     */
    @Resource
    private IBbsSignInfoExtService bbsSignInfoExtService;

    /**
     * 签约服务
     */
    @Resource
    private IBbsRenewalInfoExtService bbsRenewalInfoExtService;

    /**
     * 合同服务
     */
    @Autowired
    private BbctContractService bbctContractService;

    /**
     * 消息中心FeignClient
     */
    @Resource
    private BzfBusinessMessageFeignClient bzfBusinessMessageFeignClient;
    @Resource
    private BzfSystemCommercialFeignClient bzfSystemCommercialFeignClient;
    @Resource
    private BbHousingFeignClient bbHousingFeignClient;
    @Resource
    private BbcCheckInV2FeignClient bbcCheckInV2FeignClient;
    @Resource
    private IBbsSignInfoService iBbsSignInfoService;
    @Resource
    private BbCustomerFeignClient bbCustomerFeignClient;
    @Resource
    private RedisTemplate redisTemplate;
    /**
     * 环境变量
     */
    @Resource
    private Environment env;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsRenewalApproveInfoVo vo) {
        if(vo == null) {
            return null;
        }

        BbsRenewalApproveInfoEntity entity = new BbsRenewalApproveInfoEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setApproveId(null);
        if(!baseService.insert(entity)) {
            log.error("续签审批表新增失败:" + entity.toString());
            throw new McpException("续签审批表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getApproveId(),1)) {
                log.error("续签审批表新增后保存历史失败:" + entity.toString());
                throw new McpException("续签审批表新增后保存历史失败");
            }

            log.debug("续签审批表新增成功:"+entity.getApproveId());
            return entity.getApproveId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsRenewalApproveInfoVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsRenewalApproveInfoEntity> entityList = new ArrayList<>();
        for (BbsRenewalApproveInfoVo item:voList) {
            BbsRenewalApproveInfoEntity entity = new BbsRenewalApproveInfoEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsRenewalApproveInfoEntity item:entityList){
            item.setApproveId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("续签审批表新增失败");
            throw new McpException("续签审批表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsRenewalApproveInfoEntity::getApproveId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("续签审批表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("续签审批表批量新增后保存历史失败");
            }

            log.debug("续签审批表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param approveId 需要删除的审批id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String approveId) {
        if(!StringUtils.isEmpty(approveId)) {
            if(!baseService.saveOperationHisById(approveId,3)) {
                log.error("续签审批表删除后保存历史失败:" + approveId);
                throw new McpException("续签审批表删除后保存历史失败");
            }

            if(!baseService.removeById(approveId)) {
                log.error("续签审批表删除失败");
                throw new McpException("续签审批表删除失败"+approveId);
            }
        } else {
            throw new McpException("续签审批表删除失败审批id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param approveIdList 需要删除的审批id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> approveIdList) {
        if(!CollectionUtils.isEmpty(approveIdList)) {
            int oldSize = approveIdList.size();
            approveIdList = approveIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(approveIdList) || oldSize != approveIdList.size()) {
                throw new McpException("续签审批表批量删除失败 存在主键id为空的记录"+StringUtils.join(approveIdList));
            }

            if(!baseService.saveOperationHisByIds(approveIdList,3)) {
                log.error("续签审批表批量删除后保存历史失败:" + StringUtils.join(approveIdList));
                throw new McpException("续签审批表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(approveIdList)) {
                log.error("续签审批表批量删除失败");
                throw new McpException("续签审批表批量删除失败"+StringUtils.join(approveIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的续签审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsRenewalApproveInfoVo vo) {
        if(vo != null) {
            BbsRenewalApproveInfoEntity entity = new BbsRenewalApproveInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getApproveId())) {
                throw new McpException("续签审批表更新失败传入审批id为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("续签审批表更新失败");
                throw new McpException("续签审批表更新失败"+entity.getApproveId());
            } else {
                if(!baseService.saveOperationHisById(entity.getApproveId(),2)) {
                    log.error("续签审批表更新后保存历史失败:" + entity.getApproveId());
                    throw new McpException("续签审批表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("续签审批表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的续签审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsRenewalApproveInfoVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalApproveInfoEntity> entityList = new ArrayList<>();

            for (BbsRenewalApproveInfoVo item:voList){
                BbsRenewalApproveInfoEntity entity = new BbsRenewalApproveInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getApproveId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("续签审批表批量更新失败 存在审批id为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("续签审批表批量更新失败");
                throw new McpException("续签审批表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getApproveId())).map(BbsRenewalApproveInfoEntity::getApproveId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("续签审批表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("续签审批表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的续签审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsRenewalApproveInfoVo vo) {
        if(vo != null) {
            BbsRenewalApproveInfoEntity entity = new BbsRenewalApproveInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("续签审批表保存失败");
                throw new McpException("续签审批表保存失败"+entity.getApproveId());
            } else {
                if(!baseService.saveOperationHisById(entity.getApproveId(),4)) {
                    log.error("续签审批表保存后保存历史失败:" + entity.getApproveId());
                    throw new McpException("续签审批表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("续签审批表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的续签审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsRenewalApproveInfoVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalApproveInfoEntity> entityList = new ArrayList<>();

            for (BbsRenewalApproveInfoVo item:voList){
                BbsRenewalApproveInfoEntity entity = new BbsRenewalApproveInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("续签审批表批量保存失败");
                throw new McpException("续签审批表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getApproveId())).map(BbsRenewalApproveInfoEntity::getApproveId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("续签审批表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("续签审批表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param approveId 需要查询的审批id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsRenewalApproveInfoVo selectByIdRecord(String approveId) {
        BbsRenewalApproveInfoVo vo = new BbsRenewalApproveInfoVo();

        if(!StringUtils.isEmpty(approveId)) {
            BbsRenewalApproveInfoEntity entity = baseService.selectById(approveId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsRenewalApproveInfoPageResultVo>> selectByPageRecord(BbsRenewalApproveInfoPageVo vo) {
        List<BbsRenewalApproveInfoPageResultVo> result = bbsRenewalApproveInfoMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    @Override
    public AppReply saveApproveResult(BbsRenewalApproveInfoVo vo) {
        String generateBillKey=vo.getParentId()+"_generateBill";
        //审核通过生成账单
        if ("1".equals(vo.getApproveStatus()) && "2".equals(vo.getApproveType())) {
            String generateBillExcuteFlag = (String) redisTemplate.opsForValue().get(generateBillKey);
            if(!"1".equals(generateBillExcuteFlag)){
                RenewalingSaveVo renewalingSaveVo = bbsRenewalInfoExtService.renewalView(vo.getParentId(),null,false);
                if("1".equals(renewalingSaveVo.getDepositReturnWay())){//转入新合同
                    AppReply<Object> generateBill = bbctContractService.generateRenewalBill(vo.getParentId());
                    log.info("审核通过调用生成续租账单接口返回结果==={}", JSON.toJSONString(generateBill));
                    if (!generateBill.getCode().equals(AppReply.SUCCESS_CODE) || ObjectUtils.isEmpty(generateBill.getDate())) {
                        log.info("审核通过生成续租账单接口失败==={}", JSON.toJSONString(generateBill));
                        throw new McpException(generateBill.getMsg());
                    }
                }else{//退回银行卡或退回其它银行卡
                    //调用退租和新签生成帐单接口
                    AppReply<Object> exitFormForCreateByContract= bbctContractService.exitFormForCreateByContract(renewalingSaveVo.getParentContractCode(),renewalingSaveVo);
                    log.info("审核通过调用续租的退租接口返回结果==={}", JSON.toJSONString(exitFormForCreateByContract));
                    if (!exitFormForCreateByContract.getCode().equals(AppReply.SUCCESS_CODE) || ObjectUtils.isEmpty(exitFormForCreateByContract.getDate())) {
                        log.info("审核通过调用续租的退租接口失败==={}", JSON.toJSONString(exitFormForCreateByContract));
                        throw new McpException(exitFormForCreateByContract.getMsg());
                    }
                    AppReply<Object> generateBill = bbctContractService.generateBill(vo.getParentId(),"2");
                    log.info("审核通过调用续租的生成新签账单返回结果==={}", JSON.toJSONString(generateBill));
                    if (!generateBill.getCode().equals(AppReply.SUCCESS_CODE) || ObjectUtils.isEmpty(generateBill.getDate())) {
                        log.info("审核通过调用续租的生成新签账单失败==={}", JSON.toJSONString(generateBill));
                        throw new McpException(generateBill.getMsg());
                    }
                }
                redisTemplate.opsForValue().set(generateBillKey,"1",180, TimeUnit.DAYS);
            }
            //修改合同状态
            AppReply modifyContractStatus = bbctContractService.modifyContractStatus(vo.getParentId());
            log.info("审核通过调用修改合同状态返回结果==={}", JSON.toJSONString(modifyContractStatus));
            if (!modifyContractStatus.getCode().equals(AppReply.SUCCESS_CODE)) {
                log.info("审核通过修改合同状态失败==={}", JSON.toJSONString(modifyContractStatus));
                throw new McpException(modifyContractStatus.getMsg());
            }
            //签约-签约后同步房源状态 循环修改
            List<Map> selectproductNoBySignId = bbsRenewalApproveInfoMapper.selectproductNoBySignId(vo.getParentId());
            List<BbhgOpState> opStateList = new ArrayList<>();
            if (!selectproductNoBySignId.isEmpty()) {
                for (Map map : selectproductNoBySignId) {
                    if (map.get("productNo") != null) {
                        String productNo = map.get("productNo").toString();
                        log.info("审核通过后同步意向登记房源状态调用参数=={}", productNo);
                        BbctSyncStateToCommercialVo syncStateToCommercialVo = new BbctSyncStateToCommercialVo();
                        syncStateToCommercialVo.setProductCode(productNo);
                        syncStateToCommercialVo.setAction("1");
                        AppReply synchronizationStatusAppReply = bzfSystemCommercialFeignClient.synchronizationStatus(syncStateToCommercialVo);
                        log.info("审核通过后同步意向登记房源状态返回结果=={}", JSON.toJSONString(synchronizationStatusAppReply));
                        if (!synchronizationStatusAppReply.getCode().equals(AppReply.SUCCESS_CODE)) {
                            throw new McpException(synchronizationStatusAppReply.getMsg());
                        }
                        BbhgOpState opState = new BbhgOpState();
                        opState.setCode(productNo);
                        opState.setOperatingState("18");
                        opStateList.add(opState);
                    }
                }
            }
            //运营状态变更
//            if (!opStateList.isEmpty()) {
//                BbhgOpStateReq opStateReq = new BbhgOpStateReq();
//                opStateReq.setBbhgOpStates(opStateList);
//                log.info("审核通过后同步房态中心房源运营状态调用参数=={}", JSON.toJSONString(opStateReq));
//                AppReply<Object> syncedStateAppReply = bbHousingFeignClient.syncState(opStateReq);
//                log.info("审核通过后同步房态中心房源运营状态返回结果=={}", JSON.toJSONString(syncedStateAppReply));
//                if (!syncedStateAppReply.getCode().equals(AppReply.SUCCESS_CODE)) {
//                    throw new McpException(syncedStateAppReply.getMsg());
//                }
//            }
            //同步修月租金总额
            List<Map> maps = bbsRenewalApproveInfoMapper.selectContractCodeAndRantBySignId(vo.getParentId());
            if (!maps.isEmpty()) {
                List<BbhgBuildingHouseProductPriceVo> voList = maps.stream().filter(f -> null != f && ObjectUtils.isNotEmpty(f.get("rant")) && ObjectUtils.isNotEmpty(f.get("productNo"))).map(m -> new BbhgBuildingHouseProductPriceVo(Double.parseDouble(ObjectUtils.toString(m.get("rant"))), Objects.toString(m.get("productNo")))).collect(Collectors.toList());
                log.info("审核通过后同步房态中心房源月租金总额调用参数=={}", JSON.toJSONString(voList));
                AppReply<Object> updateMonthRentTotalPriceAppReply = bbHousingFeignClient.updateMonthRentTotalPrice(voList);
                log.info("审核通过后同步房态中心房源月租金总额返回结果=={}", JSON.toJSONString(updateMonthRentTotalPriceAppReply));
                if (!updateMonthRentTotalPriceAppReply.getCode().equals(AppReply.SUCCESS_CODE)) {
                    throw new McpException(updateMonthRentTotalPriceAppReply.getMsg());
                }

            }
            //客户中心机构或客户关系新增
            insertCustomerHouseRel(vo.getParentId());
            //入住中心 签约推送数据
//            custCheckIn(vo.getParentId(),currentTime);
        }
        AppReply msgAppReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
//        //驳回原因
//        String rejectReason = vo.getApproveStatus().equals("2") ? vo.getCommentExplanation() : "";
//        //审签成功 或者 审签成功/失败 发送信息
//        if (("1".equals(vo.getApproveType()) && "1".equals(vo.getApproveStatus())) || ("2".equals(vo.getApproveType()) &&Arrays.asList("1", "2").contains(vo.getApproveStatus()))) {
//            //发送站内信
//            AppReply website = sendMessage("website", vo.getParentId(), rejectReason, vo.getApproveType(), vo.getApproveStatus());
//            //发送短信
//            AppReply sms = sendMessage("SMS", vo.getParentId(), rejectReason, vo.getApproveType(), vo.getApproveStatus());
//            msgAppReply.setCode(website.getCode().equals(AppReply.SUCCESS_CODE) && sms.getCode().equals(AppReply.SUCCESS_CODE) ? AppReply.SUCCESS_CODE : AppReply.ERROR_CODE);
//            msgAppReply.setMsg(website.getMsg() + ";" + sms.getMsg());
//        }
//
//        if(!AppReply.SUCCESS_CODE.equals(msgAppReply.getCode())){
//            throw new McpException(JSON.toJSONString(msgAppReply));
//        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("parent_id", vo.getParentId());
        queryMap.put("del_flag", 1);
        List<BbsRenewalApproveInfoEntity> bbsApproveInfoEntities = baseService.selectByMap(queryMap);
        if (bbsApproveInfoEntities.isEmpty()) {
            return new AppReply(AppReply.ERROR_CODE, "未查询到签约记录", null);
        }
        //先清理实时表记录
        deleteBySignId(vo.getParentId());
        BbsRenewalApproveInfoEntity bbsApproveInfoEntity = bbsApproveInfoEntities.get(0);
        //在保存最新记录
        bbsApproveInfoEntity.setCommentExplanation(vo.getCommentExplanation());
        bbsApproveInfoEntity.setApproveType(vo.getApproveType());
        bbsApproveInfoEntity.setApproveStatus(vo.getApproveStatus());
        bbsApproveInfoEntity.setApproverUserId(userUtil.getUserId());
        bbsApproveInfoEntity.setApproverUserName(userUtil.getUserName(userUtil.getUserId()));
        Date currentTime=new Date(System.currentTimeMillis());
        bbsApproveInfoEntity.setApproveTime(currentTime);
        bbsApproveInfoEntity.setApproveId(IdUtil.fastSimpleUUID());
        bbsApproveInfoEntity.setCreateTime(null);
        bbsApproveInfoEntity.setModifyTime(null);
        baseService.insert(bbsApproveInfoEntity);
        //新增明细记录
        saveApproveDetailInfo(bbsApproveInfoEntity);
//        //审签未通过=未通过
//        if ("2".equals(vo.getApproveStatus()) && "1".equals(vo.getApproveType())) {
//            bbsSignInfoExtService.updateSignStatus(vo.getParentId(), SignStatusEnum.NO_PASS.getCode());
//        }
//        //审签通过=待签约
//        if ("1".equals(vo.getApproveStatus()) && "1".equals(vo.getApproveType())) {
//            bbsSignInfoExtService.updateSignStatus(vo.getParentId(), SignStatusEnum.WAIT_SIGN.getCode());
//        }
//        //审核通过=已签约
//        if ("1".equals(vo.getApproveStatus()) && "2".equals(vo.getApproveType())) {
//            bbsSignInfoExtService.updateSignStatus(vo.getParentId(), SignStatusEnum.SIGNED.getCode());
//        }
//        //审核不通过=待签约
//        if ("2".equals(vo.getApproveStatus()) && "2".equals(vo.getApproveType())) {
//            bbsSignInfoExtService.updateSignStatus(vo.getParentId(), SignStatusEnum.WAIT_SIGN.getCode());
//        }

        //审核通过清空redis生成帐单结果标识
        if ("1".equals(vo.getApproveStatus()) && "2".equals(vo.getApproveType())) {
            redisTemplate.delete(generateBillKey);
        }

        return msgAppReply;
    }
    private void custCheckIn(String signId,Date currentTime) {
        List<Map> BbciCustCheckInVoListMap = bbsRenewalApproveInfoMapper.selectCustCheckInInfo(signId);
        BbciCustCheckInVoListMap.forEach(item ->{
            if (item.get("enterprisePropertyCode").equals("00")) {
                item.put("companyName",null);
                item.put("creditCode", null);
            }
        });
        if (BbciCustCheckInVoListMap.isEmpty()) {
            return;
        }
        List<CheckInSignDataVo> BbciCustCheckInVoList = JSON.parseArray(JSON.toJSONString(BbciCustCheckInVoListMap),CheckInSignDataVo.class);
        BbciCustCheckInVoList.forEach(item -> {
            BbhgHouseInfoPageResultVo baseInfoOutVo = iBbsSignInfoService.selectBbhgProductHouseInfoByHouseProduct(item.getHouseResourceId(), true).getData();
            item.setEnterprisePropertyCode("00".equalsIgnoreCase(item.getEnterprisePropertyCode())?"01":"02");
            item.setHouseResourceCode(baseInfoOutVo.getHouseCode());
            item.setSignTime(currentTime);
            item.setCheckDataSources("02");
            item.setBusinessCode("02");
        });
        log.info("调用入住中心的人-房关系新增接口调用参数======={}", JSON.toJSONString(BbciCustCheckInVoList));
        AppReply appReply = bbcCheckInV2FeignClient.insertRecord(BbciCustCheckInVoList);
        log.info("调用入住中心的人-房关系新增接口返回结果======={}", JSON.toJSONString(appReply));
        if (!appReply.getCode().equals(AppReply.SUCCESS_CODE)) {
            throw new McpException(appReply.getMsg());
        }

    }

    public void insertCustomerHouseRel(String signId) {
        //查询签约用户信息
        List<PersonCustomerHouseDataVo> personCustomerHouseDataVoList = bbsRenewalApproveInfoMapper.selectPersonCustomerHouseInfo(signId);
        //查询houseCode
        //房屋编码（整个房子编码）
        //houseResourcesCode
        //房源编码（资产）
        for (PersonCustomerHouseDataVo p : personCustomerHouseDataVoList) {
            BbhgHouseInfoPageResultVo baseInfoOutVo = iBbsSignInfoService.selectBbhgProductHouseInfoByHouseProduct(p.getProductCode(), false).getData();
            if (null == baseInfoOutVo) {
                return;
            }
            p.setHouseCode(Objects.toString(baseInfoOutVo.getHouseCode(), p.getProductCode()));
            p.setHouseResourcesCode(p.getProductCode());
            p.setSigningDate(DateUtils.getDateTime());
        }
        Map<String, List<PersonCustomerHouseDataVo>> collect = personCustomerHouseDataVoList.stream().collect(Collectors.groupingBy(pchdv -> pchdv.getCustomerType()));
        //个人-房关系新增接口
        List<PersonCustomerHouseDataVo> personCustomerHouseDataVos = collect.get("00");
        if (null != personCustomerHouseDataVos) {
            List<PersonCustomerHouseRelInsertVo> personCustomerHouseRelInsertVos = BeanListUtils.copyListProperties(personCustomerHouseDataVos, PersonCustomerHouseRelInsertVo::new);
            log.info("调用客户中心的个人-房关系新增接口调用参数======={}", JSON.toJSONString(personCustomerHouseRelInsertVos));
            AppReply<List<PersonCustomerHouseRelInsertResultVo>> personCustomerHouseRelInsertVoAppReply = bbCustomerFeignClient.insertPersonCustomerHouseRel(personCustomerHouseRelInsertVos);
            log.info("调用客户中心的个人-房关系新增接口返回结果======={}", JSON.toJSONString(personCustomerHouseRelInsertVoAppReply));
            if ("1".equals(personCustomerHouseRelInsertVoAppReply.getCode())) {
                if (null == personCustomerHouseRelInsertVoAppReply.getData()) {
                    throw new McpException(personCustomerHouseRelInsertVoAppReply.getMsg());
                }
                List<PersonCustomerHouseRelInsertResultVo> relInsertResultVos = personCustomerHouseRelInsertVoAppReply.getData();
                for (PersonCustomerHouseRelInsertResultVo personCustomerHouseRelInsertResultVo : relInsertResultVos) {
                    if (!"1".equals(personCustomerHouseRelInsertResultVo.getOperationResult())) {
                        throw new McpException(personCustomerHouseRelInsertResultVo.getErrorCause());
                    }
                }
            } else {
                throw new McpException(personCustomerHouseRelInsertVoAppReply.getMsg());
            }
        }
        //机构-房关系新增接口
        personCustomerHouseDataVos = collect.get("01");
        if (null != personCustomerHouseDataVos) {
            List<OrgCustomerHouseRelInsertVo> orgCustomerHouseRelInsertVos = BeanListUtils.copyListProperties(personCustomerHouseDataVos, OrgCustomerHouseRelInsertVo::new);
            log.info("调用客户中心的企业-房关系新增接口调用参数======={}", JSON.toJSONString(orgCustomerHouseRelInsertVos));
            AppReply<List<OrgCustomerHouseRelInsertResultVo>> orgCustomerHouseRelInsertResultVoAppReply = bbCustomerFeignClient.insertOrgCustomerHouseRel(orgCustomerHouseRelInsertVos);
            log.info("调用客户中心的企业-房关系新增接口返回结果======={}", JSON.toJSONString(orgCustomerHouseRelInsertResultVoAppReply));
            if ("1".equals(orgCustomerHouseRelInsertResultVoAppReply.getCode())) {
                if (null == orgCustomerHouseRelInsertResultVoAppReply.getData()) {
                    throw new McpException(orgCustomerHouseRelInsertResultVoAppReply.getMsg());
                }
                List<OrgCustomerHouseRelInsertResultVo> relInsertResultVos = orgCustomerHouseRelInsertResultVoAppReply.getData();
                for (OrgCustomerHouseRelInsertResultVo orgCustomerHouseRelInsertResultVo : relInsertResultVos) {
                    if (!"1".equals(orgCustomerHouseRelInsertResultVo.getOperationResult())) {
                        throw new McpException(orgCustomerHouseRelInsertResultVo.getErrorCause());
                    }
                }
            } else {
                throw new McpException(orgCustomerHouseRelInsertResultVoAppReply.getMsg());
            }
        }
    }
    /**
     * @param type         站内信:website 短信:SMS
     * @param signId       签约id
     * @param rejectReason 驳回原因
     */
    private AppReply sendMessage(String type, String signId, String rejectReason, String approveType, String approveStatus) {
        //debug调不通 所以不发送
        if (Objects.equals(env.getProperty("spring.profiles.active"), "debug")) {
            return new AppReply<>(AppReply.SUCCESS_CODE, "debug", null);
        }
        //查询 客户名称 房源地址
        List<Map> list = bbsRenewalApproveInfoMapper.selectMsgInfo(signId);
        if (list.isEmpty()) {
            log.info("未查询到客户名称或房源地址");
            return new AppReply<>(AppReply.ERROR_CODE, "未查询到客户名称或房源地址", null);
        }
        Map map = list.get(0);
        if ("SMS".equals(type) && Objects.isNull(map.get("SMSTemplateId"))) {
            return new AppReply<>(AppReply.SUCCESS_CODE, "无需发送", null);
        }
        if ("website".equals(type) && Objects.isNull(map.get("websiteMsgTemplateId"))) {
            return new AppReply<>(AppReply.SUCCESS_CODE, "无需发送", null);
        }
        String housingAddress = list.stream().map(m -> String.valueOf(m.get("housingAddress"))).distinct().collect(Collectors.joining("、"));
        String customerName = Objects.toString(map.get("customerName"), "未查询到客户名称");
        if ("SMS".equals(type) && ObjectUtils.isEmpty(map.get("customerTel"))) {
            log.error("发送短信失败====>>>>>未查询到客户手机号码{}", signId);
            return new AppReply<>(AppReply.ERROR_CODE, "发送短信失败,未查询到客户手机号码", null);
        }
        String phone = Objects.toString(map.get("customerTel"), "");
        String websiteMsgTemplateId = Objects.toString(map.get("websiteMsgTemplateId"), "未查询到站内信模板id");
        String SMSTemplateId = Objects.toString(map.get("SMSTemplateId"), "未查询到审签成功短信模板id");
        String approveSuccessSMSTemplateId = Objects.toString(map.get("approveSuccessSMSTemplateId"), "未查询到审核成功短信模板id");
        String approveFailedSMSTemplateId = Objects.toString(map.get("approveFailedSMSTemplateId"), "未查询到审核失败短信模板id");
        if ("2".equals(approveType)) {//审核
            if ("1".equals(approveStatus)) {
                SMSTemplateId = approveSuccessSMSTemplateId;//审核成功的模板id
            } else {
                SMSTemplateId = approveFailedSMSTemplateId;//审核失败的模板id
            }
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("houseAddress", housingAddress);
        paramMap.put("customerName", customerName);
        paramMap.put("toExamineResult", rejectReason);
        List<WebsiteMsgContentVo> websiteMsgContentVo = Arrays.asList(new WebsiteMsgContentVo(paramMap, phone, signId));
        //发送信息
        AppReply<JSONObject> appReply = null;
        StringBuilder message = new StringBuilder();
        if ("website".equals(type)) {
            message.append("发送站内信");
            //站内信
            paramMap.remove("toExamineResult");
            WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(websiteMsgTemplateId, "系统站内信通知", websiteMsgContentVo);
            log.info("发送站内信==={}", JSON.toJSONString(websiteMsgVo));
            appReply = bzfBusinessMessageFeignClient.sendWebsiteMsg(websiteMsgVo);
        }
        if ("SMS".equals(type) && StringUtils.isNotEmpty(phone)) {
            message.append("发送手机短信");
            //手机短信
            if ("1".equals(approveStatus)) {
                //通过
                paramMap.remove("toExamineResult");
                WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(SMSTemplateId, "系统站内信审核成功通知", websiteMsgContentVo);
                log.info("发送短信==={}", JSON.toJSONString(websiteMsgVo));
                appReply = bzfBusinessMessageFeignClient.sendSMS(websiteMsgVo);
            } else {
                //未通过
                WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(SMSTemplateId, "系统站内信审核失败通知", websiteMsgContentVo);
                log.info("发送短信==={}", JSON.toJSONString(websiteMsgVo));
                appReply = bzfBusinessMessageFeignClient.sendSMS(websiteMsgVo);
            }
        }

        assert appReply != null;
        log.info("发送站内信/短信成功结果==={}", JSON.toJSONString(appReply));
        if (appReply.getCode().equals("1")) {
            List<Map> data = (List<Map>) appReply.getData().get("detailVos");
            if (null != data && null != data.get(0)) {
                Map detail = data.get(0);
                if (detail.get("code").equals("00")) {
                    log.info("发送站内信/短信成功==={}", JSON.toJSONString(appReply));
                    message.append("成功");
                    return new AppReply<>(AppReply.SUCCESS_CODE, message.toString(), null);
                } else {
                    message.append("失败");
                    log.info("发送站内信/短信失败==={}", JSON.toJSONString(appReply));
                }
            } else {
                message.append("失败");
                log.info("发送站内信/短信失败==={}", JSON.toJSONString(appReply));

            }
        } else {
            message.append("失败");
        }
        return new AppReply<>(AppReply.ERROR_CODE, message.toString(), null);
    }

    @Override
    public List<BbsRenewalApproveInfoVo> selectApproveRecord(String signId, String approveType) {
        List<String> approveTypes = new ArrayList<>();
        if ("1".equals(approveType)) {
            approveTypes.add("1");
        }
        if ("2".equals(approveType)) {
            approveTypes.add("1");
            approveTypes.add("2");
        }
        //查询审核明细记录
        QueryWrapper<BbsRenewalApproveDetailInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderBy(true, false, "create_time","approve_status");
        queryWrapper.eq("parent_id", signId);
        queryWrapper.eq("del_flag", DelFlagEnum.UNDELETED.getCode());
        queryWrapper.eq("parent_id", signId);
        queryWrapper.in("approve_type", approveTypes);
        List<BbsRenewalApproveDetailInfoEntity> bbsApproveDetailInfoEntities = renewalApproveDetailInfoService.select(queryWrapper);
        List<BbsRenewalApproveInfoVo> bbsApproveInfoVos = BeanListUtils.copyListProperties(bbsApproveDetailInfoEntities, BbsRenewalApproveInfoVo::new);
        bbsApproveInfoVos.forEach(t -> {
            if ("1".equals(t.getApproveType()) && "3".equals(t.getApproveStatus())) {
                t.setApprovDesc("合同审签提交");
            } else if ("1".equals(t.getApproveType()) && ("1".equals(t.getApproveStatus()) || "2".equals(t.getApproveStatus()))) {
                t.setApprovDesc("合同审签审核");
            } else if ("2".equals(t.getApproveType()) && "3".equals(t.getApproveStatus())) {
                t.setApprovDesc("租户签约提交");
            } else if ("2".equals(t.getApproveType()) && ("1".equals(t.getApproveStatus()) || "2".equals(t.getApproveStatus()))) {
                t.setApprovDesc("合同审核");
            } else if ("4".equals(t.getApproveStatus())) {
                t.setApprovDesc("撤回");
            }
        });
        return bbsApproveInfoVos;
    }

    @Override
    public List<AddressCascadeQueryResultVo> addressCascadeQuery(BbsApproveAddressCascadeQueryVo queryVo) {
        return bbsRenewalApproveInfoMapper.addressCascadeQuery(queryVo);
    }

    /**
     * 提交审签记录
     * @param signId
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void addSigningReviewInfo(String signId) {
        //先清理实时表记录
        deleteBySignId(signId);
        //再保存最新记录
        BbsRenewalApproveInfoEntity entity = new BbsRenewalApproveInfoEntity();
        entity.setApproveId(IdUtil.fastSimpleUUID());
        entity.setParentId(signId);
        entity.setApproveType("1");
        entity.setApproveStatus("3");
        entity.setSubmitUserId(userUtil.getUserId());
        entity.setSubmitUserName(userUtil.getUserName(userUtil.getUserId()));
        entity.setSubmitTime(new Date(System.currentTimeMillis()));
        entity.setDelFlag(1);
        baseService.insert(entity);
        //新增明细记录
        saveApproveDetailInfo(entity);
    }


    /**
     * 撤回签约审核/合同审核
     *
     * @param signId
     */
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public void withdrawApprove(String signId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("parent_id", signId);
        queryMap.put("del_flag", 1);
        List<BbsRenewalApproveInfoEntity> bbsApproveInfoEntities = bbsRenewalApproveInfoMapper.selectByMap(queryMap);
        if (bbsApproveInfoEntities.isEmpty()) {
            return;
        }
        BbsRenewalApproveInfoEntity entity = bbsApproveInfoEntities.get(0);
        //先清理实时表记录
        deleteBySignId(signId);
        List<BbsRenewalApproveDetailInfoEntity> bbsApproveDetailInfoEntities=renewalApproveDetailInfoService.selectByMap(queryMap);
        if(bbsApproveDetailInfoEntities!=null&&!bbsApproveDetailInfoEntities.isEmpty()){
            for(BbsRenewalApproveDetailInfoEntity tempBbsRenewalApproveDetailInfoEntity:bbsApproveDetailInfoEntities){
                tempBbsRenewalApproveDetailInfoEntity.setDelFlag(0);
            }
            renewalApproveDetailInfoService.updateBatchById(bbsApproveDetailInfoEntities);
        }
        //在保存最新记录
        entity.setApproveId(IdUtil.fastSimpleUUID());
        entity.setApproveStatus("4");
        entity.setSubmitUserId(userUtil.getUserId());
        entity.setSubmitUserName(userUtil.getUserName(userUtil.getUserId()));
        entity.setSubmitTime(new Date(System.currentTimeMillis()));
        entity.setDelFlag(0);
        baseService.insert(entity);
        //新增明细记录
        saveApproveDetailInfo(entity);
    }

    /**
     * 提交审核记录
     * @param signId
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void addContractReviewInfo(String signId) {
        //先清理实时表记录
        deleteBySignId(signId);
        //再保存最新记录
        BbsRenewalApproveInfoEntity entity = new BbsRenewalApproveInfoEntity();
        entity.setApproveId(IdUtil.fastSimpleUUID());
        entity.setParentId(signId);
        entity.setApproveType("2");
        entity.setApproveStatus("3");
        entity.setSubmitUserId(userUtil.getUserId());
        entity.setSubmitUserName(userUtil.getUserName(userUtil.getUserId()));
        entity.setSubmitTime(new Date(System.currentTimeMillis()));
        entity.setDelFlag(1);
        baseService.insert(entity);
        //新增明细记录
        saveApproveDetailInfo(entity);

    }

    private void deleteBySignId(String signId) {
        Map<String, Object> delMap = new HashMap<>();
        delMap.put("parent_id", signId);
        baseService.removeByMap(delMap);
    }

    private void saveApproveDetailInfo(BbsRenewalApproveInfoEntity entity) {
        BbsRenewalApproveDetailInfoEntity approveDetailInfoEntity = new BbsRenewalApproveDetailInfoEntity();
        BeanUtils.copyProperties(entity, approveDetailInfoEntity);
        approveDetailInfoEntity.setApproveDetailId(IdUtil.fastSimpleUUID());
        renewalApproveDetailInfoService.insert(approveDetailInfoEntity);
    }
}
