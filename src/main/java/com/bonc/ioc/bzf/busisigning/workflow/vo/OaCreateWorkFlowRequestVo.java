package com.bonc.ioc.bzf.busisigning.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: caijun
 * @date: 2024/05/23
 * @change: 2024/05/23 by <EMAIL> for init
 */
@Data
public class OaCreateWorkFlowRequestVo implements Serializable {

    /**
     * 调用接口凭证
     */
    @ApiModelProperty(value = "调用接口凭证",required = true)
    private String accessToken;

    /**
     * 用户ID
     */
    @ApiModelProperty(value ="用户ID",required = true)
    private Long userid;

    /**
     * 创建的流程实例标题（不输入按照默认标题组成生成标题）
     */
    @ApiModelProperty("创建的流程实例标题（不输入按照默认标题组成生成标题）")
    private String requestname;

    /**
     * 工作流ID
     */
    @ApiModelProperty(value ="工作流ID",required = true)
    private String workflowId;

    /**
     * 签字意见
     */
    @ApiModelProperty("签字意见")
    private String remark;

    /**
     * 是否流转到下一节点：0 否，1 是。默认值：0
     */
    @ApiModelProperty("是否流转到下一节点：0 否，1 是。默认值：0")
    private Integer isnextflow;

    /**
     * 密级（系统开启密级才使用这个参数），值大于0 才生效。且开启了密级，则获取创建人能获取的最大密级。
     */
    @ApiModelProperty("密级（系统开启密级才使用这个参数），值大于0 才生效。且开启了密级，则获取创建人能获取的最大密级。")
    private Integer requestSecLevel;

    /**
     * 保密期限（传递密级时才传递该参数）
     */
    @ApiModelProperty("保密期限（传递密级时才传递该参数）")
    private String requestSecValidity;

    /**
     * isnextflow=1时，true：要校验表单必填。false：不校验表单必填。默认值：false
     */
    @ApiModelProperty("isnextflow=1时，true：要校验表单必填。false：不校验表单必填。默认值：false")
    private Boolean isVerifyFormRequired;

    /**
     * true：执行创建节点前附加操作，false：不执行创建节点前附加操作。默认值：false
     */
    @ApiModelProperty("true：执行创建节点前附加操作，false：不执行创建节点前附加操作。默认值：false")
    private Boolean isExecuteCreateAddAction;

    /**
     * 是否验证用户创建流程权限；0：不验证。1：验证。默认值：0；
     */
    @ApiModelProperty("是否验证用户创建流程权限；0：不验证。1：验证。默认值：0；")
    private Integer isVerifyPer;

    /**
     * 可以自定义流程ID
     */
    @ApiModelProperty("可以自定义流程ID")
    private Long requestId;

    /**
     * 创建并流转失败的流程是否要删除掉；true：删除。false：不删除。默认值：false
     */
    @ApiModelProperty("创建并流转失败的流程是否要删除掉；true：删除。false：不删除。默认值：false")
    private Boolean delReqFlowFaild;

    /**
     * 数据来源
     */
    @ApiModelProperty("数据来源")
    private String businessData;

    /**
     * 关联模块数据ID（默认值0）
     */
    @ApiModelProperty("关联模块数据ID（默认值0）")
    private Long moduleDataId;

    /**
     * 表单数据
     */
    @ApiModelProperty("表单数据")
    private OaFormDataRequestVo formData;

    /**
     * 紧急程度 0：正常； 1：重要； 2：紧急。默认值：0
     */
    @ApiModelProperty("紧急程度 0：正常； 1：重要； 2：紧急。默认值：0")
    private Integer requestlevel;

    /**
     * 比如isnextflow(是否进行流转1：是，其他否),requestSecLevel（密级，系统开启密级才使用这个参数）
     */
    @ApiModelProperty("比如isnextflow(是否进行流转1：是，其他否),requestSecLevel（密级，系统开启密级才使用这个参数）")
    private String otherParams;

    private Long dataId;

}
