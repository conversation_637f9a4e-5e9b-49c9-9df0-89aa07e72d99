package com.bonc.ioc.bzf.busisigning.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OaDeleteRequestVo implements Serializable {


    @ApiModelProperty("用户ID")
    private Long userid;

    @ApiModelProperty("审批数据ID")
    private String requestId;

    @ApiModelProperty("如果以监控权限删除流程则需要传该参数在body中传入，否则以流程权限判断{“ismonitor”:”1”}")
    private String ismonitor;


}
