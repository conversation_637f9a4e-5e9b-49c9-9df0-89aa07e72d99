package com.bonc.ioc.bzf.busisigning.controller;

import com.alibaba.fastjson.JSON;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalApproveInfoService;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.utils.common.convert.UserPoint;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 续签审批表 前端控制器
 *
 * <AUTHOR>
 * @date 2023-09-21
 * @change 2023-09-21 by liupengwei for init
 */
@RestController
@RequestMapping("/contractRenewalApprove")
@Api(tags = "续签审签审核")
@Validated
@Slf4j
public class BbsRenewalApproveInfoController extends McpBaseController {
    @Resource
    private IBbsRenewalApproveInfoService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liupengwei")
    @ApiOperation(value = "新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "续签审批表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsRenewalApproveInfoVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要新增的记录 列表
     * @return  com.bonc.ioc.common.util.AppReply<List<String>> 返回新增后的主键 列表
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @PostMapping(value = "/batch/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "liupengwei")
    @ApiOperation(value = "批量新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键集合")})
    public AppReply<List<String>> insertBatchRecord(@ApiParam(value = "续签审批表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbsRenewalApproveInfoVo> voList){
        AppReply<List<String>> appReply = new AppReply<List<String>>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertBatchRecord(voList));
        return appReply;
     }

    /**
     * removeByIdRecord 根据主键删除
     * @param approveId 需要删除的审批id
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @PostMapping(value = "/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "liupengwei")
    @ApiOperation(value = "根据主键删除", notes = "根据主键删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdRecord(@ApiParam(value = "需要删除的审批id" ,required = false) @RequestBody String approveId){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdRecord(approveId);
        return appReply;
     }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param approveIdList 需要删除的审批id集合
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @PostMapping(value = "/batch/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "liupengwei")
    @ApiOperation(value = "根据主键删除-批量", notes = "根据主键批量删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdsRecord(@ApiParam(value = "需要删除的审批id" ,required = false) @RequestBody List<String> approveIdList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdsRecord(approveIdList);
        return appReply;
     }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的续签审批表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @PostMapping(value = "/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "liupengwei")
    @ApiOperation(value = "根据主键更新", notes = "根据主键更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateByIdRecord(@ApiParam(value = "需要更新的续签审批表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbsRenewalApproveInfoVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateByIdRecord(vo);
        return appReply;
     }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的续签审批表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @PostMapping(value = "/batch/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "liupengwei")
    @ApiOperation(value = "根据主键更新-批量", notes = "根据主键批量更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateBatchByIdRecord(@ApiParam(value = "需要更新的续签审批表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) List<BbsRenewalApproveInfoVo> voList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateBatchByIdRecord(voList);
        return appReply;
     }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的续签审批表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @PostMapping(value = "/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "liupengwei")
    @ApiOperation(value = "根据主键更新或新增", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveByIdRecord(@ApiParam(value = "需要更新或新增的续签审批表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsRenewalApproveInfoVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.saveByIdRecord(vo);
        return appReply;
     }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的续签审批表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @PostMapping(value = "/batch/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "liupengwei")
    @ApiOperation(value = "根据主键更新或新增-批量", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveBatchByIdRecord(@ApiParam(value = "需要更新或新增的续签审批表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbsRenewalApproveInfoVo> voList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.saveBatchByIdRecord(voList);
        return appReply;
     }

    /**
     * selectByIdRecord 根据主键查询
     * @param approveId 需要查询的审批id
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "liupengwei")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsRenewalApproveInfoVo> selectByIdRecord(@ApiParam(value = "需要查询的审批id" ,required = false) @RequestParam(required = false) String approveId){
        AppReply<BbsRenewalApproveInfoVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByIdRecord(approveId));
        return appReply;
     }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by liupengwei for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "liupengwei")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsRenewalApproveInfoPageResultVo>>> selectByPageRecord(BbsRenewalApproveInfoPageVo vo){
        AppReply<PageResult<List<BbsRenewalApproveInfoPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
     }

    /**
     * 续签合同审签-待办分页查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by 刘鹏伟 for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectSigningReviewByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "刘鹏伟")
    @ApiOperation(value = "查询合同审签(待办)分页列表", notes = "查询合同审签(待办)分页列表")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsRenewalApproveInfoPageResultVo>>> selectSigningReviewByPage(BbsRenewalApproveInfoPageVo vo) {
        vo.setApproveType("1");//合同审签
        vo.setApproveStatus("3");//待审核
        vo.setPageStatus("1");
        AppReply<PageResult<List<BbsRenewalApproveInfoPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
    }

    /**
     * 续签合同审签-已办分页查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by 刘鹏伟 for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectCompleteSigningReviewByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "刘鹏伟")
    @ApiOperation(value = "查询合同审签(已办)分页列表", notes = "查询合同审签(已办)分页列表")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsRenewalApproveInfoPageResultVo>>> selectCompleteSigningReviewByPage(BbsRenewalApproveInfoPageVo vo) {
        vo.setApproveType("1");//合同审签
        vo.setPageStatus("2");//已办
        AppReply<PageResult<List<BbsRenewalApproveInfoPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
    }

    /**
     * 续签合同审核-待办分页查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by 刘鹏伟 for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectContractReviewByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "刘鹏伟")
    @ApiOperation(value = "查询合同审核(待办)分页列表", notes = "查询合同审核(待办)分页列表")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsRenewalApproveInfoPageResultVo>>> selectContractReviewByPage(BbsRenewalApproveInfoPageVo vo) {
        vo.setApproveType("2");//合同审核
        vo.setApproveStatus("3");//待审核
        vo.setPageStatus("1");//
        AppReply<PageResult<List<BbsRenewalApproveInfoPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
    }

    /**
     * 续签合同审核-已办分页查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by 刘鹏伟 for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectCompleteContractReviewByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "刘鹏伟")
    @ApiOperation(value = "查询合同审核(已办)分页列表", notes = "查询合同审核(已办)分页列表")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsRenewalApproveInfoPageResultVo>>> selectCompleteContractReviewByPage(BbsRenewalApproveInfoPageVo vo) {
        vo.setApproveType("2");//合同审核
        vo.setPageStatus("2");//已办
        AppReply<PageResult<List<BbsRenewalApproveInfoPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
    }

    /**
     * 续签-保存合同审签/合同审核
     *
     * @param vo 需要保存的数据
     * @return com.bonc.ioc.common.util.AppReply 保存结果
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by 刘鹏伟 for init
     * @since 1.0.0
     */
    @PostMapping(value = "/saveApproveResult", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "刘鹏伟")
    @ApiOperation(value = "保存合同审签/合同审核", notes = "保存合同审签/合同审核")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveApproveResult(@ApiParam(value = "需要保存的审批结果和信息", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsRenewalApproveInfoVo vo) {
        log.info("调用保存合同审签/合同审核接口参数==={}", JSON.toJSONString(vo));
        return baseService.saveApproveResult(vo);
    }

    /**
     * 续签-查询审核记录
     *
     * @param signId 签约id
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by 刘鹏伟 for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectApproveRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "刘鹏伟")
    @ApiOperation(value = "查询审批记录", notes = "查询审批记录")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @UserPoint
    public AppReply<List<BbsRenewalApproveInfoVo>> selectApproveRecord(@ApiParam(value = "签约id", required = true) @RequestParam() String signId,
                                                                @ApiParam(value = "审批类型(1.合同审签 2.合同审核)", required = true) @RequestParam() String approveType) {
        AppReply<List<BbsRenewalApproveInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectApproveRecord(signId, approveType));
        return appReply;
    }

    /**
     * 续签-商铺地址级联查询
     */
    @GetMapping(value = "/addressCascadeQuery", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "刘鹏伟")
    @ApiOperation(value = "商铺地址级联查询", notes = "商铺地址级联查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<AddressCascadeQueryResultVo>> addressCascadeQuery(BbsApproveAddressCascadeQueryVo queryVo) {
        AppReply<List<AddressCascadeQueryResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        List<AddressCascadeQueryResultVo> data = baseService.addressCascadeQuery(queryVo);
        appReply.setData(data.isEmpty() || data.get(0) == null ? Collections.emptyList() : data);
        return appReply;
    }

    /**
     * 续签-提交审签
     *
     * @param signId
     * @return
     */
    @PostMapping(value = "/addSigningReviewInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "刘鹏伟")
    @ApiOperation(value = "提交审签", notes = "提交审签")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply addSigningReviewInfo(@ApiParam(value = "签约id", required = true) @RequestParam() String signId) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.addSigningReviewInfo(signId);
        return appReply;
    }
    /**
     * 续签-提交审核
     *
     * @param signId
     * @return
     */
    @PostMapping(value = "/addContractReviewInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "刘鹏伟")
    @ApiOperation(value = "提交审核", notes = "提交审核")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply addContractReviewInfo(@ApiParam(value = "签约id", required = true) @RequestParam() String signId) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.addContractReviewInfo(signId);
        return appReply;
    }
}

