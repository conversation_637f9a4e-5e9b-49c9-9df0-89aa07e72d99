package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.dao.BbsSignInfoMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsSignInfoEntity;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbHousingFeignClient;
import com.bonc.ioc.bzf.busisigning.service.IBbsSignInfoService;
import com.bonc.ioc.bzf.busisigning.vo.BbhgHouseInfoPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoPageVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 签约表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by liwenqiang for init
 */
@Slf4j
@Service
public class BbsSignInfoServiceImpl extends McpBaseServiceImpl<BbsSignInfoEntity> implements IBbsSignInfoService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsSignInfoMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsSignInfoService baseService;

    @Autowired
    private BbHousingFeignClient bbHousingFeignClient;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsSignInfoVo vo) {
        if(vo == null) {
            return null;
        }

        BbsSignInfoEntity entity = new BbsSignInfoEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setSignId(null);
        if(!baseService.insert(entity)) {
            log.error("签约表新增失败:" + entity.toString());
            throw new McpException("签约表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getSignId(),1)) {
                log.error("签约表新增后保存历史失败:" + entity.toString());
                throw new McpException("签约表新增后保存历史失败");
            }

            log.debug("签约表新增成功:"+entity.getSignId());
            return entity.getSignId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsSignInfoVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsSignInfoEntity> entityList = new ArrayList<>();
        for (BbsSignInfoVo item:voList) {
            BbsSignInfoEntity entity = new BbsSignInfoEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsSignInfoEntity item:entityList){
            item.setSignId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("签约表新增失败");
            throw new McpException("签约表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsSignInfoEntity::getSignId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("签约表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("签约表批量新增后保存历史失败");
            }

            log.debug("签约表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param signId 需要删除的签约id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String signId) {
        if(!StringUtils.isEmpty(signId)) {
            if(!baseService.saveOperationHisById(signId,3)) {
                log.error("签约表删除后保存历史失败:" + signId);
                throw new McpException("签约表删除后保存历史失败");
            }

            if(!baseService.removeById(signId)) {
                log.error("签约表删除失败");
                throw new McpException("签约表删除失败"+signId);
            }
        } else {
            throw new McpException("签约表删除失败签约id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param signIdList 需要删除的签约id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> signIdList) {
        if(!CollectionUtils.isEmpty(signIdList)) {
            int oldSize = signIdList.size();
            signIdList = signIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(signIdList) || oldSize != signIdList.size()) {
                throw new McpException("签约表批量删除失败 存在主键id为空的记录"+StringUtils.join(signIdList));
            }

            if(!baseService.saveOperationHisByIds(signIdList,3)) {
                log.error("签约表批量删除后保存历史失败:" + StringUtils.join(signIdList));
                throw new McpException("签约表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(signIdList)) {
                log.error("签约表批量删除失败");
                throw new McpException("签约表批量删除失败"+StringUtils.join(signIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的签约表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsSignInfoVo vo) {
        if(vo != null) {
            BbsSignInfoEntity entity = new BbsSignInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getSignId())) {
                throw new McpException("签约表更新失败传入签约id为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("签约表更新失败");
                throw new McpException("签约表更新失败"+entity.getSignId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSignId(),2)) {
                    log.error("签约表更新后保存历史失败:" + entity.getSignId());
                    throw new McpException("签约表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("签约表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的签约表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsSignInfoVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsSignInfoEntity> entityList = new ArrayList<>();

            for (BbsSignInfoVo item:voList){
                BbsSignInfoEntity entity = new BbsSignInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getSignId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("签约表批量更新失败 存在签约id为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("签约表批量更新失败");
                throw new McpException("签约表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSignId())).map(BbsSignInfoEntity::getSignId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("签约表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("签约表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的签约表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsSignInfoVo vo) {
        if(vo != null) {
            BbsSignInfoEntity entity = new BbsSignInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("签约表保存失败");
                throw new McpException("签约表保存失败"+entity.getSignId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSignId(),4)) {
                    log.error("签约表保存后保存历史失败:" + entity.getSignId());
                    throw new McpException("签约表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("签约表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的签约表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsSignInfoVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsSignInfoEntity> entityList = new ArrayList<>();

            for (BbsSignInfoVo item:voList){
                BbsSignInfoEntity entity = new BbsSignInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("签约表批量保存失败");
                throw new McpException("签约表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSignId())).map(BbsSignInfoEntity::getSignId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("签约表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("签约表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param signId 需要查询的签约id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsSignInfoVo selectByIdRecord(String signId) {
        BbsSignInfoVo vo = new BbsSignInfoVo();

        if(!StringUtils.isEmpty(signId)) {
            BbsSignInfoEntity entity = baseService.selectById(signId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsSignInfoPageResultVo>> selectByPageRecord(BbsSignInfoPageVo vo) {
        List<BbsSignInfoPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    @Override
    public AppReply<BbhgHouseInfoPageResultVo> selectBbhgProductHouseInfoByHouseProduct(String productCode,boolean isFlag){
        MultiValueMap<String, Object> multiValue = new LinkedMultiValueMap<>();
        multiValue.set("pageNumber","1");
        multiValue.set("pageSize","10");
        multiValue.set("isFlag",isFlag);
        multiValue.set("productCode",productCode);
        List<BbhgHouseInfoPageResultVo> rows = bbHousingFeignClient.selectBbhgHouseInfo(multiValue).getData().getRows();
        if(rows!=null && rows.size() > 0){
            return AppReply.success(rows.get(0));
        }
        return AppReply.error("未查询到房源信息:"+productCode);
    }
}
