package com.bonc.ioc.bzf.busisigning.factory.change;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.enums.ContractChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.CustomerTypeEnum;
import com.bonc.ioc.bzf.busisigning.factory.sign.AbstractContractSignFactory;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.file.vo.NewFIleResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbctPreviewInfoParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsChangePreviewBillResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsContractChangeTemplateVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultProductVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo;
import com.bonc.ioc.bzf.busisigning.vo.SigningSaveVo;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 乙方变更 工厂类
 *
 * <AUTHOR>
 * @since 2024/10/22
 */
@Slf4j
public class LesseeChangeFactory extends AbstractContractChangeFactory {

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param contractChangeVo             合同变更信息 vo实体
     */
    public LesseeChangeFactory(FeignServiceConfiguration feignServiceConfiguration,
                               BusinessServiceConfiguration businessServiceConfiguration,
                               BbsiContractChangeVo contractChangeVo) {
        super(feignServiceConfiguration,
                businessServiceConfiguration,
                contractChangeVo);
    }

    /**
     * 新增预览账单信息
     */
    @Override
    public void insertPreviewBills() {
        return;
    }

    /**
     * 试算账单(实时)
     *
     * @return 试算结果 vo实体
     */
    @Override
    public BbsChangePreviewBillResultVo getChargePreviewBills() {
        return null;
    }

    /**
     * changeTrial试算账单(免租期变更和缴费周期变更)
     * 主承租人变更不需要changeTrial试算
     *
     * @return 试算结果 vo实体
     */
    @Override
    public BbsChangePreviewBillResultVo getChangeTrialPreviewBills() {
        return null;
    }

    /**
     * 预览或下载
     *
     * @return 合同文件信息
     */
    @Override
    public FIleResultVo previewAndDownload() {
        if (StringUtils.isBlank(contractChangeVo.getAgreementFileId())) {
            throw new McpException("协议文件不存在");
        }
        NewFIleResultVo fileInfoVo = getFileInfoById(contractChangeVo.getAgreementFileId()).get(0);
        FIleResultVo resultVo = new FIleResultVo();
        resultVo.setFileName(fileInfoVo.getFileName());
        resultVo.setId(Integer.parseInt(fileInfoVo.getFileId()));
        resultVo.setFileUrl(fileInfoVo.getPreviewAddress());
        resultVo.setCreateTime(fileInfoVo.getFileCreateTime());
        resultVo.setState(fileInfoVo.getIsDeleted());
        return resultVo;
    }

    /**
     * 赋值子变更预览信息
     *
     * @param previewInfoParamsVo 预览信息参数 vo实体
     */
    @Override
    public void setSubChangePreviewInfo(BbctPreviewInfoParamsVo previewInfoParamsVo) {
        return;
    }

    /**
     * 合同变更信息推送给签约
     *
     * @return 签约id
     */
    @Override
    public String pushSignInfo() {
        // 获取上级合同信息
        BbctContractManagementVo parentContractInfoVo = getContractInfoByContractCode(contractChangeVo.getContractCode());
        // 获取上级合同签约信息
        SigningSaveVo parentSignInfoVo = getSignInfoByContractCode(contractChangeVo.getContractCode());
        // 赋值签约信息(默认参数)
        SigningSaveVo signingSaveVo = setDefaultSignInfo(parentContractInfoVo, parentSignInfoVo);
        // 根据乙方变更信息赋值签约信息
        setSignInfoByLesseeChangeInfo(signingSaveVo, parentContractInfoVo, parentSignInfoVo);
        // 新增签约信息
        return insertSignInfo(signingSaveVo);
    }

    /**
     * 据乙方变更信息生成预览信息
     *
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentSignInfoVo     上级合同签约信息 vo实体
     * @return 预览信息 vo实体
     */
    private BbctContractManagementVo createLesseeChangePreviewInfo(BbctContractManagementVo parentContractInfoVo,
                                                                   SigningSaveVo parentSignInfoVo) {
        BbctPreviewInfoParamsVo previewInfoParamsVo = new BbctPreviewInfoParamsVo();
        BbsSignInfoVo signInfoVo = new BbsSignInfoVo();
        // 赋值合同模板信息
        BbsContractChangeTemplateVo contractChangeTemplateVo = getContractTemplate();
        signInfoVo.setContractTemplateId(contractChangeTemplateVo.getTemplateId());
        signInfoVo.setContractTemplateName(contractChangeTemplateVo.getTemplateName());
        // 赋值来源信息
        signInfoVo.setParentContractCode(contractChangeVo.getContractCode());
        signInfoVo.setContractType(ContractChangeTypeEnum.PARTY_B_CHANGE.getCode());
        signInfoVo.setProductSourceType(ContractChangeTypeEnum.PARTY_B_CHANGE.getCode());
        signInfoVo.setContractBeginTime(parentContractInfoVo.getContractBeginTime());
        signInfoVo.setContractEndTime(parentContractInfoVo.getContractEndTime());
        // 赋值模板属性信息
        previewInfoParamsVo.setTemplateSeatMap(createTemplateSeatMap(parentSignInfoVo.getSeatInfoVoList()));
        // 赋值客户信息
        BbsResultCustomerVo customerVo = new BbsResultCustomerVo();
        customerVo.setCustomerName(contractChangeVo.getCustomerName());
        customerVo.setLegalName(contractChangeVo.getLegalRepresentative());
        customerVo.setCustomerTel(contractChangeVo.getCustomerTel());
        customerVo.setBankSubbranchName(contractChangeVo.getBankSubbranchName());
        customerVo.setBankCard(contractChangeVo.getBankCard());
        customerVo.setBankUserName(parentContractInfoVo.getUserList().get(0).getBankUserName());
        customerVo.setRegisteredAddress(contractChangeVo.getMailAddress());
        previewInfoParamsVo.setCustomerVo(customerVo);
        // 赋值产品信息
        List<BbsResultProductVo> productList = new ArrayList<>();
        List<BbctContractSubjectMatterVo> contractProductList = parentContractInfoVo.getSubjectMatterList();
        for (BbctContractSubjectMatterVo contractProductVo : contractProductList) {
            BbsResultProductVo productVo = new BbsResultProductVo();
            productVo.setProductNo(contractProductVo.getProductNo());
            productVo.setProductName(contractProductVo.getProductName());
            productList.add(productVo);
        }
        previewInfoParamsVo.setProductList(productList);
        return AbstractContractSignFactory.getInstance(signInfoVo).createPreviewInfoVo(previewInfoParamsVo);
    }

    /**
     * 根据乙方变更信息赋值签约信息
     *
     * @param signingSaveVo        签约信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentSignInfoVo     上级签约信息 vo实体
     */
    private void setSignInfoByLesseeChangeInfo(SigningSaveVo signingSaveVo,
                                               BbctContractManagementVo parentContractInfoVo,
                                               SigningSaveVo parentSignInfoVo) {
        // 赋值合同模板信息
        signingSaveVo.setContractTemplateId(parentSignInfoVo.getContractTemplateId());
        signingSaveVo.setContractTemplateName(parentSignInfoVo.getContractTemplateName());
        // 赋值来源信息
        signingSaveVo.setParentContractCode(contractChangeVo.getContractCode());
        signingSaveVo.setContractType(ContractChangeTypeEnum.PARTY_B_CHANGE.getCode());
        signingSaveVo.setProductSourceType(ContractChangeTypeEnum.PARTY_B_CHANGE.getCode());
        // 赋值新客户信息
        BbsResultCustomerVo customerVo = new BbsResultCustomerVo();
        customerVo.setCustomerNo(contractChangeVo.getCustomerNo());
        customerVo.setCustomerName(contractChangeVo.getCustomerName());
        customerVo.setCustomerType(CustomerTypeEnum.COMPANY.getCode());
        customerVo.setCustomerCreditCode(contractChangeVo.getCustomerIdNumber());
        customerVo.setRegisteredAddress(contractChangeVo.getMailAddress());
        customerVo.setCustomerTel(contractChangeVo.getCustomerTel());
        customerVo.setLegalName(contractChangeVo.getLegalRepresentative());
        customerVo.setLegalMobile(parentContractInfoVo.getUserList().get(0).getLegalMobile());
        signingSaveVo.setCustomer(customerVo);
        signingSaveVo.setSecondBankTypeCode(contractChangeVo.getBankNameCode());
        signingSaveVo.setSecondBankTypeName(contractChangeVo.getBankName());
        signingSaveVo.setSecondBankNameCode(contractChangeVo.getBankSubbranchCode());
        signingSaveVo.setSecondBankName(contractChangeVo.getBankSubbranchName());
        signingSaveVo.setSecondAccountId(contractChangeVo.getBankCard());
    }
}
