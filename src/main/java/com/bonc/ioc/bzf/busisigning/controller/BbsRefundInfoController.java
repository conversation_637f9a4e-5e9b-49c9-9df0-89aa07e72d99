package com.bonc.ioc.bzf.busisigning.controller;

import com.bonc.ioc.bzf.busisigning.feign.vo.BbctApproveVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSignerVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctRefundAmountDetailVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctStatusVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsRefundInfoService;
import com.bonc.ioc.bzf.busisigning.vo.BbsIdVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRefundInfoPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRefundInfoPageVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRefundInfoVo;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 退款申请表 前端控制器
 *
 * <AUTHOR>
 * @date 2024-08-28
 * @change 2024-08-28 by pyj for init
 */
@RestController
@RequestMapping("/bbsRefundInfoEntity")
@Api(tags = "退款申请表")
@Validated
public class BbsRefundInfoController extends McpBaseController {
    @Resource
    private IBbsRefundInfoService baseService;

    /**
     * insertRecord 新增
     *
     * @param vo 需要新增的记录
     * @return com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "pyj")
    @ApiOperation(value = "新增", notes = "新增全表数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "退款申请表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsRefundInfoVo vo) {
        AppReply<String> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要新增的记录 列表
     * @return com.bonc.ioc.common.util.AppReply<List < String>> 返回新增后的主键 列表
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @PostMapping(value = "/batch/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "pyj")
    @ApiOperation(value = "批量新增", notes = "新增全表数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增主键集合")})
    public AppReply<List<String>> insertBatchRecord(@ApiParam(value = "退款申请表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbsRefundInfoVo> voList) {
        AppReply<List<String>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.insertBatchRecord(voList));
        return appReply;
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param refundId 需要删除的退款id
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @PostMapping(value = "/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "pyj")
    @ApiOperation(value = "根据主键删除", notes = "根据主键删除表中信息 物理删除")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> removeByIdRecord(@ApiParam(value = "需要删除的退款id", required = false) @RequestBody String refundId) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.removeByIdRecord(refundId);
        return appReply;
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param refundIdList 需要删除的退款id集合
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @PostMapping(value = "/batch/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "pyj")
    @ApiOperation(value = "根据主键删除-批量", notes = "根据主键批量删除表中信息 物理删除")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> removeByIdsRecord(@ApiParam(value = "需要删除的退款id", required = false) @RequestBody List<String> refundIdList) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.removeByIdsRecord(refundIdList);
        return appReply;
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的退款申请表
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @PostMapping(value = "/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "pyj")
    @ApiOperation(value = "根据主键更新", notes = "根据主键更新表中信息 更新全部信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> updateByIdRecord(@ApiParam(value = "需要更新的退款申请表", required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbsRefundInfoVo vo) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.updateByIdRecord(vo);
        return appReply;
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的退款申请表
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @PostMapping(value = "/batch/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "pyj")
    @ApiOperation(value = "根据主键更新-批量", notes = "根据主键批量更新表中信息 更新全部信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> updateBatchByIdRecord(@ApiParam(value = "需要更新的退款申请表", required = false) @RequestBody @Validated(UpdateValidatorGroup.class) List<BbsRefundInfoVo> voList) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.updateBatchByIdRecord(voList);
        return appReply;
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的退款申请表
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @PostMapping(value = "/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "pyj")
    @ApiOperation(value = "根据主键更新或新增", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> saveByIdRecord(@ApiParam(value = "需要更新或新增的退款申请表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsRefundInfoVo vo) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.saveByIdRecord(vo);
        return appReply;
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的退款申请表
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @PostMapping(value = "/batch/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "pyj")
    @ApiOperation(value = "根据主键更新或新增-批量", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> saveBatchByIdRecord(@ApiParam(value = "需要更新或新增的退款申请表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbsRefundInfoVo> voList) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.saveBatchByIdRecord(voList);
        return appReply;
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param refundId 需要查询的退款id
     * @return com.bonc.ioc.common.util.AppReply 主键查询的数据
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "pyj")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsRefundInfoVo> selectByIdRecord(@ApiParam(value = "需要查询的退款id", required = false) @RequestParam(required = false) String refundId) {
        AppReply<BbsRefundInfoVo> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByIdRecord(refundId));
        return appReply;
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "pyj")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsRefundInfoPageResultVo>>> selectByPageRecord(BbsRefundInfoPageVo vo) {
        AppReply<PageResult<List<BbsRefundInfoPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
    }

    /**
     * 根据合同编号查询房源列表
     *
     * @param contractCode 合同编号
     * @return 房源列表
     */
    @GetMapping(value = "/selectProductListByContractCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "pyj")
    @ApiOperation(value = "根据合同编号查询房源列表", notes = "根据合同编号查询房源列表")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbctContractSubjectMatterVo>> selectProductListByContractCode(@ApiParam(value = "合同编号", required = false) @RequestParam(required = false) String contractCode) {
        AppReply<List<BbctContractSubjectMatterVo>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectProductListByContractCode(contractCode));
        return appReply;
    }

    /**
     * 根据合同编号查询承租人信息
     *
     * @param contractCode 合同编号
     * @return 房源列表
     */
    @GetMapping(value = "/selectCustomerByContractCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 12, author = "pyj")
    @ApiOperation(value = "根据合同编号查询承租人信息", notes = "根据合同编号查询承租人信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbctContractSignerVo> selectCustomerByContractCode(@ApiParam(value = "合同编号", required = false) @RequestParam(required = false) String contractCode) {
        AppReply<BbctContractSignerVo> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectCustomerByContractCode(contractCode));
        return appReply;
    }

    /**
     * 根据主键查询退款金额明细
     *
     * @param refundId 退款id
     * @return 退款金额明细 vo实体
     */
    @GetMapping(value = "/selectRefundAmountListById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 13, author = "pyj")
    @ApiOperation(value = "根据主键查询退款金额明细", notes = "根据主键查询退款金额明细")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbctRefundAmountDetailVo>> selectRefundAmountListById(@ApiParam(value = "需要查询的退款id", required = false) @RequestParam(required = false) String refundId) {
        AppReply<List<BbctRefundAmountDetailVo>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectRefundAmountListById(refundId));
        return appReply;
    }

    /**
     * 根据签约id删除
     *
     * @param signId 签约id
     * @return 是否成功
     */
    @PostMapping(value = "/removeBySignId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 14, author = "pyj")
    @ApiOperation(value = "根据签约id删除", notes = "根据签约id删除")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> removeBySignId(@ApiParam(value = "签约id", required = false) @RequestBody String signId) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.removeBySignId(signId);
        return appReply;
    }

    /**
     * 退款申请提交
     *
     * @param idVo 主键id vo实体
     * @return 是否成功
     */
    @PostMapping(value = "/refundApplySubmit", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 15, author = "pyj")
    @ApiOperation(value = "退款申请提交", notes = "退款申请提交")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> refundApplySubmit(@RequestBody BbsIdVo idVo) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.refundApplySubmit(idVo));
        return appReply;
    }

    /**
     * 处理退款申请业务审批
     *
     * @param approveVo 审批 vo实体
     * @return 是否成功
     */
    @PostMapping(value = "/dealBusinessApprove", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 16, author = "pyj")
    @ApiOperation(value = "处理退款申请业务审批", notes = "处理退款申请业务审批")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> dealBusinessApprove(@ApiParam(value = "审核实体", required = true) @RequestBody @Validated(UpdateValidatorGroup.class) BbctApproveVo approveVo) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.dealBusinessApprove(approveVo);
        return appReply;
    }

    /**
     * 更新退款进度
     *
     * @param statusVo 状态 vo实体
     * @return 是否成功
     */
    @PostMapping(value = "/updateRefundProcess", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 17, author = "pyj")
    @ApiOperation(value = "更新退款进度", notes = "更新退款进度")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> updateRefundProcess(@ApiParam(value = "审核实体", required = true) @RequestBody @Validated(UpdateValidatorGroup.class) BbctStatusVo statusVo) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.updateRefundProcess(statusVo);
        return appReply;
    }

    /**
     * 结算中心更新退款进度
     *
     * @param statusVo 状态 vo实体
     * @return 是否成功
     */
    @PostMapping(value = "/chargeUpdateRefundProcess", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 18, author = "pyj")
    @ApiOperation(value = "结算中心更新退款进度", notes = "更新退款进度")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> chargeUpdateRefundProcess(@ApiParam(value = "审核实体", required = true) @RequestBody @Validated(UpdateValidatorGroup.class) BbctStatusVo statusVo) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.chargeUpdateRefundProcess(statusVo);
        return appReply;
    }
}

