package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeResultProductEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更租金变更产品表 服务类
 *
 * <AUTHOR>
 * @date 2024-09-09
 * @change 2024-09-09 by tbh for init
 */
public interface IBbsChangeResultProductService extends IMcpBaseService<BbsChangeResultProductEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    String insertRecord(BbsChangeResultProductVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    List<String> insertBatchRecord(List<BbsChangeResultProductVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param pdctId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void removeByIdRecord(String pdctId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param pdctIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void removeByIdsRecord(List<String> pdctIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的合同变更租金变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void updateByIdRecord(BbsChangeResultProductVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的合同变更租金变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void updateBatchByIdRecord(List<BbsChangeResultProductVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的合同变更租金变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void saveByIdRecord(BbsChangeResultProductVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的合同变更租金变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    void saveBatchByIdRecord(List<BbsChangeResultProductVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param pdctId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    BbsChangeResultProductVo selectByIdRecord(String pdctId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    PageResult<List<BbsChangeResultProductPageResultVo>> selectByPageRecord(BbsChangeResultProductPageVo vo);

    /**
     * 根据合同变更id查询变更产品信息列表
     *
     * @param ccId 合同变更id
     * @return 变更产品信息列表
     */
    List<BbsChangeResultProductVo> selectByCcId(String ccId);
}
