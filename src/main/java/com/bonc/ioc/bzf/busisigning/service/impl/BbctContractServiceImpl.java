package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.consts.SymbolConst;
import com.bonc.ioc.bzf.busisigning.dao.*;
import com.bonc.ioc.bzf.busisigning.entity.*;
import com.bonc.ioc.bzf.busisigning.enums.AreaTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.ContractChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.feign.feign.*;
import com.bonc.ioc.bzf.busisigning.feign.vo.*;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbpmBillManagementPageVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsResultCustomerVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsSignInfoVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsTemplateGroupVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsTemplateSeatVo;
import com.bonc.ioc.bzf.busisigning.file.feign.BbNewFileFeignClient;
import com.bonc.ioc.bzf.busisigning.file.vo.NewFIleResultVo;
import com.bonc.ioc.bzf.busisigning.service.*;
import com.bonc.ioc.bzf.busisigning.utils.*;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.*;
import com.bonc.ioc.bzf.busisigning.vo.JsonVo.IncrementalInfoArray;
import com.bonc.ioc.bzf.busisigning.vo.payment.ChangeContractRequestV2;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.dict.entity.McpDictEntity;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.util.CollectionUtil;
import com.bonc.ioc.common.util.Path;
import com.bonc.ioc.common.utils.CurrentUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.bonc.ioc.bzf.busisigning.utils.DictTreeToBusinessFormatUtil.jsonToBusinessFormatVoList;

/**
 * 合同 服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-30
 */
@Slf4j
@Service
public class BbctContractServiceImpl implements BbctContractService {

    /*生成合同使用*/
    private String businessTypeCode1="02";//业务分类编号1
    private String businessTypeName1="商业";//业务分类名称1
    private String contractTypeCode="02";//合同分类编号
    private String modeId="3";//合同类型
    private String suffix="00";//后缀（生成商业合同编号中使用，00为原合同，01开始为本合同的补充协议）

    /*生成帐单使用*/
    private String supportOfflineSign="1";//是否支持线上签约(0.否 1.是)
    private String supportOnlineSign="1";//是否支持纸质签约(0.否 1.是)
    private String supportSceneSign="0";//是否支持电子版签约(0.否 1.是)
    private String signType="07";//签约类型(01.散租 02.趸租 03.管理协议 07.商租)
    private String productSourceType="8";//产品来源类型(0.导入方式 1.已登记 2.已选房 3.已验房 4.选择房源 5.房态中心(实时) 6.续租中心 7.调换房 8.商业 9.家具家电)
    private String checkRoomStatus="3";//验房状态(1.先验房 2.签约同时验房 3.无需验房)
    private String offer="0";//是否报盘(0.否 1.是)
    private String submitHeatingCompany="0";//是否报送热力公司(0.否 1.是)
    private String secondPayPercent="1";//缴费比例(0 ~ 1)
    private String bankIsAuthentication="0";//是否鉴权(1.是 0.否)

    @Value("${bzfsystem.exportFilePath:}")
    private String exportFilePath;//合同导出路径

    @Resource
    private BbctContractFeignClient bbctContractFeignClient;
    @Resource
    private BbctSigningFeignCilent bbctSigningFeignCilent;
    @Resource
    private BbHousingFeignClient bbHousingFeignClient;
    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    @Resource
    private BbsSignInfoMapper signInfoMapper;
    @Resource
    private BbsResultCustomerMapper customerMapper;
    @Resource
    private BbsResultRelationMapper relationMapper;
    @Resource
    private BbsResultProductMapper productMapper;
    @Resource
    private BbsTemplateSeatMapper templateSeatMapper;
    @Resource
    private BbsDictMapper dictMapper;
    @Resource
    private BbsTemplateGroupMapper templateGroupMapper;
    @Resource
    private BbsSignIncrementalConfigMapper signIncrementalConfigMapper;
    @Resource
    private BbsSignFpIntervalMapper signFpIntervalMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Resource
    private BbsRenewalInfoMapper renewalInfoMapper;
    @Resource
    private BbsRenewalCustomerMapper renewalCustomerMapper;
    @Resource
    private BbsRenewalRelationMapper renewalRelationMapper;
    @Resource
    private BbsRenewalProductMapper renewalProductMapper;
    @Resource
    private BbsRenewalTemplateSeatMapper renewalTemplateSeatMapper;
    @Resource
    private BbsRenewalTemplateGroupMapper renewalTemplateGroupMapper;
    @Resource
    private BbsRenewalIncrementalConfigMapper renewalIncrementalConfigMapper;
    @Resource
    private BbsRenewalFpIntervalMapper renewalFpIntervalMapper;
    @Resource
    private IBbsSignInfoService iBbsSignInfoService;
    @Resource
    private IBbsSignInfoExtService bbsSignInfoExtService;
    @Resource
    private IBbsRenewalInfoExtService bbsRenewalInfoByYingService;
    @Resource
    private BzfSystemCommercialFeignClient bzfSystemCommercialFeignClient;

    /**
     * 合同变更相关 服务实例
     */
    @Resource
    private IBbsiContractChangeService contractChangeService;

    @Resource
    private IBbsSignFpIntervalService signFpIntervalService;

    @Resource
    private BbsiContractChangeMapper bbsiContractChangeMapper;

    @Resource
    private BbNewFileFeignClient fileFeignClient;

    @Override
    public AppReply<String> generateContract(String signId) {
        BbctContractManagementVo vo = generateContractVo(signId);
        log.info("合同生成接口调用入参："+ JSON.toJSONString(vo));
        AppReply<String> appReplay=bbctContractFeignClient.insertOrUpdate(vo);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReplay))) {
            throw new McpException(ResultUtils.createLog("合同生成信息失败[详情: %s, contractCode: %s]",
                    appReplay, vo.getContractNo()));
        }
        log.info("合同生成接口调用返回："+ JSON.toJSONString(appReplay));
        return appReplay;
    }

    /**
     * 台账生成合同
     *
     * @param signId
     * @return
     */
    @Deprecated
    @Override
    public AppReply<String> generateContractTz(String signId) {
        BbctContractManagementVo vo = generateContractVo(signId);
        //vo.setContractStatus("1");//已签约
        vo.setPdfFileId(null);
        vo.setNewpdfFileId(null);//不生成电子合同
        vo.setPaperSigningStatus("1");//纸质签约不生成电子合同
        vo.setPassTime(vo.getContractBeginTime());//审核时间默认等于合同开始日期
        log.info("合同生成接口调用入参："+ JSON.toJSONString(vo));
        AppReply<String> appReplay=bbctContractFeignClient.insertOrUpdate(vo);
        log.info("合同生成接口调用返回："+ JSON.toJSONString(appReplay));
        return appReplay;
    }

    private BbctContractManagementVo generateContractVo(String signId) {
        BbctContractManagementVo vo = new BbctContractManagementVo();
        //通过签约id查询签约信息
        BbsSignInfoEntity signInfoEntity = new LambdaQueryChainWrapper<>(signInfoMapper)
                .eq(BbsSignInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsSignInfoEntity::getSignId, signId)
                .one();
        vo.setBusinessFormat(signInfoEntity.getBusinessFormat());//业态编码
        vo.setBusinessFormatName(signInfoEntity.getBusinessFormatName());//业态名称
        vo.setAreaType(signInfoEntity.getAreaType());//面积类型
        vo.setBaseRentCalculate("");//基础租金计算方式code
        vo.setBaseRentCalculateName("");//基础租金计算方式名称
        vo.setContractNo(signInfoEntity.getContractCode());//合同编号
        vo.setParentContractCode(signInfoEntity.getParentContractCode());
        vo.setBusinessId(signId);//业务唯一标识(签约id)
        vo.setBusinessTypeCode1(businessTypeCode1);//业务分类编号1，传02
        vo.setBusinessTypeName1(businessTypeName1);//业务分类名称1，传商业
        vo.setOffer("0");//不报盘
//        vo.setBusinessTypeCode2(businessTypeCode2);//业务分类编号2，02
//        vo.setBusinessTypeName2(businessTypeName2);//业务分类名称2，趸租
        vo.setCashPledgeCode(signInfoEntity.getCashPledgeCode());//押金标准code
        vo.setContractStatus("2");//合同状态（1已生效、2未生效、3已结束、4终止、5已逾期、6作废）
        if("19".equals(signInfoEntity.getContractType())||"20".equals(signInfoEntity.getContractType())){
            vo.setContractType(signInfoEntity.getContractType());
        }else {
            vo.setContractType("5");//合同类型(1.普通合同 2.补充合同3.续租合同 4.调换房合同 7.主承租人合同变更合同)
        }
        vo.setBusinessFormat(signInfoEntity.getBusinessFormat());//业态编码
        vo.setBusinessFormatName(signInfoEntity.getBusinessFormatName());//业态名称
        BbsDictEntity dctEntity = new LambdaQueryChainWrapper<>(dictMapper)
                .eq(BbsDictEntity::getTypeCode, "CASH_PLEDGE_CODE")
                .eq(BbsDictEntity::getCode, signInfoEntity.getCashPledgeCode())
                .one();
        vo.setCashPledgeName(dctEntity.getMeaning());//押金标准名称
        if("9".equals(signInfoEntity.getCashPledgeCode())){
            vo.setDeposit(Double.valueOf(signInfoEntity.getCashPledgeValue()));//押金
        }
        vo.setSignTime(signInfoEntity.getSignTime());//签约日期
        vo.setPaymentCycleCode(signInfoEntity.getPaymentCycleCode());//缴费周期
        BbsDictEntity dctEntity1 = new LambdaQueryChainWrapper<>(dictMapper)
                .eq(BbsDictEntity::getTypeCode, "PAYMENT_CYCLE_CODE")
                .eq(BbsDictEntity::getCode, signInfoEntity.getPaymentCycleCode())
                .one();
        vo.setPaymentCycleName(dctEntity1.getMeaning());//缴费周期名称
        //如果押金标准code是自定义

        //通过签约id查询合同属性信息
        Map<String, Object> contractAttr = new LambdaQueryChainWrapper<>(templateSeatMapper)
                .eq(BbsTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsTemplateSeatEntity::getParentId, signId)
//                .eq(BbsTemplateSeatEntity::getIsRequired, "1")
//                .eq(BbsTemplateSeatEntity::getIsShow, "1")
                .list().stream().collect(Collectors.toMap(BbsTemplateSeatEntity::getSeatKey, b->StringUtils.isNotBlank(b.getValue()) ? b.getValue() : b.getDefaultVal()));
        vo.setContractAttr(contractAttr);
//        vo.setFirstName(String.valueOf(contractAttr.get("lessorName")));

        vo.setContractBeginTime(signInfoEntity.getContractBeginTime());//合同开始日期
        vo.setContractEndTime(signInfoEntity.getContractEndTime());//合同结束日期
        vo.setContractTemplateId(signInfoEntity.getContractTemplateId());//合同模板id
        vo.setContractTypeCode(contractTypeCode);

        BbsResultRelationEntity relationEntity = new LambdaQueryChainWrapper<>(relationMapper)
                .eq(BbsResultRelationEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsResultRelationEntity::getSignInfoId, signId)
                .one();
        //查询产品相关信息
        List<BbsResultProductEntity> productEntities = new LambdaQueryChainWrapper<>(productMapper)
                .eq(BbsResultProductEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsResultProductEntity::getRrId, relationEntity.getRrId())
                .list();
        List<BbctContractSubjectMatterVo> subjectMatterList = new ArrayList<>();

        productEntities.forEach(item -> {
            BbctContractSubjectMatterVo matterVo = new BbctContractSubjectMatterVo();
            BeanUtils.copyProperties(item, matterVo);
            matterVo.setLeaseTermRent(item.getRent());//租金
            matterVo.setRentStandardNo(item.getRentStandardUnit());
            matterVo.setRentStandardName(item.getRentStandardUnitName());
            matterVo.setRent(item.getRentStandard()+"");
            matterVo.setRentUnit(item.getRentStandardUnitName());
            matterVo.setProductExtend(this.createProductExtend(item));//扩展属性
            subjectMatterList.add(matterVo);
        });
        vo.setSubjectMatterList(subjectMatterList);//产品信息
//        BigDecimal  houseStructArea = new BigDecimal(0.0);
//        for(BbctContractSubjectMatterVo contractSubjectMatterVo: subjectMatterList){
////            houseStructArea += Double.valueOf(contractSubjectMatterVo.getHouseStructArea());
//            houseStructArea = houseStructArea.add(new BigDecimal(contractSubjectMatterVo.getHouseStructArea()));
//        }
//        houseStructArea=houseStructArea .setScale(2, RoundingMode.HALF_UP);
        vo.setTotalArea(this.getSumArea(subjectMatterList,signInfoEntity));//总租赁面积
        vo.setTotalSets(productEntities.size());//签约房源套数
        //查询客户相关信息
        BbsResultCustomerEntity customerEntity = new LambdaQueryChainWrapper<>(customerMapper)
                .eq(BbsResultCustomerEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsResultCustomerEntity::getRrId, relationEntity.getRrId())
                .one();
        List<BbctContractSignerVo> userList = new ArrayList<>();
        BbctContractSignerVo signerVo = new BbctContractSignerVo();
        BeanUtils.copyProperties(customerEntity, signerVo);
        signerVo.setCustomerTypeName("00".equals(customerEntity.getCustomerType()) ? "个人" : "企业");//租户类型名称
        if ("01".equals(customerEntity.getCustomerType())) {//企业才有代理人
            signerVo.setConsignorName(customerEntity.getContactName());
            signerVo.setConsignorMobile(customerEntity.getCustomerTel());
            signerVo.setCustomerExtend(this.createCustomerExtend(customerEntity));
        }
        userList.add(signerVo);
        vo.setUserList(userList);//合同参与者信息
        vo.setTenantSupplierNo(customerEntity.getTenantSupplierNo());//租户客商编号
        vo.setTenantSupplierName(customerEntity.getTenantSupplierName());//租户客商名称
        String deptName = CurrentUtil.getDepartmentInfo()==null?"":CurrentUtil.getDepartmentInfo().getDepartmentName();
        Map<String, Object> stringObjectMap = null;
        try {
            stringObjectMap = jdbcTemplate.queryForMap(" SELECT dept_short_form FROM bzf_dept_sort_form where dept_name='"+deptName+"' ");
        } catch (DataAccessException e) {
        }
        if(stringObjectMap==null||stringObjectMap.get("dept_short_form")==null||StringUtils.isEmpty(stringObjectMap.get("dept_short_form").toString())){
            vo.setCompanyAbbr("YX");
        }else{
            vo.setCompanyAbbr(String.valueOf(stringObjectMap.get("dept_short_form")));//公司首写字母
        }
        vo.setModeId(modeId);//合同类型
        if(productEntities.size() > 0){
            String projectId = productEntities.get(0).getProjectId();
            Map<String, Object> stringObjectMap1 = null;
            try {
                stringObjectMap1 = jdbcTemplate.queryForMap(" SELECT project_short_form FROM bzf_project_sort_form where project_id='" + projectId + "' ");
            } catch (DataAccessException e) {
            }
            if(stringObjectMap1==null||stringObjectMap1.get("project_short_form")==null||StringUtils.isEmpty(stringObjectMap1.get("project_short_form").toString())){
                String projectName=filterProjectName(productEntities.get(0).getProjectName());
                if(StrUtil.emptyToDefault(projectName,"").length()>=2){
                    vo.setProjectCode(String.valueOf(PinyinUtil.convertHanzi2PinyinShouZhiMu(projectName.substring(0,2))).toUpperCase());
                }else{
                    vo.setProjectCode("WZ");
                }

            }else{
                vo.setProjectCode(String.valueOf(stringObjectMap1.get("project_short_form")));//项目简称
            }

        }
        vo.setSuffix(suffix);//后缀（生成商业合同编号中，00为原合同，01开始为本合同的补充协议）
        vo.setPdfFileId(signInfoEntity.getContractUrl());//文件id

        vo.setFirstAccountId(signInfoEntity.getFirstAccountId());//甲方账户id
        vo.setFirstAccountName(signInfoEntity.getFirstAccountName());//甲方账户名称
        vo.setFirstBankNameCode(signInfoEntity.getFirstBankNameCode());//甲方开户行
        vo.setFirstBankName(signInfoEntity.getFirstBankName());//甲方开户行名称
        vo.setAreaType(signInfoEntity.getAreaType());//面积类型
        vo.setBusinessId(signInfoEntity.getSignId());//签约结果ID

        //通过签约id查询合同甲方信息
        BbsTemplateSeatEntity templateSeatEntity = new LambdaQueryChainWrapper<>(templateSeatMapper)
                .eq(BbsTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsTemplateSeatEntity::getParentId, signId)
                .eq(BbsTemplateSeatEntity::getSeatKey, "unifiedCreditCode").one();
        if(templateSeatEntity != null){
            vo.setFirstId(templateSeatEntity.getValue());//甲方ID
            vo.setFirstSocialCode(templateSeatEntity.getValue());//甲方信用代码
        }
        BbsTemplateSeatEntity templateSeatEntity1 = new LambdaQueryChainWrapper<>(templateSeatMapper)
                .eq(BbsTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsTemplateSeatEntity::getParentId, signId)
//                .eq(BbsTemplateSeatEntity::getIsRequired, "1")
//                .eq(BbsTemplateSeatEntity::getIsShow, "1")
                .eq(BbsTemplateSeatEntity::getSeatKey, "lessorName").one();
        if(templateSeatEntity1 != null){
            vo.setFirstName(templateSeatEntity1.getValue());//甲方名称
        }

        vo.setIncreaseJson(this.createIncrement(signInfoEntity));//递增信息
        vo.setContractExtend(this.createContractOtherInfo(signInfoEntity));//合同扩展信息

        return vo;
    }

    @Override
    public AppReply modifyContractStatus(String signId) {
        //通过签约id查询签约信息
        BbsSignInfoEntity signInfoEntity = new LambdaQueryChainWrapper<>(signInfoMapper)
                .eq(BbsSignInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsSignInfoEntity::getSignId, signId)
                .one();
        //通过签约id查询续签信息
        BbsRenewalInfoEntity bbsRenewalInfoEntity = new LambdaQueryChainWrapper<>(renewalInfoMapper)
                .eq(BbsRenewalInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalInfoEntity::getSignId, signId)
                .one();
        if (null == signInfoEntity && null == bbsRenewalInfoEntity) {
            throw new McpException("未查询到签约信息");
        }
        String contractStatusYjs="1";//合同状态(已结束)
        String contractCode = null == signInfoEntity ? bbsRenewalInfoEntity.getContractCode() : signInfoEntity.getContractCode();
        log.info("修改合同状态Feign==合同号：{}，合同状态：{}", contractCode, contractStatusYjs);
        return bbctContractFeignClient.updateStatusSign(contractCode, contractStatusYjs);
    }

    @Override
    public AppReply<Object> exitFormForCreateByContract(String contractCode,RenewalingSaveVo renewalingSaveVo) {
        BbsSignInfoEntity signInfoEntity = new LambdaQueryChainWrapper<>(signInfoMapper)
                .eq(BbsSignInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsSignInfoEntity::getContractCode, contractCode)
                .one();
        BbsResultRelationEntity relationEntity = new LambdaQueryChainWrapper<>(relationMapper)
                .eq(BbsResultRelationEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsResultRelationEntity::getSignInfoId, signInfoEntity.getSignId())
                .one();
        //查询客户相关信息
        BbsResultCustomerEntity customerEntity = new LambdaQueryChainWrapper<>(customerMapper)
                .eq(BbsResultCustomerEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsResultCustomerEntity::getRrId, relationEntity.getRrId())
                .one();
        BbsResultProductEntity productEntitie = new LambdaQueryChainWrapper<>(productMapper)
                .eq(BbsResultProductEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsResultProductEntity::getRrId, relationEntity.getRrId())
                .one();
        BbsRenewalCustomerVo renewalCustomerEntity=renewalingSaveVo.getCustomer();
        BankRequestVo<ExitFormForCreateByContractVo> exitFormForCreateByContractRequestVo=new BankRequestVo<>();
        ExitFormForCreateByContractVo exitFormForCreateByContractVo=new ExitFormForCreateByContractVo();
        exitFormForCreateByContractRequestVo.setData(exitFormForCreateByContractVo);
        exitFormForCreateByContractVo.setExitDate(signInfoEntity.getContractEndTime());
        exitFormForCreateByContractVo.setProjectId(productEntitie.getProjectId());
        exitFormForCreateByContractVo.setContractId(signInfoEntity.getContractCode());
        exitFormForCreateByContractVo.setChangeTenantBankName(renewalCustomerEntity.getDepositReturnBankName());
        exitFormForCreateByContractVo.setChangeTenantHeadBankCode(renewalCustomerEntity.getDepositReturnBankNameCode());
        exitFormForCreateByContractVo.setChangeTenantBankBranchName(renewalCustomerEntity.getDepositReturnBankSubbranchName());
        exitFormForCreateByContractVo.setChangeTenantBankCode(renewalCustomerEntity.getDepositReturnBankSubbranchCode());
        exitFormForCreateByContractVo.setChangeTenantBankAccountName("3".equalsIgnoreCase(renewalingSaveVo.getDepositReturnWay())?renewalCustomerEntity.getDepositReturnBankAccount():customerEntity.getBankUserName());
        exitFormForCreateByContractVo.setChangeTenantBankAccountNo("3".equalsIgnoreCase(renewalingSaveVo.getDepositReturnWay())?renewalCustomerEntity.getDepositReturnBankCard():customerEntity.getBankCard());
        exitFormForCreateByContractVo.setChangeTenantBankProvince(renewalCustomerEntity.getDepositReturnProvinceName());
        exitFormForCreateByContractVo.setChangeTenantBankCity(renewalCustomerEntity.getDepositReturnCityName());
        exitFormForCreateByContractVo.setHouseRentOutType("00".equalsIgnoreCase(customerEntity.getCustomerType())?"02":"01");
        exitFormForCreateByContractVo.setChangeTenantIDType("00".equalsIgnoreCase(customerEntity.getCustomerType())?"01":"99");
        exitFormForCreateByContractVo.setChangeIDNumber("00".equalsIgnoreCase(customerEntity.getCustomerType())?customerEntity.getCustomerIdNumber():customerEntity.getCustomerCreditCode());
        log.info("调用工银退租接口 参数：{}", JSON.toJSONString(exitFormForCreateByContractRequestVo));
        ChargeRespondVo<Object>  exitFormForCreateByContract=bfipSettlementFeignClient.exitFormForCreateByContract(exitFormForCreateByContractRequestVo);
        log.info("调用工银退租接口 结果：{}", JSON.toJSONString(exitFormForCreateByContract));
        exitFormForCreateByContract.getCode();
        AppReply<Object> appReply;
        if(exitFormForCreateByContract!=null&&"00000".equals(exitFormForCreateByContract.getCode())){
            appReply=new AppReply<>("1","调用成功",exitFormForCreateByContract.getData());
        }else{
            appReply=AppReply.error(JSON.toJSONString(exitFormForCreateByContract));
        }
        return appReply;
    }

    @Override
    public AppReply<Object> generateBill(String signId,String dataSource) {
        BbsSignInfoAndProductInfoAndCustomerInfoVo vo = generateBillVo(signId,dataSource);
        log.info("账单生成接口调用入参：{}", JSON.toJSONString(vo,SerializerFeature.WriteMapNullValue));
        AppReply<Object> appReply= bbctSigningFeignCilent.insertAndCreatePayment(vo);
        log.info("账单生成接口调用返回结果：{}", JSON.toJSONString(appReply));
        return appReply;
    }

    private BbsSignInfoAndProductInfoAndCustomerInfoVo generateBillVo(String signId,String dataSource) {
        BbsSignInfoAndProductInfoAndCustomerInfoVo vo = new BbsSignInfoAndProductInfoAndCustomerInfoVo();
        if("1".equals(dataSource)) {//新签
            BbsResultRelationEntity relationEntity = new LambdaQueryChainWrapper<>(relationMapper)
                    .eq(BbsResultRelationEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsResultRelationEntity::getSignInfoId, signId)
                    .one();
            //查询客户相关信息
            BbsResultCustomerEntity customerEntity = new LambdaQueryChainWrapper<>(customerMapper)
                    .eq(BbsResultCustomerEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsResultCustomerEntity::getRrId, relationEntity.getRrId())
                    .one();
            BbsResultCustomerVo bbsResultCustomerVo = new BbsResultCustomerVo();
            BeanUtils.copyProperties(customerEntity, bbsResultCustomerVo);
            bbsResultCustomerVo.setBankIsAuthentication(bankIsAuthentication);//是否鉴权(1.是 0.否)
            bbsResultCustomerVo.setType(null);
            bbsResultCustomerVo.setRcId(null);
            bbsResultCustomerVo.setRrId(null);
            bbsResultCustomerVo.setConsignorName(customerEntity.getContactName());
            bbsResultCustomerVo.setConsignorMobile(customerEntity.getCustomerTel());
            bbsResultCustomerVo.setBankName(null);
            bbsResultCustomerVo.setBankNameCode(null);
            bbsResultCustomerVo.setBankCard(null);
            bbsResultCustomerVo.setBankPhone(null);
            bbsResultCustomerVo.setBankSubbranchCode(null);
            bbsResultCustomerVo.setBankSubbranchName(null);
            vo.setBbsResultCustomerVo(bbsResultCustomerVo);//趸租单位(客户信息)

            //通过签约id查询签约信息
            BbsSignInfoEntity signInfoEntity = new LambdaQueryChainWrapper<>(signInfoMapper)
                    .eq(BbsSignInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsSignInfoEntity::getSignId, signId)
                    .one();
            BbsSignInfoVo bbsSignInfoVo = new BbsSignInfoVo();
            BeanUtils.copyProperties(signInfoEntity, bbsSignInfoVo);
            bbsSignInfoVo.setSupportOfflineSign(supportOfflineSign);//是否支持线上签约(0.否 1.是)
            bbsSignInfoVo.setSupportOnlineSign(supportOnlineSign);//是否支持纸质签约(0.否 1.是)
            bbsSignInfoVo.setSupportSceneSign(supportSceneSign);//是否支持电子版签约(0.否 1.是)
            bbsSignInfoVo.setSignType(signType);//签约类型(01.散租 02.趸租 03.管理协议 07.商租)
            bbsSignInfoVo.setSignStatus(signInfoEntity.getSignStatus());//签约状态(1.暂存 2.待签约 3.已签约 4.未签约 5.待审核 6.未通过 7.租户已签 8.终止)
            bbsSignInfoVo.setProductSourceType(productSourceType);//产品来源类型(0.导入方式 1.已登记 2.已选房 3.已验房 4.选择房源 5.房态中心(实时) 6.续租中心 7.调换房 8.商业 9.家具家电)
            bbsSignInfoVo.setCheckRoomStatus(checkRoomStatus);//验房状态(1.先验房 2.签约同时验房 3.无需验房)
            bbsSignInfoVo.setContractType("5");//合同类型(1.普通合同 2.补充合同 3.续租合同 4.调换房合同 5.商业合同 6.家具家电合同)
            bbsSignInfoVo.setOffer(offer);//是否报盘(0.否 1.是)
            bbsSignInfoVo.setSubmitHeatingCompany(submitHeatingCompany);//是否报送热力公司(0.否 1.是)
            bbsSignInfoVo.setSecondPayPercent(secondPayPercent);//缴费比例(0 ~ 1)
            vo.setBbsSignInfoVo(bbsSignInfoVo);

            //查询产品相关信息
            List<BbsResultProductEntity> productEntities = new LambdaQueryChainWrapper<>(productMapper)
                    .eq(BbsResultProductEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsResultProductEntity::getRrId, relationEntity.getRrId())
                    .list();
            List<BbsResultRelationProductCustomerVo> productList = new ArrayList<>();

            for(int i = 0; i < productEntities.size(); i++){
                BbsResultProductEntity bbsResultProductEntity = productEntities.get(i);
                BbsResultRelationProductCustomerVo matterVo = new BbsResultRelationProductCustomerVo();
                BeanUtils.copyProperties(bbsResultProductEntity, matterVo);
                //产品扩展信息外接json拼接
                String productExtend = assembleProductExtend(bbsResultProductEntity, signInfoEntity, productEntities.size(), i+1);
                matterVo.setProductExtend(productExtend);//产品扩展信息
                matterVo.setGroupNo("");
                matterVo.setProjectFormat("03");
                matterVo.setRentStandardNo(String.valueOf(bbsResultProductEntity.getRentStandard()));
                matterVo.setProjectEstate(bbsResultProductEntity.getCommunityBuildingName());
                matterVo.setRrId(null);
                productList.add(matterVo);
            }
            vo.setProductList(productList);//产品信息

            //查询站内信模板信息
            BbsTemplateGroupEntity systemStationMessageTemplateGroupEntity = new LambdaQueryChainWrapper<>(templateGroupMapper)
                    .eq(BbsTemplateGroupEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsTemplateGroupEntity::getTemplateGroupId, signInfoEntity.getSystemStationMessageTemplateGroupId())
                    .one();
            if(systemStationMessageTemplateGroupEntity != null){
                BbsTemplateGroupVo systemStationMessageTemplateGroup = new BbsTemplateGroupVo();
                BeanUtils.copyProperties(systemStationMessageTemplateGroupEntity, systemStationMessageTemplateGroup);
                vo.setSystemStationMessageTemplateGroup(systemStationMessageTemplateGroup);
            }
            //查询短信模板信息
            BbsTemplateGroupEntity mobileMessageTemplateGroupEntity = new LambdaQueryChainWrapper<>(templateGroupMapper)
                    .eq(BbsTemplateGroupEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsTemplateGroupEntity::getTemplateGroupId, signInfoEntity.getMobileMessageTemplateGroupId())
                    .one();
            if(mobileMessageTemplateGroupEntity != null){
                BbsTemplateGroupVo mobileMessageTemplateGroup = new BbsTemplateGroupVo();
                BeanUtils.copyProperties(mobileMessageTemplateGroupEntity, mobileMessageTemplateGroup);
                vo.setMobileMessageTemplateGroup(mobileMessageTemplateGroup);
            }
            //通过签约id查询模板属性信息
            List<BbsTemplateSeatEntity> templateSeatEntities = new LambdaQueryChainWrapper<>(templateSeatMapper)
                    .eq(BbsTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
//                .eq(BbsTemplateSeatEntity::getIsRequired, "1")
//                .eq(BbsTemplateSeatEntity::getIsShow, "1")
                    .eq(BbsTemplateSeatEntity::getParentId, signId)
                    .list();
            List<BbsTemplateSeatVo> templateSeatList = new ArrayList<BbsTemplateSeatVo>();
            templateSeatEntities.forEach(item -> {
                BbsTemplateSeatVo templateSeatVo = new BbsTemplateSeatVo();
                BeanUtils.copyProperties(item, templateSeatVo);
                templateSeatList.add(templateSeatVo);
            });
            vo.setTemplateSeatList(templateSeatList);//模板属性信息
        }else if("2".equals(dataSource)){//续租
            BbsRenewalRelationEntity relationEntity = new LambdaQueryChainWrapper<>(renewalRelationMapper)
                    .eq(BbsRenewalRelationEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsRenewalRelationEntity::getSignInfoId, signId)
                    .one();
            //查询客户相关信息
            BbsRenewalCustomerEntity customerEntity = new LambdaQueryChainWrapper<>(renewalCustomerMapper)
                    .eq(BbsRenewalCustomerEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsRenewalCustomerEntity::getRrId, relationEntity.getRrId())
                    .one();
            BbsResultCustomerVo bbsResultCustomerVo = new BbsResultCustomerVo();
            BeanUtils.copyProperties(customerEntity, bbsResultCustomerVo);
            bbsResultCustomerVo.setBankIsAuthentication(bankIsAuthentication);//是否鉴权(1.是 0.否)
            bbsResultCustomerVo.setType(null);
            bbsResultCustomerVo.setRcId(null);
            bbsResultCustomerVo.setRrId(null);
            bbsResultCustomerVo.setConsignorName(customerEntity.getContactName());
            bbsResultCustomerVo.setConsignorMobile(customerEntity.getCustomerTel());
            vo.setBbsResultCustomerVo(bbsResultCustomerVo);//趸租单位(客户信息)

            //通过签约id查询签约信息
            BbsRenewalInfoEntity signInfoEntity = new LambdaQueryChainWrapper<>(renewalInfoMapper)
                    .eq(BbsRenewalInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsRenewalInfoEntity::getSignId, signId)
                    .one();
            BbsSignInfoVo bbsSignInfoVo = new BbsSignInfoVo();
            BeanUtils.copyProperties(signInfoEntity, bbsSignInfoVo);
            bbsSignInfoVo.setSupportOfflineSign(supportOfflineSign);//是否支持线上签约(0.否 1.是)
            bbsSignInfoVo.setSupportOnlineSign(supportOnlineSign);//是否支持纸质签约(0.否 1.是)
            bbsSignInfoVo.setSupportSceneSign(supportSceneSign);//是否支持电子版签约(0.否 1.是)
            bbsSignInfoVo.setSignType(signType);//签约类型(01.散租 02.趸租 03.管理协议 07.商租)
            bbsSignInfoVo.setSignStatus(signInfoEntity.getSignStatus());//签约状态(1.暂存 2.待签约 3.已签约 4.未签约 5.待审核 6.未通过 7.租户已签 8.终止)
            bbsSignInfoVo.setProductSourceType(productSourceType);//产品来源类型(0.导入方式 1.已登记 2.已选房 3.已验房 4.选择房源 5.房态中心(实时) 6.续租中心 7.调换房 8.商业 9.家具家电)
            bbsSignInfoVo.setCheckRoomStatus(checkRoomStatus);//验房状态(1.先验房 2.签约同时验房 3.无需验房)
            bbsSignInfoVo.setContractType("5");//合同类型(1.普通合同 2.补充合同 3.续租合同 4.调换房合同 5.商业合同 6.家具家电合同)
            bbsSignInfoVo.setOffer(offer);//是否报盘(0.否 1.是)
            bbsSignInfoVo.setSubmitHeatingCompany(submitHeatingCompany);//是否报送热力公司(0.否 1.是)
            bbsSignInfoVo.setSecondPayPercent(secondPayPercent);//缴费比例(0 ~ 1)
            vo.setBbsSignInfoVo(bbsSignInfoVo);

            //查询产品相关信息
            List<BbsRenewalProductEntity> productEntities = new LambdaQueryChainWrapper<>(renewalProductMapper)
                    .eq(BbsRenewalProductEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsRenewalProductEntity::getRrId, relationEntity.getRrId())
                    .list();
            List<BbsResultRelationProductCustomerVo> productList = new ArrayList<>();

            for(int i = 0; i < productEntities.size(); i++){
                BbsRenewalProductEntity bbsResultProductEntity = productEntities.get(i);
                BbsResultRelationProductCustomerVo matterVo = new BbsResultRelationProductCustomerVo();
                BeanUtils.copyProperties(bbsResultProductEntity, matterVo);
                //产品扩展信息外接json拼接
                String productExtend = assembleRenewalProductExtend(bbsResultProductEntity, signInfoEntity, productEntities.size(), i+1);
                matterVo.setProductExtend(productExtend);//产品扩展信息
                matterVo.setGroupNo("");
                matterVo.setProjectFormat("03");
                matterVo.setRentStandardNo(String.valueOf(bbsResultProductEntity.getRentStandard()));
                matterVo.setProjectEstate(bbsResultProductEntity.getCommunityBuildingName());
                matterVo.setRrId(null);
                productList.add(matterVo);
            }
            vo.setProductList(productList);//产品信息

            //查询站内信模板信息
            BbsRenewalTemplateGroupEntity systemStationMessageTemplateGroupEntity = new LambdaQueryChainWrapper<>(renewalTemplateGroupMapper)
                    .eq(BbsRenewalTemplateGroupEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsRenewalTemplateGroupEntity::getTemplateGroupId, signInfoEntity.getSystemStationMessageTemplateGroupId())
                    .one();
            if(systemStationMessageTemplateGroupEntity != null){
                BbsTemplateGroupVo systemStationMessageTemplateGroup = new BbsTemplateGroupVo();
                BeanUtils.copyProperties(systemStationMessageTemplateGroupEntity, systemStationMessageTemplateGroup);
                vo.setSystemStationMessageTemplateGroup(systemStationMessageTemplateGroup);
            }
            //查询短信模板信息
            BbsRenewalTemplateGroupEntity mobileMessageTemplateGroupEntity = new LambdaQueryChainWrapper<>(renewalTemplateGroupMapper)
                    .eq(BbsRenewalTemplateGroupEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsRenewalTemplateGroupEntity::getTemplateGroupId, signInfoEntity.getMobileMessageTemplateGroupId())
                    .one();
            if(mobileMessageTemplateGroupEntity != null){
                BbsTemplateGroupVo mobileMessageTemplateGroup = new BbsTemplateGroupVo();
                BeanUtils.copyProperties(mobileMessageTemplateGroupEntity, mobileMessageTemplateGroup);
                vo.setMobileMessageTemplateGroup(mobileMessageTemplateGroup);
            }
            //通过签约id查询模板属性信息
            List<BbsRenewalTemplateSeatEntity> templateSeatEntities = new LambdaQueryChainWrapper<>(renewalTemplateSeatMapper)
                    .eq(BbsRenewalTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
//                .eq(BbsTemplateSeatEntity::getIsRequired, "1")
//                .eq(BbsTemplateSeatEntity::getIsShow, "1")
                    .eq(BbsRenewalTemplateSeatEntity::getParentId, signId)
                    .list();
            List<BbsTemplateSeatVo> templateSeatList = new ArrayList<BbsTemplateSeatVo>();
            templateSeatEntities.forEach(item -> {
                BbsTemplateSeatVo templateSeatVo = new BbsTemplateSeatVo();
                BeanUtils.copyProperties(item, templateSeatVo);
                templateSeatList.add(templateSeatVo);
            });
            vo.setTemplateSeatList(templateSeatList);//模板属性信息
        }

        return vo;
    }

    /**
     * 产品扩展信息外接json拼接
     * @param signInfoEntity
     * @return
     */
    public String assembleProductExtend(BbsResultProductEntity item, BbsSignInfoEntity signInfoEntity, int listSize, int no){
        JSONObject result = new JSONObject();
        JSONArray chargeSubjectList = new JSONArray();

        JSONObject zujin = new JSONObject();//租金
        zujin.put("chargeSubjectNo", "01");//计费科目编号 01 房屋租金 02 押金 07 物业费
        zujin.put("cyclicOrSingle", "01");//循环计费或单次计费 01 循环02 单次
        //zujin.put("chargeSubjectAmount", signInfoByLwqServiceImpl.getChargeSubjectAmount("租金",item,signInfoEntity));//金额
        //zujin.put("chargeSubjectAmount", BigDecimal.valueOf(Double.valueOf(0.00)));//金额
        zujin.put("amountType", "0"+item.getRentStandardUnit());//金额类型
        zujin.put("taxRate", BigDecimal.valueOf(signInfoEntity.getRentTaxRate()).divide(BigDecimal.valueOf(100)));//税率
        if("1".equals(signInfoEntity.getRentIncrementalFlag())){
            JSONArray rentIncreaseRules = getSignIncrementalConfigList(signInfoEntity.getSignId(), "rent", signInfoEntity.getPaymentCycleCode());
            zujin.put("increaseRules", rentIncreaseRules);//递增规则
        }else{
            zujin.put("increaseRules", new JSONArray());//递增规则
        }
        zujin.put("preferentRules", JSONArray.parseArray(JSON.toJSONString(getSignFpIntervalList(signInfoEntity, "rent"))));//优惠规则
        Map paramValueZujin = new HashMap();
        paramValueZujin.put("PARAMPRICE", String.valueOf(item.getRentStandard()));//单价
        if(AreaTypeEnum.BUILD_AREA_TYPE.getCode().equals(signInfoEntity.getAreaType())) {
            paramValueZujin.put("PARAMAREA", item.getHouseStructArea());//建筑面积
        }else{
            paramValueZujin.put("PARAMAREA", item.getInnerSleeveArea());//套内建筑面积
        }
        zujin.put("paramValueList", paramValueZujin);
        chargeSubjectList.add(zujin);

        boolean yajinFlag=true;
        if("9".equals(signInfoEntity.getCashPledgeCode())&&(StringUtils.isEmpty(signInfoEntity.getCashPledgeValue())||Double.parseDouble(signInfoEntity.getCashPledgeValue())==0d)){
            yajinFlag=false;
        }
        JSONObject yajin = new JSONObject();//押金
        yajin.put("chargeSubjectNo", "02");//计费科目编号 01 房屋租金 02 押金 07 物业费
        yajin.put("cyclicOrSingle", "02");//循环计费或单次计费 01 循环02 单次
        //yajin.put("chargeSubjectAmount", "9".equals(signInfoEntity.getCashPledgeCode())?new BigDecimal(signInfoEntity.getCashPledgeValue()):signInfoByLwqServiceImpl.getChargeSubjectAmount("租金",item,signInfoEntity));//金额
        yajin.put("chargeSubjectAmount", "9".equals(signInfoEntity.getCashPledgeCode()) ? calculateDeposit(signInfoEntity.getCashPledgeValue(), listSize, no) : BigDecimal.valueOf(Double.valueOf(0.00)));//金额
        yajin.put("depositProportion", "9".equals(signInfoEntity.getCashPledgeCode()) ? null : Integer.parseInt(signInfoEntity.getCashPledgeCode()));
        yajin.put("taxRate", BigDecimal.valueOf(Double.valueOf(0.00)));//税率
        Map paramValueYajin = new HashMap();
        paramValueYajin.put("PARAMPRICE", String.valueOf(item.getRentStandard()));//单价
        //paramValueYajin.put("PARAMAREA", item.getHouseStructArea());//面积

        if(AreaTypeEnum.BUILD_AREA_TYPE.getCode().equals(signInfoEntity.getAreaType())) {
            paramValueYajin.put("PARAMAREA", item.getHouseStructArea());//建筑面积
        }else{
            paramValueYajin.put("PARAMAREA", item.getInnerSleeveArea());//套内建筑面积
        }
        yajin.put("paramValueList", paramValueYajin);
        if(yajinFlag){
            chargeSubjectList.add(yajin);
        }

        if("02-08".equals(signInfoEntity.getContractFees())){
            JSONObject woyefei = new JSONObject();//物业费
            woyefei.put("chargeSubjectNo", "07");//计费科目编号 01 房屋租金 02 押金 07 物业费
            woyefei.put("cyclicOrSingle", "01");//循环计费或单次计费 01 循环02 单次
            //woyefei.put("chargeSubjectAmount", signInfoByLwqServiceImpl.getChargeSubjectAmount("物业费",item,signInfoEntity));//金额
            //woyefei.put("chargeSubjectAmount", BigDecimal.valueOf(Double.valueOf(0.00)));//金额
            woyefei.put("amountType", "0"+item.getPropStandardUnit());//金额类型
            woyefei.put("taxRate", BigDecimal.valueOf(signInfoEntity.getPropTaxRate()).divide(BigDecimal.valueOf(100)));//税率
            if("1".equals(signInfoEntity.getPropIncrementalFlag())){
                JSONArray propIncreaseRules = getSignIncrementalConfigList(signInfoEntity.getSignId(), "prop", signInfoEntity.getPaymentCycleCode());
                woyefei.put("increaseRules", propIncreaseRules);//递增规则
            }else{
                woyefei.put("increaseRules", new JSONArray());//递增规则
            }
            woyefei.put("preferentRules", JSONArray.parseArray(JSON.toJSONString(getSignFpIntervalList(signInfoEntity, "prop"))));//优惠规则
            Map paramValueWuyefei = new HashMap();
            paramValueWuyefei.put("PARAMPRICE", String.valueOf(item.getPropStandard()));//单价
            //paramValueWuyefei.put("PARAMAREA", item.getHouseStructArea());//面积

            if(AreaTypeEnum.BUILD_AREA_TYPE.getCode().equals(signInfoEntity.getAreaType())) {
                paramValueWuyefei.put("PARAMAREA", item.getHouseStructArea());//建筑面积
            }else{
                paramValueWuyefei.put("PARAMAREA", item.getInnerSleeveArea());//套内建筑面积
            }
            woyefei.put("paramValueList", paramValueWuyefei);
            chargeSubjectList.add(woyefei);
        }

        result.put("chargeSubjectList", chargeSubjectList);

        //2024-11-15增加签约房间类型（房间取得用途）
        result.put("roomType",item.getHouseObtainPurposeCode());
        List<BusinessFormatVo> businessFormatVos = DictTreeToBusinessFormatUtil.jsonToBusinessFormatVoList(signInfoEntity.getBusinessFormat());
        String secBusinessType = businessFormatVos.stream().map(businessFormatVo -> businessFormatVo.getKey().length() > 1 ? businessFormatVo.getKey() : "0" + businessFormatVo.getKey()).collect(Collectors.joining(SymbolConst.COMMA));
        result.put("businessFormat", secBusinessType);
        return result.toJSONString();
    }

    /**
     * 计算多个房源下各个房源分配的押金金额
     * @param cashPledgeValue
     * @param listSize
     * @param no
     * @return
     */
//    public static BigDecimal calculateDeposit(String cashPledgeValue, int listSize, int no){
//        //格式化对象
//        DecimalFormat decimalFormat = new DecimalFormat("0.00");
//        Double totalAmount = Double.valueOf(cashPledgeValue);
//        double result = totalAmount / listSize;
//        if(totalAmount % listSize == 0){//整除
//            return new BigDecimal(decimalFormat.format(result));
//        }else{
//            if(no < listSize){
//                return new BigDecimal(decimalFormat.format(Math.floor(result)));//向下取整
//            }else{
//                return new BigDecimal(decimalFormat.format(Math.ceil(result)));//向上取整
//            }
//        }
//    }

    public static BigDecimal calculateDeposit(String cashPledgeValue, int listSize, int no){
        //格式化对象
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        Double totalAmount = Double.valueOf(cashPledgeValue);
        BigDecimal result = null;
        if(listSize == 1){ //只有一个房源
            result = new BigDecimal(decimalFormat.format(totalAmount));
        }else if(no == listSize) { //最后一个房源
            double average = Double.parseDouble(decimalFormat.format(totalAmount / listSize));
            result = new BigDecimal(decimalFormat.format(totalAmount - (average * (listSize - 1))));
        }else if(no < listSize) { // 非最后一个房源
            double average = totalAmount / listSize;
            result = new BigDecimal(decimalFormat.format(average));
        }else{
            throw new McpException("无效的自定义押金");
        }
        return result;
    }

    public static void main(String[] args) {
        String fileName = StringUtils.isNotEmpty("1.pdg")?"1.pdg".substring(0,"1.pdg".lastIndexOf(".")):"";
        System.out.println(fileName);
        System.out.println(calculateDeposit("900",3, 1));
        System.out.println(calculateDeposit("900",3, 2));
        System.out.println(calculateDeposit("900",3, 3));
    }
    /**
     * 通过签约id查询递增设置信息
     * @param signId
     * @param standardType
     * @param paymentCycleCode
     * @return
     */
    public JSONArray getSignIncrementalConfigList(String signId, String standardType, String paymentCycleCode){
        List<BbsSignIncrementalConfigEntity> signIncrementalConfigEntities = new LambdaQueryChainWrapper<>(signIncrementalConfigMapper)
                .eq(BbsSignIncrementalConfigEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsSignIncrementalConfigEntity::getStandardType, standardType)
                .eq(BbsSignIncrementalConfigEntity::getSignInfoId, signId)
                .list();
        JSONArray increaseRules = new JSONArray();
        signIncrementalConfigEntities.forEach(item -> {
            JSONObject increaseRule = new JSONObject();
            increaseRule.put("increaseRuleId", "");//递增规则ID
            increaseRule.put("increaseProportion", BigDecimal.valueOf(item.getIncrease()).divide(BigDecimal.valueOf(100)));//递增比例除以100
            increaseRule.put("increaseAmount", "");//递增金额
            String increasePeriod = "";
            if("year".equals(item.getUnit())){
                increasePeriod = "04";
            }
            else if("month".equals(item.getUnit())){
                increasePeriod = "01";
            }
            increaseRule.put("increasePeriod", increasePeriod);//递增周期 unit 04
            increaseRule.put("increaseOrder", item.getTimePoint());//递增顺序
            String increaseType = "";
            if("appoint".equals(item.getAdjustmentPoint())){
                increaseType = "02";
            }else{
                increaseType = "01";
            }
            increaseRule.put("increaseType", increaseType);//递增类型
            increaseRules.add(increaseRule);
        });
        return increaseRules;
    }

    /**
     * 通过签约id查询免租期信息
     * @param signInfoEntity
     * @param standardType
     * @return
     */
    public List<PreferentRules> getSignFpIntervalList(BbsSignInfoEntity signInfoEntity, String standardType){

        List<PreferentRules> resultList = new ArrayList<>();
        if ("rent".equals(standardType)) {
            if ("0".equals(signInfoEntity.getRentFreePeriodType())) {
                return resultList;
            } else if ("2".equals(signInfoEntity.getRentFreePeriodType())) {
                PreferentRules preferentRules = new PreferentRules();
                preferentRules.setPreferentRuleId(1);
                preferentRules.setPreferentialType("01");//免租
                preferentRules.setPreferentialBeginDate(signInfoEntity.getRentFpFixedDate());
                preferentRules.setPreferentialEndDate(DateUtils.getDateAddDaySubtraction(signInfoEntity.getRentFpFixedDate(), signInfoEntity.getRentFpFixedValue()));
                resultList.add(preferentRules);
            } else if ("1".equals(signInfoEntity.getRentFreePeriodType())) {
                List<BbsSignFpIntervalEntity> fpIntervalEntities = new LambdaQueryChainWrapper<>(signFpIntervalMapper)
                        .eq(BbsSignFpIntervalEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                        .eq(BbsSignFpIntervalEntity::getSignInfoId, signInfoEntity.getSignId())
                        .eq(BbsSignFpIntervalEntity::getStandardType, "rent")
                        .orderByAsc(BbsSignFpIntervalEntity::getCreateTime)
                        .list();
                AtomicInteger count = new AtomicInteger(1);
                fpIntervalEntities.forEach(fpIntervalEntity -> {
                    PreferentRules preferentRules = new PreferentRules();
                    preferentRules.setPreferentRuleId(count.getAndIncrement());
                    preferentRules.setPreferentialType("01");//免租
                    preferentRules.setPreferentialBeginDate(fpIntervalEntity.getStart());
                    preferentRules.setPreferentialEndDate(fpIntervalEntity.getEnd());
                    resultList.add(preferentRules);
                });
            }
        } else if ("prop".equals(standardType)) {
            if ("0".equals(signInfoEntity.getPropFreePeriodType())) {
                return resultList;
            } else if ("2".equals(signInfoEntity.getPropFreePeriodType())) {
                PreferentRules preferentRules = new PreferentRules();
                preferentRules.setPreferentRuleId(1);
                preferentRules.setPreferentialType("01");//免租
                preferentRules.setPreferentialBeginDate(signInfoEntity.getPropFpFixedDate());
                preferentRules.setPreferentialEndDate(DateUtils.getDateAddDaySubtraction(signInfoEntity.getPropFpFixedDate(), signInfoEntity.getPropFpFixedValue()));
                resultList.add(preferentRules);
            } else if ("1".equals(signInfoEntity.getPropFreePeriodType())) {
                List<BbsSignFpIntervalEntity> fpIntervalEntities = new LambdaQueryChainWrapper<>(signFpIntervalMapper)
                        .eq(BbsSignFpIntervalEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                        .eq(BbsSignFpIntervalEntity::getSignInfoId, signInfoEntity.getSignId())
                        .eq(BbsSignFpIntervalEntity::getStandardType, "prop")
                        .orderByAsc(BbsSignFpIntervalEntity::getCreateTime)
                        .list();
                AtomicInteger count = new AtomicInteger(1);
                fpIntervalEntities.forEach(fpIntervalEntity -> {
                    PreferentRules preferentRules = new PreferentRules();
                    preferentRules.setPreferentRuleId(count.getAndIncrement());
                    preferentRules.setPreferentialType("01");//免租
                    preferentRules.setPreferentialBeginDate(fpIntervalEntity.getStart());
                    preferentRules.setPreferentialEndDate(fpIntervalEntity.getEnd());
                    resultList.add(preferentRules);
                });
            }
        }
        return resultList;
    }
    /**
     * 通过签约id查询免租期信息
     * @param signInfoEntity
     * @param standardType
     * @return
     */
    public List<PreferentRules> getSignFpIntervalList(BbsRenewalInfoEntity signInfoEntity, String standardType){
        List<PreferentRules> resultList = new ArrayList<>();
        if ("rent".equals(standardType)) {
            if ("0".equals(signInfoEntity.getRentFreePeriodType())) {
                return resultList;
            } else if ("2".equals(signInfoEntity.getRentFreePeriodType())) {
                PreferentRules preferentRules = new PreferentRules();
                preferentRules.setPreferentRuleId(1);
                preferentRules.setPreferentialType("01");//免租
                preferentRules.setPreferentialBeginDate(signInfoEntity.getRentFpFixedDate());
                preferentRules.setPreferentialEndDate(DateUtils.getDateAddDaySubtraction(signInfoEntity.getRentFpFixedDate(), signInfoEntity.getRentFpFixedValue()));
                resultList.add(preferentRules);
            } else if ("1".equals(signInfoEntity.getRentFreePeriodType())) {
                List<BbsRenewalFpIntervalEntity> fpIntervalEntities = new LambdaQueryChainWrapper<>(renewalFpIntervalMapper)
                        .eq(BbsRenewalFpIntervalEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                        .eq(BbsRenewalFpIntervalEntity::getSignInfoId, signInfoEntity.getSignId())
                        .eq(BbsRenewalFpIntervalEntity::getStandardType, "rent")
                        .orderByAsc(BbsRenewalFpIntervalEntity::getCreateTime)
                        .list();
                AtomicInteger count = new AtomicInteger(1);
                fpIntervalEntities.forEach(fpIntervalEntity -> {
                    PreferentRules preferentRules = new PreferentRules();
                    preferentRules.setPreferentRuleId(count.getAndIncrement());
                    preferentRules.setPreferentialType("01");//免租
                    preferentRules.setPreferentialBeginDate(fpIntervalEntity.getStart());
                    preferentRules.setPreferentialEndDate(fpIntervalEntity.getEnd());
                    resultList.add(preferentRules);
                });
            }
        } else if ("prop".equals(standardType)) {
            if ("0".equals(signInfoEntity.getPropFreePeriodType())) {
                return resultList;
            } else if ("2".equals(signInfoEntity.getPropFreePeriodType())) {
                PreferentRules preferentRules = new PreferentRules();
                preferentRules.setPreferentRuleId(1);
                preferentRules.setPreferentialType("01");//免租
                preferentRules.setPreferentialBeginDate(signInfoEntity.getPropFpFixedDate());
                preferentRules.setPreferentialEndDate(DateUtils.getDateAddDaySubtraction(signInfoEntity.getPropFpFixedDate(), signInfoEntity.getPropFpFixedValue()));
                resultList.add(preferentRules);
            } else if ("1".equals(signInfoEntity.getPropFreePeriodType())) {
                List<BbsRenewalFpIntervalEntity> fpIntervalEntities = new LambdaQueryChainWrapper<>(renewalFpIntervalMapper)
                        .eq(BbsRenewalFpIntervalEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                        .eq(BbsRenewalFpIntervalEntity::getSignInfoId, signInfoEntity.getSignId())
                        .eq(BbsRenewalFpIntervalEntity::getStandardType, "prop")
                        .orderByAsc(BbsRenewalFpIntervalEntity::getCreateTime)
                        .list();
                AtomicInteger count = new AtomicInteger(1);
                fpIntervalEntities.forEach(fpIntervalEntity -> {
                    PreferentRules preferentRules = new PreferentRules();
                    preferentRules.setPreferentRuleId(count.getAndIncrement());
                    preferentRules.setPreferentialType("01");//免租
                    preferentRules.setPreferentialBeginDate(fpIntervalEntity.getStart());
                    preferentRules.setPreferentialEndDate(fpIntervalEntity.getEnd());
                    resultList.add(preferentRules);
                });
            }
        }
        return resultList;
    }

    @Override
    public AppReply<List<TreeVo>> selectTree(TreeParamVo vo) {
        vo.setProductBussinessType1("02");
        log.info("合同小区tree查询参数：{}", JSON.toJSONString(vo));
        AppReply<List<TreeVo>> listAppReply = bbctSigningFeignCilent.selectTree(vo);
        log.info("合同小区tree查询结果：{}", JSON.toJSONString(listAppReply));
        setInfo(listAppReply.getData());
        return listAppReply;
    }

    protected void setInfo(List<TreeVo> treeVos) {
        //循环treeVos 在第三级title后面增加"号楼" 第四级后面增加"单元"
        for (TreeVo vo : treeVos) {
            if (vo.getChildren() != null && vo.getChildren().size() > 0) {
                for (TreeVo treeVo : vo.getChildren()) {
                    if (treeVo.getChildren() != null && treeVo.getChildren().size() > 0) {
                        for (TreeVo treeVo1 : treeVo.getChildren()) {
                            if (treeVo1.getChildren() != null && treeVo1.getChildren().size() > 0) {
                                treeVo1.setTitle(treeVo1.getTitle() + " 号楼");
                                for (TreeVo treeVo2 : treeVo1.getChildren()) {
                                    treeVo2.setTitle(treeVo2.getTitle() + " 单元");
                                }
                            }else{
                                treeVo.setTitle(treeVo.getTitle()+" 号楼");
                                treeVo1.setTitle(treeVo1.getTitle() + " 单元");
                            }
                        }
                    }
                }
            }
        }
    }
    @Override
    public AppReply<BbctContractStatusNumVo> selectContractStatusNum(String contractTypeCode) {
        contractTypeCode = null;
        return bbctSigningFeignCilent.selectContractStatusNum(contractTypeCode, businessTypeCode1);
    }

    @Override
    public AppReply<PageResult<List<BbctContractManagementPageResultVo>>> findContractByPage(BbctContractManagementPageVo vo) {
        vo.setBusinessTypeCode1(businessTypeCode1);
        //vo.setIsProject("1"); // 不卡数据权限
        log.info("合同列表查询参数：{}", vo.toString());
        AppReply<PageResult<List<BbctContractManagementPageResultVo>>> appReply = bbctSigningFeignCilent.selectByPageCommon(vo);
        log.info("合同列表签约系统返回：{}", JSON.toJSONString(appReply));
        PageResult<List<BbctContractManagementPageResultVo>> data = appReply.getData();
        List<BbctContractManagementPageResultVo> rows = data.getRows();
        List<BbsRenewalInfoEntity> bbsRenewalInfoEntitys = new LambdaQueryChainWrapper<>(renewalInfoMapper)
                .eq(BbsRenewalInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .in(BbsRenewalInfoEntity::getParentContractCode, rows.stream().map(m->m.getContractNo()).collect(Collectors.toList()))
                .list();
        Map<String,String> bbsRenewalInfoExistsMap=new HashMap<>();
        for(BbsRenewalInfoEntity tempBbsRenewalInfoEntity:bbsRenewalInfoEntitys){
            bbsRenewalInfoExistsMap.put(tempBbsRenewalInfoEntity.getParentContractCode(),"1");
        }
        
        // 批量查询有进行中变更的合同编号
        // 包括：1.状态为0暂存、2待审核或3未通过的变更 2.change_status=1且关联的bbs_sign_info中sign_status!=3的变更
        List<String> contractCodesWithOngoingChanges = bbsiContractChangeMapper.selectContractsWithOngoingChanges(
                rows.stream().map(BbctContractManagementPageResultVo::getContractNo).collect(Collectors.toList()));
        Set<String> ongoingChangesSet = new HashSet<>(contractCodesWithOngoingChanges);
        for(BbctContractManagementPageResultVo contractManagementPageResultVo: rows){
            List<BbctContractSignerVo> signerVos = contractManagementPageResultVo.getSignerVos();
            for(BbctContractSignerVo contractSignerVo: signerVos){
                BbsDictEntity dctEntity = new LambdaQueryChainWrapper<>(dictMapper)
                        .eq(BbsDictEntity::getTypeCode, "CERTIFICATE_TYPE")
                        .eq(BbsDictEntity::getCode, contractSignerVo.getCustomerIdType())
                        .one();
                contractSignerVo.setCustomerIdTypeName(null == dctEntity ? "" : dctEntity.getMeaning());
            }
            contractManagementPageResultVo.setSignerVos(signerVos);
            if(contractManagementPageResultVo.getSubjectMatterVos() != null && contractManagementPageResultVo.getSubjectMatterVos().size() > 0){
                contractManagementPageResultVo.setProjectId(contractManagementPageResultVo.getSubjectMatterVos().get(0).getProjectId());

                //合同中心没返回这个小区名称，先这么写试试
                contractManagementPageResultVo.setCommunityBuildingName(contractManagementPageResultVo.getSubjectMatterVos().get(0).getCommunityBuildingName());
            }
            contractManagementPageResultVo.setIsRenewal(StringUtils.isEmpty(bbsRenewalInfoExistsMap.get(contractManagementPageResultVo.getContractNo()))?"0":"1");
            
            // 设置是否可以进行合同变更：如果合同有进行中的变更，则不能再次变更
            contractManagementPageResultVo.setIsChangeable(ongoingChangesSet.contains(contractManagementPageResultVo.getContractNo()) ? "0" : "1");

//            contractManagementPageResultVo.setBcxyCount(String.valueOf(contractChangeService.selectAgreementCount(contractManagementPageResultVo.getContractNo())));
            // 查询合同的协议数据
            BbctContractManagementPageVo contractManagementPageVo = new BbctContractManagementPageVo();
            contractManagementPageVo.setParentContractCode(contractManagementPageResultVo.getContractNo());
            contractManagementPageVo.setPageSize(10);
            contractManagementPageVo.setPageNumber(1);
            PageResult<List<BbctContractManagementPageResultVo>> pageResult = getContractManagementPageResultVos(contractManagementPageVo);
            Integer count  = 0;
            if (pageResult != null){
                count = pageResult.getTotal();
            }
            contractManagementPageResultVo.setBcxyCount(count+"");
            // 计租面积字段增加翻译内容
            if (AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(contractManagementPageResultVo.getAreaType())) {
                contractManagementPageResultVo.setTotalArea(SymbolUtil.addBracket(contractManagementPageResultVo.getTotalArea(),
                        AreaTypeEnum.INNER_AREA_TYPE.getDesc()));
            } else {
                contractManagementPageResultVo.setTotalArea(SymbolUtil.addBracket(contractManagementPageResultVo.getTotalArea(),
                        AreaTypeEnum.BUILD_AREA_TYPE.getDesc()));
            }
        }
        data.setRows(rows);
        appReply.setData(data);
        return appReply;
    }

    @Override
    public void contractBatchExport(HttpServletResponse response, BbctContractManagementPageVo vo) throws Exception {
        vo.setBusinessTypeCode1(businessTypeCode1);
        log.info("合同列表导出查询参数：{}", JSON.toJSONString(vo));
        AppReply<List<BbctContractManagementPageResultVo>> listAppReply = bbctContractFeignClient.selectExportList(vo);
        log.info("合同列表导出查询到记录数：{}", listAppReply.getData().size());
        downloadContractExcel(response, listAppReply.getData());
    }

    @Override
    public void exportContractList(BbctContractManagementPageVo vo) {
        bbctContractFeignClient.exportContractList(vo);
    }

    public void downloadContractExcel(HttpServletResponse response, List<BbctContractManagementPageResultVo> list) throws Exception{
        List<String> headerList = getHeaderList(new BbctContractManagementPageResultVo());
        ExportExcel ee = new ExportExcel("合同批量导出", headerList);
        for(BbctContractManagementPageResultVo vo: list){
            Row row = ee.addRow();
            List<BbctContractSignerVo> signerVos = vo.getSignerVos();
            String customerType = "";
            String customerName = "";
            String consignorName = "";
            String customerTel = "";
            String consignorMobile= "";
            String customerCreditCode = "";
            for(int i = 0; i< signerVos.size(); i++){
                BbctContractSignerVo signerVo = signerVos.get(i);
                if(i < signerVos.size()-1){
                    customerType += signerVo.getCustomerType()+",";
                    customerName += signerVo.getCustomerName()+",";
                    consignorName += signerVo.getConsignorName()+",";
                    customerTel += signerVo.getCustomerTel()+",";
                    consignorMobile+= signerVo.getConsignorMobile()+",";
                    customerCreditCode += signerVo.getCustomerCreditCode()+",";
                }else{
                    customerType += signerVo.getCustomerType();
                    customerName += signerVo.getCustomerName();
                    consignorName += signerVo.getConsignorName();
                    customerTel += signerVo.getCustomerTel();
                    consignorMobile+= signerVo.getConsignorMobile();
                    customerCreditCode += signerVo.getCustomerCreditCode();
                }
            }
            List<BbctContractSubjectMatterVo> subjectMatterVos = vo.getSubjectMatterVos();
            List<String> communityBuildingName = new ArrayList<>();
            List<String> productName = new ArrayList<>();
            BigDecimal leaseTermRent = new BigDecimal("0.0");
            boolean hasLeaseTermRent = false;
            for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterVos) {
                communityBuildingName.add(subjectMatterVo.getCommunityBuildingName());
                productName.add(subjectMatterVo.getProductName());
                if (ObjectUtils.isNotEmpty(subjectMatterVo.getLeaseTermRent())) {
                    leaseTermRent = leaseTermRent.add(BigDecimal.valueOf(subjectMatterVo.getLeaseTermRent()));
                    hasLeaseTermRent = true;
                }
            }
            // 计租面积字段增加翻译内容
            String area;
            if (AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(vo.getAreaType())) {
                area = SymbolUtil.addBracket(vo.getTotalArea(), AreaTypeEnum.INNER_AREA_TYPE.getDesc());
            } else {
                area = SymbolUtil.addBracket(vo.getTotalArea(), AreaTypeEnum.BUILD_AREA_TYPE.getDesc());
            }
            ee.addCell(row, 0, stringConvert(communityBuildingName.stream().distinct().collect(Collectors.joining(","))));//小区或楼宇名称
            ee.addCell(row, 1, stringConvert(productName.stream().distinct().collect(Collectors.joining(","))));//房源地址
            ee.addCell(row, 2, stringConvert("00".equals(customerType) ? "个人" : "企业"));//租户类型
            ee.addCell(row, 3, stringConvert(customerName));//承租人/企业名称
            ee.addCell(row, 4, stringConvert(consignorName));//企业联系人
            ee.addCell(row, 5, stringConvert("00".equals(customerType) ?customerTel:consignorMobile));//承租人/企业联系电话
            ee.addCell(row, 6, stringConvert(customerCreditCode));//统一社会信用代码
            ee.addCell(row, 7, DateUtils.formatDate(vo.getContractBeginTime(), "yyyy-MM-dd")+"~"+DateUtils.formatDate(vo.getContractEndTime(), "yyyy-MM-dd"));//租赁期限
            ee.addCell(row, 8, stringConvert(vo.getContractStatusName()));//合同状态
            ee.addCell(row, 9, stringConvert(area));//租赁面积（m²）
            ee.addCell(row, 10, hasLeaseTermRent ? leaseTermRent.stripTrailingZeros().toPlainString() : "");//租金（元/月）
            ee.addCell(row, 11, stringConvert(vo.getPaymentCycleName()));//缴费周期
            ee.addCell(row, 12, !"null".equals(vo.getSignTime()) && vo.getSignTime() != null ? DateUtils.formatDate(vo.getSignTime(), "yyyy-MM-dd"): "");//签约时间
            ee.addCell(row, 13, stringConvert(vo.getContractNo()));//合同编号
            ee.addCell(row, 14, stringConvert(vo.getIsOverdue()));//是否欠费
            ee.addCell(row, 15, stringConvert(StringUtils.isEmpty(vo.getBcxyCount()) ? "0" : vo.getBcxyCount()));//补充协议数量
            ee.addCell(row, 16, stringConvert(String.valueOf(vo.getChangeNum())));//变更次数
        }
        String filePath = exportFilePath+"f_"+IdUtil.fastSimpleUUID()+".xlsx";
        ee.writeFile(filePath);
        ee.dispose();
        log.info("==========导出文件路径:{}", filePath);
        String fileName = "合同列表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
        download(response, filePath,fileName);
    }
    private void download(HttpServletResponse response, String filePath,String fileName){
        Path.checkPathAndExt(filePath);
        File file = new File(filePath);
        InputStream fis;
        try {
            fis = new FileInputStream(file);
            response.reset();
            response.setCharacterEncoding("UTF-8");
            //response.setContentType("application/force-download");// 设置强制下载不打开
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + new String(fileName.getBytes("utf-8"), "iso8859-1"));
            response.setHeader("Content-Length", String.valueOf(file.length()));

            byte[] b = new byte[1024];
            int len;
            while ((len = fis.read(b)) != -1) {
                response.getOutputStream().write(b, 0, len);
            }
            response.flushBuffer();
            fis.close();
        }catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public String stringConvert(String str){
        return str != null && !"null".equals(str) ? str : "";
    }

    /**
     * 从实体对象中获取获取excel表头
     * @param object
     * @return
     */
    public static List<String> getHeaderList(BbctContractManagementPageResultVo object){
        Field[] fields = object.getClass().getDeclaredFields();
        List<String> headerList = new ArrayList<String>();
        for (Field field : fields) {
            // 是否引用ApiModelProperty注解
            boolean bool = field.isAnnotationPresent(ApiModelProperty.class);
            if (bool) {
                String name = field.getAnnotation(ApiModelProperty.class).name();
                if(StringUtils.isNotBlank(name)){
                    headerList.add(name);
                }
            }
        }
        return headerList;
    }

    @Override
    public AppReply<Boolean> downloadContractForEmail(EmailDownloadVo vo) {
        return bbctSigningFeignCilent.downloadEmail(vo);
    }

    @Override
    public AppReply<Boolean> sendMessageDownContract(BbctMessageDownContractVo vo) {
        return bbctSigningFeignCilent.sendMessageDownContract(vo);
    }

    @Override
    public AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> getBill(BbpmBillManagementPageVo vo) {
        if(vo != null && StringUtils.isBlank(vo.getStatus())){
            //账单状态列表(可多选，使用“,”分隔） 01 正常 02 冻结 03 关闭
            vo.setStatus("01");
        }
        log.info("账单列表查询参数：{}", JSON.toJSONString(vo));
        AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> pageResultAppReply = bbctSigningFeignCilent.selectByPage(vo);
        /*if ("1".equals(pageResultAppReply.getCode()) && null != pageResultAppReply.getData()) {
            PageResult<List<BbpmBillManagementPageResultVo>> data = pageResultAppReply.getData();
            if (null != data.getRows() &&  data.getRows().size() > 0) {
                List<BbpmBillManagementPageResultVo> rows = data.getRows();
                //用billId对rows去重
                List<BbpmBillManagementPageResultVo> collect = rows.stream().collect(Collectors.groupingBy(BbpmBillManagementPageResultVo::getBillId)).values().stream().map(list -> list.get(0)).collect(Collectors.toList());
                pageResultAppReply.setData(new PageResult<>(data.getTotal(), collect));
            }
        }*/
        return pageResultAppReply;
    }

    @Override
    public AppReply<BbpmCashPledgeVo> getFileIdByBillId(String billId, String projectId) {
        BbpmCashPledgeVo vo = new BbpmCashPledgeVo();
        vo.setBillId(billId);
        vo.setProjectId(projectId);
        return bbctSigningFeignCilent.selectByConditions(vo);
    }

    @Override
    public AppReply<String> generateRenewalContract(String signId) {
        BbctContractManagementVo vo = generateRenewalContractVo(signId);
        log.info("合同生成接口调用入参-续租：{}", JSON.toJSONString(vo));
        return bbctContractFeignClient.insertOrUpdate(vo);
    }

    private BbctContractManagementVo generateRenewalContractVo(String signId) {
        BbctContractManagementVo vo = new BbctContractManagementVo();
        //通过签约id查询签约信息
        BbsRenewalInfoEntity signInfoEntity = new LambdaQueryChainWrapper<>(renewalInfoMapper)
                .eq(BbsRenewalInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalInfoEntity::getSignId, signId)
                .one();
        vo.setBusinessFormat(signInfoEntity.getBusinessFormat());
        vo.setBusinessFormatName(signInfoEntity.getBusinessFormatName());
        vo.setBaseRentCalculate("");//基础租金计算方式code
        vo.setBaseRentCalculateName("");//基础租金计算方式名称
        vo.setContractNo(signInfoEntity.getContractCode());//合同编号
        vo.setReletSourceContractCode(signInfoEntity.getParentContractCode());//上级合同编号
        vo.setBusinessId(signId);//业务唯一标识(签约id)
        vo.setBusinessTypeCode1(businessTypeCode1);//业务分类编号1，传02
        vo.setBusinessTypeName1(businessTypeName1);//业务分类名称1，传商业
//        vo.setBusinessTypeCode2(businessTypeCode2);//业务分类编号2，02
//        vo.setBusinessTypeName2(businessTypeName2);//业务分类名称2，趸租
        vo.setCashPledgeCode(signInfoEntity.getCashPledgeCode());//押金标准code
        vo.setContractStatus("2");//合同状态（1已生效、2未生效、3已结束、4终止、5已逾期、6作废）
        vo.setReletSourceContractCode(vo.getParentContractCode());
        vo.setAreaType(signInfoEntity.getAreaType());//租赁面积类型
        vo.setContractType("10");//合同类型(1.普通合同 2.补充合同 3.续租合同 4.调换房合同 5.商业合同 6.家具家电合同  7.主承租人合同变更合同 8.购房补充协议 9.趸租补充协议 10.商业续租合同))
        BbsDictEntity dctEntity = new LambdaQueryChainWrapper<>(dictMapper)
                .eq(BbsDictEntity::getTypeCode, "CASH_PLEDGE_CODE")
                .eq(BbsDictEntity::getCode, signInfoEntity.getCashPledgeCode())
                .one();
        vo.setCashPledgeName(dctEntity.getMeaning());//押金标准名称
        if("9".equals(signInfoEntity.getCashPledgeCode())){
            vo.setDeposit(Double.valueOf(signInfoEntity.getCashPledgeValue()));//押金
        }
        vo.setSignTime(signInfoEntity.getSignTime());//签约日期
        vo.setPaymentCycleCode(signInfoEntity.getPaymentCycleCode());//缴费周期
        BbsDictEntity dctEntity1 = new LambdaQueryChainWrapper<>(dictMapper)
                .eq(BbsDictEntity::getTypeCode, "PAYMENT_CYCLE_CODE")
                .eq(BbsDictEntity::getCode, signInfoEntity.getPaymentCycleCode())
                .one();
        vo.setPaymentCycleName(dctEntity1.getMeaning());//缴费周期名称
        //如果押金标准code是自定义

        //通过签约id查询合同属性信息
        Map<String, Object> contractAttr = new LambdaQueryChainWrapper<>(renewalTemplateSeatMapper)
                .eq(BbsRenewalTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalTemplateSeatEntity::getParentId, signId)
//                .eq(BbsRenewalTemplateSeatEntity::getIsRequired, "1")
//                .eq(BbsRenewalTemplateSeatEntity::getIsShow, "1")
                .list().stream().collect(Collectors.toMap(BbsRenewalTemplateSeatEntity::getSeatKey, b->StringUtils.isNotBlank(b.getValue()) ? b.getValue() : b.getDefaultVal()));
        vo.setContractAttr(contractAttr);
//        vo.setFirstName(String.valueOf(contractAttr.get("lessorName")));

        vo.setContractBeginTime(signInfoEntity.getContractBeginTime());//合同开始日期
        vo.setContractEndTime(signInfoEntity.getContractEndTime());//合同结束日期
        vo.setContractTemplateId(signInfoEntity.getContractTemplateId());//合同模板id
        vo.setContractTypeCode(contractTypeCode);

        BbsRenewalRelationEntity relationEntity = new LambdaQueryChainWrapper<>(renewalRelationMapper)
                .eq(BbsRenewalRelationEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalRelationEntity::getSignInfoId, signId)
                .one();
        //查询产品相关信息
        List<BbsRenewalProductEntity> productEntities = new LambdaQueryChainWrapper<>(renewalProductMapper)
                .eq(BbsRenewalProductEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalProductEntity::getRrId, relationEntity.getRrId())
                .list();
        List<BbctContractSubjectMatterVo> subjectMatterList = new ArrayList<>();

        productEntities.forEach(item -> {
            BbctContractSubjectMatterVo matterVo = new BbctContractSubjectMatterVo();
            BeanUtils.copyProperties(item, matterVo);
            matterVo.setLeaseTermRent(item.getRent());//租金
            matterVo.setRent(item.getRentStandard()+"");
            matterVo.setRentUnit(item.getRentStandardUnitName());
            matterVo.setRentStandardNo(item.getRentStandardUnit());
            matterVo.setRentStandardName(item.getRentStandardUnitName());
            BbsResultProductEntity product = new BbsResultProductEntity();
            BeanUtils.copyProperties(item,product);
            matterVo.setProductExtend(this.createProductExtend(product));//扩展属性
            subjectMatterList.add(matterVo);
        });
        vo.setSubjectMatterList(subjectMatterList);//产品信息
        BigDecimal  houseStructArea = new BigDecimal(0.0);
        for(BbctContractSubjectMatterVo contractSubjectMatterVo: subjectMatterList){
//            houseStructArea += Double.valueOf(contractSubjectMatterVo.getHouseStructArea());
            if(AreaTypeEnum.BUILD_AREA_TYPE.getCode().equals(signInfoEntity.getAreaType())) {
                houseStructArea = houseStructArea.add(new BigDecimal(contractSubjectMatterVo.getHouseStructArea()));
            }else {
                houseStructArea = houseStructArea.add(new BigDecimal(contractSubjectMatterVo.getInnerSleeveArea()));
            }
        }
        houseStructArea=houseStructArea .setScale(2, RoundingMode.HALF_UP);
        vo.setTotalArea(houseStructArea.toPlainString());//总租赁面积
        vo.setTotalSets(productEntities.size());//签约房源套数
        //查询客户相关信息
        BbsRenewalCustomerEntity customerEntity = new LambdaQueryChainWrapper<>(renewalCustomerMapper)
                .eq(BbsRenewalCustomerEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalCustomerEntity::getRrId, relationEntity.getRrId())
                .one();
        List<BbctContractSignerVo> userList = new ArrayList<>();
        BbctContractSignerVo signerVo = new BbctContractSignerVo();
        BeanUtils.copyProperties(customerEntity, signerVo);
        signerVo.setCustomerTypeName("00".equals(customerEntity.getCustomerType()) ? "个人" : "企业");//租户类型名称
        if ("01".equals(customerEntity.getCustomerType())) {//企业才有代理人
            signerVo.setConsignorName(customerEntity.getContactName());
            signerVo.setConsignorMobile(customerEntity.getCustomerTel());
        }
        userList.add(signerVo);
        vo.setUserList(userList);//合同参与者信息
        vo.setTenantSupplierNo(customerEntity.getTenantSupplierNo());//租户客商编号
        vo.setTenantSupplierName(customerEntity.getTenantSupplierName());//租户客商名称
        String deptName = CurrentUtil.getDepartmentInfo()==null?"":CurrentUtil.getDepartmentInfo().getDepartmentName();
        Map<String, Object> stringObjectMap = null;
        try {
            stringObjectMap = jdbcTemplate.queryForMap(" SELECT dept_short_form FROM bzf_dept_sort_form where dept_name='"+deptName+"' ");
        } catch (DataAccessException e) {
        }
        if(stringObjectMap==null||stringObjectMap.get("dept_short_form")==null||StringUtils.isEmpty(stringObjectMap.get("dept_short_form").toString())){
            vo.setCompanyAbbr("YX");
        }else{
            vo.setCompanyAbbr(String.valueOf(stringObjectMap.get("dept_short_form")));//公司首写字母
        }
        vo.setModeId(modeId);//合同类型
        if(productEntities.size() > 0){
            String projectId = productEntities.get(0).getProjectId();
            Map<String, Object> stringObjectMap1 = null;
            try {
                stringObjectMap1 = jdbcTemplate.queryForMap(" SELECT project_short_form FROM bzf_project_sort_form where project_id='" + projectId + "' ");
            } catch (DataAccessException e) {
            }
            if(stringObjectMap1==null||stringObjectMap1.get("project_short_form")==null||StringUtils.isEmpty(stringObjectMap1.get("project_short_form").toString())){
                String projectName=filterProjectName(productEntities.get(0).getProjectName());
                if(StrUtil.emptyToDefault(projectName,"").length()>=2){
                    vo.setProjectCode(String.valueOf(PinyinUtil.convertHanzi2PinyinShouZhiMu(projectName.substring(0,2))).toUpperCase());
                }else{
                    vo.setProjectCode("WZ");
                }
            }else{
                vo.setProjectCode(String.valueOf(stringObjectMap1.get("project_short_form")));//项目简称
            }

        }
        vo.setSuffix(suffix);//后缀（生成商业合同编号中，00为原合同，01开始为本合同的补充协议）
        vo.setPdfFileId(signInfoEntity.getContractUrl());//文件id

        vo.setFirstAccountId(signInfoEntity.getFirstAccountId());//甲方账户id
        vo.setFirstAccountName(signInfoEntity.getFirstAccountName());//甲方账户名称
        vo.setFirstBankNameCode(signInfoEntity.getFirstBankNameCode());//甲方开户行
        vo.setFirstBankName(signInfoEntity.getFirstBankName());//甲方开户行名称

        //通过签约id查询合同甲方信息
        BbsRenewalTemplateSeatEntity templateSeatEntity = new LambdaQueryChainWrapper<>(renewalTemplateSeatMapper)
                .eq(BbsRenewalTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalTemplateSeatEntity::getParentId, signId)
                .eq(BbsRenewalTemplateSeatEntity::getSeatKey, "unifiedCreditCode").one();
        if(templateSeatEntity != null){
            vo.setFirstId(templateSeatEntity.getValue());//甲方ID
            vo.setFirstSocialCode(templateSeatEntity.getValue());//甲方信用代码
        }
        BbsRenewalTemplateSeatEntity templateSeatEntity1 = new LambdaQueryChainWrapper<>(renewalTemplateSeatMapper)
                .eq(BbsRenewalTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalTemplateSeatEntity::getParentId, signId)
                .eq(BbsRenewalTemplateSeatEntity::getSeatKey, "lessorName").one();
        if(templateSeatEntity1 != null){
            vo.setFirstName(templateSeatEntity1.getValue());//甲方名称
        }

        return vo;
    }

    @Override
    public AppReply<Object> generateRenewalBill(String signId) {
        BbsSignInfoAndProductInfoAndCustomerInfoVo vo = generateRenewalBillVo(signId);
        log.info("续租账单生成接口调用-入参：{}", JSON.toJSONString(vo));
        AppReply<Object> appReply= bbctSigningFeignCilent.insertAndCreatePayment(vo);
        log.info("续租账单生成接口调用-返回结果：{}", JSON.toJSONString(appReply));
        return appReply;
    }
    private BbsSignInfoAndProductInfoAndCustomerInfoVo generateRenewalBillVo(String signId) {
        BbsSignInfoAndProductInfoAndCustomerInfoVo vo = new BbsSignInfoAndProductInfoAndCustomerInfoVo();
        BbsRenewalRelationEntity relationEntity = new LambdaQueryChainWrapper<>(renewalRelationMapper)
                .eq(BbsRenewalRelationEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalRelationEntity::getSignInfoId, signId)
                .one();
        //查询客户相关信息
        BbsRenewalCustomerEntity customerEntity = new LambdaQueryChainWrapper<>(renewalCustomerMapper)
                .eq(BbsRenewalCustomerEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalCustomerEntity::getRrId, relationEntity.getRrId())
                .one();
        BbsResultCustomerVo bbsResultCustomerVo = new BbsResultCustomerVo();
        BeanUtils.copyProperties(customerEntity, bbsResultCustomerVo);
        bbsResultCustomerVo.setBankIsAuthentication(bankIsAuthentication);//是否鉴权(1.是 0.否)
        bbsResultCustomerVo.setType(null);
        bbsResultCustomerVo.setBankName(null);
        bbsResultCustomerVo.setBankNameCode(null);
        bbsResultCustomerVo.setBankCard(null);
        bbsResultCustomerVo.setBankPhone(null);
        bbsResultCustomerVo.setBankSubbranchCode(null);
        bbsResultCustomerVo.setBankSubbranchName(null);
        bbsResultCustomerVo.setConsignorMobile(customerEntity.getCustomerTel());
        vo.setBbsResultCustomerVo(bbsResultCustomerVo);//趸租单位(客户信息)

        //通过签约id查询签约信息
        BbsRenewalInfoEntity signInfoEntity = new LambdaQueryChainWrapper<>(renewalInfoMapper)
                .eq(BbsRenewalInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalInfoEntity::getSignId, signId)
                .one();
        BbsSignInfoVo bbsSignInfoVo = new BbsSignInfoVo();
        BeanUtils.copyProperties(signInfoEntity, bbsSignInfoVo);
        bbsSignInfoVo.setSupportOfflineSign(supportOfflineSign);//是否支持线上签约(0.否 1.是)
        bbsSignInfoVo.setSupportOnlineSign(supportOnlineSign);//是否支持纸质签约(0.否 1.是)
        bbsSignInfoVo.setSupportSceneSign(supportSceneSign);//是否支持电子版签约(0.否 1.是)
        bbsSignInfoVo.setSignType(signType);//签约类型(01.散租 02.趸租 03.管理协议 07.商租)
        bbsSignInfoVo.setSignStatus(bbsSignInfoVo.getSignStatus());//签约状态(1.暂存 2.待签约 3.已签约 4.未签约 5.待审核 6.未通过 7.租户已签 8.终止)
        bbsSignInfoVo.setProductSourceType(productSourceType);//产品来源类型(0.导入方式 1.已登记 2.已选房 3.已验房 4.选择房源 5.房态中心(实时) 6.续租中心 7.调换房 8.商业 9.家具家电)
        bbsSignInfoVo.setCheckRoomStatus(checkRoomStatus);//验房状态(1.先验房 2.签约同时验房 3.无需验房)
        bbsSignInfoVo.setContractType("10");//合同类型(1.普通合同 2.补充合同 3.续租合同 4.调换房合同 5.商业合同 6.家具家电合同  7.主承租人合同变更合同 8.购房补充协议 9.趸租补充协议 10.商业续租合同)
        bbsSignInfoVo.setOffer(offer);//是否报盘(0.否 1.是)
        bbsSignInfoVo.setSubmitHeatingCompany(submitHeatingCompany);//是否报送热力公司(0.否 1.是)
        bbsSignInfoVo.setSecondPayPercent(secondPayPercent);//缴费比例(0 ~ 1)
//        bbsSignInfoVo.setSignExtend(getSignExtendJsonStr(signInfoEntity,customerEntity));
        vo.setBbsSignInfoVo(bbsSignInfoVo);

        //查询产品相关信息
        List<BbsRenewalProductEntity> productEntities = new LambdaQueryChainWrapper<>(renewalProductMapper)
                .eq(BbsRenewalProductEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalProductEntity::getRrId, relationEntity.getRrId())
                .list();
        List<BbsResultRelationProductCustomerVo> productList = new ArrayList<>();
        for(int i = 0; i < productEntities.size(); i++){
            BbsRenewalProductEntity bbsResultProductEntity = productEntities.get(i);
            BbsResultRelationProductCustomerVo matterVo = new BbsResultRelationProductCustomerVo();
            BeanUtils.copyProperties(bbsResultProductEntity, matterVo);
            //产品扩展信息外接json拼接
            String productExtend = assembleRenewalProductExtend(bbsResultProductEntity, signInfoEntity, productEntities.size(), i+1);
            matterVo.setProductExtend(productExtend);//产品扩展信息
            matterVo.setGroupNo("");
            matterVo.setProjectFormat("03");
            matterVo.setRentStandardNo(String.valueOf(bbsResultProductEntity.getRentStandard()));
            matterVo.setProjectEstate(bbsResultProductEntity.getCommunityBuildingName());
            productList.add(matterVo);
        }
        vo.setProductList(productList);//产品信息

        //查询站内信模板信息
        BbsRenewalTemplateGroupEntity systemStationMessageTemplateGroupEntity = new LambdaQueryChainWrapper<>(renewalTemplateGroupMapper)
                .eq(BbsRenewalTemplateGroupEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalTemplateGroupEntity::getTemplateGroupId, signInfoEntity.getSystemStationMessageTemplateGroupId())
                .one();
        if(systemStationMessageTemplateGroupEntity != null){
            BbsTemplateGroupVo systemStationMessageTemplateGroup = new BbsTemplateGroupVo();
            BeanUtils.copyProperties(systemStationMessageTemplateGroupEntity, systemStationMessageTemplateGroup);
            vo.setSystemStationMessageTemplateGroup(systemStationMessageTemplateGroup);
        }
        //查询短信模板信息
        BbsRenewalTemplateGroupEntity mobileMessageTemplateGroupEntity = new LambdaQueryChainWrapper<>(renewalTemplateGroupMapper)
                .eq(BbsRenewalTemplateGroupEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalTemplateGroupEntity::getTemplateGroupId, signInfoEntity.getMobileMessageTemplateGroupId())
                .one();
        if(mobileMessageTemplateGroupEntity != null){
            BbsTemplateGroupVo mobileMessageTemplateGroup = new BbsTemplateGroupVo();
            BeanUtils.copyProperties(mobileMessageTemplateGroupEntity, mobileMessageTemplateGroup);
            vo.setMobileMessageTemplateGroup(mobileMessageTemplateGroup);
        }
        //通过签约id查询模板属性信息
        List<BbsRenewalTemplateSeatEntity> templateSeatEntities = new LambdaQueryChainWrapper<>(renewalTemplateSeatMapper)
                .eq(BbsRenewalTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
//                .eq(BbsRenewalTemplateSeatEntity::getIsRequired, "1")
//                .eq(BbsRenewalTemplateSeatEntity::getIsShow, "1")
                .eq(BbsRenewalTemplateSeatEntity::getParentId, signId)
                .list();
        List<BbsTemplateSeatVo> templateSeatList = new ArrayList<BbsTemplateSeatVo>();
        templateSeatEntities.forEach(item -> {
            BbsTemplateSeatVo templateSeatVo = new BbsTemplateSeatVo();
            BeanUtils.copyProperties(item, templateSeatVo);
            templateSeatList.add(templateSeatVo);
        });
        vo.setTemplateSeatList(templateSeatList);//模板属性信息
        return vo;
    }

    /**
     * 获取生成帐单的参数“扩展”的json字符串
     * @param signInfoEntity
     * @param customerEntity
     * @return
     */
//    private String getSignExtendJsonStr(BbsRenewalInfoEntity signInfoEntity, BbsRenewalCustomerEntity customerEntity){
//        String resultStr="";
//        Map<String,Object> resultMap=new HashMap<>();
//        /*押金*/
//        if("1".equals(signInfoEntity.getDepositReturnWay())){//转入新合同
//            resultMap.put("returnType","01");
//        }else if("2".equals(signInfoEntity.getDepositReturnWay())){//退回银行卡
//            resultMap.put("returnType","02");
//            resultMap.put("bankProvince",customerEntity.getDepositReturnProvinceName());
//            resultMap.put("bankCity",customerEntity.getDepositReturnCityName());
//            resultMap.put("bankName",customerEntity.getDepositReturnBankName());
//            resultMap.put("bankCode",customerEntity.getDepositReturnBankNameCode());
//            resultMap.put("bankBranchName",customerEntity.getDepositReturnBankSubbranchName());
//            resultMap.put("bankBranchCode",customerEntity.getDepositReturnBankSubbranchCode());
//            resultMap.put("bankAccountName",customerEntity.getDepositReturnBankAccount());
//            resultMap.put("bankAccountNo",customerEntity.getDepositReturnBankCard());
//        }else if("3".equals(signInfoEntity.getDepositReturnWay())){//退回其他银行卡
//            resultMap.put("returnType","03");
//            resultMap.put("bankProvince",customerEntity.getDepositReturnProvinceName());
//            resultMap.put("bankCity",customerEntity.getDepositReturnCityName());
//            resultMap.put("bankName",customerEntity.getDepositReturnBankName());
//            resultMap.put("bankCode",customerEntity.getDepositReturnBankNameCode());
//            resultMap.put("bankBranchName",customerEntity.getDepositReturnBankSubbranchName());
//            resultMap.put("bankBranchCode",customerEntity.getDepositReturnBankSubbranchCode());
//            resultMap.put("bankAccountName",customerEntity.getDepositReturnBankAccount());
//            resultMap.put("bankAccountNo",customerEntity.getDepositReturnBankCard());
//        }
//        resultStr=JSON.toJSONString(resultStr);
//        return resultStr;
//
//    }
    /**
     * 产品扩展信息外接json拼接
     * @param signInfoEntity
     * @return
     */
    public String assembleRenewalProductExtend(BbsRenewalProductEntity item, BbsRenewalInfoEntity signInfoEntity, int listSize, int no){
        JSONObject result = new JSONObject();
        JSONArray chargeSubjectList = new JSONArray();

        JSONObject zujin = new JSONObject();//租金
        zujin.put("chargeSubjectNo", "01");//计费科目编号 01 房屋租金 02 押金 07 物业费
        zujin.put("cyclicOrSingle", "01");//循环计费或单次计费 01 循环02 单次
        //zujin.put("chargeSubjectAmount", signInfoByLwqServiceImpl.getChargeSubjectAmount("租金",item,signInfoEntity));//金额
        //zujin.put("chargeSubjectAmount", BigDecimal.valueOf(Double.valueOf(0.00)));//金额
        zujin.put("amountType", "0"+item.getRentStandardUnit());//金额类型
        zujin.put("taxRate", BigDecimal.valueOf(signInfoEntity.getRentTaxRate()).divide(BigDecimal.valueOf(100)));//税率
        if("1".equals(signInfoEntity.getPropIncrementalFlag())){
            JSONArray rentIncreaseRules = getSignIncrementalConfigList(signInfoEntity.getSignId(), "rent", signInfoEntity.getPaymentCycleCode());
            zujin.put("increaseRules", rentIncreaseRules);//递增规则
        }else{
            zujin.put("increaseRules", new JSONArray());//递增规则
        }

        zujin.put("preferentRules", JSONArray.parseArray(JSON.toJSONString(getSignFpIntervalList(signInfoEntity, "rent"))));//优惠规则
        Map paramValueZujin = new HashMap();
        paramValueZujin.put("PARAMPRICE", String.valueOf(item.getRentStandard()));//单价
        if(AreaTypeEnum.BUILD_AREA_TYPE.getCode().equals(signInfoEntity.getAreaType())) {
            paramValueZujin.put("PARAMAREA", item.getHouseStructArea());//建筑面积
        }else{
            paramValueZujin.put("PARAMAREA", item.getInnerSleeveArea());//套内建筑面积
        }
        zujin.put("paramValueList", paramValueZujin);
        chargeSubjectList.add(zujin);

        boolean yajinFlag=true;
        if("9".equals(signInfoEntity.getCashPledgeCode())&&(StringUtils.isEmpty(signInfoEntity.getCashPledgeValue())||Double.parseDouble(signInfoEntity.getCashPledgeValue())==0d)){
            yajinFlag=false;
        }
        JSONObject yajin = new JSONObject();//押金
        yajin.put("chargeSubjectNo", "02");//计费科目编号 01 房屋租金 02 押金 07 物业费
        yajin.put("cyclicOrSingle", "02");//循环计费或单次计费 01 循环02 单次
        //yajin.put("chargeSubjectAmount", "9".equals(signInfoEntity.getCashPledgeCode())?new BigDecimal(signInfoEntity.getCashPledgeValue()):signInfoByLwqServiceImpl.getChargeSubjectAmount("租金",item,signInfoEntity));//金额
        yajin.put("chargeSubjectAmount", "9".equals(signInfoEntity.getCashPledgeCode()) ? calculateDeposit(signInfoEntity.getCashPledgeValue(), listSize, no) : BigDecimal.valueOf(Double.valueOf(0.00)));//金额
        yajin.put("depositProportion", "9".equals(signInfoEntity.getCashPledgeCode()) ? null : Integer.parseInt(signInfoEntity.getCashPledgeCode()));
        yajin.put("taxRate", BigDecimal.valueOf(Double.valueOf(0.00)));//税率
        Map paramValueYajin = new HashMap();
        paramValueYajin.put("PARAMPRICE", "0.00");//单价
        paramValueYajin.put("PARAMAREA", item.getHouseStructArea());//面积
        yajin.put("paramValueList", paramValueYajin);
        if(yajinFlag){
            chargeSubjectList.add(yajin);
        }


        if("02-08".equals(signInfoEntity.getContractFees())){
            JSONObject woyefei = new JSONObject();//物业费
            woyefei.put("chargeSubjectNo", "07");//计费科目编号 01 房屋租金 02 押金 07 物业费
            woyefei.put("cyclicOrSingle", "01");//循环计费或单次计费 01 循环02 单次
            //woyefei.put("chargeSubjectAmount", signInfoByLwqServiceImpl.getChargeSubjectAmount("物业费",item,signInfoEntity));//金额
            //woyefei.put("chargeSubjectAmount", BigDecimal.valueOf(Double.valueOf(0.00)));//金额
            woyefei.put("amountType", "0"+item.getPropStandardUnit());//金额类型
            woyefei.put("taxRate", BigDecimal.valueOf(signInfoEntity.getPropTaxRate()).divide(BigDecimal.valueOf(100)));//税率
            JSONArray propIncreaseRules = getRenewalIncrementalConfigList(signInfoEntity.getSignId(), "prop", signInfoEntity.getPaymentCycleCode());
            woyefei.put("increaseRules", propIncreaseRules);//递增规则
            JSONArray propPreferentRules = JSONArray.parseArray(JSON.toJSONString(getSignFpIntervalList(signInfoEntity, "prop")));//优惠规则
            woyefei.put("preferentRules", propPreferentRules);//优惠规则
            Map paramValueWuyefei = new HashMap();
            paramValueWuyefei.put("PARAMPRICE", String.valueOf(item.getPropStandard()));//单价
            paramValueWuyefei.put("PARAMAREA", item.getHouseStructArea());//面积
            woyefei.put("paramValueList", paramValueWuyefei);
            chargeSubjectList.add(woyefei);
        }

        result.put("chargeSubjectList", chargeSubjectList);
        //2024-11-15增加签约房间类型（房间取得用途）
        result.put("roomType",item.getHouseObtainPurposeCode());
        List<BusinessFormatVo> businessFormatVos = DictTreeToBusinessFormatUtil.jsonToBusinessFormatVoList(signInfoEntity.getBusinessFormat());
        String secBusinessType = businessFormatVos.stream().map(businessFormatVo -> businessFormatVo.getKey().length() > 1 ? businessFormatVo.getKey() : "0" + businessFormatVo.getKey()).collect(Collectors.joining(SymbolConst.COMMA));
        result.put("businessFormat", secBusinessType);        return result.toJSONString();
    }

    /**
     * 通过签约id查询递增设置信息
     * @param signId
     * @param standardType
     * @param paymentCycleCode
     * @return
     */
    public JSONArray getRenewalIncrementalConfigList(String signId, String standardType, String paymentCycleCode){
        List<BbsRenewalIncrementalConfigEntity> signIncrementalConfigEntities = new LambdaQueryChainWrapper<>(renewalIncrementalConfigMapper)
                .eq(BbsRenewalIncrementalConfigEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalIncrementalConfigEntity::getStandardType, standardType)
                .eq(BbsRenewalIncrementalConfigEntity::getSignInfoId, signId)
                .list();
        JSONArray increaseRules = new JSONArray();
        signIncrementalConfigEntities.forEach(item -> {
            JSONObject increaseRule = new JSONObject();
            increaseRule.put("increaseRuleId", "");//递增规则ID
            increaseRule.put("increaseProportion", BigDecimal.valueOf(item.getIncrease()).divide(BigDecimal.valueOf(100)));//递增比例除以100
            increaseRule.put("increaseAmount", "");//递增金额
            String increasePeriod = "";
            if("year".equals(item.getUnit())){
                increasePeriod = "04";
            }
            else if("month".equals(item.getUnit())){
                increasePeriod = "01";
            }
            increaseRule.put("increasePeriod", increasePeriod);//递增周期 unit 04
            increaseRule.put("increaseOrder", item.getTimePoint());//递增顺序
            String increaseType = "";
            if("appoint".equals(item.getAdjustmentPoint())){
                increaseType = "02";
            }else{
                increaseType = "01";
            }
            increaseRule.put("increaseType", increaseType);//递增类型
            increaseRules.add(increaseRule);
        });
        return increaseRules;
    }

//    /**
//     * 通过签约id查询免租期信息
//     * @param signId
//     * @param standardType
//     * @return
//     */
//    public JSONArray getRenewalFpIntervalList(String signId, String standardType){
//        List<BbsRenewalFpIntervalEntity> signFpIntervalEntities = new LambdaQueryChainWrapper<>(renewalFpIntervalMapper)
//                .eq(BbsRenewalFpIntervalEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
//                .eq(BbsRenewalFpIntervalEntity::getStandardType, standardType)
//                .eq(BbsRenewalFpIntervalEntity::getSignInfoId, signId)
//                .list();
//        JSONArray preferentRules = new JSONArray();
//        signFpIntervalEntities.forEach(item -> {
//            JSONObject preferentRule = new JSONObject();
//            preferentRule.put("preferentRuleId", "");//优惠规则ID
//            preferentRule.put("preferentialType", "01");//优惠方式
//            preferentRule.put("preferentialBeginDate", item.getStart());//优惠开始日期
//            preferentRule.put("preferentialEndDate", item.getEnd());//优惠结束日期
//            preferentRules.add(preferentRule);
//        });
//        return preferentRules;
//    }

    @Override
    public AppReply<Object> generateBill(String signId) {
        return generateBill(signId,"1");
    }

    @Override
    public AppReply<List<JSONObject>> getHousesByHouseCodes(List<String> houseCodes) {
        log.info("获取房源信息开始================>>>>>>>>>>>{}",DateUtils.getDateTime());
        List<JSONObject> list = new ArrayList<JSONObject>();
        for(String houseCode: houseCodes){
            log.info("循环{}开始{}================>>>>>>>>>>>{}", houseCode, "selectBbhgProductHouseInfoByCode", DateUtils.getDateTime());
            //JSONObject baseInfoOutVo = bbHousingFeignClient.selectBbhgProductHouseInfoByCode(houseCode).getData().getJSONObject("baseInfoOutVo");
            BbhgHouseInfoPageResultVo baseInfoOutVo = iBbsSignInfoService.selectBbhgProductHouseInfoByHouseProduct(houseCode,false).getData();
            log.info("循环{}结束{}================>>>>>>>>>>>{}", houseCode, "selectBbhgProductHouseInfoByCode", DateUtils.getDateTime());
            log.info("循环{}开始{}================>>>>>>>>>>>{}", houseCode, "selectHouseInfoByIdOrCode", DateUtils.getDateTime());
            AppReply<JSONObject> jsonObjectAppReply = bbHousingFeignClient.selectHouseInfoByIdOrCode(StrUtil.emptyToDefault(baseInfoOutVo==null?null:baseInfoOutVo.getBaseHouseCode(),houseCode), null);
            log.info("循环{}结束{}================>>>>>>>>>>>{}", houseCode, "selectHouseInfoByIdOrCode", DateUtils.getDateTime());
            JSONObject json = jsonObjectAppReply.getData();
            if(baseInfoOutVo!=null&&!StringUtils.isEmpty(baseInfoOutVo.getHouseAddr())){
                json.put("houseAddr",baseInfoOutVo.getHouseAddr());
            }
            if(baseInfoOutVo!=null&&!StringUtils.isEmpty(baseInfoOutVo.getHouseStructArea())){
                json.put("buildArea",baseInfoOutVo.getHouseStructArea());
            }
            list.add(json);
        }
        AppReply<List<JSONObject>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(list);
        log.info("获取房源信息结束================>>>>>>>>>>>{}",DateUtils.getDateTime());
        return appReply;
    }

    @Override
    public AppReply<ContractingProgressVo> getContractingProgress(String contractCode, String signType) {
        ContractingProgressVo contractingProgressVo = new ContractingProgressVo();
        contractingProgressVo.setContractCode(contractCode);
        String intentionId = null;
        List<BscIntentionTimeNodeVo> bscIntentionTimeNodeVos = null;
        if (StrUtil.equals(signType, "1")) {
            SigningSaveVo signViewResultVo = bbsSignInfoExtService.signView(null,contractCode,false);
            intentionId = signViewResultVo.getCustomer().getIntentionId();
            String customerType = signViewResultVo.getCustomer().getCustomerType();//00:个人  01：企业
            String creditCode = signViewResultVo.getCustomer().getCustomerCreditCode();//信息用代码
            String idNumber = signViewResultVo.getCustomer().getCustomerIdNumber();//证件号码
            contractingProgressVo.setContactPerson(signViewResultVo.getCustomer().getContactName());//联系人
            contractingProgressVo.setContactPhone(signViewResultVo.getCustomer().getCustomerTel());//联系电话
            contractingProgressVo.setCustomerType(customerType);
            contractingProgressVo.setTenantry(signViewResultVo.getCustomer().getCustomerName());
            contractingProgressVo.setCertificateNumber(customerType.equals("00")?idNumber:creditCode);
            bscIntentionTimeNodeVos = signInfoMapper.selectApproveTimeNodeBySignId(signViewResultVo.getSignId());
        }
        if (StrUtil.equals(signType, "2")) {
            RenewalingSaveVo renewalingSaveVo = bbsRenewalInfoByYingService.renewalView(null,contractCode,false);
            intentionId = renewalingSaveVo.getCustomer().getIntentionId();
            String customerType = renewalingSaveVo.getCustomer().getCustomerType();//00:个人  01：企业
            String creditCode = renewalingSaveVo.getCustomer().getCustomerCreditCode();//信息用代码
            String idNumber = renewalingSaveVo.getCustomer().getCustomerIdNumber();//证件号码
            contractingProgressVo.setContactPerson(renewalingSaveVo.getCustomer().getContactName());//联系人
            contractingProgressVo.setContactPhone(renewalingSaveVo.getCustomer().getCustomerTel());//联系电话
            contractingProgressVo.setCustomerType(customerType);
            contractingProgressVo.setTenantry(renewalingSaveVo.getCustomer().getCustomerName());
            contractingProgressVo.setCertificateNumber(customerType.equals("00")?idNumber:creditCode);
            bscIntentionTimeNodeVos = renewalInfoMapper.selectApproveTimeNodeBySignId(renewalingSaveVo.getSignId());
        }
        log.info(" bzfSystemCommercialFeignClient.getIntentionTimeNodes================>>>>>>>>>intentionId>>{}", intentionId);
        AppReply<List<BscIntentionTimeNodeVo>> appReply = bzfSystemCommercialFeignClient.getIntentionTimeNodes(intentionId);
        log.info(" bzfSystemCommercialFeignClient.getIntentionTimeNodes================>>>>>>>>>appReply>>{}", JSON.toJSONString(appReply));
        List<BscIntentionTimeNodeVo> bscIntentionTimeNodeVoList = appReply.getData();
        if(bscIntentionTimeNodeVoList==null){
            bscIntentionTimeNodeVoList=new ArrayList<>();
        }
        if (ObjectUtil.isNotEmpty(bscIntentionTimeNodeVos)) {
            for (int i = 0; i < bscIntentionTimeNodeVos.size(); i++) {
                BscIntentionTimeNodeVo item = bscIntentionTimeNodeVos.get(i);
                if (i < bscIntentionTimeNodeVos.size()-1 && StrUtil.equals(item.getStatus(), "待审核")) {
                    item.setStatus(null);
                    item.setName("商户签约");
                }else if (StrUtil.equals(item.getStatus(), "待审核")) {
                    BscIntentionTimeNodeVo node = new BscIntentionTimeNodeVo();
                    BeanUtil.copyProperties(item,node,"status");
                    node.setName("商户签约");
                    bscIntentionTimeNodeVoList.add(node);
                }
                bscIntentionTimeNodeVoList.add(item);
            }
        }else{
            BscIntentionTimeNodeVo item=new BscIntentionTimeNodeVo();
            item.setName("商户签约");
            item.setTimeNode(null);
            item.setStatus(null);
            bscIntentionTimeNodeVoList.add(item);
        }
        //bscIntentionTimeNodeVos = bscIntentionTimeNodeVos.stream()
        //        .sorted(Comparator.comparing(BscIntentionTimeNodeVo::getTimeNode).reversed())
        //        .collect(Collectors.toList());
        contractingProgressVo.setBscIntentionTimeNodeVos(bscIntentionTimeNodeVoList);
        return AppReply.success(contractingProgressVo);
    }

    @Override
    public AppReply<String> checkIntention(String contractNo) {
        //查询客户和产品
        List<Map> customerNoAndProductNo = customerMapper.getCustomerNoAndProductNo(contractNo);
        if (ObjectUtil.isEmpty(customerNoAndProductNo)) {
            return new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, "0");
        }
        BscIntentionCheckParamVo vo = new BscIntentionCheckParamVo();
        vo.setCustomerId(customerNoAndProductNo.get(0).get("customerNo").toString());
        vo.setProductCodes(customerNoAndProductNo.get(0).get("productNo").toString());
        log.info("查询意向客户信息：{}", JSON.toJSONString(vo));
        return bzfSystemCommercialFeignClient.checkIntention(vo);
    }

    private String filterProjectName(String projectName){
        if(StringUtils.isEmpty(projectName)){
            return "";
        }
        return projectName.replaceAll("\\[.*?\\]", "").replaceAll("【.*?】", "").replaceAll("\\(.*?\\)", "").replaceAll("（.*?）", "").replaceAll("\\d", "");
    }

    /**
     * 创建合同其他信息
     * @param signInfo
     * @return
     */
    @Override
    public String createContractOtherInfo(BbsSignInfoEntity signInfo){
       // String signId = signInfo.getSignId();
        ContractOtherInfo info = new ContractOtherInfo();
        info.setRentTaxRate(signInfo.getRentTaxRate());//租金税率
        info.setPropTaxRate(signInfo.getPropTaxRate());//物业费税率
        info.setInvoicingBeforePayment(signInfo.getInvoicingBeforePayment());//是否支持先开票后付款
        info.setContractFees(signInfo.getContractFees());//合同费用(02-07.租金包含物业费 02-08.租金+物业费 02-09.仅租金（物业费由物业公司收取）

        /**
         * 免租期
         */
        info.setRentFreeVo(createFreeVo(signInfo,"rent"));//租金免租期
        info.setPropFreeVo(createFreeVo(signInfo,"prop"));//物业费免租期

        /**
         * 水印
         */
        WatermarkVo watermarkVo = new WatermarkVo();
        watermarkVo.setContractWatermark(signInfo.getContractWatermark());
        watermarkVo.setSecondWartmarkLogoName(signInfo.getSecondWartmarkLogoName());
        watermarkVo.setSecondWartmarkLogoId(signInfo.getSecondWartmarkLogoId());
        watermarkVo.setSecondWartmarkLogoUrl(signInfo.getSecondWartmarkLogoUrl());
        watermarkVo.setSecondWartmarkName(signInfo.getSecondWartmarkName());
        info.setWatermarkVo(watermarkVo);

        String msg = JSON.toJSONString(info,SerializerFeature.WriteMapNullValue);
        log.info(String.format("合同其他信息：%s",msg));
        return msg;
    }

    /**
     * 递增整体设置
     * @param signInfo
     * @return
     */
    @Override
    public String createIncrement(BbsSignInfoEntity signInfo){
        String signId = signInfo.getSignId();

//        info.setRentTaxRate(signInfo.getRentTaxRate());//租金税率
//        info.setPropTaxRate(signInfo.getPropTaxRate());//物业费税率
        //租金递增
        IncrementExtendVo extendVo = new IncrementExtendVo();
        IncrementVo rentIncrementVo = new IncrementVo();
        rentIncrementVo.setIncrementList(this.createIncrementalConfigBean("rent",signId));
        rentIncrementVo.setIncrementalType(signInfo.getRentIncrementalType());
        rentIncrementVo.setIncrementalFlag(signInfo.getRentIncrementalFlag());
        extendVo.setRentIncrementVo(rentIncrementVo);

        IncrementVo propIncrementVo = new IncrementVo();
        propIncrementVo.setIncrementList(this.createIncrementalConfigBean("prop",signId));
        propIncrementVo.setIncrementalType(signInfo.getRentIncrementalType());
        propIncrementVo.setIncrementalFlag(signInfo.getPropIncrementalFlag());
        extendVo.setPropIncrementVo(propIncrementVo);

        String msg = JSON.toJSONString(extendVo,SerializerFeature.WriteMapNullValue);
        log.info(String.format("递增信息：%s",msg));
        return msg;
    }

    /**
     * 创建免租期bean
     * @param signInfo
     * @param type
     * @return
     */
    private FreeVo createFreeVo(BbsSignInfoEntity signInfo ,String type){
        FreeVo rentFreeVo = new FreeVo();
        if("rent".equals(type)){
            if("2".equals(signInfo.getRentFreePeriodType())){
                rentFreeVo.setFpFixedDate(signInfo.getRentFpFixedDate());
                rentFreeVo.setFpFixedValue(signInfo.getRentFpFixedValue());
                rentFreeVo.setFreePeriodType("2");
            }else if ("1".equals(signInfo.getRentFreePeriodType())){
                List<BbsSignFpIntervalEntity> signFpIntervalEntityList = new LambdaQueryChainWrapper<>(signFpIntervalMapper)
                        .eq(BbsSignFpIntervalEntity::getDelFlag,DelFlagEnum.UNDELETED.getCode())
                        .eq(BbsSignFpIntervalEntity::getSignInfoId,signInfo.getSignId())
                        .eq(BbsSignFpIntervalEntity::getStandardType,type)
                        .list();
                if(CollectionUtil.isNotEmpty(signFpIntervalEntityList)){
                    List<FreeSectionVo> list = new ArrayList<>();
                    for(BbsSignFpIntervalEntity entity:signFpIntervalEntityList){
                        FreeSectionVo freeSectionVo = new FreeSectionVo();
                        BeanUtils.copyProperties(entity,freeSectionVo);
                        list.add(freeSectionVo);
                    }
                    rentFreeVo.setFreePeriodType("1");
                    rentFreeVo.setFreeSections(list);
                }
            }else {
                rentFreeVo.setFreePeriodType("0");
            }
        }else{//物业费
            if("2".equals(signInfo.getPropFreePeriodType())){
                rentFreeVo.setFpFixedDate(signInfo.getPropFpFixedDate());
                rentFreeVo.setFpFixedValue(signInfo.getPropFpFixedValue());
                rentFreeVo.setFreePeriodType("2");
            }else if ("1".equals(signInfo.getPropFreePeriodType())){
                List<BbsSignFpIntervalEntity> signFpIntervalEntityList = new LambdaQueryChainWrapper<>(signFpIntervalMapper)
                        .eq(BbsSignFpIntervalEntity::getDelFlag,DelFlagEnum.UNDELETED.getCode())
                        .eq(BbsSignFpIntervalEntity::getSignInfoId,signInfo.getSignId())
                        .eq(BbsSignFpIntervalEntity::getStandardType,type)
                        .list();
                if(CollectionUtil.isNotEmpty(signFpIntervalEntityList)){
                    List<FreeSectionVo> list = new ArrayList<>();
                    for(BbsSignFpIntervalEntity entity:signFpIntervalEntityList){
                        FreeSectionVo freeSectionVo = new FreeSectionVo();
                        BeanUtils.copyProperties(entity,freeSectionVo);
                        list.add(freeSectionVo);
                    }
                    rentFreeVo.setFreePeriodType("1");
                    rentFreeVo.setFreeSections(list);
                }
            }else {
                rentFreeVo.setFreePeriodType("0");
            }
        }

        return rentFreeVo;
    }

    /**
     * 生成递增率List
     * @param type
     * @param signId
     * @return
     */
    private List<BbsSignIncrementalConfigVo> createIncrementalConfigBean(String type,String signId){
        List<BbsSignIncrementalConfigEntity> incrementalConfigEntities = new LambdaQueryChainWrapper<>(signIncrementalConfigMapper)
                .eq(BbsSignIncrementalConfigEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsSignIncrementalConfigEntity::getStandardType,type)
                .eq(BbsSignIncrementalConfigEntity::getSignInfoId, signId)
                .list();
        List<BbsSignIncrementalConfigVo> result = new ArrayList<>();
        for(BbsSignIncrementalConfigEntity entity: incrementalConfigEntities){
            BbsSignIncrementalConfigVo vo = new BbsSignIncrementalConfigVo();
            BeanUtils.copyProperties(entity,vo);
            result.add(vo);
        }
        return result;
    }

    /**
     * 房源扩展
     * @param productEntity
     * @return
     */

    public String createProductExtend(BbsResultProductEntity productEntity){
        ProductExtend productExtend = new ProductExtend();
//        StandardVo rentStandard = new StandardVo();
//        rentStandard.setStandardUnit(productEntity.getRentStandardUnit());
//        rentStandard.setStandardUnitName(productEntity.getRentStandardUnitName());
//        rentStandard.setStandard(productEntity.getRentStandard());
//        productExtend.setRentStandard(rentStandard);//租金标准

        StandardVo propStandard = new StandardVo();
        propStandard.setStandardUnit(productEntity.getPropStandardUnit());
        propStandard.setStandardUnitName(productEntity.getPropStandardUnitName());
        propStandard.setStandard(productEntity.getPropStandard());
        productExtend.setPropStandard(propStandard);//物业费标准

        //productExtend.setAreaType(productExtend.getAreaType());//面积类型

        String msg = JSON.toJSONString(productExtend,SerializerFeature.WriteMapNullValue);
        log.info("productExtend:"+msg);
        return msg;
    }

    /**
     * 合同客户扩展
     * @param entity
     * @return
     */
    private String createCustomerExtend(BbsResultCustomerEntity entity){
        CustomerExtendVo customerExtendVo = new CustomerExtendVo();
        customerExtendVo.setRegisteredAddress(entity.getRegisteredAddress());
        String msg = JSON.toJSONString(customerExtendVo,SerializerFeature.WriteMapNullValue);
        log.info("customerExtendVo:"+msg);
        return msg;
    }

    /**
     * 根据面积类型获取面积
     * @param subjectMatterList
     * @param bbsSignInfo
     * @return
     */
    public String getSumArea(List<BbctContractSubjectMatterVo> subjectMatterList,BbsSignInfoEntity bbsSignInfo){
        BigDecimal  area = new BigDecimal(0.0);
        for(BbctContractSubjectMatterVo contractSubjectMatterVo: subjectMatterList){
            area = area.add(new BigDecimal(AreaTypeEnum.BUILD_AREA_TYPE.getCode().equals(bbsSignInfo.getAreaType()) ? contractSubjectMatterVo.getHouseStructArea():contractSubjectMatterVo.getInnerSleeveArea()));
        }
        area=area .setScale(2, RoundingMode.HALF_UP);
        return area.toPlainString();
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbctContractManagementPageResultVo>> selectByPageAgreementList(BbctContractManagementPageVo vo) {
        PageResult<List<BbctContractManagementPageResultVo>> pageResult = getContractManagementPageResultVos(vo);
        List<BbctContractManagementPageResultVo>  bbctContractManagementPageResultVos = new ArrayList<>();
        if (pageResult != null){
            bbctContractManagementPageResultVos = pageResult.getRows();
        }
        for(BbctContractManagementPageResultVo bbctContractManagementPageResultVo:bbctContractManagementPageResultVos){

            AppReply<List<NewFIleResultVo>> appReply =   fileFeignClient.getFileInfoById( bbctContractManagementPageResultVo.getNewpdfFileId());
            if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
                log.error("调用文件中心失败:" + (Objects.isNull(appReply) ? null : appReply.toString()));
                throw new McpException("调用文件中心失败!");
            }
            List<NewFIleResultVo> fIleResultVos = appReply.getData();
            if (CollectionUtils.isNotEmpty(fIleResultVos)){
                NewFIleResultVo newFIleResultVo =  fIleResultVos.get(0);
                if (newFIleResultVo!= null ){
                   String fileName = StringUtils.isNotEmpty(newFIleResultVo.getFileName())?newFIleResultVo.getFileName().substring(0,newFIleResultVo.getFileName().lastIndexOf(".")):"";
                    bbctContractManagementPageResultVo.setNewpdfFileName(fileName);
                    // 变更类型名称
                    bbctContractManagementPageResultVo.setChangeTypeName(signInfoMapper.selectChangeTypeByiSgnId(bbctContractManagementPageResultVo.getBusinessId()));
                }
            }
        }
        return new PageResult<>(pageResult.getTotal(),bbctContractManagementPageResultVos);
    }
    private  PageResult<List<BbctContractManagementPageResultVo>> getContractManagementPageResultVos(BbctContractManagementPageVo vo) {
        log.info("商业合同列表根据合同号获取所有生效协议分页查询入参：{}", JSON.toJSONString(vo));
        AppReply<PageResult<List<BbctContractManagementPageResultVo>>> appReply = bbctContractFeignClient.selectByPageAgreementList(vo);
        log.error("商业合同列表根据合同号获取所有生效协议分页查询:" + (Objects.isNull(appReply) ? null : appReply.toString()));
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            log.error("商业合同列表根据合同号获取所有生效协议分页查询:" + (Objects.isNull(appReply) ? null : appReply.toString()));
            throw new McpException("商业合同列表根据合同号获取所有生效协议分页查询失败"+appReply.getMsg());
        }
        return appReply.getData();
    }
//    public static void main(String[] args) {
//        CustomerExtendVo customerExtendVo = new CustomerExtendVo();
//        customerExtendVo.setRegisteredAddress(null);
//        String msg = JSON.toJSONString(customerExtendVo, SerializerFeature.WriteMapNullValue);
//        System.out.println(msg);
//        log.info("customerExtendVo:"+msg);
//    }
}
