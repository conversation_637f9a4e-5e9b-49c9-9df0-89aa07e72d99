package com.bonc.ioc.bzf.busisigning.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsContractChangeTemplateEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsContractChangeTemplateMapper;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.service.IBbsContractChangeTemplateService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;

import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更模板表 服务类实现
 *
 * <AUTHOR>
 * @date 2024-10-25
 * @change 2024-10-25 by pyj for init
 */
@Slf4j
@Service
public class BbsContractChangeTemplateServiceImpl extends McpBaseServiceImpl<BbsContractChangeTemplateEntity> implements IBbsContractChangeTemplateService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsContractChangeTemplateMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsContractChangeTemplateService baseService;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbsContractChangeTemplateVo vo) {
        if (vo == null) {
            return null;
        }

        BbsContractChangeTemplateEntity entity = new BbsContractChangeTemplateEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setRelationId(null);
        if (!baseService.insert(entity)) {
            log.error("合同变更模板表新增失败:" + entity.toString());
            throw new McpException("合同变更模板表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getRelationId(), 1)) {
                log.error("合同变更模板表新增后保存历史失败:" + entity.toString());
                throw new McpException("合同变更模板表新增后保存历史失败");
            }

            log.debug("合同变更模板表新增成功:" + entity.getRelationId());
            return entity.getRelationId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsContractChangeTemplateVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsContractChangeTemplateEntity> entityList = new ArrayList<>();
        for (BbsContractChangeTemplateVo item : voList) {
            BbsContractChangeTemplateEntity entity = new BbsContractChangeTemplateEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsContractChangeTemplateEntity item : entityList) {
            item.setRelationId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("合同变更模板表新增失败");
            throw new McpException("合同变更模板表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbsContractChangeTemplateEntity::getRelationId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("合同变更模板表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("合同变更模板表批量新增后保存历史失败");
            }

            log.debug("合同变更模板表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param relationId 需要删除的关联id
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String relationId) {
        if (!StringUtils.isEmpty(relationId)) {
            if (!baseService.saveOperationHisById(relationId, 3)) {
                log.error("合同变更模板表删除后保存历史失败:" + relationId);
                throw new McpException("合同变更模板表删除后保存历史失败");
            }

            if (!baseService.removeById(relationId)) {
                log.error("合同变更模板表删除失败");
                throw new McpException("合同变更模板表删除失败" + relationId);
            }
        } else {
            throw new McpException("合同变更模板表删除失败关联id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param relationIdList 需要删除的关联id
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> relationIdList) {
        if (!CollectionUtils.isEmpty(relationIdList)) {
            int oldSize = relationIdList.size();
            relationIdList = relationIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(relationIdList) || oldSize != relationIdList.size()) {
                throw new McpException("合同变更模板表批量删除失败 存在主键id为空的记录" + StringUtils.join(relationIdList));
            }

            if (!baseService.saveOperationHisByIds(relationIdList, 3)) {
                log.error("合同变更模板表批量删除后保存历史失败:" + StringUtils.join(relationIdList));
                throw new McpException("合同变更模板表批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(relationIdList)) {
                log.error("合同变更模板表批量删除失败");
                throw new McpException("合同变更模板表批量删除失败" + StringUtils.join(relationIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的合同变更模板表
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsContractChangeTemplateVo vo) {
        if (vo != null) {
            BbsContractChangeTemplateEntity entity = new BbsContractChangeTemplateEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getRelationId())) {
                throw new McpException("合同变更模板表更新失败传入关联id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("合同变更模板表更新失败");
                throw new McpException("合同变更模板表更新失败" + entity.getRelationId());
            } else {
                if (!baseService.saveOperationHisById(entity.getRelationId(), 2)) {
                    log.error("合同变更模板表更新后保存历史失败:" + entity.getRelationId());
                    throw new McpException("合同变更模板表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("合同变更模板表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的合同变更模板表
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsContractChangeTemplateVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsContractChangeTemplateEntity> entityList = new ArrayList<>();

            for (BbsContractChangeTemplateVo item : voList) {
                BbsContractChangeTemplateEntity entity = new BbsContractChangeTemplateEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getRelationId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("合同变更模板表批量更新失败 存在关联id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("合同变更模板表批量更新失败");
                throw new McpException("合同变更模板表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getRelationId())).map(BbsContractChangeTemplateEntity::getRelationId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("合同变更模板表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("合同变更模板表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的合同变更模板表
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsContractChangeTemplateVo vo) {
        if (vo != null) {
            BbsContractChangeTemplateEntity entity = new BbsContractChangeTemplateEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("合同变更模板表保存失败");
                throw new McpException("合同变更模板表保存失败" + entity.getRelationId());
            } else {
                if (!baseService.saveOperationHisById(entity.getRelationId(), 4)) {
                    log.error("合同变更模板表保存后保存历史失败:" + entity.getRelationId());
                    throw new McpException("合同变更模板表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("合同变更模板表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的合同变更模板表
     * @return void
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsContractChangeTemplateVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsContractChangeTemplateEntity> entityList = new ArrayList<>();

            for (BbsContractChangeTemplateVo item : voList) {
                BbsContractChangeTemplateEntity entity = new BbsContractChangeTemplateEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("合同变更模板表批量保存失败");
                throw new McpException("合同变更模板表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getRelationId())).map(BbsContractChangeTemplateEntity::getRelationId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("合同变更模板表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("合同变更模板表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param relationId 需要查询的关联id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsContractChangeTemplateVo selectByIdRecord(String relationId) {
        BbsContractChangeTemplateVo vo = new BbsContractChangeTemplateVo();

        if (!StringUtils.isEmpty(relationId)) {
            BbsContractChangeTemplateEntity entity = baseService.selectById(relationId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-10-25
     * @change 2024-10-25 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsContractChangeTemplatePageResultVo>> selectByPageRecord(BbsContractChangeTemplatePageVo vo) {
        List<BbsContractChangeTemplatePageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据变更类型查询
     *
     * @param changeType 变更类型
     * @return 合同变更模板信息 vo实体
     */
    @Override
    public BbsContractChangeTemplateVo selectByChangeType(String changeType) {
        BbsContractChangeTemplateEntity entity = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BbsContractChangeTemplateEntity::getChangeType, changeType)
                .eq(BbsContractChangeTemplateEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .one();
        if (Objects.isNull(entity)) {
            return null;
        } else {
            BbsContractChangeTemplateVo vo = new BbsContractChangeTemplateVo();
            BeanUtils.copyProperties(entity, vo);
            return vo;
        }
    }
}
