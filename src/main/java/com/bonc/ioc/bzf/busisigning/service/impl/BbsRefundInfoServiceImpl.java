package com.bonc.ioc.bzf.busisigning.service.impl;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.bonc.ioc.bzf.busisigning.consts.KeywordConst;
import com.bonc.ioc.bzf.busisigning.entity.BbsRefundInfoEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsRefundInfoMapper;
import com.bonc.ioc.bzf.busisigning.enums.ApproveStatusEnum;
import com.bonc.ioc.bzf.busisigning.enums.BackTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.CustomerTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.PaymentBackTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.RefundChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.RefundProcessEnum;
import com.bonc.ioc.bzf.busisigning.enums.RefundStatusEnum;
import com.bonc.ioc.bzf.busisigning.enums.WhetherEnum;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctPaymentV2FeignClient;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctApproveVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSignerVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctRefundAmountDetailVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctStatusVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsRefundApproveInfoService;
import com.bonc.ioc.bzf.busisigning.service.IBbsRefundInfoService;
import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeService;
import com.bonc.ioc.bzf.busisigning.utils.FeesSummaryJsonConverter;
import com.bonc.ioc.bzf.busisigning.utils.MoneyUtil;
import com.bonc.ioc.bzf.busisigning.utils.ResultUtils;
import com.bonc.ioc.bzf.busisigning.vo.payment.BusinessGeneratePaymentByGXParamVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.BusinessGeneratePaymentByGXResultVo;
import com.bonc.ioc.bzf.busisigning.workflow.component.WorkflowCommponent;
import com.bonc.ioc.bzf.busisigning.workflow.enums.BusinessTypeCodeEnum;
import com.bonc.ioc.bzf.busisigning.workflow.service.IFwWorkflowService;
import com.bonc.ioc.bzf.busisigning.workflow.vo.BaseFlowVo;
import com.bonc.ioc.bzf.busisigning.workflow.vo.BbfwRequestBusinessMappingVo;
import com.bonc.ioc.bzf.busisigning.workflow.vo.OaCreateWorkFlowRequestV2Vo;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.vo.FeesSummaryVo;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;

import com.bonc.ioc.common.base.page.PageResult;

import java.math.BigDecimal;

/**
 * 退款申请表 服务类实现
 *
 * <AUTHOR>
 * @date 2024-08-28
 * @change 2024-08-28 by pyj for init
 */
@Slf4j
@Service
public class BbsRefundInfoServiceImpl extends McpBaseServiceImpl<BbsRefundInfoEntity> implements IBbsRefundInfoService {

    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRefundInfoMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsRefundInfoService baseService;

    /**
     * 退款审批相关 服务实例
     */
    @Resource
    private IBbsRefundApproveInfoService refundApproveInfoService;

    /**
     * 合同变更相关 服务实例
     */
    @Resource
    private IBbsiContractChangeService contractChangeService;

    /**
     * 合同中心 feign实例
     */
    @Resource
    private BbctContractFeignClient contractFeignClient;

    /**
     * 缴费中心 feign实例
     */
    @Resource
    private BbctPaymentV2FeignClient paymentV2FeignClient;

    /**
     * 字典 缓存实例
     */
    @Resource
    private McpDictSession mcpDictSession;

    @Resource
    private IFwWorkflowService workflowService;

    @Resource
    private WorkflowCommponent workflowCommponent;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbsRefundInfoVo vo) {
        if (vo == null) {
            return null;
        }

        BbsRefundInfoEntity entity = new BbsRefundInfoEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setRefundId(null);
        if (!baseService.insert(entity)) {
            log.error("退款申请表新增失败:" + entity.toString());
            throw new McpException("退款申请表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getRefundId(), 1)) {
                log.error("退款申请表新增后保存历史失败:" + entity.toString());
                throw new McpException("退款申请表新增后保存历史失败");
            }

            log.debug("退款申请表新增成功:" + entity.getRefundId());
            return entity.getRefundId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsRefundInfoVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsRefundInfoEntity> entityList = new ArrayList<>();
        for (BbsRefundInfoVo item : voList) {
            BbsRefundInfoEntity entity = new BbsRefundInfoEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsRefundInfoEntity item : entityList) {
            item.setRefundId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("退款申请表新增失败");
            throw new McpException("退款申请表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbsRefundInfoEntity::getRefundId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("退款申请表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("退款申请表批量新增后保存历史失败");
            }

            log.debug("退款申请表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param refundId 需要删除的退款id
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String refundId) {
        if (!StringUtils.isEmpty(refundId)) {
            if (!baseService.saveOperationHisById(refundId, 3)) {
                log.error("退款申请表删除后保存历史失败:" + refundId);
                throw new McpException("退款申请表删除后保存历史失败");
            }

            if (!baseService.removeById(refundId)) {
                log.error("退款申请表删除失败");
                throw new McpException("退款申请表删除失败" + refundId);
            }
        } else {
            throw new McpException("退款申请表删除失败退款id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param refundIdList 需要删除的退款id
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> refundIdList) {
        if (!CollectionUtils.isEmpty(refundIdList)) {
            int oldSize = refundIdList.size();
            refundIdList = refundIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(refundIdList) || oldSize != refundIdList.size()) {
                throw new McpException("退款申请表批量删除失败 存在主键id为空的记录" + StringUtils.join(refundIdList));
            }

            if (!baseService.saveOperationHisByIds(refundIdList, 3)) {
                log.error("退款申请表批量删除后保存历史失败:" + StringUtils.join(refundIdList));
                throw new McpException("退款申请表批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(refundIdList)) {
                log.error("退款申请表批量删除失败");
                throw new McpException("退款申请表批量删除失败" + StringUtils.join(refundIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的退款申请表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsRefundInfoVo vo) {
        if (vo != null) {
            BbsRefundInfoEntity entity = new BbsRefundInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getRefundId())) {
                throw new McpException("退款申请表更新失败传入退款id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("退款申请表更新失败");
                throw new McpException("退款申请表更新失败" + entity.getRefundId());
            } else {
                if (!baseService.saveOperationHisById(entity.getRefundId(), 2)) {
                    log.error("退款申请表更新后保存历史失败:" + entity.getRefundId());
                    throw new McpException("退款申请表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("退款申请表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的退款申请表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsRefundInfoVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsRefundInfoEntity> entityList = new ArrayList<>();

            for (BbsRefundInfoVo item : voList) {
                BbsRefundInfoEntity entity = new BbsRefundInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getRefundId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("退款申请表批量更新失败 存在退款id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("退款申请表批量更新失败");
                throw new McpException("退款申请表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getRefundId())).map(BbsRefundInfoEntity::getRefundId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("退款申请表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("退款申请表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的退款申请表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsRefundInfoVo vo) {
        if (vo != null) {
            BbsRefundInfoEntity entity = new BbsRefundInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("退款申请表保存失败");
                throw new McpException("退款申请表保存失败" + entity.getRefundId());
            } else {
                if (!baseService.saveOperationHisById(entity.getRefundId(), 4)) {
                    log.error("退款申请表保存后保存历史失败:" + entity.getRefundId());
                    throw new McpException("退款申请表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("退款申请表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的退款申请表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsRefundInfoVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsRefundInfoEntity> entityList = new ArrayList<>();

            for (BbsRefundInfoVo item : voList) {
                BbsRefundInfoEntity entity = new BbsRefundInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("退款申请表批量保存失败");
                throw new McpException("退款申请表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getRefundId())).map(BbsRefundInfoEntity::getRefundId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("退款申请表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("退款申请表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param refundId 需要查询的退款id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsRefundInfoVo selectByIdRecord(String refundId) {
        BbsRefundInfoVo vo = new BbsRefundInfoVo();
        if (!StringUtils.isEmpty(refundId)) {
            BbsRefundInfoEntity entity = baseService.selectById(refundId);
            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsRefundInfoPageResultVo>> selectByPageRecord(BbsRefundInfoPageVo vo) {
        List<BbsRefundInfoPageResultVo> result = baseMapper.selectByPageCustom(vo);
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }

    /**
     * 根据合同编号查询房源列表
     *
     * @param contractCode 合同编号
     * @return 房源列表
     */
    @Override
    public List<BbctContractSubjectMatterVo> selectProductListByContractCode(String contractCode) {
        BbctContractManagementVo contractManagementVo = selectContractByIdNo(contractCode);
        return contractManagementVo.getSubjectMatterList();
    }

    /**
     * 根据合同编号查询承租人信息
     *
     * @param contractCode 合同编号
     * @return 房源列表
     */
    @Override
    public BbctContractSignerVo selectCustomerByContractCode(String contractCode) {
        BbctContractManagementVo contractManagementVo = selectContractByIdNo(contractCode);
        return contractManagementVo.getUserList().get(0);
    }

    /**
     * 根据主键查询退款金额明细
     *
     * @param refundId 退款id
     * @return 退款金额明细 vo实体
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<BbctRefundAmountDetailVo> selectRefundAmountListById(String refundId) {
        BbsRefundInfoVo refundInfoVo = selectByIdRecord(refundId);
        BbsiContractChangeVo contractChangeVo = contractChangeService.selectBySignId(refundInfoVo.getSignId());
        if (Objects.isNull(contractChangeVo)) {
            throw new McpException(String.format("查询退款金额明细失败，合同变更信息不存在[refundId=%s]", refundId));
        }
        String cashPledgeRefundAmountStr;
        String rentRefundAmountStr;
        String propertyAmountStr;
        if (RefundChangeTypeEnum.RENT_CHANGE.getCode().equals(refundInfoVo.getChangeType())) {
            // 租金变更
            cashPledgeRefundAmountStr = contractChangeVo.getRentAmountReceivable();
            rentRefundAmountStr = contractChangeVo.getRentAmountReceivableLease();
            propertyAmountStr = contractChangeVo.getPropertyAmountReceivable();
        } else if (RefundChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode().equals(refundInfoVo.getChangeType())
                    || RefundChangeTypeEnum.RECEIVABLE_ADJUSTMENT_CHANGE.getCode().equals(refundInfoVo.getChangeType())) {
            // 计租面积变更  、应收调整变更
            cashPledgeRefundAmountStr = contractChangeVo.getRentAmountReceivable();
            rentRefundAmountStr = contractChangeVo.getRentAmountReceivableLease();
            propertyAmountStr = contractChangeVo.getPropertyAmountReceivable();
        } else if (RefundChangeTypeEnum.CASH_PLEDGE_CHANGE.getCode().equals(refundInfoVo.getChangeType())) {
            // 保证金变更
            cashPledgeRefundAmountStr = contractChangeVo.getCashAmountReceivable();
            rentRefundAmountStr = KeywordConst.ZERO_STR;
            propertyAmountStr =  KeywordConst.ZERO_STR;
        } else if (RefundChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode().equals(refundInfoVo.getChangeType())) {
            // 缩租面积变更
            cashPledgeRefundAmountStr = contractChangeVo.getReduceAmountReceivable();
            rentRefundAmountStr = contractChangeVo.getReduceAmountReceivableLease();
            propertyAmountStr = contractChangeVo.getPropertyAmountReceivable();
        } else if (RefundChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode().equals(refundInfoVo.getChangeType())) {
            // 免租期变更
            String feesSummaryJson = contractChangeVo.getFeesSummaryJson();
            List<FeesSummaryVo> feesSummaryList = FeesSummaryJsonConverter.convertFromJson(feesSummaryJson);
            // 初始化变量
            cashPledgeRefundAmountStr = KeywordConst.ZERO_STR;
            rentRefundAmountStr = KeywordConst.ZERO_STR;
            propertyAmountStr = KeywordConst.ZERO_STR;
            // 从费用汇总列表中提取不同类型的费用
            if (CollectionUtils.isNotEmpty(feesSummaryList)) {
                for (FeesSummaryVo feesSummary : feesSummaryList) {
                    // 跳过合计行
                    if ("合计".equals(feesSummary.getFeeItem())) {
                        continue;
                    }
                    // 根据费用项目名称提取对应的金额，按feeItem类型累加refundTotalAmount
                    if ("房屋租金".equals(feesSummary.getFeeItem())) {
                        // 房屋租金累加赋值给rentRefundAmountStr
                        BigDecimal currentAmount = feesSummary.getRefundTotalAmount() != null ? feesSummary.getRefundTotalAmount() : BigDecimal.ZERO;
                        BigDecimal existingAmount = new BigDecimal(rentRefundAmountStr);
                        rentRefundAmountStr = existingAmount.add(currentAmount).toString();
                    } else if ("押金".equals(feesSummary.getFeeItem())) {
                        // 押金累加赋值给cashPledgeRefundAmountStr
                        BigDecimal currentAmount = feesSummary.getRefundTotalAmount() != null ? feesSummary.getRefundTotalAmount() : BigDecimal.ZERO;
                        BigDecimal existingAmount = new BigDecimal(cashPledgeRefundAmountStr);
                        cashPledgeRefundAmountStr = existingAmount.add(currentAmount).toString();
                    } else if ("物业费".equals(feesSummary.getFeeItem())) {
                        // 物业费累加赋值给propertyAmountStr
                        BigDecimal currentAmount = feesSummary.getRefundTotalAmount() != null ? feesSummary.getRefundTotalAmount() : BigDecimal.ZERO;
                        BigDecimal existingAmount = new BigDecimal(propertyAmountStr);
                        propertyAmountStr = existingAmount.add(currentAmount).toString();
                    }
                }
            }
        } else {
            throw new McpException(String.format("查询退款金额明细失败，变更类型异常[refundId=%s, changeType=%s]",
                    refundId, refundInfoVo.getChangeType()));
        }
        List<BbctRefundAmountDetailVo> detailList = new ArrayList<>();
        // 应退押金金额
        BbctRefundAmountDetailVo cashPledgeRefundAmountDetailVo = new BbctRefundAmountDetailVo();
        cashPledgeRefundAmountDetailVo.setRefundAmountTypeName("应退押金金额");
        cashPledgeRefundAmountDetailVo.setRefundAmountName(cashPledgeRefundAmountStr);
        detailList.add(cashPledgeRefundAmountDetailVo);
        // 应退租金金额
        BbctRefundAmountDetailVo rentRefundAmountDetailVo = new BbctRefundAmountDetailVo();
        rentRefundAmountDetailVo.setRefundAmountTypeName("应退租金金额");
        rentRefundAmountDetailVo.setRefundAmountName(rentRefundAmountStr);
        detailList.add(rentRefundAmountDetailVo);
        // 应退物业费金额
        BbctRefundAmountDetailVo propertyAmountDetailVo = new BbctRefundAmountDetailVo();
        propertyAmountDetailVo.setRefundAmountTypeName("应退物业费金额");
        propertyAmountDetailVo.setRefundAmountName(propertyAmountStr);
        detailList.add(propertyAmountDetailVo);
        // 合计金额
        BbctRefundAmountDetailVo totalRefundAmountDetailVo = new BbctRefundAmountDetailVo();
        totalRefundAmountDetailVo.setRefundAmountTypeName("合计金额");
        totalRefundAmountDetailVo.setRefundAmountName("应退" + MoneyUtil.plus(cashPledgeRefundAmountStr, rentRefundAmountStr,propertyAmountStr));
        detailList.add(totalRefundAmountDetailVo);
        return detailList;
    }

    /**
     * 根据签约id删除
     *
     * @param signId 签约id
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeBySignId(String signId) {
        baseMapper.deleteBySignId(signId);
    }

    /**
     * 退款申请提交
     *
     * @param idVo 主键id vo实体
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbfwRequestBusinessMappingVo refundApplySubmit(BbsIdVo idVo) {
        // refundApproveInfoService.submit(idVo.getId());
        // BbsRefundInfoVo refundInfoVo = new BbsRefundInfoVo();
        // refundInfoVo.setRefundId(idVo.getId());
        // refundInfoVo.setRefundStatus(RefundStatusEnum.REFUND_BUSINESS_APPROVE.getCode());
        // updateByIdRecord(refundInfoVo);

        //验证idVo是否为空
        if (Objects.isNull(idVo)) {
            throw new McpException("idVo不能为空");
        }
        //验证idVo.getId()为空时跑出异常
        if (StringUtils.isBlank(idVo.getId())) {
            throw new McpException("id不能为空");
        }
        //验证workflowId为空或者空字符串时跑出异常
        if (StringUtils.isBlank(idVo.getWorkflowId())) {
            throw new McpException("workflowId不能为空");
        }
        //验证businessTypeCode为空或者空字符串时跑出异常
        if (StringUtils.isBlank(idVo.getBusinessTypeCode())) {
            throw new McpException("businessTypeCode不能为空");
        }

        //查询退款申请表是否存在
        BbsRefundInfoVo refundInfoVo = new BbsRefundInfoVo();
        refundInfoVo.setRefundId(idVo.getId());
        refundInfoVo = selectByIdRecord(idVo.getId());
        if (Objects.isNull(refundInfoVo)) {
            throw new McpException("退款申请表不存在");
        }

        String refundStatus = refundInfoVo.getRefundStatus();
        
        if(!RefundStatusEnum.REFUND_FAILED.getCode().equals(refundStatus)){
              //不是退款失败时
             //验证requestId是否为空
             if(StringUtils.isBlank(idVo.getRequestId())){
                //首次提交
                OaCreateWorkFlowRequestV2Vo flowRequestV2Vo = new OaCreateWorkFlowRequestV2Vo();
                flowRequestV2Vo.setBusinessId(idVo.getId());
                flowRequestV2Vo.setWorkflowId(idVo.getWorkflowId());
                flowRequestV2Vo.setBusinessTypeCode(idVo.getBusinessTypeCode());
                flowRequestV2Vo.setIsnextflow(1);//提交
                flowRequestV2Vo.setRequestname(workflowCommponent.getWorkName(refundInfoVo.getContractNo(),"3"));

                try {
                    //创建流程
                    Long requestId = this.workflowService.doCreateRequest(flowRequestV2Vo);

                     //更新退款申请表状态
                    refundInfoVo.setRefundId(idVo.getId());
                    refundInfoVo.setRefundStatus(RefundStatusEnum.REFUND_BUSINESS_APPROVE.getCode());
                    //后加2025-06-30 待测试
                    refundInfoVo.setRefundProcess(RefundProcessEnum.NO_REFUND.getCode());
                    updateByIdRecord(refundInfoVo);

                    BbfwRequestBusinessMappingVo bbfwRequestBusinessMappingVo = new BbfwRequestBusinessMappingVo();
                    bbfwRequestBusinessMappingVo.setRequestId(requestId.toString());
                    bbfwRequestBusinessMappingVo.setWorkflowId(idVo.getWorkflowId());
                    bbfwRequestBusinessMappingVo.setBusinessTypeCode(idVo.getBusinessTypeCode());
                    bbfwRequestBusinessMappingVo.setBusinessId(idVo.getId());
                    return  bbfwRequestBusinessMappingVo;
                }catch (Exception e){
                    log.error("创建流程失败");
                    throw new McpException("创建流程失败:"+e.getMessage());
                }
            }else{
                //驳回后提交
                BaseFlowVo flowVo = new BaseFlowVo();
                if((idVo.getRequestId()!=null)) {
                    flowVo.setRequestId(idVo.getRequestId().toString());
                }else{
                    throw new McpException("requestId不能为空");
                }
                this.workflowService.submitRequest(flowVo);

                 //更新退款申请表状态
                refundInfoVo.setRefundId(idVo.getId());
                refundInfoVo.setRefundStatus(RefundStatusEnum.REFUND_BUSINESS_APPROVE.getCode());
                updateByIdRecord(refundInfoVo);

                BbfwRequestBusinessMappingVo bbfwRequestBusinessMappingVo = new BbfwRequestBusinessMappingVo();
                bbfwRequestBusinessMappingVo.setRequestId(idVo.getRequestId().toString());
                return bbfwRequestBusinessMappingVo;
            }      
        }else{
            /**
             * 退款不需要再发起流程
             * 退款失败时 要重新复制一条数据处理发起流程
             * 退款失败重新发起流程，其实不在这里，前端到时调用的是 /updateRefundProcess: 更新退款进度接口，而且之前没有推送工银，流程其实不完整（由于工银没提供接口，不了了之）
             */
        }
        return null;
    }

    /**
     * 处理退款申请业务审批
     *
     * @param approveVo 审批 vo实体
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void dealBusinessApprove(BbctApproveVo approveVo) {
        String refundId = approveVo.getId();
        if (ApproveStatusEnum.APPROVED.getCode().equals(approveVo.getStatus())) {
            // refundApproveInfoService.approve(refundId, approveVo.getRemark());
            BbsRefundInfoVo refundInfoVo = new BbsRefundInfoVo();
            refundInfoVo.setRefundId(refundId);
            refundInfoVo.setRefundStatus(RefundStatusEnum.REFUNDING.getCode());
            refundInfoVo.setRefundProcess(RefundProcessEnum.NO_REFUND.getCode());
            updateByIdRecord(refundInfoVo);
            pushRefundInfo(refundId);
        } else if (ApproveStatusEnum.UNAPPROVED.getCode().equals(approveVo.getStatus())) {
            // refundApproveInfoService.unApprove(refundId, approveVo.getRemark());
            BbsRefundInfoVo refundInfoVo = new BbsRefundInfoVo();
            refundInfoVo.setRefundId(refundId);
            refundInfoVo.setRefundStatus(RefundStatusEnum.REFUND_SUBMIT_AGAIN.getCode());
            //后加2025-06-30 待测试
            refundInfoVo.setRefundProcess(RefundProcessEnum.NO_REFUND.getCode());
            updateByIdRecord(refundInfoVo);
        } else {
            throw new McpException(String.format("处理审核失败[refundId: %s]", refundId));
        }
    }

    /**
     * 更新退款进度
     *
     * @param statusVo 状态 vo实体
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateRefundProcess(BbctStatusVo statusVo) {
        String refundId = statusVo.getId();
        if (RefundProcessEnum.NO_REFUND.getCode().equals(statusVo.getStatus())) {
            // 查询当前退款信息
            BbsRefundInfoVo currentRefundInfo = selectByIdRecord(refundId);

            BbsRefundInfoVo refundInfoVo = new BbsRefundInfoVo();
            refundInfoVo.setRefundId(refundId);
            refundInfoVo.setRefundStatus(RefundStatusEnum.REFUNDING.getCode());
            refundInfoVo.setRefundProcess(RefundProcessEnum.NO_REFUND.getCode());
            updateByIdRecord(refundInfoVo);

            // 判断是否需要推送退款信息：当前状态为退款失败(6)且进度为退款失败(2)时，需要重新推送
            if (RefundStatusEnum.REFUND_FAILED.getCode().equals(currentRefundInfo.getRefundStatus()) 
                && RefundProcessEnum.REFUND_FAILED.getCode().equals(currentRefundInfo.getRefundProcess())) {
                log.info("修改退款路径后需要重新推送退款信息给工银[refundId: {}]", refundId);
                pushRefundInfo(refundId);
            }
        } else if (RefundProcessEnum.REFUND_FAILED.getCode().equals(statusVo.getStatus())) {
            BbsRefundInfoVo refundInfoVo = new BbsRefundInfoVo();
            refundInfoVo.setRefundId(refundId);
            refundInfoVo.setRefundStatus(RefundStatusEnum.REFUND_FAILED.getCode());
            refundInfoVo.setRefundProcess(RefundProcessEnum.REFUND_FAILED.getCode());
            updateByIdRecord(refundInfoVo);
        } else if (RefundProcessEnum.REFUND_SUCCESS.getCode().equals(statusVo.getStatus())) {
            BbsRefundInfoVo refundInfoVo = new BbsRefundInfoVo();
            refundInfoVo.setRefundId(refundId);
            refundInfoVo.setRefundStatus(RefundStatusEnum.REFUND_SUCCESS.getCode());
            refundInfoVo.setRefundProcess(RefundProcessEnum.REFUND_SUCCESS.getCode());
            updateByIdRecord(refundInfoVo);
        } else {
            throw new McpException(String.format("更新退款进度失败[refundId: %s]", refundId));
        }
    }

    /**
     * 结算中心更新退款进度
     *
     * @param statusVo 状态 vo实体
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void chargeUpdateRefundProcess(BbctStatusVo statusVo) {
        String paymentCode = statusVo.getId();
        if (RefundProcessEnum.REFUND_FAILED.getCode().equals(statusVo.getStatus())) {
            new LambdaUpdateChainWrapper<>(baseMapper)
                    .set(BbsRefundInfoEntity::getRefundStatus, RefundStatusEnum.REFUND_FAILED.getCode())
                    .set(BbsRefundInfoEntity::getRefundProcess, RefundProcessEnum.REFUND_FAILED.getCode())
                    .eq(BbsRefundInfoEntity::getPaymentCode, paymentCode)
                    .update();
        } else if (RefundProcessEnum.REFUND_SUCCESS.getCode().equals(statusVo.getStatus())) {
            new LambdaUpdateChainWrapper<>(baseMapper)
                    .set(BbsRefundInfoEntity::getRefundStatus, RefundStatusEnum.REFUND_SUCCESS.getCode())
                    .set(BbsRefundInfoEntity::getRefundProcess, RefundProcessEnum.REFUND_SUCCESS.getCode())
                    .eq(BbsRefundInfoEntity::getPaymentCode, paymentCode)
                    .update();
        } else {
            throw new McpException(String.format("更新退款进度失败[paymentCode: %s]", paymentCode));
        }
    }

    /**
     * 推送退款信息
     *
     * @param refundId 退款id
     */
    private void pushRefundInfo(String refundId) {
        BbsRefundInfoVo refundInfoVo = selectByIdRecord(refundId);
        BusinessGeneratePaymentByGXParamVo requestVo = new BusinessGeneratePaymentByGXParamVo();
        BbctContractManagementVo contractManagementVo = selectContractByIdNo(refundInfoVo.getContractNo());
        requestVo.setProjectId(contractManagementVo.getSubjectMatterList().get(0).getProjectId());
        BbctRefundUseChangeInfoVo refundUseChangeInfoVo = contractChangeService
                .refundUseChangeInfo(refundInfoVo.getSignId(), refundInfoVo.getChangeType());
        requestVo.setRequestDate(refundUseChangeInfoVo.getRequestDate());
        requestVo.setChangeAccountingPeriodType(refundUseChangeInfoVo.getChangeAccountingPeriodType());
        requestVo.setRefundId(refundId);
        requestVo.setContractCode(refundInfoVo.getContractNo());
        requestVo.setName(refundInfoVo.getCustomerName());
        requestVo.setCertNo(refundInfoVo.getCustomerIdNumber());
        requestVo.setBankAccountNo(refundInfoVo.getBankAccountId());
        requestVo.setBankCode(refundInfoVo.getBankCode());
        requestVo.setBankName(refundInfoVo.getBankName());
        requestVo.setBankBranchCode(refundInfoVo.getSubBankCode());
        requestVo.setBankBranchName(refundInfoVo.getSubBankName());
        requestVo.setBankProvinceName(refundInfoVo.getBankProvinceName());
        requestVo.setBankCityName(refundInfoVo.getBankCityName());
        requestVo.setIsOriginReturn(BackTypeEnum.SIGN_BANK_CARD.getCode().equals(refundInfoVo.getBackType()) ?
                PaymentBackTypeEnum.BACK_TRACK.getCode() : PaymentBackTypeEnum.NON_BACK_TRACK.getCode());
        requestVo.setRegenerate(StringUtils.isBlank(refundInfoVo.getPaymentCode()) ?
                WhetherEnum.NO.getCode() : WhetherEnum.YES.getCode());
        requestVo.setPaymentCode(refundInfoVo.getPaymentCode());
        if(refundInfoVo.getChangeType() != null && "6".equals(refundInfoVo.getChangeType())){
            requestVo.setSource("2");
        }else{
            requestVo.setSource("1");
        }
        if (CustomerTypeEnum.COMPANY.getCode().equals(contractManagementVo.getUserList().get(0).getCustomerType())) {
            requestVo.setOwner("01");
        } else {
            requestVo.setOwner("02");
        }
        // 缴费中心 推送退款信息
        BusinessGeneratePaymentByGXResultVo paymentInfoVo = pushBusinessGeneratePayment(requestVo);
        // 更新付款单唯一标识
        BbsRefundInfoVo updateVo = new BbsRefundInfoVo();
        updateVo.setRefundId(refundId);
        updateVo.setPaymentCode(paymentInfoVo.getPaymentCode());
        updateByIdRecord(updateVo);
    }

    /**
     * 推送退款信息
     *
     * @param requestVo 退款信息推送请求参数 vo实体
     * @return 付款单信息
     */
    private BusinessGeneratePaymentByGXResultVo pushBusinessGeneratePayment(BusinessGeneratePaymentByGXParamVo requestVo) {
        AppReply<BusinessGeneratePaymentByGXResultVo> appReply = paymentV2FeignClient.pushBusinessGeneratePayment(requestVo);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("推送退款信息失败[详情: %s, refundId: %s]", appReply, requestVo.getRefundId()));
        }
        return appReply.getData();
    }

    /**
     * 合同中心 查询合同信息
     *
     * @param contractCode 合同编号
     * @return 合同信息
     */
    private BbctContractManagementVo selectContractByIdNo(String contractCode) {
        AppReply<BbctContractManagementVo> appReply = contractFeignClient.selectByIdNo(contractCode, null);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("查询合同信息失败[详情: %s, contractCode: %s]", appReply, contractCode));
        }
        return appReply.getData();
    }
}
