package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalIncrementalConfigEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 签约-递增设置（当租金/物业费是否递增标识为1时有效） 服务类
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
public interface IBbsRenewalIncrementalConfigService extends IMcpBaseService<BbsRenewalIncrementalConfigEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    String insertRecord(BbsRenewalIncrementalConfigVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    List<String> insertBatchRecord(List<BbsRenewalIncrementalConfigVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param sicId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void removeByIdRecord(String sicId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param sicIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void removeByIdsRecord(List<String> sicIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的签约-递增设置（当租金/物业费是否递增标识为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void updateByIdRecord(BbsRenewalIncrementalConfigVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的签约-递增设置（当租金/物业费是否递增标识为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void updateBatchByIdRecord(List<BbsRenewalIncrementalConfigVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的签约-递增设置（当租金/物业费是否递增标识为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void saveByIdRecord(BbsRenewalIncrementalConfigVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的签约-递增设置（当租金/物业费是否递增标识为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void saveBatchByIdRecord(List<BbsRenewalIncrementalConfigVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param sicId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    BbsRenewalIncrementalConfigVo selectByIdRecord(String sicId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    PageResult<List<BbsRenewalIncrementalConfigPageResultVo>> selectByPageRecord(BbsRenewalIncrementalConfigPageVo vo);

    /**
     * 根据签约id和标准类型查询
     *
     * @param signId       签约id
     * @param standardType 标准类型
     * @return 递增设置列表
     */
    List<BbsRenewalIncrementalConfigVo> selectBySignIdAndStandardType(String signId, String standardType);
}
