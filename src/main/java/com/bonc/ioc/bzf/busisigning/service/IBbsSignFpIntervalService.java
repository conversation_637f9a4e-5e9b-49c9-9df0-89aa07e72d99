package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsSignFpIntervalEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 签约-免租期-区间（当租金/物业费免租期分类为1时有效） 服务类
 *
 * <AUTHOR>
 * @date 2023-09-01
 * @change 2023-09-01 by liwenqi<PERSON> for init
 */
public interface IBbsSignFpIntervalService extends IMcpBaseService<BbsSignFpIntervalEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change
     * 2023-09-01 by liwenqi<PERSON> for init
     */
    String insertRecord(BbsSignFpIntervalVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change
     * 2023-09-01 by liwenqiang for init
     */
    List<String> insertBatchRecord(List<BbsSignFpIntervalVo> voList);


    List<String> insertBatchRecordBbsSignFpIntervalEntity(List<BbsSignFpIntervalEntity> voList);
    /**
     * removeByIdRecord 根据主键删除
     * @param sfiId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change
     * 2023-09-01 by liwenqiang for init
     */
    void removeByIdRecord(String sfiId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param sfiIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change
     * 2023-09-01 by liwenqiang for init
     */
    void removeByIdsRecord(List<String> sfiIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change
     * 2023-09-01 by liwenqiang for init
     */
    void updateByIdRecord(BbsSignFpIntervalVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change
     * 2023-09-01 by liwenqiang for init
     */
    void updateBatchByIdRecord(List<BbsSignFpIntervalVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change
     * 2023-09-01 by liwenqiang for init
     */
    void saveByIdRecord(BbsSignFpIntervalVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change
     * 2023-09-01 by liwenqiang for init
     */
    void saveBatchByIdRecord(List<BbsSignFpIntervalVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param sfiId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change
     * 2023-09-01 by liwenqiang for init
     */
    BbsSignFpIntervalVo selectByIdRecord(String sfiId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change
     * 2023-09-01 by liwenqiang for init
     */
    PageResult<List<BbsSignFpIntervalPageResultVo>> selectByPageRecord(BbsSignFpIntervalPageVo vo);

    /**
     * 根据签约id和标准类型查询
     *
     * @param signId       签约id
     * @param standardType 标准类型
     * @return 免租期信息列表
     */
    List<BbsSignFpIntervalEntity> selectBySignIdAndStandardType(String signId, String standardType);
}
