package com.bonc.ioc.bzf.busisigning.workflow.vo;


import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * requestId和业务中台组件映射表 实体类
 *
 * <AUTHOR>
 * @date 2025-04-19
 * @change 2025-04-19 by 宋鑫 for init
 */
@Data
@ApiModel(value="BbfwRequestBusinessMappingVo对象", description="requestId和业务中台组件映射表")
public class BbfwRequestBusinessMappingVo extends McpBaseVo implements Serializable{


    /**
     * 泛微审批流id
     */
    @ApiModelProperty(value = "泛微审批流id")
    @NotBlank(message = "泛微审批流id不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    private String requestId;

    /**
     * 业务中台id
     */
    @ApiModelProperty(value = "业务中台id")
    @NotBlank(message = "业务中台id不能为空",groups = {UpdateValidatorGroup.class , InsertValidatorGroup.class})
    private String businessId;

    /**
     * 工作流id
     */
    @ApiModelProperty(value = "工作流id")
    @NotBlank(message = "工作流id不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    private String workflowId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 其他参数(JSON)
     */
    @ApiModelProperty(value = "其他参数(JSON)")
    private String jsonMsg;

    /**
     * 删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标识（1 未删除 0 已删除）" ,hidden = true)
    private Integer delFlag;


    /**
     * 业务分类编号
     */
    @ApiModelProperty(value = "业务分类编号")
    @NotBlank(message = "业务分类编号不能为空",groups = {UpdateValidatorGroup.class , InsertValidatorGroup.class})
    private String businessTypeCode;

}
