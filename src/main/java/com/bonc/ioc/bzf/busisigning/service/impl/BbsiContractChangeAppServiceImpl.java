package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.dao.BbsContractChangeApproveDetailMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsContractChangeApproveInfoMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsiContractChangeMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsContractChangeApproveDetailEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsContractChangeApproveInfoEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeEntity;
import com.bonc.ioc.bzf.busisigning.enums.ApproveStatusEnum;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.feign.feign.BzfBusinessMessageFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.vo.WebsiteMsgContentVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.WebsiteMsgVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeAppService;
import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeService;
import com.bonc.ioc.bzf.busisigning.utils.UserUtil;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.BankCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.BankCardOCRResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 合同变更表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by fzq for init
 */
@Slf4j
@Service
public class BbsiContractChangeAppServiceImpl extends McpBaseServiceImpl<BbsiContractChangeEntity> implements IBbsiContractChangeAppService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsiContractChangeMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsiContractChangeAppService baseService;

    /**
     * 获取登录用户
     */
    @Resource
    private UserUtil userUtil;

    @Resource
    private IBbsiContractChangeService contractChangeService;

    @Resource
    private BbsContractChangeApproveInfoMapper bbsContractChangeApproveInfoMapper;

    @Resource
    private BbsContractChangeApproveDetailMapper bbsContractChangeApproveDetailMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Value("${tencentOcr.secretId:AKIDa7PXQ4VigT3PakogxXyCCxfK5g3AObfc}")
    private String secretId;

    @Value("${tencentOcr.secretKey:rUBeuCGKGzWpunRq6D8qKApbItTs9NQH}")
    private String secretKey;
    @Value("${bzfsystem.messageTemplate.contractChangeApplay.mobileCodeTemplateId:7ccccc1d7fc147cfb4bbea5aa42d5735}")
    private String mobileCodeTemplateId;

    /**
     * 消息中心FeignClient
     */
    @Resource
    private BzfBusinessMessageFeignClient bzfBusinessMessageFeignClient;

    /**
     * 环境变量
     */
    @Resource
    private Environment env;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2023-08-29
     * @change 2023-08-29 by fzq for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbsiContractChangeVo vo) {
        if (vo == null) {
            return null;
        }
        BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
        BeanUtils.copyProperties(vo, entity);
        entity.setDelFlag(DelFlagEnum.UNDELETED.getCode());
        String userId = userUtil.getUserId();
        String userName = userUtil.getUserName(userId);
        entity.setChangeUserId(userId);
        entity.setChangeUserName(userName);
        if (!baseService.insert(entity)) {
            log.error("合同变更表新增失败:" + entity.toString());
            throw new McpException("合同变更表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getCcId(), 1)) {
                log.error("合同变更表新增后保存历史失败:" + entity.toString());
                throw new McpException("合同变更表新增后保存历史失败");
            }
            if ("1".equals(vo.getChangeStatus())) {
                //contractChangeResultService.contractChange(vo);
                BbsContractChangeApproveInfoEntity bbsContractChangeApproveInfoEntity = new BbsContractChangeApproveInfoEntity();
                bbsContractChangeApproveInfoEntity.setApproverUserId(userId);
                bbsContractChangeApproveInfoEntity.setApproverUserName(userName);
                bbsContractChangeApproveInfoEntity.setCcId(entity.getCcId());
                bbsContractChangeApproveInfoEntity.setContractCode(vo.getContractCode());
                bbsContractChangeApproveInfoEntity.setApproveStatus(ApproveStatusEnum.WAIT_APPROVE.getCode());
                bbsContractChangeApproveInfoEntity.setApproveTime(new Date());
                bbsContractChangeApproveInfoMapper.insert(bbsContractChangeApproveInfoEntity);
                BbsContractChangeApproveDetailEntity bbsContractChangeApproveDetailEntity = new BbsContractChangeApproveDetailEntity();
                bbsContractChangeApproveDetailEntity.setApproverUserId(userId);
                bbsContractChangeApproveDetailEntity.setApproverUserName(userName);
                bbsContractChangeApproveDetailEntity.setApproveStatus(ApproveStatusEnum.WAIT_APPROVE.getCode());
                bbsContractChangeApproveDetailEntity.setApproveTime(new Date());
                bbsContractChangeApproveDetailEntity.setCcId(entity.getCcId());
                bbsContractChangeApproveDetailEntity.setDescription("变更申请提交");
                bbsContractChangeApproveDetailMapper.insert(bbsContractChangeApproveDetailEntity);
            }
            log.debug("合同变更表新增成功:" + entity.getCcId());
            return entity.getCcId();
        }
    }

    @Override
    public List<BbsiContractChangeRecordVo> selectChangeRecordList(String contractCode) {
        return baseMapper.selectAppChangeRecordList(contractCode);
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param ccId 需要查询的
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsiContractChangeVo selectByIdRecord(String ccId) {
        BbsiContractChangeVo vo = new BbsiContractChangeVo();

        if (!StringUtils.isEmpty(ccId)) {
            BbsiContractChangeEntity entity = baseService.selectById(ccId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                BbsiContractChangeCustomerVo bbsiContractChangeCustomerVo = contractChangeService.selectCustomInfo(vo.getContractCode());
                if (bbsiContractChangeCustomerVo == null) {
                    throw new McpException("客户信息查询为null");
                }
                vo.setCustomerTypeCode(bbsiContractChangeCustomerVo.getCustomerType());
                vo.setProjectId(bbsiContractChangeCustomerVo.getProjectId());
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public BbsiContractChangeVo selectByContractCodeAndApproveStatus(String contractCode) {
        if (StringUtils.isEmpty(contractCode)) {
            throw new McpException("入参contractCode为空");
        }
        List<BbsiContractChangeVo> voList = baseMapper.selectByContractCodeAndApproveStatus(contractCode);
        if (voList == null || voList.isEmpty()) {
            throw new McpException("未查询到任何合同记录");
        }
        BbsiContractChangeVo vo = voList.get(0);
        BbsiContractChangeCustomerVo bbsiContractChangeCustomerVo = contractChangeService.selectCustomInfo(vo.getContractCode());
        if (bbsiContractChangeCustomerVo == null) {
            throw new McpException("客户信息查询为null");
        }
        vo.setCustomerTypeCode(bbsiContractChangeCustomerVo.getCustomerType());
        return vo;
    }

    @Override
    public List<BbsContractChangeApproveDetailVo> selectApproveDetail(String ccId) {
        return baseMapper.selectApproveDetail(ccId);
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param ccId 需要删除的
     * @return void
     * <AUTHOR>
     * @date 2023-08-30
     * @change 2023-08-30 by fzq for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String ccId) {
        if (!StringUtils.isEmpty(ccId)) {
            if (!baseService.saveOperationHisById(ccId, 3)) {
                log.error("合同变更表删除后保存历史失败:" + ccId);
                throw new McpException("合同变更表删除后保存历史失败");
            }
            BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
            entity.setCcId(ccId);
            entity.setDelFlag(DelFlagEnum.DELETED.getCode());
            if (!baseService.updateById(entity)) {
                log.error("合同变更表删除失败");
                throw new McpException("合同变更表删除失败" + ccId);
            }
        } else {
            throw new McpException("合同变更表删除失败为空");
        }
    }

    @Override
    public BankCardOCRResponse bankCardOCR(String imageBase64) {
        BankCardOCRResponse resp;
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(secretId, secretKey);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            OcrClient client = new OcrClient(cred, "ap-beijing", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            BankCardOCRRequest req = new BankCardOCRRequest();
            req.setImageBase64(imageBase64);
            // 返回的resp是一个IDCardOCRResponse的实例，与请求对象对应
            resp = client.BankCardOCR(req);
            log.info("[OCR识别]识别结果=[{}]", JSONObject.toJSONString(resp));
            return resp;
        } catch (TencentCloudSDKException e) {
            log.error("[OCR识别]识别异常", e);
            throw new McpException("[OCR识别]识别异常", e.getMessage());
        }
    }

    @Override
    public void getVerificationCode(VerificationCodeVo vo) {
        String key = vo.getTel() + ":" + vo.getCcId();
        String code = "";
        Random random = new Random();
        //生成六位数字验证码
        for (int i = 0; i < 6; i++) {
            int num = random.nextInt(10);
            code += num;
        }
        log.info("【{}】短信验证码【{}】", key, code);
        redisTemplate.opsForValue().set(key, code, 10, TimeUnit.MINUTES);
        AppReply appReply = sendMessage(vo.getTel(), code);
        if (!appReply.getCode().equals(AppReply.SUCCESS_CODE)) {
            throw new McpException(appReply.getMsg());
        }
    }

    @Override
    public Boolean checkVerificationCode(VerificationCodeVo vo) {
        String key = vo.getTel() + ":" + vo.getCcId();
        String redisCode = (String) redisTemplate.opsForValue().get(key);
        if (redisCode == null) {
            throw new McpException("验证码已过期");
        } else if (StringUtils.isEmpty(vo.getCode())) {
            throw new McpException("验证码为空");
        } else if (redisCode.equals(vo.getCode())) {
            return true;
        } else {
            throw new McpException("验证码填写错误");
        }
    }

    /**
     * 发送短信验证码
     * @param phone
     * @param code
     * @return
     */
    private AppReply sendMessage(String phone,String code) {
        //debug调不通 所以不发送
        if (Objects.equals(env.getProperty("spring.profiles.active"), "debug")) {
            return new AppReply<>(AppReply.SUCCESS_CODE, "debug", null);
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("bzfSmsContent", code);
        List<WebsiteMsgContentVo> websiteMsgContentVo = Arrays.asList(new WebsiteMsgContentVo(paramMap, phone, IdUtil.fastSimpleUUID()));
        WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(String.valueOf(mobileCodeTemplateId), "短信验证码", websiteMsgContentVo);
        log.info("发送短信验证码入参==={}", JSON.toJSONString(websiteMsgVo));
        AppReply<JSONObject> appReply = bzfBusinessMessageFeignClient.sendSMS(websiteMsgVo);
        log.info("发送短信验证码结果==={}", JSON.toJSONString(appReply));
        return appReply;
    }
}
