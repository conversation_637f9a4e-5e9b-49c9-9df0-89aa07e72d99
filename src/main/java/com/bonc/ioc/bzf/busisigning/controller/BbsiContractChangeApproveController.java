package com.bonc.ioc.bzf.busisigning.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.bonc.ioc.bzf.busisigning.feign.feign.BzfSystemCommercialFeignClient;
import com.bonc.ioc.bzf.busisigning.service.IBbsApproveInfoService;
import com.bonc.ioc.bzf.busisigning.service.IBbsContractChangeApproveInfoService;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.utils.common.convert.UserPoint;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.dict.entity.McpDictEntity;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 合同变更审核 前端控制器
 *
 * <AUTHOR> @date
 */
@RestController
@RequestMapping("/contractChangeApprove")
@Api(tags = "合同变更审核")
@Slf4j
public class BbsiContractChangeApproveController {
    @Resource
    private IBbsContractChangeApproveInfoService baseService;
    @Resource
    private IBbsApproveInfoService approveInfoService;
    @Resource
    private BzfSystemCommercialFeignClient bzfSystemCommercialFeignClient;
    @Resource
    private Environment env;

    @Resource
    private McpDictSession mcpDictSession;

    /*
    合同变更审核变更列表查询
     */
    @GetMapping(value = "/selectByPageRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "")
    @ApiOperation(value = "合同变更申请审核分页查询", notes = "合同变更申请审核分页查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "2")
    public AppReply<PageResult<List<BbsiContractChangeApproveListPageResultVo>>> selectContractAuditPageRecord(BbsiContractChangeApproveListPageResultVo vo) {
        AppReply<PageResult<List<BbsiContractChangeApproveListPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
    }

    /**
     * 商铺地址级联查询
     */
//        @GetMapping(value = "/addressCascadeQuery", produces = "application/json;charset=UTF-8")
//    @ApiOperationSupport(order = 1, author = "")
//    @ApiOperation(value = "商铺地址级联查询", notes = "商铺地址级联查询")
//    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
//    public AppReply<List<AddressCascadeQueryResultVo>> addressCascadeQuery(BbsApproveAddressCascadeQueryVo queryVo) {
//        AppReply<List<AddressCascadeQueryResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
//        List<AddressCascadeQueryResultVo> data = baseService.addressCascadeQuery(queryVo);
//        appReply.setData(data.isEmpty() || data.get(0) == null ? Collections.emptyList() : data);
//        return appReply;
//    }

    /**
     * 调用意向客户中心查询业态
     *
     * @return
     */
    @GetMapping(value = "/selectBusinessFormat", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "")
    @ApiOperation(value = "查询业态码表树", notes = "查询业态码表树")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<JSONArray> selectBusinessFormat() {
        AppReply<JSONArray> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            AppReply<JSONArray> businessFormat = bzfSystemCommercialFeignClient.selectBusinessFormat(!Objects.equals(env.getProperty("spring.profiles.active"), "debug"), "BUSINESS_FORMAT");
            if (businessFormat.getCode().equals(AppReply.SUCCESS_CODE)) {
                appReply.setData(businessFormat.getData());
            } else {
                new AppReply(AppReply.ERROR_CODE, "查询业态码表树失败", null);
            }
        } catch (Exception e) {
            appReply.setData(new JSONArray());
        }
        return appReply;
    }


    /**
     * 根据字典code查询code对应字典
     *
     * @param dictCode 字典code
     * @return 字典实体
     */
    @GetMapping(value = "/selectByDictCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "")
    @ApiOperation(value = "根据字典code查询code对应字典", notes = "根据字典code查询code对应字典")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<McpDictEntity>> selectByDictCode(@ApiParam(value = "需要查询的字典编码", required = false) @RequestParam(required = false) String dictCode) {
        AppReply<List<McpDictEntity>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply.setData(mcpDictSession.getMcpDictUtil().getPairsListByDictCode(dictCode));
        } catch (Exception e) {
            appReply.setData(new ArrayList<>());
        }
        return appReply;
    }


    @PostMapping(value = "/saveApproveResult", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "")
    @ApiOperation(value = "保存合同变更申请审核", notes = "保存合同变更申请审核")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveApproveResult(@ApiParam(value = "需要保存的审批结果和信息", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsiContractChangeApproveInfoVo vo) {
        log.info("调用保存合同变更审核接口参数==={}", JSON.toJSONString(vo));
        return baseService.saveContractChangeApproveResult(vo);
    }

    /**
     * 审批记录查询
     */
    @GetMapping(value = "/selectByContractCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "")
    @ApiOperation(value = "审批记录查询", notes = "审批记录查询")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @UserPoint
    public AppReply<List<BbsApproveDetailInfoVo>> selectByContractCode(@RequestParam String ccId) {
        AppReply<List<BbsApproveDetailInfoVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        List<BbsApproveDetailInfoVo> data = baseService.selectByContractCode(ccId);
        appReply.setData(data.isEmpty() || data.get(0) == null ? Collections.emptyList() : data);
        return appReply;
    }

    /*
    变更信息详情
     */
    @GetMapping(value = "/selectContractChangeApprove", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "")
    @ApiOperation(value = "变更信息详情", notes = "变更信息详情")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsiContractChangeApproveVo> selectContractChangeApprove(BbsiContractChangeApproveVo vo) {
        AppReply<BbsiContractChangeApproveVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        BbsiContractChangeApproveVo data = baseService.selectContractChangeApprove(vo);
        appReply.setData(Objects.isNull(data) ? null : data);
        return appReply;
    }

    @GetMapping(value = "/selectChangeStatus", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "")
    @ApiOperation(value = "查询合同申请状态", notes = "查询合同申请状态")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<ContractStatusResultVo>> selectContractStatus(@ApiParam(value = "合同编码集合", required = false) @RequestParam List<String> contractNos) {
        AppReply<List<ContractStatusResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        List<ContractStatusResultVo> data = baseService.selectContractStatus(contractNos);
        appReply.setData(Objects.isNull(data) ? null : data);
        return appReply;
    }
}

