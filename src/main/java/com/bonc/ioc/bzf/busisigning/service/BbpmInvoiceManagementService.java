package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.vo.BbpmInvoiceQueryVo;
import com.bonc.ioc.common.base.page.PageResult;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 发票管理 服务类
 *
 * <AUTHOR>
 * @date 2023-08-30
 */
@Service
public interface BbpmInvoiceManagementService{

    /**
     * selectByPage 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     */
    PageResult<List<BbpmInvoiceQueryVo>> selectByPage(BbpmInvoiceQueryVo vo);
}
