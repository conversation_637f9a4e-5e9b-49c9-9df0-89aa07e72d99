package com.bonc.ioc.bzf.busisigning.factory.change.otherchange;

import cn.hutool.extra.spring.SpringUtil;
import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.consts.SymbolConst;
import com.bonc.ioc.bzf.busisigning.enums.ContractChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.utils.ListUtil;
import com.bonc.ioc.bzf.busisigning.utils.ResultUtils;
import com.bonc.ioc.bzf.busisigning.vo.BbctPreviewInfoParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.BbpmBillManagementVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo;
import com.bonc.ioc.bzf.busisigning.vo.SigningSaveVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsParamsVo;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 子变更 抽象工厂类
 *
 * <AUTHOR>
 * @since 2024/10/24
 */
@Slf4j
public abstract class AbstractSubChangeFactory {

    /**
     * feign服务 配置实例
     */
    protected FeignServiceConfiguration feignServiceConfiguration;

    /**
     * 业务服务 配置实例
     */
    protected BusinessServiceConfiguration businessServiceConfiguration;

    /**
     * 合同变更信息 vo实体实例
     */
    protected BbsiContractChangeVo contractChangeVo;

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param contractChangeVo             合同变更信息 vo实体
     */
    protected AbstractSubChangeFactory(FeignServiceConfiguration feignServiceConfiguration,
                                       BusinessServiceConfiguration businessServiceConfiguration,
                                       BbsiContractChangeVo contractChangeVo) {
        this.feignServiceConfiguration = feignServiceConfiguration;
        this.businessServiceConfiguration = businessServiceConfiguration;
        this.contractChangeVo = contractChangeVo;
    }

    /**
     * 获取实例列表
     *
     * @param contractChangeVo 合同变更信息 vo实体
     * @return 实例列表
     */
    public static List<AbstractSubChangeFactory> getInstances(BbsiContractChangeVo contractChangeVo) {
        log.info("getInstances方法-0");
        FeignServiceConfiguration feignServiceConfiguration = SpringUtil.getBean(FeignServiceConfiguration.class);
        BusinessServiceConfiguration businessServiceConfiguration = SpringUtil.getBean(BusinessServiceConfiguration.class);
        List<AbstractSubChangeFactory> instanceList = new ArrayList<>();
        List<String> changeTypeItemList = ListUtil.splitBySymbol(contractChangeVo.getChangeTypeItem(), SymbolConst.COMMA);
        log.info("getInstances方法-1");
        if (changeTypeItemList.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())) {
            instanceList.add(new RentChangeFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    contractChangeVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())) {
            instanceList.add(new AreaChangeFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    contractChangeVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.BUSINESS_FORMAT_CHANGE.getCode())) {
            instanceList.add(new BusinessFormatChangeFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    contractChangeVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.LEASE_DATE_CHANGE.getCode())) {
            instanceList.add(new LeaseDateChangeFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    contractChangeVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.SERVICE_TERMS_CHANGE.getCode())) {
            instanceList.add(new ServiceTermsChangeFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    contractChangeVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.PAYABLE_DATE_CHANGE.getCode())) {
            instanceList.add(new PayableDateChangeFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    contractChangeVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())) {
            instanceList.add(new LeaseAreaReductionChangeFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    contractChangeVo));
        }
        //  保障金处理在缩租之后
        if (changeTypeItemList.contains(ContractChangeTypeEnum.BOND_CHANGE.getCode())) {
            instanceList.add(new CashPledgeChangeFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    contractChangeVo));
        }
        log.info("getInstances方法-2");
        return instanceList;
    }

    /**
     * 根据账单获取押金金额
     *
     * @param contractCode 合同编号
     * @param projectId    项目id
     * @param houseName    产品名称
     * @return 押金金额
     */
    protected BigDecimal getCashPledgeByBill(String contractCode,
                                             String projectId,
                                             String houseName) {
        BbpmBillManagementVo requestVo = new BbpmBillManagementVo();
        requestVo.setContractCode(contractCode);
        requestVo.setProjectId(projectId);
        requestVo.setHouseName(houseName);
        requestVo.setChargeSubjectNo("02");
        requestVo.setProjectFormat("03");
        requestVo.setChargeOwner("02");
        //先查个人再查企业(01企业 02个人)
        AppReply<List<BbpmBillManagementVo>> appReply = feignServiceConfiguration
                .getPaymentV2FeignClient()
                .selectMyBillList(requestVo);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(String.format("查询账单失败[合同编号=%s]", contractCode));
        }
        if (CollectionUtils.isEmpty(appReply.getData())) {
            requestVo.setChargeOwner("01");
            appReply = feignServiceConfiguration
                    .getPaymentV2FeignClient()
                    .selectMyBillList(requestVo);
            if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
                throw new McpException(String.format("查询账单失败[合同编号=%s]", contractCode));
            }
        }
        List<BbpmBillManagementVo> resultBillList = appReply.getData();
        if (CollectionUtils.isEmpty(resultBillList)) {
            return new BigDecimal(0);
        } else {
            BbpmBillManagementVo resultBillVo = resultBillList.get(0);
            return resultBillVo.getShouldPayAmount();
        }
    }

    /**
     * 逗号拼接
     *
     * @param target 目标字符串
     * @param addStr 新增字符串
     * @return 拼接结果
     */
    protected String jointByComma(String target, String addStr) {
        if (StringUtils.isBlank(target)) {
            return addStr;
        } else {
            return target + SymbolConst.COMMA + addStr;
        }
    }

    /**
     * 赋值试算账单请求参数
     *
     * @param previewBillsParamsVo 试算账单请求参数 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentSignInfoVo     上级合同签约信息 vo实体
     */
    public abstract void setPreviewBillsParams(PreviewBillsParamsVo previewBillsParamsVo,
                                               BbctContractManagementVo parentContractInfoVo,
                                               SigningSaveVo parentSignInfoVo);

    /**
     * 赋值预览信息
     *
     * @param previewInfoParamsVo 预览信息参数 vo实体
     */
    public abstract void setPreviewInfo(BbctPreviewInfoParamsVo previewInfoParamsVo);

    /**
     * 根据变更信息赋值签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     */
    public abstract void setSignInfoByChangeInfo(SigningSaveVo signingSaveVo);
}
