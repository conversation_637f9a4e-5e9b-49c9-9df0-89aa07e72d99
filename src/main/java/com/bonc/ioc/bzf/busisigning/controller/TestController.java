package com.bonc.ioc.bzf.busisigning.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.feign.feign.*;
import com.bonc.ioc.bzf.busisigning.feign.feign.convervo.ProjectVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BankRequestVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbpmBillManagementPageVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.ChargeBankResultVo;
import com.bonc.ioc.bzf.busisigning.service.BbctContractService;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalInfoExtService;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * @author: liwenqiang
 * @createDate: 2023-08-25 15:49
 * @Version 1.0
 **/
@Slf4j
@RestController
@Api(tags = "测试")
public class TestController {

    @Autowired
    private BbpaymentFeignClient bbpaymentFeignClient;

    @Autowired
    private BbHousingFeignClient bbHousingFeignClient;

    @Autowired
    private BbCustomerFeignClient bbCustomerFeignClient;

    @Autowired
    private BbctSigningFeignCilent bbctSigningFeignCilent;

    @Autowired
    private BbprojectFeignClient bbprojectFeignClient;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 测试使用 无任何作用
     * */
    @GetMapping("/test")
    public String test(@RequestParam("id") String id){
        System.out.println("id = " + id);
        return id;
    }

    /**
     * 测试使用 无任何作用
     * */
    @GetMapping(value = "/selectByPageRecord",produces = "application/json;charset=UTF-8")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> selectByPageRecord(BbpmBillManagementPageVo vo){
        return bbpaymentFeignClient.selectByPageRecord(vo);
    }

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;
    @GetMapping(value = "/test1",produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "lxg")
    @ApiOperation(value = "测试1", notes = "测试1")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public ChargeRespondVo<List<ChargeBankResultVo>> test1(ChargeBankParamsVo chargeBankParamsVo){
        try {
            BankRequestVo<ChargeBankParamsVo> bankRequestVo=new BankRequestVo<>();
            bankRequestVo.setData(chargeBankParamsVo);
            return  bfipSettlementFeignClient.listByProjectId(bankRequestVo);
        }catch (Exception e){
            log.error("=======================================测试1异常，异常信息为：",e);
        }
        return null;
    }

    /**
     * 通过客户ID查询客户全部信息
     * @param personCustomerId 客户ID
     * @return  com.bonc.ioc.common.util.AppReply 查询的数据
     * <AUTHOR>
     * @date  2023-09-07
     */
    @GetMapping(value = "/getPersonCustomerById", produces = "application/json;charset=UTF-8")
    AppReply<JSONObject> getPersonCustomerById(@RequestParam("personCustomerId") String personCustomerId) {
        return bbCustomerFeignClient.getPersonCustomerById(personCustomerId);
    }

    /**
     * 根据机构客户ID或统一信用代码查询机构客户全部信息
     * @param idOrCreditCode 客户ID或统一信用代码
     * @return  com.bonc.ioc.common.util.AppReply 查询的数据
     * <AUTHOR>
     * @date  2023-09-07
     */
    @GetMapping(value = "/getEnterpriseCustomerByIdOrCreditCode", produces = "application/json;charset=UTF-8")
    AppReply<JSONObject> getEnterpriseCustomerByIdOrCreditCode(@RequestParam("idOrCreditCode") String idOrCreditCode){
        return bbCustomerFeignClient.getEnterpriseCustomerByIdOrCreditCode(idOrCreditCode);
    }

    /**
     * 根据项目id查询项目信息
     * @param projectId 项目id
     * @return  com.bonc.ioc.common.util.AppReply 查询的数据
     * <AUTHOR>
     * @date  2023-09-07
     */
    @GetMapping(value = "/getProjectInfoByProjectId", produces = "application/json;charset=UTF-8")
    AppReply<JSONObject> selectProjectInfoByProjectId(@RequestParam("projectId") String projectId) {
        ProjectVo projectVo = new ProjectVo();
        projectVo.setProjectId(projectId);
        return bbprojectFeignClient.selectByProjectId(projectVo);
    }

    /**
     * 测试e0414FB45381
     *
     * @param id 参数
     * @return com.bonc.ioc.common.util.AppReply 查询的数据
     */
    @PostMapping(value = "/test/e0414FB45381", produces = "application/json;charset=UTF-8")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    AppReply<JSONObject> e0414FB45381(@RequestParam String id,@RequestParam(required = false) String commercialDbName,@RequestParam(required = false) String housingDbName) {
      if (Pattern.matches("^[0-9a-fA-F]{32}$", id)) {
            return AppReply.success(jdbcTemplate.batchUpdate(
                    "update "+(StrUtil.isEmpty(commercialDbName)?"bzf_system_commercial":commercialDbName)+".bsc_intention_info set status = '04',result_is_successful = '02' where intention_id in(select t3.intention_id from bbs_sign_info t1 left join bbs_result_relation t2 on t1.sign_id=t2.sign_info_id left join bbs_result_customer t3 on t2.rr_id=t3.rr_id where t1.sign_id='" + id + "')",
                    "UPDATE "+(StrUtil.isEmpty(housingDbName)?"bzf_business_housing_v2":housingDbName)+".bbhg_house_info SET operate_state = '15' WHERE house_code in (select brp.product_no from bbs_result_customer brc left join bbs_result_product brp on brc.rr_id=brp.rr_id where brc.rr_id in(select rr_id from bbs_result_relation where sign_info_id='" + id + "'))",
                    "UPDATE "+(StrUtil.isEmpty(housingDbName)?"bzf_business_housing_v2":housingDbName)+".bbhg_split SET operate_state = '15' WHERE split_code in(select brp.product_no from bbs_result_customer brc left join bbs_result_product brp on brc.rr_id=brp.rr_id where brc.rr_id in(select rr_id from bbs_result_relation where sign_info_id='" + id + "'))",
                    "update "+(StrUtil.isEmpty(housingDbName)?"bzf_business_housing_v2":housingDbName)+".bbhg_house_info base set base.operate_state = null  where base.house_code in (select distinct split.house_code from "+(StrUtil.isEmpty(housingDbName)?"bzf_business_housing_v2":housingDbName)+".bbhg_split split where base.house_code = split.house_code and split.split_code in(select brp.product_no from bbs_result_customer brc left join bbs_result_product brp on brc.rr_id=brp.rr_id where brc.rr_id in(select rr_id from bbs_result_relation where sign_info_id='" + id + "')))",
                    "delete from bbs_contract_change_approve_info where cc_id in(select cc_id from bbs_contract_change where contract_code in(SELECT contract_code FROM bbs_sign_info where sign_id in('" + id + "')))",
                    "delete from bbs_contract_change_approve_detail where cc_id in(select cc_id from bbs_contract_change where contract_code in(SELECT contract_code FROM bbs_sign_info where sign_id in('" + id + "')))",
                    "delete from bbs_contract_change where contract_code in(SELECT contract_code FROM bbs_sign_info where sign_id in('" + id + "'))",
                    "delete from bbs_contract_change_result where contract_code in(SELECT contract_code FROM bbs_sign_info where sign_id in('" + id + "'))",
                    "delete from bbs_result_customer where rr_Id in(SELECT rr_id FROM bbs_result_relation where sign_info_id in('" + id + "'))",
                    "delete from bbs_result_product where rr_Id in(SELECT rr_id FROM bbs_result_relation where sign_info_id in('" + id + "'))",
                    "delete from bbs_template_group where template_group_id in(SELECT system_station_message_template_group_id FROM bbs_sign_info where sign_id in('" + id + "')) or template_group_id in(SELECT mobile_message_template_group_id FROM bbs_sign_info where sign_id in('" + id + "'))",
                    "delete from bbs_template_seat where parent_id in('" + id + "')",
                    "delete from bbs_sign_fp_interval where sign_info_id in('" + id + "')",
                    "delete from bbs_sign_incremental_config where sign_info_id in('" + id + "')",
                    "delete FROM bbs_sign_info where sign_id in('" + id + "')",
                    "delete FROM bbs_result_relation where sign_info_id in('" + id + "')",
                    "delete FROM bbs_approve_detail_info where parent_id in('" + id + "')",
                    "delete FROM bbs_approve_info where parent_id in('" + id + "')"));
        }
        return AppReply.error("执行失败");
    }

    /**
     * 测试e0414FB45382
     *
     * @param q 参数
     * @return com.bonc.ioc.common.util.AppReply 查询的数据
     */
    @PostMapping(value = "/test/e0414FB45382", produces = "application/json;charset=UTF-8")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    AppReply<JSONObject> e0414FB45382(@RequestBody String q) {
        if (StrUtil.startWithIgnoreCase(q, "select")) {
            return AppReply.success(jdbcTemplate.queryForList(q));
        } else {
            return AppReply.success("只支持查询");
        }
    }

    /**
     * 测试e0414FB45383
     *
     * @param id 参数
     * @return com.bonc.ioc.common.util.AppReply 查询的数据
     */
    @PostMapping(value = "/test/e0414FB45383", produces = "application/json;charset=UTF-8")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    AppReply<JSONObject> e0414FB45383(@RequestParam String id,@RequestParam String commercialDbName) {
        return AppReply.success(jdbcTemplate.batchUpdate("update "+(StrUtil.isEmpty(commercialDbName)?"bzf_system_commercial":commercialDbName)+".bsc_intention_info set status = '04',result_is_successful = '02' where intention_id in(select t3.intention_id from bbs_sign_info t1 left join bbs_result_relation t2 on t1.sign_id=t2.sign_info_id left join bbs_result_customer t3 on t2.rr_id=t3.rr_id where t1.sign_id='" + id + "')"));
    }


    /**
     * 测试e0414FB45384
     *
     * @param id 参数
     * @return com.bonc.ioc.common.util.AppReply 查询的数据
     */
    @PostMapping(value = "/test/e0414FB45384", produces = "application/json;charset=UTF-8")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    AppReply<JSONObject> e0414FB45384(@RequestParam String id,@RequestParam(required = false) String commercialDbName,@RequestParam(required = false) String housingDbName) {
        if (Pattern.matches("^[0-9a-fA-F]{32}$", id)) {
            return AppReply.success(jdbcTemplate.batchUpdate(
                    "update "+(StrUtil.isEmpty(commercialDbName)?"bzf_system_commercial":commercialDbName)+".bsc_intention_info set status = '04',result_is_successful = '02' where intention_id in(select t3.intention_id from bbs_renewal_info t1 left join bbs_renewal_relation t2 on t1.sign_id=t2.sign_info_id left join bbs_renewal_customer t3 on t2.rr_id=t3.rr_id where t1.sign_id='" + id + "')",
                    "UPDATE "+(StrUtil.isEmpty(housingDbName)?"bzf_business_housing_v2":housingDbName)+".bbhg_house_info SET operate_state = '15' WHERE house_code in (select brp.product_no from bbs_renewal_customer brc left join bbs_renewal_product brp on brc.rr_id=brp.rr_id where brc.rr_id in(select rr_id from bbs_renewal_relation where sign_info_id='" + id + "'))",
                    "UPDATE "+(StrUtil.isEmpty(housingDbName)?"bzf_business_housing_v2":housingDbName)+".bbhg_split SET operate_state = '15' WHERE split_code in(select brp.product_no from bbs_renewal_customer brc left join bbs_renewal_product brp on brc.rr_id=brp.rr_id where brc.rr_id in(select rr_id from bbs_renewal_relation where sign_info_id='" + id + "'))",
                    "update "+(StrUtil.isEmpty(housingDbName)?"bzf_business_housing_v2":housingDbName)+".bbhg_house_info base set base.operate_state = null  where base.house_code in (select distinct split.house_code from "+(StrUtil.isEmpty(housingDbName)?"bzf_business_housing_v2":housingDbName)+".bbhg_split split where base.house_code = split.house_code and split.split_code in(select brp.product_no from bbs_renewal_customer brc left join bbs_renewal_product brp on brc.rr_id=brp.rr_id where brc.rr_id in(select rr_id from bbs_renewal_relation where sign_info_id='" + id + "')))",
                    "delete from bbs_contract_change_approve_info where cc_id in(select cc_id from bbs_contract_change where contract_code in(SELECT contract_code FROM bbs_renewal_info where sign_id in('" + id + "')))",
                    "delete from bbs_contract_change_approve_detail where cc_id in(select cc_id from bbs_contract_change where contract_code in(SELECT contract_code FROM bbs_renewal_info where sign_id in('" + id + "')))",
                    "delete from bbs_contract_change where contract_code in(SELECT contract_code FROM bbs_renewal_info where sign_id in('" + id + "'))",
                    "delete from bbs_contract_change_result where contract_code in(SELECT contract_code FROM bbs_renewal_info where sign_id in('" + id + "'))",
                    "delete from bbs_renewal_apply_approve_info where parent_id in(select renewal_apply_info_id from bbs_renewal_apply_info where contract_code in(SELECT parent_contract_code FROM bbs_renewal_info where sign_id in('" + id + "')))",
                    "delete from bbs_renewal_apply_approve_detail_info where parent_id in(select renewal_apply_info_id from bbs_renewal_apply_info where contract_code in(SELECT parent_contract_code FROM bbs_renewal_info where sign_id in('" + id + "')))",
                    "delete from bbs_renewal_apply_info where contract_code in(SELECT parent_contract_code FROM bbs_renewal_info where sign_id in('" + id + "'))",
                    "delete from bbs_renewal_apply_info_product where contract_code in(SELECT parent_contract_code FROM bbs_renewal_info where sign_id in('" + id + "'))",
                    "delete from bbs_renewal_customer where rr_Id in(SELECT rr_id FROM bbs_renewal_relation where sign_info_id in('" + id + "'))",
                    "delete from bbs_renewal_product where rr_Id in(SELECT rr_id FROM bbs_renewal_relation where sign_info_id in('" + id + "'))",
                    "delete from bbs_renewal_template_group where template_group_id in(SELECT system_station_message_template_group_id FROM bbs_renewal_info where sign_id in('" + id + "')) or template_group_id in(SELECT mobile_message_template_group_id FROM bbs_renewal_info where sign_id in('" + id + "'))",
                    "delete from bbs_renewal_template_seat where parent_id in('" + id + "')",
                    "delete from bbs_renewal_fp_interval where sign_info_id in('" + id + "')",
                    "delete from bbs_renewal_incremental_config where sign_info_id in('" + id + "')",
                    "delete FROM bbs_renewal_info where sign_id in('" + id + "')",
                    "delete FROM bbs_renewal_relation where sign_info_id in('" + id + "')",
                    "delete FROM bbs_renewal_approve_detail_info where parent_id in('" + id + "')",
                    "delete FROM bbs_renewal_approve_info where parent_id in('" + id + "')"));
        }
        return AppReply.error("执行失败");
    }

    @GetMapping(value = "/getBankBranchCode", produces = "application/json;charset=UTF-8")
    AppReply<JSONObject> getBankBranchCode(@RequestParam("projectId") String projectId,@RequestParam("bankBranchName") String bankBranchName,@RequestParam("size") String size,@RequestParam("current") String current,@RequestParam("fullPage") String fullPage) {
        BankRequestVo<ChargeBankBranchParamsVo> bankBranchRequestVo=new BankRequestVo<>();
        ChargeBankBranchParamsVo chargeBankBranchParamsVo=new ChargeBankBranchParamsVo();
        chargeBankBranchParamsVo.setProjectId(projectId);
        chargeBankBranchParamsVo.setBankBranchName(bankBranchName);
        chargeBankBranchParamsVo.setSize(size);
        chargeBankBranchParamsVo.setCurrent(current);
        chargeBankBranchParamsVo.setFullPage(fullPage);
        bankBranchRequestVo.setData(chargeBankBranchParamsVo);
        log.info("工银支行列表接口参数:{}", JSON.toJSONString(bankBranchRequestVo));
        String listBankBranchCodeReturnStr=bfipSettlementFeignClient.listBankBranchCode(bankBranchRequestVo);
        log.info("工银支行列表接口结果:{}",listBankBranchCodeReturnStr);
        return AppReply.success(listBankBranchCodeReturnStr);
    }


    @Resource
    private IBbsRenewalInfoExtService bbsRenewalInfoExtService;
    @Autowired
    private BbctContractService bbctContractService;

    @GetMapping(value = "/exitFormForCreateByContract", produces = "application/json;charset=UTF-8")
    AppReply<Object> exitFormForCreateByContract(@RequestParam("renewalSignInfoId") String renewalSignInfoId) {
        RenewalingSaveVo renewalingSaveVo = bbsRenewalInfoExtService.renewalView(renewalSignInfoId,null,false);
        AppReply<Object> exitFormForCreateByContract= bbctContractService.exitFormForCreateByContract(renewalingSaveVo.getParentContractCode(),renewalingSaveVo);
        return exitFormForCreateByContract;
    }

//    @Resource
//    private YongYouAdapter yongYouAdapter;
//    @GetMapping(value = "/getBankBranchList", produces = "application/json;charset=UTF-8")
//    String getBankBranchList(@RequestParam("bankType") String bankType) {
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("bankType", bankType);
//        return yongYouAdapter.getBankBranchList(paramMap);
//
//    }
//    @GetMapping(value = "/getBankBranchList2", produces = "application/json;charset=UTF-8")
//    String getBankBranchList2(@RequestParam("bankType") String bankType) {
//        return JSONObject.toJSONString(yongYouAdapter.selectPayingSubBank(bankType));

//    }


}
