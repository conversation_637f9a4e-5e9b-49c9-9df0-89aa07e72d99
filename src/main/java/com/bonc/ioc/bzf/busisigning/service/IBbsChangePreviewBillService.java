package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangePreviewBillEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 预览账单表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-30
 * @change 2024-10-30 by pyj for init
 */
public interface IBbsChangePreviewBillService extends IMcpBaseService<BbsChangePreviewBillEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbsChangePreviewBillVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbsChangePreviewBillVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param billId 需要删除的账单id
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String billId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param billIdList 需要删除的账单id
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> billIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的预览账单表
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbsChangePreviewBillVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的预览账单表
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbsChangePreviewBillVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的预览账单表
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbsChangePreviewBillVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的预览账单表
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbsChangePreviewBillVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param billId 需要查询的账单id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    BbsChangePreviewBillVo selectByIdRecord(String billId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbsChangePreviewBillPageResultVo>> selectByPageRecord(BbsChangePreviewBillPageVo vo);

    /**
     * 根据合同变更id和账单类型查询
     *
     * @param ccId     合同变更id
     * @param billType 账单类型
     * @return 账单信息列表
     */
    List<BbsChangePreviewBillVo> selectByCcIdAndBillType(String ccId, String billType);
}
