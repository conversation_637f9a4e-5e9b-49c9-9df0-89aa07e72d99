package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsTemplateGroupEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 通知模板组表 服务类
 *
 * <AUTHOR>
 * @date 2023-09-05
 * @change 2023-09-05 by liwen<PERSON><PERSON> for init
 */
public interface IBbsTemplateGroupService extends IMcpBaseService<BbsTemplateGroupEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-05
     * @change
     * 2023-09-05 by liwenqi<PERSON> for init
     */
    String insertRecord(BbsTemplateGroupVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-05
     * @change
     * 2023-09-05 by liwenqiang for init
     */
    List<String> insertBatchRecord(List<BbsTemplateGroupVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param templateGroupId 需要删除的模板组id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-05
     * @change
     * 2023-09-05 by liwenqiang for init
     */
    void removeByIdRecord(String templateGroupId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param templateGroupIdList 需要删除的模板组id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-05
     * @change
     * 2023-09-05 by liwenqiang for init
     */
    void removeByIdsRecord(List<String> templateGroupIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-05
     * @change
     * 2023-09-05 by liwenqiang for init
     */
    void updateByIdRecord(BbsTemplateGroupVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-05
     * @change
     * 2023-09-05 by liwenqiang for init
     */
    void updateBatchByIdRecord(List<BbsTemplateGroupVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-05
     * @change
     * 2023-09-05 by liwenqiang for init
     */
    void saveByIdRecord(BbsTemplateGroupVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-05
     * @change
     * 2023-09-05 by liwenqiang for init
     */
    void saveBatchByIdRecord(List<BbsTemplateGroupVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param templateGroupId 需要查询的模板组id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-05
     * @change
     * 2023-09-05 by liwenqiang for init
     */
    BbsTemplateGroupVo selectByIdRecord(String templateGroupId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-05
     * @change
     * 2023-09-05 by liwenqiang for init
     */
    PageResult<List<BbsTemplateGroupPageResultVo>> selectByPageRecord(BbsTemplateGroupPageVo vo);
}
