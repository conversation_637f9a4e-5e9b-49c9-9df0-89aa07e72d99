package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalTemplateGroupEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 通知模板组表 服务类
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
public interface IBbsRenewalTemplateGroupService extends IMcpBaseService<BbsRenewalTemplateGroupEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    String insertRecord(BbsRenewalTemplateGroupVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    List<String> insertBatchRecord(List<BbsRenewalTemplateGroupVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param templateGroupId 需要删除的模板组id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void removeByIdRecord(String templateGroupId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param templateGroupIdList 需要删除的模板组id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void removeByIdsRecord(List<String> templateGroupIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void updateByIdRecord(BbsRenewalTemplateGroupVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void updateBatchByIdRecord(List<BbsRenewalTemplateGroupVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void saveByIdRecord(BbsRenewalTemplateGroupVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void saveBatchByIdRecord(List<BbsRenewalTemplateGroupVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param templateGroupId 需要查询的模板组id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    BbsRenewalTemplateGroupVo selectByIdRecord(String templateGroupId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    PageResult<List<BbsRenewalTemplateGroupPageResultVo>> selectByPageRecord(BbsRenewalTemplateGroupPageVo vo);
}
