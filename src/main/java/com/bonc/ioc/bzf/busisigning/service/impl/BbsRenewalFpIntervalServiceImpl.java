package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalFpIntervalEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalFpIntervalMapper;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalFpIntervalService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 签约-免租期-区间（当租金/物业费免租期分类为1时有效） 服务类实现
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
@Slf4j
@Service
public class BbsRenewalFpIntervalServiceImpl extends McpBaseServiceImpl<BbsRenewalFpIntervalEntity> implements IBbsRenewalFpIntervalService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalFpIntervalMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsRenewalFpIntervalService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsRenewalFpIntervalVo vo) {
        if(vo == null) {
            return null;
        }

        BbsRenewalFpIntervalEntity entity = new BbsRenewalFpIntervalEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setSfiId(null);
        if(!baseService.insert(entity)) {
            log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）新增失败:" + entity.toString());
            throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getSfiId(),1)) {
                log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）新增后保存历史失败:" + entity.toString());
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）新增后保存历史失败");
            }

            log.debug("签约-免租期-区间（当租金/物业费免租期分类为1时有效）新增成功:"+entity.getSfiId());
            return entity.getSfiId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsRenewalFpIntervalVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsRenewalFpIntervalEntity> entityList = new ArrayList<>();
        for (BbsRenewalFpIntervalVo item:voList) {
            BbsRenewalFpIntervalEntity entity = new BbsRenewalFpIntervalEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsRenewalFpIntervalEntity item:entityList){
            item.setSfiId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）新增失败");
            throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsRenewalFpIntervalEntity::getSfiId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量新增后保存历史失败");
            }

            log.debug("签约-免租期-区间（当租金/物业费免租期分类为1时有效）新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param sfiId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String sfiId) {
        if(!StringUtils.isEmpty(sfiId)) {
            if(!baseService.saveOperationHisById(sfiId,3)) {
                log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）删除后保存历史失败:" + sfiId);
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）删除后保存历史失败");
            }

            if(!baseService.removeById(sfiId)) {
                log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）删除失败");
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）删除失败"+sfiId);
            }
        } else {
            throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param sfiIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> sfiIdList) {
        if(!CollectionUtils.isEmpty(sfiIdList)) {
            int oldSize = sfiIdList.size();
            sfiIdList = sfiIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(sfiIdList) || oldSize != sfiIdList.size()) {
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量删除失败 存在主键id为空的记录"+StringUtils.join(sfiIdList));
            }

            if(!baseService.saveOperationHisByIds(sfiIdList,3)) {
                log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量删除后保存历史失败:" + StringUtils.join(sfiIdList));
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(sfiIdList)) {
                log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量删除失败");
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量删除失败"+StringUtils.join(sfiIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsRenewalFpIntervalVo vo) {
        if(vo != null) {
            BbsRenewalFpIntervalEntity entity = new BbsRenewalFpIntervalEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getSfiId())) {
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）更新失败");
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）更新失败"+entity.getSfiId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSfiId(),2)) {
                    log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）更新后保存历史失败:" + entity.getSfiId());
                    throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsRenewalFpIntervalVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalFpIntervalEntity> entityList = new ArrayList<>();

            for (BbsRenewalFpIntervalVo item:voList){
                BbsRenewalFpIntervalEntity entity = new BbsRenewalFpIntervalEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getSfiId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量更新失败");
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSfiId())).map(BbsRenewalFpIntervalEntity::getSfiId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsRenewalFpIntervalVo vo) {
        if(vo != null) {
            BbsRenewalFpIntervalEntity entity = new BbsRenewalFpIntervalEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）保存失败");
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）保存失败"+entity.getSfiId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSfiId(),4)) {
                    log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）保存后保存历史失败:" + entity.getSfiId());
                    throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsRenewalFpIntervalVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalFpIntervalEntity> entityList = new ArrayList<>();

            for (BbsRenewalFpIntervalVo item:voList){
                BbsRenewalFpIntervalEntity entity = new BbsRenewalFpIntervalEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量保存失败");
                throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSfiId())).map(BbsRenewalFpIntervalEntity::getSfiId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("签约-免租期-区间（当租金/物业费免租期分类为1时有效）批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param sfiId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsRenewalFpIntervalVo selectByIdRecord(String sfiId) {
        BbsRenewalFpIntervalVo vo = new BbsRenewalFpIntervalVo();

        if(!StringUtils.isEmpty(sfiId)) {
            BbsRenewalFpIntervalEntity entity = baseService.selectById(sfiId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsRenewalFpIntervalPageResultVo>> selectByPageRecord(BbsRenewalFpIntervalPageVo vo) {
        List<BbsRenewalFpIntervalPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
