package com.bonc.ioc.bzf.busisigning.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: caijun
 * @date: 2024/05/23
 * @change: 2024/05/23 by <EMAIL> for init
 */
@Data
public class OaFormDataDetailsOptionsRequestVo implements Serializable {

    /**
     * 选项id
     */
    @ApiModelProperty("选项id")
    private Long optionId;

    /**
     * 图片单选多选的选项的图片id
     */
    @ApiModelProperty("图片单选多选的选项的图片id")
    private Long targetId;

    /**
     * 选项名称
     */
    @ApiModelProperty("选项名称")
    private String content;

    /**
     * 选项类型
     */
    @ApiModelProperty("选项类型")
    private String type;

}
