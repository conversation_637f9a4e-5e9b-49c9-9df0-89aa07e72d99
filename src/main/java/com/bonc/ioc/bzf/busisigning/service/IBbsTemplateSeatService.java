package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsTemplateSeatEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import java.util.Map;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 模板属性表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-31
 * @change 2023-08-31 by liwenqi<PERSON> for init
 */
public interface IBbsTemplateSeatService extends IMcpBaseService<BbsTemplateSeatEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-31
     * @change
     * 2023-08-31 by l<PERSON><PERSON><PERSON><PERSON> for init
     */
    String insertRecord(BbsTemplateSeatVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-31
     * @change
     * 2023-08-31 by liwenqiang for init
     */
    List<String> insertBatchRecord(List<BbsTemplateSeatVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param seatId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-31
     * @change
     * 2023-08-31 by liwenqiang for init
     */
    void removeByIdRecord(String seatId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param seatIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-31
     * @change
     * 2023-08-31 by liwenqiang for init
     */
    void removeByIdsRecord(List<String> seatIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-31
     * @change
     * 2023-08-31 by liwenqiang for init
     */
    void updateByIdRecord(BbsTemplateSeatVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-31
     * @change
     * 2023-08-31 by liwenqiang for init
     */
    void updateBatchByIdRecord(List<BbsTemplateSeatVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-31
     * @change
     * 2023-08-31 by liwenqiang for init
     */
    void saveByIdRecord(BbsTemplateSeatVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-31
     * @change
     * 2023-08-31 by liwenqiang for init
     */
    void saveBatchByIdRecord(List<BbsTemplateSeatVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param seatId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-31
     * @change
     * 2023-08-31 by liwenqiang for init
     */
    BbsTemplateSeatVo selectByIdRecord(String seatId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-31
     * @change
     * 2023-08-31 by liwenqiang for init
     */
    PageResult<List<BbsTemplateSeatPageResultVo>> selectByPageRecord(BbsTemplateSeatPageVo vo);

    /**
     * 根据上级id查询map集
     *
     * @param parentId 上级id
     * @return 模板属性map集
     */
    Map<String, String> selectMapByParentId(String parentId);
}
