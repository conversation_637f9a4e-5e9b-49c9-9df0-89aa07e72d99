package com.bonc.ioc.bzf.busisigning.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.service.BbpmInvoiceManagementService;
import com.bonc.ioc.bzf.busisigning.vo.BbpmInvoiceQueryVo;
import com.bonc.ioc.common.base.page.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: lidongyang
 * @createDate: 2023-08-30
 * @Version 1.0
 **/
@Slf4j
@Service
public class BbpmInvoiceManagementServiceImpl implements BbpmInvoiceManagementService {

    /**
     * selectByPage 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     */
    @Override
    public PageResult<List<BbpmInvoiceQueryVo>> selectByPage(BbpmInvoiceQueryVo vo) {
        log.info("======================发票管理列表分页查询，入参为：{}", JSONObject.toJSONString(vo));
        return new PageResult<>();
    }

}
