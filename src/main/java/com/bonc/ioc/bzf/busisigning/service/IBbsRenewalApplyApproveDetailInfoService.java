package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyApproveDetailInfoEntity;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveDetailInfoResultVo;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveDetailInfoVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

/**
 * 续签申请信息接口
 *
 * @author: hechengyao
 * @createDate: 2023-09-21
 * @Version 1.0
 **/
public interface IBbsRenewalApplyApproveDetailInfoService extends IMcpBaseService<BbsRenewalApplyApproveDetailInfoEntity> {

    /**
     * 续签申请审核-增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    String insertRecord(RenewalApplyApproveDetailInfoVo vo);

    /**
     * 续签申请审核-查（查看详细信息）
     *
     * @param approveDetailId 审批明细id
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    BbsRenewalApplyApproveDetailInfoEntity queryRenewalApplyApproveInfo(String approveDetailId);

    /**
     * 续签申请审核-查看续签申请审核记录
     *
     * @param parentId 申请信息id
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    PageResult<List<RenewalApplyApproveDetailInfoResultVo>> selectByListRecord(String parentId);

}
