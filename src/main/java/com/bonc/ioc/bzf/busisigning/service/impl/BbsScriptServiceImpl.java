package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bonc.ioc.bzf.busisigning.consts.KeywordConst;
import com.bonc.ioc.bzf.busisigning.enums.ChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.IdTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.PaymentChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.WhetherEnum;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbCustomerFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbcCheckInV2FeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctPaymentV2FeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.convervo.CompanyVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BankRequestVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSignerVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsCheckChangeVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsCheckCustChangeVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsScriptService;
import com.bonc.ioc.bzf.busisigning.service.IBbsiContractChangeService;
import com.bonc.ioc.bzf.busisigning.service.IContractChangeResultService;
import com.bonc.ioc.bzf.busisigning.utils.DateUtils;
import com.bonc.ioc.bzf.busisigning.utils.ExcelUtil;
import com.bonc.ioc.bzf.busisigning.utils.ResultUtils;
import com.bonc.ioc.bzf.busisigning.utils.UuidUtil;
import com.bonc.ioc.bzf.busisigning.vo.BbsLesseeChangeExcelVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsPaymentDateChangeExcelVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo;
import com.bonc.ioc.bzf.busisigning.vo.ChargePageVo;
import com.bonc.ioc.bzf.busisigning.vo.ChargeRespondVo;
import com.bonc.ioc.bzf.busisigning.vo.TaxrateParamVo;
import com.bonc.ioc.bzf.busisigning.vo.TaxrateResultVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.BusinessContractChangeOwner;
import com.bonc.ioc.bzf.busisigning.vo.payment.BusinessContractChangePaymentDateDTO;
import com.bonc.ioc.bzf.busisigning.vo.payment.ChangeBillPaymentDateDTO;
import com.bonc.ioc.bzf.busisigning.vo.payment.ChangeContractBusinessRequest;
import com.bonc.ioc.bzf.busisigning.vo.payment.ChangeContractPaymentDateRequest;
import com.bonc.ioc.bzf.busisigning.vo.payment.ChargeSubjectParamsBusinessRequest;
import com.bonc.ioc.bzf.utils.common.convert.CopyFieldUtil;
import com.bonc.ioc.bzf.utils.common.imports.BbsImportResultVo;
import com.bonc.ioc.bzf.utils.common.imports.ImportEnum;
import com.bonc.ioc.common.context.SystemContextHolder;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 脚本相关 服务实现类
 *
 * <AUTHOR>
 * @since 2024/5/15
 */
@Slf4j
@Service
public class BbsScriptServiceImpl implements IBbsScriptService {

    /**
     * excel相关 工具类
     */
    @Resource
    private ExcelUtil excelUtil;

    /**
     * 合同中心 feign实例
     */
    @Resource
    private BbctContractFeignClient contractFeignClient;

    /**
     * 客户中心 feign实例
     */
    @Resource
    private BbCustomerFeignClient customerFeignClient;

    /**
     * 缴费中心 feign实例
     */
    @Resource
    private BbctPaymentV2FeignClient paymentV2FeignClient;

    /**
     * 工银 feign实例
     */
    @Resource
    private BfipSettlementFeignClient settlementFeignClient;

    /**
     * 入住中心 feign实例
     */
    @Resource
    private BbcCheckInV2FeignClient checkInV2FeignClient;

    /**
     * 合同变更相关 服务实例
     */
    @Resource
    private IBbsiContractChangeService contractChangeService;

    /**
     * 合同变更结果相关 服务实例
     */
    @Resource
    private IContractChangeResultService contractChangeResultService;

    /**
     * excel导入--主承租人变更
     *
     * @param file    文件信息
     * @param batchNo 批次号
     * @return 导入结果
     */
    @Override
    public BbsImportResultVo<BbsLesseeChangeExcelVo> excelImportLesseeChange(MultipartFile file, String batchNo) {
        if (StringUtils.isBlank(batchNo)) {
            batchNo = UuidUtil.generateUuid();
        }
        String traceId = (String) SystemContextHolder.getCurrentContext().get(KeywordConst.TRACE_ID);
        BbsImportResultVo<BbsLesseeChangeExcelVo> resultVo = new BbsImportResultVo<>();
        int sucessSize = 0;
        int failedSize = 0;
        List<BbsLesseeChangeExcelVo> sucessList = new ArrayList<>();
        List<BbsLesseeChangeExcelVo> failedList = new ArrayList<>();
        List<String> headList = new ArrayList<>();
        headList.add("原合同编号");
        headList.add("变更前承租人/企业名称");
        headList.add("变更前企业联系人");
        headList.add("变更前承租人/企业联系电话");
        headList.add("变更前证件类型");
        headList.add("变更前证件号码/社会统一信用代码");
        headList.add("变更后承租人/企业名称");
        headList.add("变更后企业联系人");
        headList.add("变更后承租人/企业联系电话");
        headList.add("变更后证件类型");
        headList.add("变更后证件号码/社会统一信用代码");
        headList.add("变更后付款银行");
        headList.add("变更后银行卡户名");
        headList.add("变更后开户行支行");
        headList.add("变更后付款账号");
        headList.add("总行编码");
        headList.add("支行编码");
        headList.add("银行类别编码");
        List<BbsLesseeChangeExcelVo> voList = excelUtil.importExcel(file,
                BbsLesseeChangeExcelVo.class, null, headList);
        log.info(String.format("导入主承租人变更信息[json: %s]",
                JSON.toJSONString(voList, SerializerFeature.WriteMapNullValue)));
        if (CollectionUtils.isNotEmpty(voList)) {
            for (BbsLesseeChangeExcelVo excelVo : voList) {
                excelVo.setTraceId(traceId);
                excelVo.setBatchNo(batchNo);
                excelVo.setImportKey(ImportEnum.LESSEE_CHANGE.getDesc());
                excelVo.setImportBody(JSON.toJSONString(excelVo));
                boolean insertContractChange = false;
                boolean updateContract = false;
                String ccId = null;
                BbsiContractChangeResultVo oldContractChangeResultVo = null;
                BbctContractManagementVo contractManagementVo = null;
                try {
                    excelVo.translate();
                    excelVo.check();
                    // 合同中心 查询合同信息
                    contractManagementVo = selectContractByIdNo(excelVo.getContractNo());
                    // 客户中心 查询客户信息
                    BbsResultCustomerVo customerVo = getCustomerVoFromCustomerCenter(excelVo);
                    // 更新合同变更库
                    String agreementNo = getContractCode("4", excelVo.getContractNo());
                    ccId = insertContractChange(contractManagementVo.getContractNo(), agreementNo,
                            customerVo, contractManagementVo.getUserList().get(0));
                    oldContractChangeResultVo =
                            updateContractChangeResult(contractManagementVo.getContractNo(), customerVo);
                    insertContractChange = true;
                    // 合同中心 更新数据
                    String oldCustomerId = contractManagementVo.getUserList().get(0).getCustomerNo();
                    updateContractInfo(contractManagementVo, customerVo);
                    updateContract = true;
                    // 推送账单
                    createLesseeChangePayment(contractManagementVo, agreementNo);
                    pushCheckIn(contractManagementVo, oldCustomerId, customerVo.getCustomerNo());
                    excelVo.setImportSuccess(WhetherEnum.YES.getCode());
                    sucessList.add(excelVo);
                    sucessSize++;
                } catch (Exception e) {
                    excelVo.setImportSuccess(WhetherEnum.NO.getCode());
                    excelVo.setImportResult(e.getMessage());
                    excelVo.setImportException(ExceptionUtils.getStackTrace(e));
                    log.error(String.format("导入失败[详情: %s, contractCode: %s]",
                            e.getMessage(), excelVo.getContractNo()), e);
                    failedList.add(excelVo);
                    failedSize++;
                    rollbackContractChange(ccId);
                    rollbackContractChangeResult(insertContractChange, excelVo.getContractNo(), oldContractChangeResultVo);
                    rollbackContractInfo(updateContract, contractManagementVo);
                }
            }
        }
        resultVo.setBatchNo(batchNo);
        resultVo.setTraceId(traceId);
        resultVo.setImportKey(ImportEnum.LESSEE_CHANGE.getDesc());
        resultVo.setSucessSize(sucessSize);
        resultVo.setFailedSize(failedSize);
        resultVo.setSucessList(sucessList);
        resultVo.setFailedList(failedList);
        return resultVo;
    }

    /**
     * excel导入--应缴日期变更
     *
     * @param file    文件信息
     * @param batchNo 批次号
     * @return 导入结果
     */
    @Override
    public BbsImportResultVo<BbsPaymentDateChangeExcelVo> excelImportPaymentDate(MultipartFile file, String batchNo) {
        if (StringUtils.isBlank(batchNo)) {
            batchNo = UuidUtil.generateUuid();
        }
        String traceId = (String) SystemContextHolder.getCurrentContext().get(KeywordConst.TRACE_ID);
        BbsImportResultVo<BbsPaymentDateChangeExcelVo> resultVo = new BbsImportResultVo<>();
        int sucessSize = 0;
        int failedSize = 0;
        List<BbsPaymentDateChangeExcelVo> sucessList = new ArrayList<>();
        List<BbsPaymentDateChangeExcelVo> failedList = new ArrayList<>();
        List<String> headList = new ArrayList<>();
        headList.add("原合同号");
        headList.add("账单周期");
        headList.add("账单唯一识别码");
        headList.add("变更前应缴费日期");
        headList.add("变更后应缴费日期");
        List<BbsPaymentDateChangeExcelVo> voList = excelUtil.importExcel(file,
                BbsPaymentDateChangeExcelVo.class, null, headList);
        log.info(String.format("导入应缴日期变更信息[json: %s]",
                JSON.toJSONString(voList, SerializerFeature.WriteMapNullValue)));
        if (CollectionUtils.isNotEmpty(voList)) {
            Map<String, List<BbsPaymentDateChangeExcelVo>> contractCodeMap = new HashMap<>();
            for (BbsPaymentDateChangeExcelVo vo : voList) {
                vo.check();
                if (contractCodeMap.containsKey(vo.getContractNo())) {
                    contractCodeMap.get(vo.getContractNo()).add(vo);
                } else {
                    List<BbsPaymentDateChangeExcelVo> tempList = new ArrayList<>();
                    tempList.add(vo);
                    contractCodeMap.put(vo.getContractNo(), tempList);
                }
            }
            for (Map.Entry<String, List<BbsPaymentDateChangeExcelVo>> entry : contractCodeMap.entrySet()) {
                String contractCode = entry.getKey();
                List<BbsPaymentDateChangeExcelVo> valueList = entry.getValue();
                BbsPaymentDateChangeExcelVo excelVo = new BbsPaymentDateChangeExcelVo();
                excelVo.setTraceId(traceId);
                excelVo.setBatchNo(batchNo);
                excelVo.setImportKey(ImportEnum.PAYMENT_DATE_CHANGE.getDesc());
                excelVo.setImportBody(JSON.toJSONString(valueList));
                String ccId = null;
                try {
                    // 合同中心 查询合同信息
                    BbctContractManagementVo contractManagementVo = selectContractByIdNo(contractCode);
                    // 更新合同变更库
                    String agreementNo = getContractCode("4", contractCode);
                    ccId = insertContractChange(contractCode, agreementNo, valueList);
                    // 推送账单
                    createPaymentDateChangePayment(contractManagementVo, valueList);
                    excelVo.setImportSuccess(WhetherEnum.YES.getCode());
                    sucessList.add(excelVo);
                    sucessSize++;
                } catch (Exception e) {
                    excelVo.setImportSuccess(WhetherEnum.NO.getCode());
                    excelVo.setImportResult(e.getMessage());
                    excelVo.setImportException(ExceptionUtils.getStackTrace(e));
                    log.error(String.format("导入失败[详情: %s, contractCode: %s]",
                            e.getMessage(), contractCode), e);
                    failedList.add(excelVo);
                    failedSize++;
                    rollbackContractChange(ccId);
                }
            }
        }
        resultVo.setBatchNo(batchNo);
        resultVo.setTraceId(traceId);
        resultVo.setImportKey(ImportEnum.PAYMENT_DATE_CHANGE.getDesc());
        resultVo.setSucessSize(sucessSize);
        resultVo.setFailedSize(failedSize);
        resultVo.setSucessList(sucessList);
        resultVo.setFailedList(failedList);
        return resultVo;
    }

    /**
     * 新增合同变更记录
     *
     * @param contractNo    合同编号
     * @param agreementNo   协议编号
     * @param customerVo    客户信息 vo实体
     * @param oldCustomerVo 老客户信息 vo实体
     * @return 合同变更id
     */
    private String insertContractChange(String contractNo,
                                        String agreementNo,
                                        BbsResultCustomerVo customerVo,
                                        BbctContractSignerVo oldCustomerVo) {
        BbsiContractChangeVo contractChangeVo = new BbsiContractChangeVo();
        contractChangeVo.setContractCode(contractNo);
        contractChangeVo.setAgreementCode(agreementNo);
        contractChangeVo.setChangeType(ChangeTypeEnum.LESSEE_CHANGE.getCode());
        contractChangeVo.setCustomerNo(customerVo.getCustomerNo());
        contractChangeVo.setCustomerNoOld(oldCustomerVo.getCustomerNo());
        contractChangeVo.setCustomerName(customerVo.getCustomerName());
        contractChangeVo.setCustomerNameOld(oldCustomerVo.getCustomerName());
        contractChangeVo.setCustomerIdType(customerVo.getCustomerIdType());
        contractChangeVo.setCustomerIdNumber(customerVo.getCustomerIdNumber());
        contractChangeVo.setCustomerTel(customerVo.getCustomerTel());
        contractChangeVo.setCustomerTelOld(oldCustomerVo.getCustomerTel());
        contractChangeVo.setMailAddress(customerVo.getMailAddress());
        contractChangeVo.setMailAddressOld(oldCustomerVo.getMailAddress());
        contractChangeVo.setBankCard(customerVo.getBankCard());
        contractChangeVo.setBankCardOld(oldCustomerVo.getBankCard());
        contractChangeVo.setBankNameCode(customerVo.getBankNameCode());
        contractChangeVo.setBankNameCodeOld(oldCustomerVo.getBankNameCode());
        contractChangeVo.setBankName(customerVo.getBankName());
        contractChangeVo.setBankNameOld(oldCustomerVo.getBankName());
        contractChangeVo.setBankSubbranchCode(customerVo.getBankSubbranchCode());
        contractChangeVo.setBankSubbranchCodeOld(oldCustomerVo.getBankSubbranchCode());
        contractChangeVo.setBankSubbranchName(customerVo.getBankSubbranchName());
        contractChangeVo.setBankSubbranchNameOld(oldCustomerVo.getBankSubbranchName());
        contractChangeVo.setBankNccCategoryCode(customerVo.getBankNccCategoryCode());
        contractChangeVo.setBankNccCategoryCodeOld(oldCustomerVo.getBankNccCategoryCode());
        contractChangeVo.setCurrentTransactors(WhetherEnum.NO.getCode());
        contractChangeVo.setSource("PC");
        contractChangeVo.setChangeStatus(WhetherEnum.YES.getCode());
        return contractChangeService.insertRecord(contractChangeVo, true);
    }

    /**
     * 新增合同变更记录
     *
     * @param contractNo    合同编号
     * @param agreementNo   协议编号
     * @param paymentDateChangeExcelList    应缴日期变更信息列表
     * @return 合同变更id
     */
    private String insertContractChange(String contractNo,
                                        String agreementNo,
                                        List<BbsPaymentDateChangeExcelVo> paymentDateChangeExcelList) {
        BbsiContractChangeVo contractChangeVo = new BbsiContractChangeVo();
        contractChangeVo.setContractCode(contractNo);
        contractChangeVo.setAgreementCode(agreementNo);
        contractChangeVo.setChangeType(ChangeTypeEnum.PAYMENT_DATE_CHANGE.getCode());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("paymentDateChange", paymentDateChangeExcelList);
        contractChangeVo.setChangeExtend(JSON.toJSONString(jsonObject));
        return contractChangeService.insertRecord(contractChangeVo, true);
    }

    /**
     * 回滚合同变更记录
     *
     * @param ccId 合同变更记录id
     */
    private void rollbackContractChange(String ccId) {
        if (StringUtils.isNotBlank(ccId)) {
            contractChangeService.deleteByIdRecord(ccId);
        }
    }

    /**
     * 更新合同变更结果
     *
     * @param contractNo 合同编号
     * @param customerVo 客户信息 vo实体
     * @return 老合同变更结果
     */
    private BbsiContractChangeResultVo updateContractChangeResult(String contractNo,
                                                                  BbsResultCustomerVo customerVo) {
        BbsiContractChangeResultVo oldContractChangeResultVo =
                contractChangeResultService.selectByContractCode(contractNo);
        BbsiContractChangeResultVo contractChangeResultVo = new BbsiContractChangeResultVo();
        contractChangeResultVo.setContractCode(contractNo);
        contractChangeResultVo.setCustomerNo(customerVo.getCustomerNo());
        contractChangeResultVo.setCustomerName(customerVo.getCustomerName());
        contractChangeResultVo.setMailAddress(customerVo.getMailAddress());
        contractChangeResultVo.setBankNameCode(customerVo.getBankNameCode());
        contractChangeResultVo.setBankName(customerVo.getBankName());
        contractChangeResultVo.setBankSubbranchCode(customerVo.getBankSubbranchCode());
        contractChangeResultVo.setBankSubbranchName(customerVo.getBankSubbranchName());
        contractChangeResultVo.setBankCard(customerVo.getBankCard());
        contractChangeResultVo.setCustomerTel(customerVo.getCustomerTel());
        contractChangeResultVo.setBankNccCategoryCode(customerVo.getBankNccCategoryCode());
        contractChangeResultService.insertOrUpdate(contractChangeResultVo);
        return oldContractChangeResultVo;
    }

    /**
     * 回滚合同变更结果
     *
     * @param insertContractChange      是否新增合同变更记录
     * @param contractNo                合同编号
     * @param oldContractChangeResultVo 老合同变更结果
     */
    private void rollbackContractChangeResult(boolean insertContractChange,
                                              String contractNo,
                                              BbsiContractChangeResultVo oldContractChangeResultVo) {
        if (insertContractChange) {
            if (Objects.isNull(oldContractChangeResultVo)) {
                contractChangeResultService.deleteByContractCode(contractNo);
            } else {
                contractChangeResultService.insertOrUpdate(oldContractChangeResultVo);
            }
        }
    }

    /**
     * 合同中心 合同信息变更
     *
     * @param contractManagementVo 合同信息 vo实体
     * @param customerVo           客户信息 vo实体
     */
    private void updateContractInfo(BbctContractManagementVo contractManagementVo,
                                    BbsResultCustomerVo customerVo) {
        List<BbctContractSignerVo> userList = new ArrayList<>();
        BbctContractSignerVo signerVo = new BbctContractSignerVo();
        BeanUtils.copyProperties(customerVo, signerVo);
        signerVo.setCustomerTypeName("企业");
        String consignorName = contractManagementVo.getUserList().get(0).getConsignorName();
        String consignorMobile = contractManagementVo.getUserList().get(0).getConsignorMobile();
        signerVo.setConsignorName(consignorName);
        signerVo.setConsignorMobile(consignorMobile);
        userList.add(signerVo);
        contractManagementVo.setUserList(userList);
        contractManagementVo.setChangeUser(WhetherEnum.YES.getCode());
        List<BbctContractManagementVo> contractList = new ArrayList<>();
        contractList.add(contractManagementVo);
        AppReply appReply = contractFeignClient.changeBatchAddOne(contractList);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("更新合同信息失败[详情: %s, contractCode: %s]",
                    appReply, contractManagementVo.getContractNo()));
        }
    }

    /**
     * 创建商业主承租人变更账单
     *
     * @param contractManagementVo 合同信息 vo实体
     * @param agreementNo          协议编号
     */
    private void createLesseeChangePayment(BbctContractManagementVo contractManagementVo,
                                           String agreementNo) {
        String projectId = contractManagementVo.getSubjectMatterList().get(0).getProjectId();
        BbctContractSignerVo customerVo = contractManagementVo.getUserList().get(0);
        ChangeContractBusinessRequest request = new ChangeContractBusinessRequest();
        request.setChangeType(PaymentChangeTypeEnum.BUSINESS_LESSEE_CHANGE.getCode());
        request.setProjectId(projectId);
        BusinessContractChangeOwner businessContractChangeOwner = new BusinessContractChangeOwner();
        businessContractChangeOwner.setRequestTime(DateUtils.getDateTime());
        businessContractChangeOwner.setSignDate(DateUtils.getDate());
        businessContractChangeOwner.setAgreementCode(agreementNo);
        businessContractChangeOwner.setContractCode(contractManagementVo.getContractNo());
        businessContractChangeOwner.setCompanyId(customerVo.getCustomerNo());
        businessContractChangeOwner.setCompanyIDType("01");
        businessContractChangeOwner.setSocialCreditCode(customerVo.getCustomerCreditCode());
        businessContractChangeOwner.setCompanyName(customerVo.getCustomerName());
        businessContractChangeOwner.setCompanyCustomerNo(customerVo.getCustomerNo());
        businessContractChangeOwner.setCompanySupplierNo(customerVo.getCompanySupplierNo());
        businessContractChangeOwner.setCompanySupplierName(customerVo.getCompanySupplierName());
        businessContractChangeOwner.setCompanyBankName(customerVo.getBankName());
        businessContractChangeOwner.setCompanyBankCode(customerVo.getBankNameCode());
        businessContractChangeOwner.setCompanyBankBranchName(customerVo.getBankSubbranchName());
        businessContractChangeOwner.setCompanyBankBrachCode(customerVo.getBankSubbranchCode());
        businessContractChangeOwner.setCompanyBankAccountName(customerVo.getBankUserName());
        businessContractChangeOwner.setCompanyBankAccountNo(customerVo.getBankCard());
        businessContractChangeOwner.setAuthorizedAgent(customerVo.getConsignorName());
        businessContractChangeOwner.setAuthorizedAgentMobile(customerVo.getConsignorMobile());
        List<ChargeSubjectParamsBusinessRequest> chargeList = new ArrayList<>();
        ChargeSubjectParamsBusinessRequest rentCharge = new ChargeSubjectParamsBusinessRequest();
        rentCharge.setChargeSubjectNo("01");
        TaxrateResultVo rentTaxVo = getTaxByCharge(projectId,
                "01",
                "02",
                customerVo.getCustomerNo());
        BigDecimal rentTaxRate = Objects.isNull(rentTaxVo) ? null : percentageStr(rentTaxVo.getCompanyTaxRate());
        rentCharge.setTaxRate(rentTaxRate);
        chargeList.add(rentCharge);
        ChargeSubjectParamsBusinessRequest propertyCharge = new ChargeSubjectParamsBusinessRequest();
        propertyCharge.setChargeSubjectNo("07");
        TaxrateResultVo propertyTaxVo = getTaxByCharge(projectId,
                "07",
                "02",
                customerVo.getCustomerNo());
        BigDecimal propertyTaxRate = Objects.isNull(propertyTaxVo) ? null : percentageStr(propertyTaxVo.getCompanyTaxRate());
        propertyCharge.setTaxRate(propertyTaxRate);
        chargeList.add(propertyCharge);
        businessContractChangeOwner.setChargeSubjectList(chargeList);
        request.setBusinessContractChangeOwnerDTO(businessContractChangeOwner);
        createBusinessLesseeChangePayment(request);
    }

    /**
     * 创建应缴日期变更账单
     * @param contractManagementVo 合同信息 vo实体
     * @param paymentDateChangeExcelList 应缴日期变更信息列表
     */
    private void createPaymentDateChangePayment(BbctContractManagementVo contractManagementVo,
                                                List<BbsPaymentDateChangeExcelVo> paymentDateChangeExcelList) {
        ChangeContractPaymentDateRequest request = new ChangeContractPaymentDateRequest();
        request.setChangeType(PaymentChangeTypeEnum.PAYMENT_DATE_CHANGE.getCode());
        request.setProjectId(contractManagementVo.getSubjectMatterList().get(0).getProjectId());

        BusinessContractChangePaymentDateDTO businessContractChangePaymentDateDTO = new BusinessContractChangePaymentDateDTO();
        businessContractChangePaymentDateDTO.setContractCode(contractManagementVo.getContractNo());
        List<ChangeBillPaymentDateDTO> changeBillPaymentDateDTOList = new ArrayList<>();
        for (BbsPaymentDateChangeExcelVo excelVo : paymentDateChangeExcelList) {
            ChangeBillPaymentDateDTO changeBillPaymentDateDTO = new ChangeBillPaymentDateDTO();
            changeBillPaymentDateDTO.setBillCode(excelVo.getBillCode());
            changeBillPaymentDateDTO.setPaymentDate(DateUtils.parseDate(excelVo.getAfterPaymentDate()));
            changeBillPaymentDateDTOList.add(changeBillPaymentDateDTO);
        }
        businessContractChangePaymentDateDTO.setChangeBillPaymentDateDTOList(changeBillPaymentDateDTOList);
        request.setBusinessContractChangePaymentDateDTO(businessContractChangePaymentDateDTO);
        createBusinessPaymentDateChangePayment(request);
    }

    /**
     * 工银 获取税率实体
     *
     * @param projectId       项目id
     * @param chargeSubjectNo 计费科目类型
     * @param tenantry        承租方
     * @param companyId       企业id
     * @return 税率实体
     */
    private TaxrateResultVo getTaxByCharge(String projectId,
                                           String chargeSubjectNo,
                                           String tenantry,
                                           String companyId) {
        TaxrateParamVo taxrateParamVo = new TaxrateParamVo();
        taxrateParamVo.setProjectId(projectId);
        taxrateParamVo.setYeTai("03");
        taxrateParamVo.setChargeItemId(chargeSubjectNo);
        taxrateParamVo.setTenantry(tenantry);
        taxrateParamVo.setCompanyId(companyId);
        taxrateParamVo.setCurrent("1");
        taxrateParamVo.setSize("10");
        BankRequestVo<TaxrateParamVo> bankRequestVo = new BankRequestVo<>();
        bankRequestVo.setData(taxrateParamVo);
        log.info("工银查询税率参数:{}", JSON.toJSONString(bankRequestVo));
        ChargeRespondVo<ChargePageVo<List<TaxrateResultVo>>> chargeRespondVo =
                settlementFeignClient.taxrateQusery(bankRequestVo);
        log.info("工银查询税率返回:{}", JSON.toJSONString(chargeRespondVo));
        if (chargeRespondVo.getData() == null) {
            throw new McpException("查询税率异常:" + chargeRespondVo.getMessage());
        }
        List<TaxrateResultVo> taxList = chargeRespondVo.getData().getRecords();
        if (CollectionUtils.isEmpty(chargeRespondVo.getData().getRecords())) {
            return null;
        }
        return taxList.get(0);
    }

    /**
     * 缴费中心 创建商业主承租人变更账单
     *
     * @param request 请求体
     */
    private void createBusinessLesseeChangePayment(ChangeContractBusinessRequest request) {
        AppReply<Object> appReply = paymentV2FeignClient.createBusinessLesseeChangePayment(request);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            log.error("缴费中心生成账单失败:" + (Objects.isNull(appReply) ? null : appReply.toString()));
            throw new McpException("缴费中心生成账单失败");
        }
    }

    /**
     * 缴费中心 创建应缴日期变更账单
     *
     * @param request 请求体
     */
    private void createBusinessPaymentDateChangePayment(ChangeContractPaymentDateRequest request) {
        AppReply<Object> appReply = paymentV2FeignClient.createBusinessPaymentDateChangePayment(request);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            log.error("缴费中心生成账单失败:" + (Objects.isNull(appReply) ? null : appReply.toString()));
            throw new McpException("缴费中心生成账单失败");
        }
    }

    /**
     * 合同中心 合同信息回滚
     *
     * @param contractManagementVo 合同信息 vo实体
     */
    private void rollbackContractInfo(boolean updateContract, BbctContractManagementVo contractManagementVo) {
        if (updateContract) {
            List<BbctContractManagementVo> contractList = new ArrayList<>();
            contractList.add(contractManagementVo);
            AppReply appReply = contractFeignClient.changeFallback(contractList);
            if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
                throw new McpException(ResultUtils.createLog("回滚失败[详情: %s, contractCode: %s]",
                        appReply, contractManagementVo.getContractNo()));
            }
        }
    }

    /**
     * 合同中心 查询合同信息
     *
     * @param contractCode 合同编号
     * @return 合同信息
     */
    private BbctContractManagementVo selectContractByIdNo(String contractCode) {
        AppReply<BbctContractManagementVo> appReply = contractFeignClient.selectByIdNo(contractCode, null);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("查询合同信息失败[详情: %s, contractCode: %s]", appReply, contractCode));
        }
        return appReply.getData();
    }

    /**
     * 从客户中心获取客户信息
     *
     * @param excelVo 导入信息 vo实体
     * @return 客户信息
     */
    private BbsResultCustomerVo getCustomerVoFromCustomerCenter(BbsLesseeChangeExcelVo excelVo) {
        BbsResultCustomerVo bbsResultCustomerVo = new BbsResultCustomerVo();
        AppReply<JSONObject> appReply =
                customerFeignClient.getEnterpriseCustomerByIdOrCreditCode(excelVo.getNewCustomerIdNumber());
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || appReply.getData() == null) {
            throw new McpException(ResultUtils.createLog("客户中心获取企业信息失败[详情: %s, 合同编号: %s]",
                    appReply, excelVo.getContractNo()));
        }
        JSONObject enterpriseJsonObjectData = appReply.getData();
        CompanyVo companyVo = new CompanyVo();
        try {
            CopyFieldUtil.transJSONObject(companyVo, enterpriseJsonObjectData, CopyFieldUtil.TYPE_CUSTOMER);
        } catch (Exception e) {
            throw new McpException("获取企业信息异常", e);
        }
        BeanUtil.copyProperties(companyVo, bbsResultCustomerVo, CopyOptions.create().setIgnoreNullValue(true));
        bbsResultCustomerVo.setCustomerRatio(1f);
        bbsResultCustomerVo.setCustomerType("01");
        bbsResultCustomerVo.setEmailAddress(Optional.ofNullable(enterpriseJsonObjectData.
                getJSONObject("currentAgentInfo").getString("email")).orElse("无"));
        bbsResultCustomerVo.setBankCard(excelVo.getNewBankAccountId());
        bbsResultCustomerVo.setBankUserName(excelVo.getNewBankAccountName());
        bbsResultCustomerVo.setBankNameCode(excelVo.getNewBankNameCode());
        bbsResultCustomerVo.setBankName(excelVo.getNewBankName());
        bbsResultCustomerVo.setBankNccCategoryCode(excelVo.getNewBankNccCategoryCode());
        bbsResultCustomerVo.setBankSubbranchCode(excelVo.getNewSubBankNameCode());
        bbsResultCustomerVo.setBankSubbranchName(excelVo.getNewSubBankName());
        bbsResultCustomerVo.setCustomerIdType(IdTypeEnum.TYPE_NINETY_NONE.getCode());
        if (StrUtil.isEmpty(bbsResultCustomerVo.getCompanySupplierNo())) {
            bbsResultCustomerVo.setCompanySupplierNo(bbsResultCustomerVo.getCustomerNo());
        }
        bbsResultCustomerVo.setContactName(enterpriseJsonObjectData.getJSONObject("currentAgentInfo").getString("agentName"));
        bbsResultCustomerVo.setCustomerTel(enterpriseJsonObjectData.getJSONObject("currentAgentInfo").getString("agentPhone"));
        if (StrUtil.isEmpty(bbsResultCustomerVo.getMailAddress())) {
            bbsResultCustomerVo.setMailAddress(enterpriseJsonObjectData.getString("storeLocatedAddress"));
        }
        return bbsResultCustomerVo;
    }

    /**
     * 获取合同编号
     *
     * @return 合同编号
     */
    private String getContractCode(String modeId, String contractCode) {
        AppReply<String> appReply = contractFeignClient.contractCodeGeneration(modeId, contractCode);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || com.alibaba.cloud.commons.lang.StringUtils.isBlank(appReply.getData())) {
            log.error("合同中心生成编号失败:" + (Objects.isNull(appReply) ? null : appReply.toString()));
            throw new McpException("合同中心生成编号失败");
        }
        return appReply.getData();
    }

    private BigDecimal percentageStr(String percentageStr) {
        // 去除百分号并将字符串转换为数字
        double percentage = Double.parseDouble(percentageStr.replace("%", ""));
        // 将数字转换为BigDecimal并保留4位小数
        BigDecimal decimalValue = BigDecimal.valueOf(percentage / 100).setScale(4, BigDecimal.ROUND_HALF_UP);
        return decimalValue;
    }

    /**
     * 入住中心 推送入住变更信息
     *
     * @param contractManagementVo 合同信息 vo实体
     * @param oldCustomerId        老客户id
     * @param newCustomerId        新客户id
     */
    private void pushCheckIn(BbctContractManagementVo contractManagementVo,
                             String oldCustomerId,
                             String newCustomerId) {
        String contractNo = contractManagementVo.getContractNo();
        try {
            BbsCheckChangeVo checkChangeVo = new BbsCheckChangeVo();
            checkChangeVo.setCheckChangeBusinessCode("02");
            List<BbsCheckCustChangeVo> checkCustChangeList = new ArrayList<>();
            BbsCheckCustChangeVo checkCustChangeVo = new BbsCheckCustChangeVo();
            checkCustChangeVo.setContractCode(contractNo);
            checkCustChangeVo.setOldContractCode(contractNo);
            checkCustChangeVo.setCustomerId(newCustomerId);
            checkCustChangeVo.setOldCustomerId(oldCustomerId);
            checkCustChangeVo.setContractBeginTime(contractManagementVo.getContractBeginTime());
            checkCustChangeVo.setContractEndTime(contractManagementVo.getContractEndTime());
            checkCustChangeList.add(checkCustChangeVo);
            checkChangeVo.setCustChangeContracts(checkCustChangeList);
            log.info("入住中心推送入住变更信息传参: "+ JSON.toJSONString(checkChangeVo));
            AppReply appReply = checkInV2FeignClient.contractcode(checkChangeVo);
            if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
                log.error(ResultUtils.createLog("入住中心推送入住变更信息失败[详情: %s, 合同编号: %s]",
                        appReply, contractNo));
            } else {
                log.info(String.format("入住中心推送入住变更信息成功[合同编号: %s]", contractNo));
            }
        } catch (Exception e) {
            log.error(String.format("入住中心推送入住变更信息失败[合同编号: %s]", contractNo), e);
        }
    }
}
