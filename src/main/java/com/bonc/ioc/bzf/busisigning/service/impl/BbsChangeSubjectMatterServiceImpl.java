package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeSubjectMatterEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsChangeSubjectMatterMapper;
import com.bonc.ioc.bzf.busisigning.service.IBbsChangeSubjectMatterService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 缩租面积变更历史产品信息表 服务类实现
 *
 * <AUTHOR>
 * @date 2024-10-25
 * @change 2024-10-25 by tbh for init
 */
@Slf4j
@Service
public class BbsChangeSubjectMatterServiceImpl extends McpBaseServiceImpl<BbsChangeSubjectMatterEntity> implements IBbsChangeSubjectMatterService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsChangeSubjectMatterMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsChangeSubjectMatterService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsChangeSubjectMatterVo vo) {
        if(vo == null) {
            return null;
        }

        BbsChangeSubjectMatterEntity entity = new BbsChangeSubjectMatterEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setSubjectMatterId(null);
        if(!baseService.insert(entity)) {
            log.error("缩租面积变更历史产品信息表新增失败:" + entity.toString());
            throw new McpException("缩租面积变更历史产品信息表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getSubjectMatterId(),1)) {
                log.error("缩租面积变更历史产品信息表新增后保存历史失败:" + entity.toString());
                throw new McpException("缩租面积变更历史产品信息表新增后保存历史失败");
            }

            log.debug("缩租面积变更历史产品信息表新增成功:"+entity.getSubjectMatterId());
            return entity.getSubjectMatterId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsChangeSubjectMatterVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsChangeSubjectMatterEntity> entityList = new ArrayList<>();
        for (BbsChangeSubjectMatterVo item:voList) {
            BbsChangeSubjectMatterEntity entity = new BbsChangeSubjectMatterEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsChangeSubjectMatterEntity item:entityList){
            item.setSubjectMatterId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("缩租面积变更历史产品信息表新增失败");
            throw new McpException("缩租面积变更历史产品信息表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsChangeSubjectMatterEntity::getSubjectMatterId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("缩租面积变更历史产品信息表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("缩租面积变更历史产品信息表批量新增后保存历史失败");
            }

            log.debug("缩租面积变更历史产品信息表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param subjectMatterId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String subjectMatterId) {
        if(!StringUtils.isEmpty(subjectMatterId)) {
            if(!baseService.saveOperationHisById(subjectMatterId,3)) {
                log.error("缩租面积变更历史产品信息表删除后保存历史失败:" + subjectMatterId);
                throw new McpException("缩租面积变更历史产品信息表删除后保存历史失败");
            }

            if(!baseService.removeById(subjectMatterId)) {
                log.error("缩租面积变更历史产品信息表删除失败");
                throw new McpException("缩租面积变更历史产品信息表删除失败"+subjectMatterId);
            }
        } else {
            throw new McpException("缩租面积变更历史产品信息表删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param subjectMatterIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> subjectMatterIdList) {
        if(!CollectionUtils.isEmpty(subjectMatterIdList)) {
            int oldSize = subjectMatterIdList.size();
            subjectMatterIdList = subjectMatterIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(subjectMatterIdList) || oldSize != subjectMatterIdList.size()) {
                throw new McpException("缩租面积变更历史产品信息表批量删除失败 存在主键id为空的记录"+StringUtils.join(subjectMatterIdList));
            }

            if(!baseService.saveOperationHisByIds(subjectMatterIdList,3)) {
                log.error("缩租面积变更历史产品信息表批量删除后保存历史失败:" + StringUtils.join(subjectMatterIdList));
                throw new McpException("缩租面积变更历史产品信息表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(subjectMatterIdList)) {
                log.error("缩租面积变更历史产品信息表批量删除失败");
                throw new McpException("缩租面积变更历史产品信息表批量删除失败"+StringUtils.join(subjectMatterIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的缩租面积变更历史产品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsChangeSubjectMatterVo vo) {
        if(vo != null) {
            BbsChangeSubjectMatterEntity entity = new BbsChangeSubjectMatterEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getSubjectMatterId())) {
                throw new McpException("缩租面积变更历史产品信息表更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("缩租面积变更历史产品信息表更新失败");
                throw new McpException("缩租面积变更历史产品信息表更新失败"+entity.getSubjectMatterId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSubjectMatterId(),2)) {
                    log.error("缩租面积变更历史产品信息表更新后保存历史失败:" + entity.getSubjectMatterId());
                    throw new McpException("缩租面积变更历史产品信息表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("缩租面积变更历史产品信息表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的缩租面积变更历史产品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsChangeSubjectMatterVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsChangeSubjectMatterEntity> entityList = new ArrayList<>();

            for (BbsChangeSubjectMatterVo item:voList){
                BbsChangeSubjectMatterEntity entity = new BbsChangeSubjectMatterEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getSubjectMatterId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("缩租面积变更历史产品信息表批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("缩租面积变更历史产品信息表批量更新失败");
                throw new McpException("缩租面积变更历史产品信息表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSubjectMatterId())).map(BbsChangeSubjectMatterEntity::getSubjectMatterId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("缩租面积变更历史产品信息表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("缩租面积变更历史产品信息表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的缩租面积变更历史产品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsChangeSubjectMatterVo vo) {
        if(vo != null) {
            BbsChangeSubjectMatterEntity entity = new BbsChangeSubjectMatterEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("缩租面积变更历史产品信息表保存失败");
                throw new McpException("缩租面积变更历史产品信息表保存失败"+entity.getSubjectMatterId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSubjectMatterId(),4)) {
                    log.error("缩租面积变更历史产品信息表保存后保存历史失败:" + entity.getSubjectMatterId());
                    throw new McpException("缩租面积变更历史产品信息表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("缩租面积变更历史产品信息表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的缩租面积变更历史产品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsChangeSubjectMatterVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsChangeSubjectMatterEntity> entityList = new ArrayList<>();

            for (BbsChangeSubjectMatterVo item:voList){
                BbsChangeSubjectMatterEntity entity = new BbsChangeSubjectMatterEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("缩租面积变更历史产品信息表批量保存失败");
                throw new McpException("缩租面积变更历史产品信息表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSubjectMatterId())).map(BbsChangeSubjectMatterEntity::getSubjectMatterId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("缩租面积变更历史产品信息表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("缩租面积变更历史产品信息表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param subjectMatterId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsChangeSubjectMatterVo selectByIdRecord(String subjectMatterId) {
        BbsChangeSubjectMatterVo vo = new BbsChangeSubjectMatterVo();

        if(!StringUtils.isEmpty(subjectMatterId)) {
            BbsChangeSubjectMatterEntity entity = baseService.selectById(subjectMatterId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsChangeSubjectMatterPageResultVo>> selectByPageRecord(BbsChangeSubjectMatterPageVo vo) {
        List<BbsChangeSubjectMatterPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
