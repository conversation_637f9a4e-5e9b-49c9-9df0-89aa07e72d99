package com.bonc.ioc.bzf.busisigning.workflow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="流转基础VO", description="流转基础VO")

public class BaseFlowVo {

    @ApiModelProperty(value = "requestId")
    private String requestId;

    @ApiModelProperty(value = "建议")
    private String remark;

    @ApiModelProperty(value = "泛微用户ID" , hidden = true)
    private Long userId;

    @ApiModelProperty(value = "流程退回方式：0：自由退回；1：逐级退回；2：指定范围退回；3：按出口退回；不传则获取后台设置的退回方式进行退回；" , hidden = true)
    private Integer rejectToType;
}
