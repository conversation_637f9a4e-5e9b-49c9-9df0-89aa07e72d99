package com.bonc.ioc.bzf.busisigning.factory.change.otherchange;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.enums.ReductionProductTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.WhetherEnum;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctChargeRuleSubParamsBigVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.vo.JsonVo.IncrementalInfoArray;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillParamsRoomChargeSubjectVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsParamsRoomVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsParamsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 保证金变更 工厂类
 *
 * <AUTHOR>
 * @since 2024/10/24
 */
@Slf4j
public class CashPledgeChangeFactory extends AbstractSubChangeFactory {

    private List<BbsChangeResultProductVo> productList = null;

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param contractChangeVo             合同变更信息 vo实体
     */
    public CashPledgeChangeFactory(FeignServiceConfiguration feignServiceConfiguration,
                                   BusinessServiceConfiguration businessServiceConfiguration,
                                   BbsiContractChangeVo contractChangeVo) {
        super(feignServiceConfiguration,
                businessServiceConfiguration,
                contractChangeVo);
    }

    /**
     * 赋值试算账单请求参数
     *
     * @param previewBillsParamsVo 试算账单请求参数 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentSignInfoVo     上级合同签约信息 vo实体
     */
    @Override
    public void setPreviewBillsParams(PreviewBillsParamsVo previewBillsParamsVo,
                                      BbctContractManagementVo parentContractInfoVo,
                                      SigningSaveVo parentSignInfoVo) {
        previewBillsParamsVo.setChangeAccountingPeriodType(contractChangeVo.getRentEffectiveExecutionDate());
        previewBillsParamsVo.setCutShortRoomList(Collections.emptyList());
        setRoomList(previewBillsParamsVo, parentContractInfoVo, parentSignInfoVo);
    }

    /**
     * 赋值试算需要的产品列表
     *
     * @param previewBillsParamsVo 试算账单请求参数 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentSignInfoVo     上级合同签约信息 vo实体
     */
    private void setRoomList(PreviewBillsParamsVo previewBillsParamsVo,
                             BbctContractManagementVo parentContractInfoVo,
                             SigningSaveVo parentSignInfoVo) {
        List<PreviewBillsParamsRoomVo> roomList;
        if (Objects.isNull(previewBillsParamsVo.getRoomList())) {
            roomList = new ArrayList<>();
        } else {
            roomList = previewBillsParamsVo.getRoomList();
        }
        //获取老合同房源
        List<BbctContractSubjectMatterVo> oldProductList = parentContractInfoVo.getSubjectMatterList();
        // 获取变更的产品列表
        List<BbsChangeResultProductVo> changeProductList = getChangeProductListExcludeReturnedProduct();
        productList = changeProductList;
        //已退产品编号set集
        Set<String> productNoSet = getReturnedProductSet();
        //把老合同房源放入保障金房源列表中----变更列表中存在的房源除外，缩租房源除外
        for (BbctContractSubjectMatterVo oldProductVo : oldProductList){
            boolean isRoom = true;
            for (BbsChangeResultProductVo changeProductVo : changeProductList) {
                //变更列表中存在的房源除外
                if(changeProductVo.getProductNo().equals(oldProductVo.getProductNo())){
                    isRoom = false;
                }
            }
            //缩租房源除外
            if(productNoSet.contains(oldProductVo.getProductNo())){
                isRoom = false;
            }
            if(isRoom){
                BbsChangeResultProductVo oldToNewVo = new BbsChangeResultProductVo();
                BeanUtils.copyProperties(oldProductVo,oldToNewVo);
//                oldToNewVo.setRentStandardUnit(oldProductVo.getRentUnit());
                oldToNewVo.setRentStandard(Double.parseDouble(oldProductVo.getRent()));
                productList.add(oldToNewVo);
            }
        }
        //roomList.add(createRoomInfo(changeProductVo, parentContractInfoVo, parentSignInfoVo));
        roomList = createRoomList(productList,parentContractInfoVo);
        previewBillsParamsVo.setRoomList(roomList);
    }

    /**
     * 获取变更的产品列表(排除已退产品)
     *
     * @return 变更的产品列表
     */
    private List<BbsChangeResultProductVo> getChangeProductListExcludeReturnedProduct() {
        List<BbsChangeResultProductVo> resultList = new ArrayList<>();
        List<BbsChangeResultProductVo> changeProductList = getChangeProductList();
        Set<String> productNoSet = getReturnedProductSet();
        // 租金变更的产品列表中排除缩租的已退产品
        for (BbsChangeResultProductVo changeProductVo : changeProductList) {
            if (!productNoSet.contains(changeProductVo.getProductNo())) {
                resultList.add(changeProductVo);
            }
        }
        return resultList;
    }

    /**
     * 获取变更的产品列表
     *
     * @return 变更的产品列表
     */
    private List<BbsChangeResultProductVo> getChangeProductList() {
        //变更房源集合
        List<BbsChangeResultProductVo> changeProductList = new ArrayList<>();
        //获取租金变更房源
        List<BbsChangeResultProductVo> rentVoList = new ArrayList<>();
        if (Objects.isNull(contractChangeVo.getProductVoList())) {
            rentVoList = businessServiceConfiguration
                    .getChangeResultProductService()
                    .selectByCcId(contractChangeVo.getCcId());
        } else {
            rentVoList = contractChangeVo.getProductVoList();
        }

        //把租金变更房源添加到变更房源列表
        rentVoList.forEach(item->{
            changeProductList.add(item);
        });

        //获取计租面积变更房源
        List<BbsChangeCalculatedProductVo> calculatedToRentVoList = new ArrayList<>();
        if (Objects.isNull(contractChangeVo.getCalculatedProductVoList())) {
            calculatedToRentVoList = businessServiceConfiguration
                    .getContractChangeV2Service()
                    .selectByCcId(contractChangeVo.getCcId());

        } else {
            calculatedToRentVoList = contractChangeVo.getCalculatedProductVoList();
        }

        //把计租面积变更房源添加到变更房源列表
        calculatedToRentVoList.forEach(item->{
            BbsChangeResultProductVo rentVo = new BbsChangeResultProductVo();
            BeanUtils.copyProperties(item,rentVo);
            changeProductList.add(rentVo);
        });

        //获取已选的拆分类型的产品列表
        List<BbsChangeShopInfoVo> shopToRentVoList = new ArrayList<>();
        if (Objects.isNull(contractChangeVo.getShopInfoVoList())) {
            shopToRentVoList = businessServiceConfiguration
                    .getChangeShopInfoService()
                    .selectSplitProductByCcIdAndType(contractChangeVo.getCcId(), ReductionProductTypeEnum.CHOOSE.getCode());
        } else {
            List<BbsChangeShopInfoVo> resultList = new ArrayList<>();
            List<BbsChangeShopInfoVo> shopInfoList = contractChangeVo.getShopInfoVoList();
            for (BbsChangeShopInfoVo shopInfoVo : shopInfoList) {
                if (WhetherEnum.YES.getCode().equals(shopInfoVo.getIsSplit()) &&
                        ReductionProductTypeEnum.CHOOSE.getCode().equals(shopInfoVo.getType())) {
                    resultList.add(shopInfoVo);
                }
            }
            shopToRentVoList = resultList;
        }

        //把拆分房源添加到变更房源列表
        shopToRentVoList.forEach(item->{
            BbsChangeResultProductVo rentVo = new BbsChangeResultProductVo();
            BeanUtils.copyProperties(item,rentVo);
            changeProductList.add(rentVo);
        });
        return changeProductList;
    }


    /**
     * 获取已退产品编号set集
     *
     * @return 已退产品编号set集
     */
    private Set<String> getReturnedProductSet() {
        if (Objects.isNull(contractChangeVo.getShopInfoVoList())) {
            return businessServiceConfiguration
                    .getChangeShopInfoService()
                    .selectReturnedProductNoByCcId(contractChangeVo.getCcId());
        } else {
            Set<String> productNoSet = new HashSet<>();
            List<BbsChangeShopInfoVo> shopInfoList = contractChangeVo.getShopInfoVoList();
            for (BbsChangeShopInfoVo shopInfoVo : shopInfoList) {
                if (WhetherEnum.YES.getCode().equals(shopInfoVo.getIsSplit())) {
                    productNoSet.add(shopInfoVo.getProductNoOld());
                } else {
                    productNoSet.add(shopInfoVo.getProductNo());
                }
            }
            return productNoSet;
        }
    }

    /**
     * 生成试算的产品信息
     *
     * @param changeProductList      房源产品集合
     * @return 试算的产品信息
     */
    private List<PreviewBillsParamsRoomVo> createRoomList(List<BbsChangeResultProductVo> changeProductList,BbctContractManagementVo parentContractInfoVo) {
        List<PreviewBillsParamsRoomVo> roomList = new ArrayList<>();
        int index = 0;
        for(BbsChangeResultProductVo productVo : changeProductList){
            index++;
            PreviewBillsParamsRoomVo roomVo = new PreviewBillsParamsRoomVo();
            roomVo.setHouseId(productVo.getProductNo());
            roomVo.setHouseName(productVo.getProductName());
            List<PreviewBillParamsRoomChargeSubjectVo> chargeSubjectList = new ArrayList<>();
            // 保证金计费规则
            chargeSubjectList.add(createCashPledgeChargeSubject(productVo,parentContractInfoVo,index));
            roomVo.setChargeSubjectList(chargeSubjectList);
            roomList.add(roomVo);
        }
        return roomList;
    }

    /**
     * 生成保证金计费规则
     *
     * @param changeProductVo      变更后的产品信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @return 保证金计费规则
     */
    private PreviewBillParamsRoomChargeSubjectVo createCashPledgeChargeSubject(BbsChangeResultProductVo changeProductVo,
                                                                               BbctContractManagementVo parentContractInfoVo,int currentIndex) {
        PreviewBillParamsRoomChargeSubjectVo cashPledgeChargeSubjectVo = new PreviewBillParamsRoomChargeSubjectVo();
        cashPledgeChargeSubjectVo.setChargeSubjectNo("02");
        cashPledgeChargeSubjectVo.setCyclicOrSingle("02");
        cashPledgeChargeSubjectVo.setChargeSubjectPeriod(Integer.valueOf(parentContractInfoVo.getPaymentCycleCode()));
//        cashPledgeChargeSubjectVo.setAmountType("0" + changeProductVo.getRentStandardUnit());
        if ("9".equals(contractChangeVo.getCashPledgeCode())) {
//            cashPledgeChargeSubjectVo.setDepositProportion(new BigDecimal(1));    一口价不传比例
            BigDecimal cashAmount = new BigDecimal(contractChangeVo.getCashPledgeValue());
//            cashPledgeChargeSubjectVo.setChargeSubjectAmount(cashAmount);
            cashPledgeChargeSubjectVo.setChargeSubjectAmount(getSplitProductCashPledge(cashAmount,currentIndex));
//            productList.size();
        } else {
            cashPledgeChargeSubjectVo.setDepositProportion(new BigDecimal(contractChangeVo.getCashPledgeCode()));
            cashPledgeChargeSubjectVo.setParamList((JSONObject) JSON.toJSON(new BbctChargeRuleSubParamsBigVo()));
            BbctChargeRuleSubParamsBigVo chargeRuleSubParamsVo = new BbctChargeRuleSubParamsBigVo();
            chargeRuleSubParamsVo.setPARAMPRICE(String.valueOf(changeProductVo.getRentStandard()));
            chargeRuleSubParamsVo.setPARAMAREA(changeProductVo.getHouseStructArea());
            cashPledgeChargeSubjectVo.setParamValueList((JSONObject) JSON.toJSON(chargeRuleSubParamsVo));
        }
        cashPledgeChargeSubjectVo.setTaxRate(BigDecimal.valueOf(0.00));
        return cashPledgeChargeSubjectVo;
    }

    /**
     * 获取拆分产品的保证金金额
     *
     * @param cashAmount      变更后的一口价保障金
     * @return 拆分产品的保证金金额
     */
    private BigDecimal getSplitProductCashPledge(BigDecimal cashAmount,int currentIndex) {
        int splitSize = productList.size();
        BigDecimal parentProductCashPledge = cashAmount;
        if (splitSize < 2 || 0 == parentProductCashPledge.intValue()) {
            return parentProductCashPledge;
        }
        double splitProductCashPledge = parentProductCashPledge.doubleValue() / splitSize;
        if (0 == parentProductCashPledge.doubleValue() % splitSize) {
            return BigDecimal.valueOf(splitProductCashPledge);
        } else {
            if (currentIndex < splitSize) {
                //向下取整
                return BigDecimal.valueOf(Math.floor(splitProductCashPledge));
            } else {
                //向上取整
                return BigDecimal.valueOf(Math.ceil(splitProductCashPledge));
            }
        }
    }

    /**
     * 赋值预览信息
     *
     * @param previewInfoParamsVo 预览信息参数 vo实体
     */
    @Override
    public void setPreviewInfo(BbctPreviewInfoParamsVo previewInfoParamsVo) {
        return;
    }

    /**
     * 根据变更信息赋值签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     */
    @Override
    public void setSignInfoByChangeInfo(SigningSaveVo signingSaveVo) {
        signingSaveVo.setCashPledgeCode(contractChangeVo.getCashPledgeCode());
        if ("9".equals(contractChangeVo.getCashPledgeCode())) {
            signingSaveVo.setCashPledgeValue(String.valueOf(contractChangeVo.getCashPledgeValue()));
        }
    }
}
