package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsResultRelationEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 人-产品：关系表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by liwenqi<PERSON> for init
 */
public interface IBbsResultRelationService extends IMcpBaseService<BbsResultRelationEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by li<PERSON><PERSON><PERSON> for init
     */
    String insertRecord(BbsResultRelationVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    List<String> insertBatchRecord(List<BbsResultRelationVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param rrId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void removeByIdRecord(String rrId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param rrIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void removeByIdsRecord(List<String> rrIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的人-产品：关系表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void updateByIdRecord(BbsResultRelationVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的人-产品：关系表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void updateBatchByIdRecord(List<BbsResultRelationVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的人-产品：关系表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void saveByIdRecord(BbsResultRelationVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的人-产品：关系表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    void saveBatchByIdRecord(List<BbsResultRelationVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param rrId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    BbsResultRelationVo selectByIdRecord(String rrId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    PageResult<List<BbsResultRelationPageResultVo>> selectByPageRecord(BbsResultRelationPageVo vo);

    /**
     * 根据签约id查询
     *
     * @param signId 签约id
     * @return 人房关系信息 vo实体
     */
    BbsResultRelationVo selectBySignId(String signId);
}
