package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.feign.vo.BbcSignContractVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>

 */
@Service
public interface IBbsRenewalRewriteService {
    /**
     * 重写合同主要信息
     * @param vo
     * @param bbctContractManagementVo
     */
    void rewriteInfo(BbsRenewalInfoVo vo,BbctContractManagementVo bbctContractManagementVo);

    /**
     * 获取老合同信息
     * @param contractCode
     * @return
     */
    BbctContractManagementVo selectContractByIdNo(String contractCode);

    /**
     * 付款银行
     * @param vo
     * @param bbctContractManagementVo
     */
    void bankInfo(BbsResultCustomerVo vo,BbctContractManagementVo bbctContractManagementVo);

    /**
     * 从合同处理房源信息
     * @param result
     * @param bbctContractManagementVo
     */
    void productInfo(List<BbsResultProductVo> result,BbctContractManagementVo bbctContractManagementVo);

    /**
     * 处理递增
     * @param contractManagementVo
     * @param vo
     * @param signId
     */
    void incrementVo(BbctContractManagementVo contractManagementVo,BbsRenewalInfoVo vo,String signId);

    /**
     * 免租免租期子表处理
     * @param contractManagementVo
     * @param vo
     * @param signId
     */
    void freeChildVo(BbctContractManagementVo contractManagementVo,BbsRenewalInfoVo vo,String signId);

}
