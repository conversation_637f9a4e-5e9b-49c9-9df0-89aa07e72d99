package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyApproveInfoEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyInfoEntity;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveInfoPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyInfoPageVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;

/**
 * 续签申请信息接口
 *
 * @author: hechengyao
 * @createDate: 2023-09-21
 * @Version 1.0
 **/
public interface IBbsRenewalApplyApproveInfoService extends IMcpBaseService<BbsRenewalApplyApproveInfoEntity> {

    /**
     * 续签申请审核-增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    String insertRecord(RenewalApplyApproveInfoVo vo);

    /**
     * 续签申请审核-改（改变状态）
     *
     * @param vo 查看谁的状态
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    BbsRenewalApplyApproveInfoEntity updateRenewalApplyApproveInfo(RenewalApplyApproveInfoVo vo);

    /**
     * 续签申请审核-审核
     * @param vo 需要保存的续签申请审核结果和信息
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    AppReply applyApprove(RenewalApplyApproveInfoVo vo);

    /**
     * 续签申请审核-查（查看详细信息）
     *
     * @param parentId 申请信息id
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    RenewalApplyApproveInfoVo selectByIdRecord(String parentId);

    /**
     * 续签申请审核-分页
     *
     * @param vo 查看谁的状态
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    PageResult<List<RenewalApplyApproveInfoPageResultVo>> selectByPageRecord(RenewalApplyInfoPageVo vo);

    /**
     * 续签申请审核-提交申请审核，接收app端信息，保存信息的接口
     *
     * @param renewalApplyInfoId 申请信息id
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    void addApplyApproveReviewInfo(String renewalApplyInfoId);


}