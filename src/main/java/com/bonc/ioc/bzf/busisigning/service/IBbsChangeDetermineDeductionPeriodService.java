package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeDetermineDeductionPeriodEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 确定的抵扣账期 服务类
 *
 * <AUTHOR>
 * @date 2024-10-30
 * @change 2024-10-30 by tbh for init
 */
public interface IBbsChangeDetermineDeductionPeriodService extends IMcpBaseService<BbsChangeDetermineDeductionPeriodEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    String insertRecord(BbsChangeDetermineDeductionPeriodVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    List<String> insertBatchRecord(List<BbsChangeDetermineDeductionPeriodVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param periodId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    void removeByIdRecord(String periodId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param periodIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    void removeByIdsRecord(List<String> periodIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的确定的抵扣账期
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    void updateByIdRecord(BbsChangeDetermineDeductionPeriodVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的确定的抵扣账期
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    void updateBatchByIdRecord(List<BbsChangeDetermineDeductionPeriodVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的确定的抵扣账期
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    void saveByIdRecord(BbsChangeDetermineDeductionPeriodVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的确定的抵扣账期
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    void saveBatchByIdRecord(List<BbsChangeDetermineDeductionPeriodVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param periodId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    BbsChangeDetermineDeductionPeriodVo selectByIdRecord(String periodId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    PageResult<List<BbsChangeDetermineDeductionPeriodPageResultVo>> selectByPageRecord(BbsChangeDetermineDeductionPeriodPageVo vo);
}
