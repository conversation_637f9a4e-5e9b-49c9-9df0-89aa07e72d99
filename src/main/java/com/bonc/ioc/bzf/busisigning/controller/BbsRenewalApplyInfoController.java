package com.bonc.ioc.bzf.busisigning.controller;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyInfoEntity;
import com.bonc.ioc.bzf.busisigning.utils.UserUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.bonc.ioc.common.validator.inf.*;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.RestController;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalApplyInfoService;
import io.swagger.annotations.*;

import java.util.List;
import java.util.Map;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 续签申请信息表 前端控制器
 *
 * <AUTHOR>
 * @date 2023-09-21
 * @change 2023-09-21 by King-Y for init
 */
@RestController
@RequestMapping("/bbsRenewalApplyInfoEntity")
@Api(tags = "续签申请")
@Validated
public class BbsRenewalApplyInfoController extends McpBaseController {
    @Resource
    private IBbsRenewalApplyInfoService baseService;

    /**
     * insertRecord 获取租户信息
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @GetMapping(value = "/getUserMsg", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "King-Y")
    @ApiOperation(value = "获取租户信息", notes = "获取租户信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Map<String, Object>> getUserMsg(@ApiParam(value = "合同编号" ,required = true) @RequestParam(required = true) String contractNo){
        AppReply<Map<String, Object>> appReply = new AppReply<Map<String, Object>>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.getUserMsg(contractNo));
        return appReply;
    }

    /**
     * insertRecord 新增
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "King-Y")
    @ApiOperation(value = "新增", notes = "新增全表数据")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "续签申请信息表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsRenewalApplyInfoVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
     }

    /**
     * selectByIdRecord 根据合同编号查询
     * @param contractNo 合同编号
     * @return  com.bonc.ioc.common.util.AppReply 查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @GetMapping(value = "/selectByContractNo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "King-Y")
    @ApiOperation(value = "续租信息查看(通过合同编号)", notes = "(通过合同编号)")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsRenewalApplyInfoEntity> selectByContractNo(@ApiParam(value = "合同编号" ,required = false) @RequestParam(required = false) String contractNo){
        AppReply<BbsRenewalApplyInfoEntity> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByContractNo(contractNo));
        return appReply;
    }

    /**
     * selectByIdRecord 取消申请
     * @param renewalApplyInfoId 申请信息id
     * @return  com.bonc.ioc.common.util.AppReply 返回的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @GetMapping(value = "/cancelApplyById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "King-Y")
    @ApiOperation(value = "取消申请", notes = "取消申请")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> cancelApplyById(@ApiParam(value = "申请信息id" ,required = false) @RequestParam(required = false) String renewalApplyInfoId){
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.cancelApplyById(renewalApplyInfoId);
        appReply.setData("操作成功");
        return appReply;
    }

    /**
     * selectByIdRecord 获取续租申请书文件id
     * @return  com.bonc.ioc.common.util.AppReply 返回的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @GetMapping(value = "/getApplyFileId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "King-Y")
    @ApiOperation(value = "获取续租申请书文件id", notes = "获取续租申请书文件id")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> cancelApplyById(){
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.getApplyFileId());
        return appReply;
    }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要新增的记录 列表
     * @return  com.bonc.ioc.common.util.AppReply<List<String>> 返回新增后的主键 列表
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @PostMapping(value = "/batch/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "King-Y")
    @ApiOperation(value = "批量新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键集合")})
    public AppReply<List<String>> insertBatchRecord(@ApiParam(value = "续签申请信息表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbsRenewalApplyInfoVo> voList){
        AppReply<List<String>> appReply = new AppReply<List<String>>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertBatchRecord(voList));
        return appReply;
     }

    /**
     * removeByIdRecord 根据主键删除
     * @param renewalApplyInfoId 需要删除的申请信息id
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @PostMapping(value = "/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "King-Y")
    @ApiOperation(value = "根据主键删除", notes = "根据主键删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdRecord(@ApiParam(value = "需要删除的申请信息id" ,required = false) @RequestBody String renewalApplyInfoId){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdRecord(renewalApplyInfoId);
        return appReply;
     }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param renewalApplyInfoIdList 需要删除的申请信息id集合
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @PostMapping(value = "/batch/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "King-Y")
    @ApiOperation(value = "根据主键删除-批量", notes = "根据主键批量删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdsRecord(@ApiParam(value = "需要删除的申请信息id" ,required = false) @RequestBody List<String> renewalApplyInfoIdList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdsRecord(renewalApplyInfoIdList);
        return appReply;
     }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的续签申请信息表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @PostMapping(value = "/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "King-Y")
    @ApiOperation(value = "根据主键更新", notes = "根据主键更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateByIdRecord(@ApiParam(value = "需要更新的续签申请信息表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbsRenewalApplyInfoVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateByIdRecord(vo);
        return appReply;
     }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的续签申请信息表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @PostMapping(value = "/batch/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "King-Y")
    @ApiOperation(value = "根据主键更新-批量", notes = "根据主键批量更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateBatchByIdRecord(@ApiParam(value = "需要更新的续签申请信息表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) List<BbsRenewalApplyInfoVo> voList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateBatchByIdRecord(voList);
        return appReply;
     }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的续签申请信息表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @PostMapping(value = "/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "King-Y")
    @ApiOperation(value = "根据主键更新或新增", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveByIdRecord(@ApiParam(value = "需要更新或新增的续签申请信息表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbsRenewalApplyInfoVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.saveByIdRecord(vo);
        return appReply;
     }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的续签申请信息表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @PostMapping(value = "/batch/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "King-Y")
    @ApiOperation(value = "根据主键更新或新增-批量", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveBatchByIdRecord(@ApiParam(value = "需要更新或新增的续签申请信息表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbsRenewalApplyInfoVo> voList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.saveBatchByIdRecord(voList);
        return appReply;
     }

    /**
     * selectByIdRecord 根据主键查询
     * @param renewalApplyInfoId 需要查询的申请信息id
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "King-Y")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsRenewalApplyInfoVo> selectByIdRecord(@ApiParam(value = "需要查询的申请信息id" ,required = false) @RequestParam(required = false) String renewalApplyInfoId){
        AppReply<BbsRenewalApplyInfoVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByIdRecord(renewalApplyInfoId));
        return appReply;
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-21
     * @change
     * 2023-09-21 by King-Y for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "King-Y")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbsRenewalApplyInfoPageResultVo>>> selectByPageRecord(BbsRenewalApplyInfoPageVo vo){
        AppReply<PageResult<List<BbsRenewalApplyInfoPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
     }

}

