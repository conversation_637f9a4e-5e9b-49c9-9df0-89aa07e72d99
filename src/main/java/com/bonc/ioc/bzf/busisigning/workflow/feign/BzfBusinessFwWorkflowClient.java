package com.bonc.ioc.bzf.busisigning.workflow.feign;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.workflow.vo.*;

import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import com.bonc.ioc.common.util.AppReply;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "bzf-business-fw-workflow",
        configuration = {FeignExceptionConfiguration.class})
public interface BzfBusinessFwWorkflowClient {


//    @PostMapping(value = "/bzf-business-fw-workflow/bbfwRequestBusinessMappingEntity/removeByRequestId")
//    @FeignExceptionCheck
//    AppReply removeByRequestId(@RequestParam("requestId") String requestId);

    /**
     * 根据requestId获取BusinessId
     * @param requestId
     * @return
     */
    @GetMapping(value = "/bzf-business-fw-workflow/bbfwRequestBusinessMappingEntity/selectById")
    @FeignExceptionCheck
    AppReply<BbfwRequestBusinessMappingVo> selectById(@RequestParam("requestId") String requestId);


    /**
     * 业务Id业务分类查询
     * @param vo
     * @return
     */
    @GetMapping(value = "/bzf-business-fw-workflow/bbfwRequestBusinessMappingEntity/selectByBusinessId")
    @FeignExceptionCheck
    AppReply<BbfwRequestBusinessMappingVo> businessId2WorkFlow(@SpringQueryMap BbfwRequestBusinessMappingVo vo);


    /**
     * 创建流程
     * @param requestV2Vo
     * @return
     */
    @PostMapping(value = "/bzf-business-fw-workflow/fwWorkFlow/v2/doCreateRequest")
    @FeignExceptionCheck
    AppReply<OaWorkFlowResultVo> doCreateRequest(@RequestBody OaCreateWorkFlowRequestV2Vo requestV2Vo);

    /**
     * 删除流程
     * @param param
     * @return
     */
    @PostMapping(value = "/bzf-business-fw-workflow/fwWorkFlow/deleteRequest")
    @FeignExceptionCheck
    AppReply deleteRequest(@RequestBody OaDeleteRequestVo param);


    /**
     * 同意
     * @param param
     * @return
     */
    @PostMapping(value = "/bzf-business-fw-workflow/fwWorkFlow/v2/submitRequest")
    @FeignExceptionCheck
    AppReply submitRequest(@RequestBody BaseFlowVo param);

    /**
     * 强制终止
     * @param param
     * @return
     */
    @PostMapping(value = "/bzf-business-fw-workflow/fwWorkFlow/v2/doForceOver")
    @FeignExceptionCheck
    AppReply doForceOver(@RequestBody BaseFlowVo param);

    @GetMapping(value ="/bzf-business-fw-workflow/bbfwBusinessTypeEntity/selectById")
    @FeignExceptionCheck
    AppReply<JSONObject> selectWorkFlowId(@RequestParam("businessTypeCode") String businessTypeCode);
}
