package com.bonc.ioc.bzf.busisigning.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsChangeResultProductEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsChangeResultProductMapper;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.service.IBbsChangeResultProductService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更租金变更产品表 服务类实现
 *
 * <AUTHOR>
 * @date 2024-09-09
 * @change 2024-09-09 by tbh for init
 */
@Slf4j
@Service
public class BbsChangeResultProductServiceImpl extends McpBaseServiceImpl<BbsChangeResultProductEntity> implements IBbsChangeResultProductService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsChangeResultProductMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsChangeResultProductService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsChangeResultProductVo vo) {
        if(vo == null) {
            return null;
        }

        BbsChangeResultProductEntity entity = new BbsChangeResultProductEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setPdctId(null);
        if(!baseService.insert(entity)) {
            log.error("合同变更租金变更产品表新增失败:" + entity.toString());
            throw new McpException("合同变更租金变更产品表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getPdctId(),1)) {
                log.error("合同变更租金变更产品表新增后保存历史失败:" + entity.toString());
                throw new McpException("合同变更租金变更产品表新增后保存历史失败");
            }

            log.debug("合同变更租金变更产品表新增成功:"+entity.getPdctId());
            return entity.getPdctId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsChangeResultProductVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsChangeResultProductEntity> entityList = new ArrayList<>();
        for (BbsChangeResultProductVo item:voList) {
            BbsChangeResultProductEntity entity = new BbsChangeResultProductEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsChangeResultProductEntity item:entityList){
            item.setPdctId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("合同变更租金变更产品表新增失败");
            throw new McpException("合同变更租金变更产品表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsChangeResultProductEntity::getPdctId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("合同变更租金变更产品表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("合同变更租金变更产品表批量新增后保存历史失败");
            }

            log.debug("合同变更租金变更产品表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param pdctId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String pdctId) {
        if(!StringUtils.isEmpty(pdctId)) {
            if(!baseService.saveOperationHisById(pdctId,3)) {
                log.error("合同变更租金变更产品表删除后保存历史失败:" + pdctId);
                throw new McpException("合同变更租金变更产品表删除后保存历史失败");
            }

            if(!baseService.removeById(pdctId)) {
                log.error("合同变更租金变更产品表删除失败");
                throw new McpException("合同变更租金变更产品表删除失败"+pdctId);
            }
        } else {
            throw new McpException("合同变更租金变更产品表删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param pdctIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> pdctIdList) {
        if(!CollectionUtils.isEmpty(pdctIdList)) {
            int oldSize = pdctIdList.size();
            pdctIdList = pdctIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(pdctIdList) || oldSize != pdctIdList.size()) {
                throw new McpException("合同变更租金变更产品表批量删除失败 存在主键id为空的记录"+StringUtils.join(pdctIdList));
            }

            if(!baseService.saveOperationHisByIds(pdctIdList,3)) {
                log.error("合同变更租金变更产品表批量删除后保存历史失败:" + StringUtils.join(pdctIdList));
                throw new McpException("合同变更租金变更产品表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(pdctIdList)) {
                log.error("合同变更租金变更产品表批量删除失败");
                throw new McpException("合同变更租金变更产品表批量删除失败"+StringUtils.join(pdctIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的合同变更租金变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsChangeResultProductVo vo) {
        if(vo != null) {
            BbsChangeResultProductEntity entity = new BbsChangeResultProductEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getPdctId())) {
                throw new McpException("合同变更租金变更产品表更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("合同变更租金变更产品表更新失败");
                throw new McpException("合同变更租金变更产品表更新失败"+entity.getPdctId());
            } else {
                if(!baseService.saveOperationHisById(entity.getPdctId(),2)) {
                    log.error("合同变更租金变更产品表更新后保存历史失败:" + entity.getPdctId());
                    throw new McpException("合同变更租金变更产品表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("合同变更租金变更产品表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的合同变更租金变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsChangeResultProductVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsChangeResultProductEntity> entityList = new ArrayList<>();

            for (BbsChangeResultProductVo item:voList){
                BbsChangeResultProductEntity entity = new BbsChangeResultProductEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getPdctId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("合同变更租金变更产品表批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("合同变更租金变更产品表批量更新失败");
                throw new McpException("合同变更租金变更产品表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getPdctId())).map(BbsChangeResultProductEntity::getPdctId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("合同变更租金变更产品表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("合同变更租金变更产品表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的合同变更租金变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsChangeResultProductVo vo) {
        if(vo != null) {
            BbsChangeResultProductEntity entity = new BbsChangeResultProductEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("合同变更租金变更产品表保存失败");
                throw new McpException("合同变更租金变更产品表保存失败"+entity.getPdctId());
            } else {
                if(!baseService.saveOperationHisById(entity.getPdctId(),4)) {
                    log.error("合同变更租金变更产品表保存后保存历史失败:" + entity.getPdctId());
                    throw new McpException("合同变更租金变更产品表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("合同变更租金变更产品表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的合同变更租金变更产品表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsChangeResultProductVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsChangeResultProductEntity> entityList = new ArrayList<>();

            for (BbsChangeResultProductVo item:voList){
                BbsChangeResultProductEntity entity = new BbsChangeResultProductEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("合同变更租金变更产品表批量保存失败");
                throw new McpException("合同变更租金变更产品表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getPdctId())).map(BbsChangeResultProductEntity::getPdctId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("合同变更租金变更产品表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("合同变更租金变更产品表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param pdctId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsChangeResultProductVo selectByIdRecord(String pdctId) {
        BbsChangeResultProductVo vo = new BbsChangeResultProductVo();

        if(!StringUtils.isEmpty(pdctId)) {
            BbsChangeResultProductEntity entity = baseService.selectById(pdctId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsChangeResultProductPageResultVo>> selectByPageRecord(BbsChangeResultProductPageVo vo) {
        List<BbsChangeResultProductPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据合同变更id查询变更产品信息列表
     *
     * @param ccId 合同变更id
     * @return 变更产品信息列表
     */
    @Override
    public List<BbsChangeResultProductVo> selectByCcId(String ccId) {
        List<BbsChangeResultProductEntity> entityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BbsChangeResultProductEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsChangeResultProductEntity::getCcId, ccId)
                .list();
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        } else {
            List<BbsChangeResultProductVo> voList = new ArrayList<>();
            for (BbsChangeResultProductEntity entity : entityList) {
                BbsChangeResultProductVo vo = new BbsChangeResultProductVo();
                BeanUtils.copyProperties(entity, vo);
                voList.add(vo);
            }
            return voList;
        }
    }
}
