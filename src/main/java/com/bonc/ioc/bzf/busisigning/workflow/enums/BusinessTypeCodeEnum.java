package com.bonc.ioc.bzf.busisigning.workflow.enums;

/**
 * 商业签约业务分类
 *
 * <AUTHOR>
 * @since 2023/5/6
 */
public enum BusinessTypeCodeEnum {


    CODE_003003("003003", "商业新签合同审签"),


    CODE_003004("003004", "商业新签合同审核"),

    CODE_003005("003005", "商业续签合同审签"),


    CODE_003006("003006", "商业续签合同审核"),

    CODE_003007("003007", "商业退款申请审核"),

    CODE_003008("003008", "商业基本信息变更审核"),

    CODE_003009("003009", "商业其他信息变更审核"),

    CODE_003010("003010", "合同变更上传结果审核"),

    CODE_003015("003015", "商业应收调整审核");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    BusinessTypeCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    BusinessTypeCodeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        BusinessTypeCodeEnum[] enums = values();
        for (BusinessTypeCodeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
