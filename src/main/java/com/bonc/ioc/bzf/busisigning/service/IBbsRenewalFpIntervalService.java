package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalFpIntervalEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 签约-免租期-区间（当租金/物业费免租期分类为1时有效） 服务类
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
public interface IBbsRenewalFpIntervalService extends IMcpBaseService<BbsRenewalFpIntervalEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    String insertRecord(BbsRenewalFpIntervalVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    List<String> insertBatchRecord(List<BbsRenewalFpIntervalVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param sfiId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void removeByIdRecord(String sfiId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param sfiIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void removeByIdsRecord(List<String> sfiIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void updateByIdRecord(BbsRenewalFpIntervalVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void updateBatchByIdRecord(List<BbsRenewalFpIntervalVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void saveByIdRecord(BbsRenewalFpIntervalVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的签约-免租期-区间（当租金/物业费免租期分类为1时有效）
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void saveBatchByIdRecord(List<BbsRenewalFpIntervalVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param sfiId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    BbsRenewalFpIntervalVo selectByIdRecord(String sfiId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    PageResult<List<BbsRenewalFpIntervalPageResultVo>> selectByPageRecord(BbsRenewalFpIntervalPageVo vo);
}
