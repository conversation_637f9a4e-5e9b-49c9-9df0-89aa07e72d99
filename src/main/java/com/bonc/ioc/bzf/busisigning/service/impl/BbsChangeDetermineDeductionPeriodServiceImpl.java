package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeDetermineDeductionPeriodEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsChangeDetermineDeductionPeriodMapper;
import com.bonc.ioc.bzf.busisigning.service.IBbsChangeDetermineDeductionPeriodService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 确定的抵扣账期 服务类实现
 *
 * <AUTHOR>
 * @date 2024-10-30
 * @change 2024-10-30 by tbh for init
 */
@Slf4j
@Service
public class BbsChangeDetermineDeductionPeriodServiceImpl extends McpBaseServiceImpl<BbsChangeDetermineDeductionPeriodEntity> implements IBbsChangeDetermineDeductionPeriodService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsChangeDetermineDeductionPeriodMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsChangeDetermineDeductionPeriodService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsChangeDetermineDeductionPeriodVo vo) {
        if(vo == null) {
            return null;
        }

        BbsChangeDetermineDeductionPeriodEntity entity = new BbsChangeDetermineDeductionPeriodEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setPeriodId(null);
        if(!baseService.insert(entity)) {
            log.error("确定的抵扣账期新增失败:" + entity.toString());
            throw new McpException("确定的抵扣账期新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getPeriodId(),1)) {
                log.error("确定的抵扣账期新增后保存历史失败:" + entity.toString());
                throw new McpException("确定的抵扣账期新增后保存历史失败");
            }

            log.debug("确定的抵扣账期新增成功:"+entity.getPeriodId());
            return entity.getPeriodId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsChangeDetermineDeductionPeriodVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsChangeDetermineDeductionPeriodEntity> entityList = new ArrayList<>();
        for (BbsChangeDetermineDeductionPeriodVo item:voList) {
            BbsChangeDetermineDeductionPeriodEntity entity = new BbsChangeDetermineDeductionPeriodEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsChangeDetermineDeductionPeriodEntity item:entityList){
            item.setPeriodId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("确定的抵扣账期新增失败");
            throw new McpException("确定的抵扣账期新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsChangeDetermineDeductionPeriodEntity::getPeriodId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("确定的抵扣账期批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("确定的抵扣账期批量新增后保存历史失败");
            }

            log.debug("确定的抵扣账期新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param periodId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String periodId) {
        if(!StringUtils.isEmpty(periodId)) {
            if(!baseService.saveOperationHisById(periodId,3)) {
                log.error("确定的抵扣账期删除后保存历史失败:" + periodId);
                throw new McpException("确定的抵扣账期删除后保存历史失败");
            }

            if(!baseService.removeById(periodId)) {
                log.error("确定的抵扣账期删除失败");
                throw new McpException("确定的抵扣账期删除失败"+periodId);
            }
        } else {
            throw new McpException("确定的抵扣账期删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param periodIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> periodIdList) {
        if(!CollectionUtils.isEmpty(periodIdList)) {
            int oldSize = periodIdList.size();
            periodIdList = periodIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(periodIdList) || oldSize != periodIdList.size()) {
                throw new McpException("确定的抵扣账期批量删除失败 存在主键id为空的记录"+StringUtils.join(periodIdList));
            }

            if(!baseService.saveOperationHisByIds(periodIdList,3)) {
                log.error("确定的抵扣账期批量删除后保存历史失败:" + StringUtils.join(periodIdList));
                throw new McpException("确定的抵扣账期批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(periodIdList)) {
                log.error("确定的抵扣账期批量删除失败");
                throw new McpException("确定的抵扣账期批量删除失败"+StringUtils.join(periodIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的确定的抵扣账期
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsChangeDetermineDeductionPeriodVo vo) {
        if(vo != null) {
            BbsChangeDetermineDeductionPeriodEntity entity = new BbsChangeDetermineDeductionPeriodEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getPeriodId())) {
                throw new McpException("确定的抵扣账期更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("确定的抵扣账期更新失败");
                throw new McpException("确定的抵扣账期更新失败"+entity.getPeriodId());
            } else {
                if(!baseService.saveOperationHisById(entity.getPeriodId(),2)) {
                    log.error("确定的抵扣账期更新后保存历史失败:" + entity.getPeriodId());
                    throw new McpException("确定的抵扣账期更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("确定的抵扣账期更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的确定的抵扣账期
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsChangeDetermineDeductionPeriodVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsChangeDetermineDeductionPeriodEntity> entityList = new ArrayList<>();

            for (BbsChangeDetermineDeductionPeriodVo item:voList){
                BbsChangeDetermineDeductionPeriodEntity entity = new BbsChangeDetermineDeductionPeriodEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getPeriodId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("确定的抵扣账期批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("确定的抵扣账期批量更新失败");
                throw new McpException("确定的抵扣账期批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getPeriodId())).map(BbsChangeDetermineDeductionPeriodEntity::getPeriodId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("确定的抵扣账期批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("确定的抵扣账期批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的确定的抵扣账期
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsChangeDetermineDeductionPeriodVo vo) {
        if(vo != null) {
            BbsChangeDetermineDeductionPeriodEntity entity = new BbsChangeDetermineDeductionPeriodEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("确定的抵扣账期保存失败");
                throw new McpException("确定的抵扣账期保存失败"+entity.getPeriodId());
            } else {
                if(!baseService.saveOperationHisById(entity.getPeriodId(),4)) {
                    log.error("确定的抵扣账期保存后保存历史失败:" + entity.getPeriodId());
                    throw new McpException("确定的抵扣账期保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("确定的抵扣账期保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的确定的抵扣账期
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsChangeDetermineDeductionPeriodVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsChangeDetermineDeductionPeriodEntity> entityList = new ArrayList<>();

            for (BbsChangeDetermineDeductionPeriodVo item:voList){
                BbsChangeDetermineDeductionPeriodEntity entity = new BbsChangeDetermineDeductionPeriodEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("确定的抵扣账期批量保存失败");
                throw new McpException("确定的抵扣账期批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getPeriodId())).map(BbsChangeDetermineDeductionPeriodEntity::getPeriodId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("确定的抵扣账期批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("确定的抵扣账期批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param periodId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsChangeDetermineDeductionPeriodVo selectByIdRecord(String periodId) {
        BbsChangeDetermineDeductionPeriodVo vo = new BbsChangeDetermineDeductionPeriodVo();

        if(!StringUtils.isEmpty(periodId)) {
            BbsChangeDetermineDeductionPeriodEntity entity = baseService.selectById(periodId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change
     * 2024-10-30 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsChangeDetermineDeductionPeriodPageResultVo>> selectByPageRecord(BbsChangeDetermineDeductionPeriodPageVo vo) {
        List<BbsChangeDetermineDeductionPeriodPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
