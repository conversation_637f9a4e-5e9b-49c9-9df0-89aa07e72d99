package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsImportRecordEntity;
import com.bonc.ioc.bzf.busisigning.vo.BbsImportRecordPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsImportRecordPageVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsImportRecordVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

/**
 * 导入记录表 服务类
 *
 * <AUTHOR>
 * @date 2024-03-07
 * @change 2024-03-07 by pyj for init
 */
public interface IBbsImportRecordService extends IMcpBaseService<BbsImportRecordEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbsImportRecordVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbsImportRecordVo> voList);

    /**
     * 批量插入
     *
     * @param voList 对象列表
     * @return 插入结果
     */
    List<String> insertObjectBatchRecord(List<Object> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param importId 需要删除的主键id
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String importId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param importIdList 需要删除的主键id
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> importIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的导入记录表
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbsImportRecordVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的导入记录表
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbsImportRecordVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的导入记录表
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbsImportRecordVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的导入记录表
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbsImportRecordVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param importId 需要查询的主键id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    BbsImportRecordVo selectByIdRecord(String importId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbsImportRecordPageResultVo>> selectByPageRecord(BbsImportRecordPageVo vo);
}
