package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsApproveInfoEntity;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;

/**
 * 审批表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by 刘鹏伟 for init
 */
public interface IBbsApproveInfoService extends IMcpBaseService<BbsApproveInfoEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by 刘鹏伟 for init
     */
    String insertRecord(BbsApproveInfoVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by 刘鹏伟 for init
     */
    List<String> insertBatchRecord(List<BbsApproveInfoVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param approveId 需要删除的审批id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by 刘鹏伟 for init
     */
    void removeByIdRecord(String approveId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param approveIdList 需要删除的审批id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by 刘鹏伟 for init
     */
    void removeByIdsRecord(List<String> approveIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by 刘鹏伟 for init
     */
    void updateByIdRecord(BbsApproveInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by 刘鹏伟 for init
     */
    void updateBatchByIdRecord(List<BbsApproveInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by 刘鹏伟 for init
     */
    void saveByIdRecord(BbsApproveInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的审批表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by 刘鹏伟 for init
     */
    void saveBatchByIdRecord(List<BbsApproveInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param approveId 需要查询的审批id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by 刘鹏伟 for init
     */
    BbsApproveInfoVo selectByIdRecord(String approveId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by 刘鹏伟 for init
     */
    PageResult<List<BbsApproveInfoPageResultVo>> selectByPageRecord(BbsApproveInfoPageVo vo);

    /**
     * 添加待审签记录
     * @param signId
     */
    void addSigningReviewInfo(String signId);

    /**
     * 添加待审签记录
     * @param signId
     */
    void addContractReviewInfo(String signId);


    /**
     * 保存审核/审签结果
     *
     * @param vo
     */
    AppReply saveApproveResult(BbsApproveInfoVo vo);

    /**
     * 查询审核记录
     *
     * @param signId
     * @param approveType
     * @return
     */
    List<BbsApproveInfoVo> selectApproveRecord(String signId, String approveType);

    /**
     * 商铺地址级联查询
     *
     * @param queryVo
     * @return
     */
    List<AddressCascadeQueryResultVo> addressCascadeQuery(BbsApproveAddressCascadeQueryVo queryVo);

    AppReply saveApproveResultChange(BbsApproveInfoVo vo);
}
