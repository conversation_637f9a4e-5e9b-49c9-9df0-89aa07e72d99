package com.bonc.ioc.bzf.busisigning.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.feign.feign.BzfSystemCommercialFeignClient;
import com.bonc.ioc.bzf.busisigning.vo.AddressCascadeQueryResultVo;
import com.bonc.ioc.bzf.busisigning.vo.DropDownParamVo;
import com.bonc.ioc.common.dict.entity.McpDictEntity;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.util.AppReply;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiParam;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: liwenqiang
 * @createDate: 2023-08-28 09:11
 * @Version 1.0
 **/
@RestController
@RequestMapping("/dict")
@Api(tags = "字典")
public class DictController {

    @Resource
    private Environment env;

    @Resource
    private McpDictSession mcpDictSession;

    @Resource
    private BzfSystemCommercialFeignClient bzfSystemCommercialFeignClient;

    /**
     * 根据字典code查询code对应字典
     *
     * @param dictCode 字典code
     * @return 字典实体
     */
    @GetMapping(value = "/selectByDictCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "pyj")
    @ApiOperation(value = "根据字典code查询code对应字典", notes = "根据字典code查询code对应字典")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<McpDictEntity>> selectByDictCode(@ApiParam(value = "需要查询的字典编码", required = false) @RequestParam(required = false) String dictCode) {
        AppReply<List<McpDictEntity>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            appReply.setData(mcpDictSession.getMcpDictUtil().getPairsListByDictCode(dictCode));
        } catch (Exception e) {
            appReply.setData(new ArrayList<>());
        }
        return appReply;
    }

    /**
     * 调用意向客户中心查询业态
     *
     * @return
     */
    @GetMapping(value = "/selectBusinessFormat", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "刘鹏伟")
    @ApiOperation(value = "查询业态码表树", notes = "查询业态码表树")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<JSONArray> selectBusinessFormat() {
        AppReply<JSONArray> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        try {
            AppReply<JSONArray> businessFormat = bzfSystemCommercialFeignClient.selectBusinessFormat(!Objects.equals(env.getProperty("spring.profiles.active"), "debug"), "BUSINESS_FORMAT");
            if (businessFormat.getCode().equals(AppReply.SUCCESS_CODE)) {
                appReply.setData(businessFormat.getData());
            } else {
                new AppReply(AppReply.ERROR_CODE, "查询业态码表树失败", null);
            }
        } catch (Exception e) {
            appReply.setData(new JSONArray());
        }
        return appReply;
    }

    /**
     * 调用意向客户中心查询租户性质
     *
     * @return
     */
    @GetMapping(value = "/selectIntentionInfoType", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "魏超")
    @ApiOperation(value = "查询租户性质", notes = "查询租户性质")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<JSONObject>> selectIntentionInfoType() {
        return bzfSystemCommercialFeignClient.selectIntentionInfoType("INTENTION_INFO_TYPE");
    }

    /**
     * 签约-中招意向客户信息列表小区下拉框
     *
     * @return
     */
    @GetMapping(value = "/selectDropDownList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "魏超")
    @ApiOperation(value = "租户-商铺地址下拉框", notes = "租户-商铺地址下拉框")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<AddressCascadeQueryResultVo>> selectDropDownList(DropDownParamVo dropDownType) {
        AppReply<List<String>> appReply = bzfSystemCommercialFeignClient.selectDropDownList(dropDownType);
        if (appReply.getData() != null && appReply.getData().size() > 0) {
            List<AddressCascadeQueryResultVo> data = appReply.getData().stream().map(item -> {
                AddressCascadeQueryResultVo resultVo= new AddressCascadeQueryResultVo();
                resultVo.setCode(item);
                if (StrUtil.equals(dropDownType.getDropDownType(), "3")) {
                    resultVo.setValue(item + "号楼");
                } else if (StrUtil.equals(dropDownType.getDropDownType(), "4")) {
                    resultVo.setValue(item + "单元");
                }else {
                    resultVo.setValue(item);
                }
                return resultVo;
            }).collect(Collectors.toList());
            return AppReply.success(data);
        }
        return AppReply.success(new ArrayList<>());
    }
}
