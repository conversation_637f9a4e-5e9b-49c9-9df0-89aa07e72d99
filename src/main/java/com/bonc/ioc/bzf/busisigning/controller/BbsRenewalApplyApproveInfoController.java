package com.bonc.ioc.bzf.busisigning.controller;

import com.alibaba.fastjson.JSON;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalApplyApproveInfoEntity;
import com.bonc.ioc.bzf.busisigning.file.feign.BbNewFileFeignClient;
import com.bonc.ioc.bzf.busisigning.file.service.FileService;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.file.vo.NewFIleResultVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalApplyApproveInfoService;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveInfoPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyApproveInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.RenewalApplyInfoPageVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: hechengyao
 * @createDate: 2023-09-20
 * @Version 1.0
 **/
@RestController
@RequestMapping("/renewal/apply/approve/info/")
@Api(tags = "续签申请审核")
@Slf4j
public class BbsRenewalApplyApproveInfoController {

    @Resource
    private IBbsRenewalApplyApproveInfoService baseService;

    @Resource
    private FileService fileService;

    @Resource
    private BbNewFileFeignClient bbNewFileFeignClient;

    /**
     * 续签申请审核-增
     *
     * @param vo 需要新增的数据
     * @return com.bonc.ioc.common.util.AppReply 新增的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "hcy")
    @ApiOperation(value = "续签申请审核-新增", notes = "续签申请审核-新增")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> insertRecord(RenewalApplyApproveInfoVo vo) {
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
    }

    /**
     * 续签申请审核-修改
     *
     * @param vo 修改的对象
     * @return com.bonc.ioc.common.util.AppReply 修改的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @PostMapping(value = "/update", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "hcy")
    @ApiOperation(value = "续签申请审核-修改", notes = "续签申请审核-修改")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbsRenewalApplyApproveInfoEntity> updateRenewalApplyInfo(RenewalApplyApproveInfoVo vo) {
        AppReply<BbsRenewalApplyApproveInfoEntity> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.updateRenewalApplyApproveInfo(vo));
        return appReply;
    }

    /**
     * 续签申请审核-审核
     *
     * @param vo 需要保存的续签申请审核结果和信息
     * @return com.bonc.ioc.common.util.AppReply 修改的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @PostMapping(value = "/applyApprove", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "hcy")
    @ApiOperation(value = "续签申请审核-通过/不通过", notes = "续签申请审核-通过/不通过")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply applyApprove(@ApiParam(value = "需要保存的审批结果和信息", required = false) @RequestBody @Validated(InsertValidatorGroup.class) RenewalApplyApproveInfoVo vo) {
        log.info("调用续签申请审核-审核-接口参数==={}", JSON.toJSONString(vo));
        return baseService.applyApprove(vo);
    }

    /**
     * 续签申请审核-查（查看详细信息）
     *
     * @param parentId 申请信息id
     * @return com.bonc.ioc.common.util.AppReply 查询的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "hcy")
    @ApiOperation(value = "续签申请审核-查看详细信息", notes = "续签申请审核-查看详细信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<RenewalApplyApproveInfoVo> selectByIdRecord(@RequestParam("parentId") String parentId) {
        AppReply<RenewalApplyApproveInfoVo> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(baseService.selectByIdRecord(parentId));
        return appReply;
    }

    /**
     * 续签申请审核-分页列表
     *
     * @param vo 需要查询的条件
     * @return com.bonc.ioc.common.util.AppReply 分页查询的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @GetMapping(value = "/selectByPageRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "hcy")
    @ApiOperation(value = "续签申请审核-分页列表", notes = "续签申请审核-分页列表")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<RenewalApplyApproveInfoPageResultVo>>> selectByPageRecord(RenewalApplyInfoPageVo vo) {
        AppReply<PageResult<List<RenewalApplyApproveInfoPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        vo.setDelFlag(1);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
    }

    /**
     * 续签申请审核-根据文件id集合获得文件信息
     *
     * @param fileId 附件id(多个以逗号分割)
     * @return com.bonc.ioc.common.util.AppReply 根据文件id集合获得文件信息
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @PostMapping(value = "/getFileInfoById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "hcy")
    @ApiOperation(value = "续签申请审核-根据文件id集合获得文件信息）", notes = "续签申请审核-查根据文件id集合获得文件信息")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<FIleResultVo> selectFile(@ApiParam(value = "需要查询的文件id", required = false) @RequestParam("fileId") String fileId) {
        String[] fileIds = fileId.split(",");
        AppReply<List<NewFIleResultVo>> newResultVo = this.bbNewFileFeignClient.getFileInfoByFileIds(fileIds);
        AppReply<FIleResultVo> oldResultVo = fileService.toOldFileResultVo(newResultVo);
        return oldResultVo;
    }

    /**
     * 续签申请审核-提交申请审核，接收app端信息，保存信息的接口
     *
     * @param renewalApplyInfoId 申请信息id
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @PostMapping(value = "/addApplyApproveReviewInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "hcy")
    @ApiOperation(value = "续签申请审核-审核", notes = "提交审核")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply addApplyApproveReviewInfo(@ApiParam(value = "签约id", required = true) @RequestParam("renewalApplyInfoId") String renewalApplyInfoId) {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        baseService.addApplyApproveReviewInfo(renewalApplyInfoId);
        return appReply;
    }

    @GetMapping(value = "/test", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "hcy")
    @ApiOperation(value = "测试", notes = "测试")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> test() {
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        String aa = "asdfg";
        log.info(">>>>>> aa = {}", aa);
        appReply.setData(aa);
        return appReply;
    }

}
