package com.bonc.ioc.bzf.busisigning.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.io.Serializable;
import java.util.List;

/**
 * @author: caijun
 * @date: 2024/05/23
 * @change: 2024/05/23 by <EMAIL> for init
 */
@Data
public class OaFormDataRequestVo implements Serializable {

    /**
     * 业务数据id
     */
    @ApiModelProperty("业务数据id")
    @A
    private Long dataId;

    /**
     * 表单字段数据
     */
    @ApiModelProperty(value = "表单字段数据",required = true)
    private List<OaFormDataDetailsRequestVo> dataDetails;

    /**
     * 表单id
     */
    @ApiModelProperty("表单id")
    private Long formId;

    /**
     * 模块，默认workflow
     */
    @ApiModelProperty("模块，默认workflow")
    private String module;


}
