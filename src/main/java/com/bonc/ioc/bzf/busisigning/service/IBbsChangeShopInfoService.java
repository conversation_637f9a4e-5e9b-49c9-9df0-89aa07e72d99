package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeShopInfoEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import java.util.Set;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import org.apache.ibatis.annotations.Param;

/**
 * 缩租面积变更变更后商品信息表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-25
 * @change 2024-10-25 by tbh for init
 */
public interface IBbsChangeShopInfoService extends IMcpBaseService<BbsChangeShopInfoEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    String insertRecord(BbsChangeShopInfoVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    List<String> insertBatchRecord(List<BbsChangeShopInfoVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param shopInfoId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void removeByIdRecord(String shopInfoId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param shopInfoIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void removeByIdsRecord(List<String> shopInfoIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的缩租面积变更变更后商品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void updateByIdRecord(BbsChangeShopInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的缩租面积变更变更后商品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void updateBatchByIdRecord(List<BbsChangeShopInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的缩租面积变更变更后商品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void saveByIdRecord(BbsChangeShopInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的缩租面积变更变更后商品信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    void saveBatchByIdRecord(List<BbsChangeShopInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param shopInfoId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    BbsChangeShopInfoVo selectByIdRecord(String shopInfoId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-25
     * @change
     * 2024-10-25 by tbh for init
     */
    PageResult<List<BbsChangeShopInfoPageResultVo>> selectByPageRecord(BbsChangeShopInfoPageVo vo);

    /**
     * 根据合同变更id和类型查询
     *
     * @param ccId 合同变更id
     * @param type 类型
     * @return 变更时产品信息列表
     */
    List<BbsChangeShopInfoVo> selectByCcIdAndType(String ccId, String type);

    /**
     * 根据合同变更id和类型查询拆分的产品
     *
     * @param ccId 合同变更id
     * @param type 类型
     * @return 变更时拆分的产品信息列表
     */
    List<BbsChangeShopInfoVo> selectSplitProductByCcIdAndType(String ccId, String type);

    /**
     * 根据合同变更id查询拆分的产品
     *
     * @param ccId 合同变更id
     * @return 变更时拆分的产品信息列表
     */
    List<BbsChangeShopInfoVo> selectSplitProductByCcId(String ccId);

    /**
     * 根据合同变更id和类型查询(排除拆分的产品)
     *
     * @param ccId 合同变更id
     * @param type 类型
     * @return 变更时产品信息列表(排除拆分的产品)
     */
    List<BbsChangeShopInfoVo> selectExcludeSplitProductByCcIdAndType(String ccId, String type);

    /**
     * 根据合同变更id查询已退产品编号
     *
     * @param ccId 合同变更id
     * @return 已退产品编号set集
     */
    Set<String> selectReturnedProductNoByCcId(String ccId);

    /**
     * 根据合同变更id和上级产品编号获取拆分数量
     *
     * @param ccId            合同变更id
     * @param parentProductNo 上级产品编号
     * @return 拆分数量
     */
    int getSplitSizeByCcIdAndParentProduct(String ccId, String parentProductNo);

    List<String> selectDelProductNoByCcId(String ccId);
}
