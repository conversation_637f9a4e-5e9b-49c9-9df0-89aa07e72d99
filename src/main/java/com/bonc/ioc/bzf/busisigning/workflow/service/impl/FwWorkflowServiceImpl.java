package com.bonc.ioc.bzf.busisigning.workflow.service.impl;


import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.bonc.ioc.bzf.busisigning.dao.BbsApproveInfoMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApproveDetailInfoMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApproveInfoMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsSignInfoMapper;
import com.bonc.ioc.bzf.busisigning.entity.*;
import com.bonc.ioc.bzf.busisigning.enums.*;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbpaymentFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctApproveVo;
import com.bonc.ioc.bzf.busisigning.service.*;
import com.bonc.ioc.bzf.busisigning.utils.UserUtil;
import com.bonc.ioc.bzf.busisigning.vo.BbsApproveInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsChangeReturnBankCardVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalApproveInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo;
import com.bonc.ioc.bzf.busisigning.vo.adjust.BbpmReceivableAdjustVo;
import com.bonc.ioc.bzf.busisigning.vo.adjust.RentingOutDetailVO;
import com.bonc.ioc.bzf.busisigning.workflow.enums.NodeKeyEnum;
import com.bonc.ioc.bzf.busisigning.workflow.feign.BzfBusinessFwWorkflowClient;
import com.bonc.ioc.bzf.busisigning.workflow.service.IFwWorkflowService;
import com.bonc.ioc.bzf.busisigning.workflow.vo.*;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.utils.CurrentUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class FwWorkflowServiceImpl implements IFwWorkflowService {

    @Resource
    private BzfBusinessFwWorkflowClient bzfBusinessFwWorkflowClient;

    @Resource
    private IBbsApproveInfoService approveInfoService;

    @Resource
    private IBbsSignInfoService signInfoService;

    @Resource
    private IBbsRenewalInfoExtService renewalInfoExtService;

    @Resource
    private IBbsRenewalApproveInfoService renewalApproveInfoService;

    @Resource
    private BbsRenewalApproveDetailInfoMapper bbsRenewalApproveDetailInfoMapper;

    @Resource
    private BbctContractService contractService;

    @Resource
    private BbsSignInfoMapper signInfoMapper;

    @Resource
    private IBbsSignInfoExtService signInfoExtService;

    @Resource
    private IBbsRefundInfoService refundInfoService;

    @Resource
    private BbsRenewalApproveInfoMapper bbsRenewalApproveInfoMapper;

    @Resource
    private IBbsiContractChangeService contractChangeService;

    @Resource
    private IBbsChangeReturnBankCardService iBbsChangeReturnBankCardService;

    @Resource
    private IContractChangeResultService contractChangeResultService;

    @Resource
    private UserUtil userUtil;

    @Resource
    private BbsApproveInfoMapper bbsApproveInfoMapper;

    @Resource
    private IBbsContractChangeApproveInfoService contractChangeApproveInfoService;

    @Resource
    private BbpaymentFeignClient bbpaymentFeignClient;

    @Resource
    private IBbsAdjustService adjustService;

    /**
     * requestId 转 业务中台Id
     *
     * @param requestId
     * @return
     */
    @Override
    public String requestId2BusinessId(String requestId) {
        AppReply<BbfwRequestBusinessMappingVo> appReply =  bzfBusinessFwWorkflowClient.selectById(requestId);
        if(AppReply.SUCCESS_CODE.equals(appReply.getCode())
                && appReply.getData()!=null
                && StringUtils.isNotBlank(appReply.getData().getBusinessId())
        ){
            return appReply.getData().getBusinessId();
        }
        return null;
    }

    /**
     * 业务中台Id 获取 工作流参数
     *
     * @param businessId
     * @param businessTypeCode
     * @return
     */
    @Override
    public BbfwRequestBusinessMappingVo businessId2WorkFlow(String businessId, String businessTypeCode) {
        BbfwRequestBusinessMappingVo param = new BbfwRequestBusinessMappingVo();
        param.setBusinessId(businessId);
        param.setBusinessTypeCode(businessTypeCode);
        AppReply<BbfwRequestBusinessMappingVo> appReply =  this.bzfBusinessFwWorkflowClient.businessId2WorkFlow(param);
        if(AppReply.SUCCESS_CODE.equals(appReply.getCode())){
            return appReply.getData();
        }
        return null;
    }


    /**
     * 按照 requestId 删除数据
     *
     * @param requestId
     */
    @Override
    public void removeByRequestId(String requestId) {
        OaDeleteRequestVo requestVo = new OaDeleteRequestVo();
        requestVo.setRequestId(requestId);
        bzfBusinessFwWorkflowClient.deleteRequest(requestVo);
    }

    /**
     * 创建流程
     *
     * @param requestV2Vo
     * @return
     */
    @Override
    public Long doCreateRequest(OaCreateWorkFlowRequestV2Vo requestV2Vo) {
        if(StringUtils.isBlank(requestV2Vo.getBusinessId())){
            throw new McpException("业务Id不能为空");
        }
        if(StringUtils.isBlank(requestV2Vo.getWorkflowId())){
            throw new McpException("workflowId不能为空");
        }
        if(StringUtils.isBlank(requestV2Vo.getBusinessTypeCode())){
            throw new McpException("业务分类编码不能为空");
        }
        AppReply<OaWorkFlowResultVo> appReply = bzfBusinessFwWorkflowClient.doCreateRequest(requestV2Vo);
        if(AppReply.SUCCESS_CODE.equals(appReply.getCode())){
            return appReply.getData().getRequestId();
        }
        return null;
    }

    /**
     * 同意
     *
     * @param param
     */
    @Override
    public void submitRequest(BaseFlowVo param) {
        bzfBusinessFwWorkflowClient.submitRequest(param);
    }

    /**
     * 强制终止
     *
     * @param requestId
     */
    @Override
    public void doForceOver(String requestId) {
        BaseFlowVo vo = new BaseFlowVo();
        vo.setRequestId(requestId);
        bzfBusinessFwWorkflowClient.doForceOver(vo);
    }

    /**
     * 根据业务分类编号获取workflowId
     *
     * @param businessTypeCode
     * @return
     */
    @Override
    public String selectWorkFlowId(String businessTypeCode) {
        AppReply<JSONObject> appReply = bzfBusinessFwWorkflowClient.selectWorkFlowId(businessTypeCode);
        String workflowId = null;
        if(AppReply.SUCCESS_CODE.equals(appReply.getCode())&& appReply.getData()!=null){
            if(StringUtils.isNotBlank(appReply.getData().getString("workflowId"))){
                workflowId =appReply.getData().getString("workflowId");
            }else{
                throw new McpException(businessTypeCode+"分类没有对应的配置");
            }
        }else{
            throw new McpException(businessTypeCode+"分类没有对应的配置");
        }
        return workflowId;
    }

    /**
     * 签约审签
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public AppReply<Object> signApprove(CallBakParamsVo vo) {
        LambdaUpdateWrapper<BbsSignInfoEntity> wrapper = new LambdaUpdateWrapper<>();
        String businessId = this.requestId2BusinessId(vo.getRequestId());
        BbsApproveInfoVo infoVo = new BbsApproveInfoVo();
        if(StringUtils.isBlank(businessId)){
            throw new McpException("没有对应的业务数据");
        }
        infoVo.setApproveType("1");//合同审签
        infoVo.setParentId(businessId);
        infoVo.setCommentExplanation(vo.getComment());
        if(WhetherEnum.YES.getCode().equals(vo.getType())){ // 同意
            //infoVo.setApproveStatus(ApproveStatusEnum.APPROVED.getCode());
            if(NodeKeyEnum.END.getCode().equalsIgnoreCase(vo.getNodeKey())){
                wrapper.set(BbsSignInfoEntity::getSignStatus, SignStatusEnum.WAIT_SIGN.getCode())
                        .eq(BbsSignInfoEntity::getSignId,businessId);
                signInfoService.update(wrapper);
                infoVo.setApproveStatus(ApproveStatusEnum.APPROVED.getCode());
                this.approveInfoService.saveApproveResult(infoVo);

//                BbsApproveInfoVo infoVo1 = new BbsApproveInfoVo();
//                infoVo.setParentId(businessId);
//                infoVo1.setApproveType("2");//合同审核
//                infoVo1.setParentId(businessId);
//                infoVo1.setApproveStatus(ApproveStatusEnum.NOT_BEGIN.getCode());//未发起
//                this.approveInfoService.saveApproveResult(infoVo1);

                BbsApproveInfoEntity bbsApproveInfoEntity = new BbsApproveInfoEntity();
                bbsApproveInfoEntity.setApproveType("2");
                bbsApproveInfoEntity.setDelFlag(1);
                bbsApproveInfoEntity.setParentId(businessId);
                bbsApproveInfoEntity.setApproveStatus(ApproveStatusEnum.NOT_BEGIN.getCode());
                bbsApproveInfoEntity.setApproverUserId(CurrentUtil.getUserId());
                bbsApproveInfoEntity.setApproverUserName(userUtil.getUserName(userUtil.getUserId()));
                bbsApproveInfoEntity.setApproveTime(new Date());
                bbsApproveInfoEntity.setApproveId(IdUtil.fastSimpleUUID());
                bbsApproveInfoMapper.insert(bbsApproveInfoEntity);
            }else{
                wrapper.set(BbsSignInfoEntity::getSignStatus, SignStatusEnum.WAIT_AUDIT.getCode())
                        .eq(BbsSignInfoEntity::getSignId,businessId);
                signInfoService.update(wrapper);
            }
        }else if(WhetherEnum.NO.getCode().equals(vo.getType())){ // 驳回
            infoVo.setApproveStatus(ApproveStatusEnum.UNAPPROVED.getCode());
            infoVo.setCommentExplanation(vo.getComment());
            wrapper.set(BbsSignInfoEntity::getSignStatus, SignStatusEnum.NO_PASS.getCode())
                    .eq(BbsSignInfoEntity::getSignId,businessId);
            signInfoService.update(wrapper);
            this.approveInfoService.saveApproveResult(infoVo);
        }else{
            throw new McpException("操作类型只能是 1、0"+vo.getType());
        }

        return AppReply.success(vo.getRequestId());
    }

    /**
     * 新签合同审核
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public AppReply signContractApprove(CallBakParamsVo vo) {
        LambdaUpdateWrapper<BbsSignInfoEntity> wrapper = new LambdaUpdateWrapper<>();

        String businessId = this.requestId2BusinessId(vo.getRequestId());
        if(StringUtils.isBlank(businessId)){
            throw new McpException("没有对应的业务数据");
        }
        BbsApproveInfoVo infoVo = new BbsApproveInfoVo();
        infoVo.setApproveType("2");//合同审核
        infoVo.setParentId(businessId);
        infoVo.setCommentExplanation(vo.getComment());
        if(WhetherEnum.YES.getCode().equals(vo.getType())){ // 同意
            if(NodeKeyEnum.START.getCode().equalsIgnoreCase(vo.getNodeKey())) {// 租户已签

                //signInfoExtService.paperContractsSubmit(businessId); //发起节点没有回调
            }else if(NodeKeyEnum.END.getCode().equalsIgnoreCase(vo.getNodeKey())){ //已签约
                wrapper.set(BbsSignInfoEntity::getSignStatus, SignStatusEnum.SIGNED.getCode())
                        .eq(BbsSignInfoEntity::getSignId,businessId);
                signInfoService.update(wrapper);
                infoVo.setApproveStatus(ApproveStatusEnum.APPROVED.getCode());
                this.approveInfoService.saveApproveResult(infoVo);
            }else{ // 其他节点 都是 待审核
//                //infoVo.setApproveStatus(ApproveStatusEnum.WAIT_APPROVE.getCode());
//                wrapper.set(BbsSignInfoEntity::getSignStatus, SignStatusEnum.WAIT_AUDIT.getCode())
//                            .eq(BbsSignInfoEntity::getSignId,businessId);
//                signInfoService.update(wrapper);
            }
        }else if(WhetherEnum.NO.getCode().equals(vo.getType())){ // 驳回
            //infoVo.setApproveStatus(ApproveStatusEnum.UNAPPROVED.getCode());
            wrapper.set(BbsSignInfoEntity::getSignStatus, SignStatusEnum.UNSIGNED.getCode())
                    .eq(BbsSignInfoEntity::getSignId,businessId);
            infoVo.setApproveStatus(ApproveStatusEnum.UNAPPROVED.getCode());
            signInfoService.update(wrapper);
            this.approveInfoService.saveApproveResult(infoVo);
        }else{
            throw new McpException("操作类型只能是 1、0"+vo.getType());
        }
        return AppReply.success(vo.getRequestId());
    }

    /**
     * 续租审签
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public AppReply<Object> renewalApprove(CallBakParamsVo vo) {
        LambdaUpdateWrapper<BbsRenewalInfoEntity> wrapper = new LambdaUpdateWrapper<>();
        String businessId = this.requestId2BusinessId(vo.getRequestId());
        BbsRenewalApproveInfoVo infoVo = new BbsRenewalApproveInfoVo();

        if(StringUtils.isBlank(businessId)){
            throw new McpException("没有对应的业务数据");
        }
        infoVo.setApproveType("1");//合同审核
        infoVo.setParentId(businessId);
        infoVo.setCommentExplanation(vo.getComment());
        if(WhetherEnum.YES.getCode().equals(vo.getType())){ // 同意
            if(NodeKeyEnum.END.getCode().equalsIgnoreCase(vo.getNodeKey())){
                wrapper.set(BbsRenewalInfoEntity::getSignStatus, SignStatusEnum.WAIT_SIGN.getCode())
                        .eq(BbsRenewalInfoEntity::getSignId,businessId);
                renewalInfoExtService.update(wrapper);
                infoVo.setApproveStatus(ApproveStatusEnum.APPROVED.getCode());
                this.renewalApproveInfoService.saveApproveResult(infoVo);

                BbsRenewalApproveDetailInfoEntity bbsRenewalApproveInfoEntity = new BbsRenewalApproveDetailInfoEntity();
                bbsRenewalApproveInfoEntity.setApproveType("2");
                bbsRenewalApproveInfoEntity.setDelFlag(1);
                bbsRenewalApproveInfoEntity.setParentId(businessId);
                bbsRenewalApproveInfoEntity.setApproveStatus(ApproveStatusEnum.NOT_BEGIN.getCode());
                bbsRenewalApproveInfoEntity.setApproverUserId(CurrentUtil.getUserId());
                bbsRenewalApproveInfoEntity.setApproverUserName(userUtil.getUserName(userUtil.getUserId()));
                bbsRenewalApproveInfoEntity.setApproveTime(new Date());
                bbsRenewalApproveInfoEntity.setApproveId(IdUtil.fastSimpleUUID());
                bbsRenewalApproveInfoEntity.setApproveDetailId(IdUtil.fastSimpleUUID());
                bbsRenewalApproveDetailInfoMapper.insert(bbsRenewalApproveInfoEntity);

            }else{
                wrapper.set(BbsRenewalInfoEntity::getSignStatus, SignStatusEnum.WAIT_AUDIT.getCode())
                        .eq(BbsRenewalInfoEntity::getSignId,businessId);
                renewalInfoExtService.update(wrapper);
            }
        }else if(WhetherEnum.NO.getCode().equals(vo.getType())){ // 驳回
            infoVo.setApproveStatus(ApproveStatusEnum.UNAPPROVED.getCode());
            infoVo.setCommentExplanation(vo.getComment());
            wrapper.set(BbsRenewalInfoEntity::getSignStatus, SignStatusEnum.NO_PASS.getCode())
                    .eq(BbsRenewalInfoEntity::getSignId,businessId);
            renewalInfoExtService.update(wrapper);
            this.renewalApproveInfoService.saveApproveResult(infoVo);
        }else{
            throw new McpException("操作类型只能是 1、0"+vo.getType());
        }
        return AppReply.success(vo.getRequestId());
    }

    /**
     * 续租合同审核
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public AppReply renewalContractApprove(CallBakParamsVo vo) {
        LambdaUpdateWrapper<BbsRenewalInfoEntity> wrapper = new LambdaUpdateWrapper<>();

        String businessId = this.requestId2BusinessId(vo.getRequestId());
        if(StringUtils.isBlank(businessId)){
            throw new McpException("没有对应的业务数据");
        }
        BbsRenewalApproveInfoVo infoVo = new BbsRenewalApproveInfoVo();
        infoVo.setApproveType("2");//合同审核
        infoVo.setParentId(businessId);
        infoVo.setCommentExplanation(vo.getComment());
        if(WhetherEnum.YES.getCode().equals(vo.getType())){ // 同意
            if(NodeKeyEnum.START.getCode().equalsIgnoreCase(vo.getNodeKey())) {// 租户已签

                //renewalInfoExtService.paperContractsSubmit(businessId);
            }else if(NodeKeyEnum.END.getCode().equalsIgnoreCase(vo.getNodeKey())){ //已签约
                infoVo.setApproveStatus(ApproveStatusEnum.APPROVED.getCode());
                wrapper.set(BbsRenewalInfoEntity::getSignStatus, SignStatusEnum.SIGNED.getCode())
                        .eq(BbsRenewalInfoEntity::getSignId,businessId);
                renewalInfoExtService.update(wrapper);
                this.renewalApproveInfoService.saveApproveResult(infoVo);
            }else{ // 其他节点 都是 待审核
//                //infoVo.setApproveStatus(ApproveStatusEnum.WAIT_APPROVE.getCode());
//                wrapper.set(BbsSignInfoEntity::getSignStatus, SignStatusEnum.WAIT_AUDIT.getCode())
//                            .eq(BbsSignInfoEntity::getSignId,businessId);
//                signInfoService.update(wrapper);
            }
        }else if(WhetherEnum.NO.getCode().equals(vo.getType())){ // 驳回
            wrapper.set(BbsRenewalInfoEntity::getSignStatus, SignStatusEnum.UNSIGNED.getCode())
                    .eq(BbsRenewalInfoEntity::getSignId,businessId);
            infoVo.setApproveStatus(ApproveStatusEnum.UNAPPROVED.getCode());
            renewalInfoExtService.update(wrapper);
            this.renewalApproveInfoService.saveApproveResult(infoVo);
        }else{
            throw new McpException("操作类型只能是 1、0"+vo.getType());
        }
        return AppReply.success(vo.getRequestId());
    }

    /**
     * 处理退款申请业务审批
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public AppReply<Object> dealBusinessApprove(CallBakParamsVo vo) {
        String businessId = this.requestId2BusinessId(vo.getRequestId());
        if(StringUtils.isBlank(businessId)){
            throw new McpException("没有对应的业务数据");
        }
        BbctApproveVo approveVo = new BbctApproveVo();
        approveVo.setId(businessId);
        if(WhetherEnum.YES.getCode().equals(vo.getType())){ // 同意
            if(NodeKeyEnum.END.getCode().equalsIgnoreCase(vo.getNodeKey())){
                approveVo.setStatus(ApproveStatusEnum.APPROVED.getCode());
                refundInfoService.dealBusinessApprove(approveVo);
            }else{
                //这个其实没有用 驳回提交service里面处理了
                approveVo.setStatus(ApproveStatusEnum.WAIT_APPROVE.getCode());
            }
        }else if(WhetherEnum.NO.getCode().equals(vo.getType())){ // 驳回
            approveVo.setStatus(ApproveStatusEnum.UNAPPROVED.getCode());
            refundInfoService.dealBusinessApprove(approveVo);
        }else{
            throw new McpException("操作类型只能是 1、0"+vo.getType());
        }

        return AppReply.success(vo.getRequestId());
    }


    @Transactional(rollbackFor = {Exception.class})
    @Override
    public AppReply<Object> dealBasicInformationChangeApprove(CallBakParamsVo vo) {
        LambdaUpdateWrapper<BbsiContractChangeEntity> wrapper = new LambdaUpdateWrapper<>();
        String businessId = this.requestId2BusinessId(vo.getRequestId());
        if(StringUtils.isBlank(businessId)){
            throw new McpException("没有对应的业务数据");
        }
        String changeStatus = "";

        if(WhetherEnum.YES.getCode().equals(vo.getType())){
            if(NodeKeyEnum.END.getCode().equalsIgnoreCase(vo.getNodeKey())) {
                // 同意
                changeStatus = ChangeStatusEnum.ALREADY_PASSED.getCode();
                BbsiContractChangeVo contractChangeVo = contractChangeService.selectByIdRecord(businessId);
                contractChangeResultService.contractChange(contractChangeVo);
            }
        }else if(WhetherEnum.NO.getCode().equals(vo.getType())){
            // 驳回
            changeStatus = ChangeStatusEnum.NO_PASS.getCode();
        }else{
            throw new McpException("操作类型只能是 1、0"+vo.getType());
        }
        wrapper.set(BbsiContractChangeEntity::getChangeStatus, changeStatus)
                .eq(BbsiContractChangeEntity::getCcId,businessId);
        contractChangeService.update(wrapper);
        return AppReply.success(vo.getRequestId());
    }
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public AppReply<Object> dealOtherInformationChangeApprove(CallBakParamsVo vo) {
        LambdaUpdateWrapper<BbsiContractChangeEntity> wrapper = new LambdaUpdateWrapper<>();
        String businessId = this.requestId2BusinessId(vo.getRequestId());
        if(StringUtils.isBlank(businessId)){
            throw new McpException("没有对应的业务数据");
        }
        String changeStatus = "";

        if(WhetherEnum.YES.getCode().equals(vo.getType())){
            if(NodeKeyEnum.END.getCode().equalsIgnoreCase(vo.getNodeKey())) {
                // 同意
                changeStatus = ChangeStatusEnum.ALREADY_PASSED.getCode();

                contractChangeApproveInfoService.saveContractChangeApproveResultV2(businessId, ApproveStatusEnum.APPROVED.getCode());

                wrapper.set(BbsiContractChangeEntity::getChangeStatus, changeStatus)
                        .eq(BbsiContractChangeEntity::getCcId,businessId);
                contractChangeService.update(wrapper);
            }
        }else if(WhetherEnum.NO.getCode().equals(vo.getType())){
            // 驳回
            changeStatus = ChangeStatusEnum.NO_PASS.getCode();

            contractChangeApproveInfoService.saveContractChangeApproveResultV2(businessId, ApproveStatusEnum.UNAPPROVED.getCode());

            wrapper.set(BbsiContractChangeEntity::getChangeStatus, changeStatus)
                    .eq(BbsiContractChangeEntity::getCcId,businessId);
            contractChangeService.update(wrapper);
        }else{
            throw new McpException("操作类型只能是 1、0"+vo.getType());
        }
        return AppReply.success(vo.getRequestId());
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public AppReply<Object> deaUploadResultApprove(CallBakParamsVo vo) {
        LambdaUpdateWrapper<BbsiContractChangeEntity> wrapper = new LambdaUpdateWrapper<>();
        String businessId = this.requestId2BusinessId(vo.getRequestId());
        if(StringUtils.isBlank(businessId)){
            throw new McpException("没有对应的业务数据");
        }
        BbsApproveInfoVo infoVo = new BbsApproveInfoVo();
        infoVo.setApproveType("2");//合同审核
        infoVo.setParentId(businessId);
        infoVo.setCommentExplanation(vo.getComment());

        if(WhetherEnum.YES.getCode().equals(vo.getType())){
            if(NodeKeyEnum.END.getCode().equalsIgnoreCase(vo.getNodeKey())) {
                infoVo.setApproveStatus(ApproveStatusEnum.APPROVED.getCode());
                approveInfoService.saveApproveResultChange(infoVo);
            }
        }else if(WhetherEnum.NO.getCode().equals(vo.getType())){
            // 驳回
            infoVo.setApproveStatus(ApproveStatusEnum.UNAPPROVED.getCode());
            approveInfoService.saveApproveResultChange(infoVo);
        }else{
            throw new McpException("操作类型只能是 1、0"+vo.getType());
        }
        return AppReply.success(vo.getRequestId());
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public AppReply<Object> dealAdjustResultApprove(CallBakParamsVo vo) {
        String businessId = this.requestId2BusinessId(vo.getRequestId());
        if (StringUtils.isNotBlank(businessId)) {
            //获取数据
            AppReply<BbpmReceivableAdjustVo> appReply = bbpaymentFeignClient.adjustSelectByCcid(businessId);
            if (AppReply.SUCCESS_CODE.equals(appReply.getCode())) {
                BbpmReceivableAdjustVo adjustVo = appReply.getData();
                BbsiContractChangeEntity entity = new BbsiContractChangeEntity();
                if(WhetherEnum.YES.getCode().equals(vo.getType())){
                    if(NodeKeyEnum.END.getCode().equalsIgnoreCase(vo.getNodeKey())) {
                        // 同意
                        //生成新的协议编号
                        String agreementCode = adjustService.getContractCode("4", adjustVo.getContractNo());
                        entity.setCcId(adjustVo.getChangeId());
                        entity.setAgreementCode(agreementCode);
                        entity.setChangeStatus(ChangeStatusEnum.ALREADY_PASSED.getCode());
                        //推送上传结果审核数据
                        String signId = adjustService.pushSignInfo(adjustVo,agreementCode);
                        entity.setSignId(signId);

                        if(StringUtils.isBlank(signId)){
                            throw new McpException("更新合同变更表signId时signId为空"+signId);
                        }
                        if (adjustVo.getRefundPath() != null && !adjustVo.getRefundPath().equals("")){
                            if (adjustVo.getCalculationResultVo() != null &&
                                    (
                                            (adjustVo.getCalculationResultVo().calculateTotalPersonalRefundAmount() != null && adjustVo.getCalculationResultVo().calculateTotalPersonalRefundAmount().compareTo(BigDecimal.ZERO) > 0) ||
                                                    (adjustVo.getCalculationResultVo().calculateTotalCompanyRefundAmount() != null && adjustVo.getCalculationResultVo().calculateTotalCompanyRefundAmount().compareTo(BigDecimal.ZERO) > 0)
                                    )
                            ) {
                                if (adjustVo.getCalculationResultVo().getRentingOutDetailVOList() != null) {
                                    BigDecimal zjTotal = BigDecimal.ZERO;
                                    BigDecimal yjTotal = BigDecimal.ZERO;
                                    BigDecimal wyfTotal = BigDecimal.ZERO;
                                    for (RentingOutDetailVO detail : adjustVo.getCalculationResultVo().getRentingOutDetailVOList()) {
                                        if (detail.getRentingOutMoneyPerson() != null) {
                                            if(detail.getChargeSubject() != null && detail.getChargeSubject().equals("01")){
                                                //租金
                                                zjTotal = zjTotal.add(detail.getRentingOutMoneyPerson().add(detail.getRentingOutMoneyCompany()));
                                            }else if(detail.getChargeSubject() != null && detail.getChargeSubject().equals("02")){
                                                //押金
                                                yjTotal = yjTotal.add(detail.getRentingOutMoneyPerson().add(detail.getRentingOutMoneyCompany()));
                                            } else if (detail.getChargeSubject() != null && detail.getChargeSubject().equals("07")) {
                                                //物业费
                                                wyfTotal = wyfTotal.add(detail.getRentingOutMoneyPerson().add(detail.getRentingOutMoneyCompany()));
                                            }

                                        }
                                    }
                                    entity.setRentAmountReceivableLease(zjTotal.toString());
                                    entity.setRentAmountReceivable(yjTotal.toString());
                                    entity.setPropertyAmountReceivable(wyfTotal.toString());
                                }

                            }

                            //插入退回银行卡信息

                            BbsChangeReturnBankCardVo bankCardVo = new BbsChangeReturnBankCardVo();
                            bankCardVo.setCcId(adjustVo.getChangeId());
                            bankCardVo.setSourceType("04");
//                openingBank:"开户行编码"
//                openingBankName:"开户行名称"
//                refundPath:"退回路径编码"
//                accountHolder: "开户人姓名"
//                bankIdNumber: "证件号码"
//                bankCard: "银行卡号"
//                bankBranchName: "开户支行名称"
//                bankBranchCode: "开户支行编码"
//                openingCity: "所在市编码"
//                openingCityName: "所在市名称"
//                openingProvince: "所在省编码"
//                openingProvinceName: "所在省名称"
//                changeBankReason: "修改原因"
                            bankCardVo.setBackType(adjustVo.getRefundPath());
                            bankCardVo.setBankAccountId(adjustVo.getBankCard());
                            bankCardVo.setBankCode(adjustVo.getOpeningBank());
                            bankCardVo.setBankName(adjustVo.getOpeningBankName());
                            bankCardVo.setSubBankCode(adjustVo.getBankBranchCode());
                            bankCardVo.setSubBankName(adjustVo.getBankBranchName());
                            bankCardVo.setBankProvinceCode(adjustVo.getOpeningProvince());
                            bankCardVo.setBankProvinceName(adjustVo.getOpeningProvinceName());
                            bankCardVo.setBankCityCode(adjustVo.getOpeningCity());
                            bankCardVo.setBankCityName(adjustVo.getOpeningCityName());
                            bankCardVo.setBankAccountName(adjustVo.getAccountHolder());
                            bankCardVo.setCustomerIdType("01");
                            bankCardVo.setCustomerIdNumber(adjustVo.getBankIdNumber());
                            bankCardVo.setChangeReason(adjustVo.getChangeBankReason());
                            iBbsChangeReturnBankCardService.insertRecord(bankCardVo);
                        }
                    }
                }else if(WhetherEnum.NO.getCode().equals(vo.getType())){
                    // 驳回
                    entity.setCcId(adjustVo.getChangeId());
                    entity.setChangeStatus(ChangeStatusEnum.NO_PASS.getCode());
                }else{
                    throw new McpException("操作类型只能是 1、0"+vo.getType());
                }
                contractChangeService.updateById(entity);
            }
        }else{
            throw new McpException("没有对应的业务数据");
        }
        return AppReply.success(vo.getRequestId());
    }

}
