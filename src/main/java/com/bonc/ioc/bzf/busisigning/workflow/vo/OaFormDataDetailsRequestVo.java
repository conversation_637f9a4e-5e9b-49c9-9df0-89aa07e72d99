package com.bonc.ioc.bzf.busisigning.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: caijun
 * @date: 2024/05/23
 * @change: 2024/05/23 by <EMAIL> for init
 */
@Data
public class OaFormDataDetailsRequestVo implements Serializable {

    /**
     * 文本内容
     */
    @ApiModelProperty(value = "文本内容", required = true)
    private String content;

    /**
     * 字段数据库名，与fieldId二选一必填。
     */
    @ApiModelProperty(value = "字段数据库名，与fieldId二选一必填。")
    private String dataKey;

    /**
     * 字段ID，与dataKey二选一必填。
     */
    @ApiModelProperty(value = "字段ID，与dataKey二选一必填。")
    private String fieldId;

    /**
     * 明细数据时，填写行数。
     */
    @ApiModelProperty(value = "明细数据时，填写行数。")
    private String dataIndex;

    /**
     * 浏览按钮数据，默认为null，有值的时候，以这个值为准，忽略content的内容。
     */
    @ApiModelProperty(value = "浏览按钮数据，默认为null，有值的时候，以这个值为准，忽略content的内容。")
    private List<OaFormDataDetailsOptionsRequestVo> dataOptions;

    /**
     * 明细表ID，当传递明细数据且字段传递的dataKey则该值必填。
     */
    @ApiModelProperty(value = "明细表ID，当传递明细数据且字段传递的dataKey则该值必填。")
    private String subFormId;

    /**
     * type
     */
    @ApiModelProperty(value = "type")
    private String type;

}
