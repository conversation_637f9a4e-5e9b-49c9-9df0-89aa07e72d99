package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsApproveDetailInfoEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 审批明细表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-31
 * @change 2023-08-31 by 刘鹏伟 for init
 */
public interface IBbsApproveDetailInfoService extends IMcpBaseService<BbsApproveDetailInfoEntity>{
	/**
	 * insertRecord 新增
	 * @param vo 需要保存的记录
	 * @return  String 返回新增后的主键
	 * @since 1.0.0
	 * <AUTHOR>
	 * @date  2023-08-31
	 * @change
	 * 2023-08-31 by 刘鹏伟 for init
	 */
	String insertRecord(BbsApproveDetailInfoVo vo);
	
	/**
	 * insertBatchRecord 批量新增
	 * @param voList 需要保存的记录
	 * @return  List<String> 返回新增后的主键
	 * @since 1.0.0
	 * <AUTHOR>
	 * @date  2023-08-31
	 * @change
	 * 2023-08-31 by 刘鹏伟 for init
	 */
	List<String> insertBatchRecord(List<BbsApproveDetailInfoVo> voList);
	
	/**
	 * removeByIdRecord 根据主键删除
	 * @param approveId 需要删除的审批id
	 * @return  void
	 * @since 1.0.0
	 * <AUTHOR>
	 * @date  2023-08-31
	 * @change
	 * 2023-08-31 by 刘鹏伟 for init
	 */
	void removeByIdRecord(String approveId);
	
	/**
	 * removeByIdsRecord 根据主键集合删除
	 * @param approveIdList 需要删除的审批id
	 * @return  void
	 * @since 1.0.0
	 * <AUTHOR>
	 * @date  2023-08-31
	 * @change
	 * 2023-08-31 by 刘鹏伟 for init
	 */
	void removeByIdsRecord(List<String> approveIdList);
	
	/**
	 * updateByIdRecord 根据主键更新 表全部信息
	 * @param vo 需要更新的审批明细表
	 * @return  void
	 * @since 1.0.0
	 * <AUTHOR>
	 * @date  2023-08-31
	 * @change
	 * 2023-08-31 by 刘鹏伟 for init
	 */
	void updateByIdRecord(BbsApproveDetailInfoVo vo);
	
	/**
	 * updateBatchByIdRecord 根据主键集合更新
	 * @param voList 需要更新的审批明细表
	 * @return  void
	 * @since 1.0.0
	 * <AUTHOR>
	 * @date  2023-08-31
	 * @change
	 * 2023-08-31 by 刘鹏伟 for init
	 */
	void updateBatchByIdRecord(List<BbsApproveDetailInfoVo> voList);
	
	/**
	 * saveByIdRecord 根据主键更新或新增
	 * @param vo 需要更新的审批明细表
	 * @return  void
	 * @since 1.0.0
	 * <AUTHOR>
	 * @date  2023-08-31
	 * @change
	 * 2023-08-31 by 刘鹏伟 for init
	 */
	void saveByIdRecord(BbsApproveDetailInfoVo vo);
	
	/**
	 * saveBatchByIdRecord 根据主键更新或新增
	 * @param voList 需要删除的审批明细表
	 * @return  void
	 * @since 1.0.0
	 * <AUTHOR>
	 * @date  2023-08-31
	 * @change
	 * 2023-08-31 by 刘鹏伟 for init
	 */
	void saveBatchByIdRecord(List<BbsApproveDetailInfoVo> voList);
	
	/**
	 * selectByIdRecord 根据主键查询
	 * @param approveId 需要查询的审批id
	 * @return  根据id查询结果
	 * @since 1.0.0
	 * <AUTHOR>
	 * @date  2023-08-31
	 * @change
	 * 2023-08-31 by 刘鹏伟 for init
	 */
	BbsApproveDetailInfoVo selectByIdRecord(String approveId);
	
	/**
	 * selectByPageRecord 分页查询
	 * @param vo 需要查询的条件
	 * @return  分页查询结果
	 * @since 1.0.0
	 * <AUTHOR>
	 * @date  2023-08-31
	 * @change
	 * 2023-08-31 by 刘鹏伟 for init
	 */
	PageResult<List<BbsApproveDetailInfoPageResultVo>> selectByPageRecord(BbsApproveDetailInfoPageVo vo);
}
