package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRefundApproveDetailInfoEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 退款审批明细表 服务类
 *
 * <AUTHOR>
 * @date 2024-08-28
 * @change 2024-08-28 by pyj for init
 */
public interface IBbsRefundApproveDetailInfoService extends IMcpBaseService<BbsRefundApproveDetailInfoEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbsRefundApproveDetailInfoVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbsRefundApproveDetailInfoVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param approveId 需要删除的审批id
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String approveId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param approveIdList 需要删除的审批id
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> approveIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的退款审批明细表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbsRefundApproveDetailInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的退款审批明细表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbsRefundApproveDetailInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的退款审批明细表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbsRefundApproveDetailInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的退款审批明细表
     * @return void
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbsRefundApproveDetailInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param approveId 需要查询的审批id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    BbsRefundApproveDetailInfoVo selectByIdRecord(String approveId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbsRefundApproveDetailInfoPageResultVo>> selectByPageRecord(BbsRefundApproveDetailInfoPageVo vo);

    /**
     * 根据上级id查询审批明细列表
     *
     * @param parentId 上级id
     * @return 审批明细列表
     */
    List<BbsRefundApproveDetailInfoEntity> selectByParentId(String parentId);
}
