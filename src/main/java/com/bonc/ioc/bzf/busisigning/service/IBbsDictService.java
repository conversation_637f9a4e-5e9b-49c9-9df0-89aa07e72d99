package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsDictEntity;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsDictPageResultVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsDictPageVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbsDictVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

/**
 * 字典表 服务类
 *
 * <AUTHOR>
 * @date 2023-05-05
 * @change 2023-05-05 by pyj for init
 */
public interface IBbsDictService extends IMcpBaseService<BbsDictEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbsDictVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbsDictVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param dictId 需要删除的字典表主键
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String dictId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param dictIdList 需要删除的字典表主键
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> dictIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的字典表
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbsDictVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的字典表
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbsDictVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的字典表
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbsDictVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的字典表
     * @return void
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbsDictVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param dictId 需要查询的字典表主键
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    BbsDictVo selectByIdRecord(String dictId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbsDictPageResultVo>> selectByPageRecord(BbsDictPageVo vo);

    List<BbsDictVo> selectBusinessFormat();
}
