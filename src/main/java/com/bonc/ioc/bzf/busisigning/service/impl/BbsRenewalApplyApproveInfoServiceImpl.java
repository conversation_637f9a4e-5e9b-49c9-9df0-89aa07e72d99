package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApplyApproveDetailInfoMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApplyApproveInfoMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalApplyInfoMapper;
import com.bonc.ioc.bzf.busisigning.entity.*;
import com.bonc.ioc.bzf.busisigning.feign.feign.BzfBusinessMessageFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.vo.WebsiteMsgContentVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.WebsiteMsgVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalApplyApproveDetailInfoService;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalApplyApproveInfoService;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalApplyInfoService;
import com.bonc.ioc.bzf.busisigning.utils.UserUtil;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;

/**
 * @author: hechengyao
 * @createDate: 2023-09-21
 * @Version 1.0
 **/
@Slf4j
@Service
public class BbsRenewalApplyApproveInfoServiceImpl extends McpBaseServiceImpl<BbsRenewalApplyApproveInfoEntity> implements IBbsRenewalApplyApproveInfoService {


    /**
     * 审核明细Service
     */
    @Resource
    private IBbsRenewalApplyApproveDetailInfoService renewalApplyApproveDetailInfoService;

    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalApplyApproveInfoMapper baseMapper;

    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalApplyApproveDetailInfoMapper renewalApplyApproveDetailMapper;

    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalApplyInfoMapper renewalApplyInfoMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsRenewalApplyApproveInfoService baseService;


    /**
     * renewalApplyInfoService 服务
     */
    @Resource
    private IBbsRenewalApplyInfoService renewalApplyInfoService;

    /**
     * 获取登录用户
     */
    @Resource
    private UserUtil userUtil;

    /**
     * 消息中心FeignClient
     */
    @Resource
    private BzfBusinessMessageFeignClient bzfBusinessMessageFeignClient;

    /**
     * 环境变量
     */
    @Resource
    private Environment env;

    @Value("${bzfsystem.messageTemplate.renewalApply.systemSuccessTemplateId:b21a80fe6ed548279053bcdbbeeb5b3f}")
    private String systemSuccess;

    @Value("${bzfsystem.messageTemplate.renewalApply.systemFailedTemplateId:0c36285621ae49e08905ee1456ea036a}")
    private String systemFailed;

    @Value("${bzfsystem.messageTemplate.renewalApply.mobileSuccessTemplateId:043b0902ec334afcb3969f4f7ec2ef49}")
    private String mobileSuccess;

    @Value("${bzfsystem.messageTemplate.renewalApply.mobileFailedTemplateId:7347469ae01d4b89b4d1d33c8c76ac34}")
    private String mobileFailed;

    /**
     * 续签申请审核-增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(RenewalApplyApproveInfoVo vo) {
        if (vo == null) {
            return null;
        }
        BbsRenewalApplyApproveInfoEntity entity = new BbsRenewalApplyApproveInfoEntity();
        BeanUtils.copyProperties(vo, entity);

        if (!baseService.insert(entity)) {
            log.error("续签申请审核信息表新增失败:" + entity.toString());
            throw new McpException("续签申请审核信息表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getApproveId(), 1)) {
                log.error("续签申请信息表新增后保存历史失败:" + entity.toString());
                throw new McpException("续签申请信息表新增后保存历史失败");
            }

            log.debug("续签申请信息表新增后保存历史成功:" + entity.getApproveId());
            return entity.getApproveId();
        }
    }

    /**
     * 续签申请审核-改（改变状态）
     *
     * @param vo 查看谁的状态
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @Override
    public BbsRenewalApplyApproveInfoEntity updateRenewalApplyApproveInfo(RenewalApplyApproveInfoVo vo) {
        return null;
    }

    /**
     * 续签申请审核-审核
     *
     * @param vo 需要保存的续签申请审核结果和信息
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @Override
    public AppReply applyApprove(RenewalApplyApproveInfoVo vo) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("parent_id", vo.getParentId());
        queryMap.put("del_flag", 1);
        List<BbsRenewalApplyApproveInfoEntity> bbsApplyApproveInfoEntities = baseService.selectByMap(queryMap);
        if (bbsApplyApproveInfoEntities.isEmpty()) {
            return new AppReply(AppReply.ERROR_CODE, "未查询到签约记录", null);
        }
        //先清理实时表记录
        deleteBySignId(vo.getParentId());
        BbsRenewalApplyApproveInfoEntity bbsApproveInfoEntity = bbsApplyApproveInfoEntities.get(0);
        //在保存最新记录
        bbsApproveInfoEntity.setCommentExplanation(vo.getCommentExplanation());
        bbsApproveInfoEntity.setApproveStatus(vo.getApproveStatus());
        bbsApproveInfoEntity.setApproverUserId(userUtil.getUserId());
        bbsApproveInfoEntity.setApproverUserName(userUtil.getUserName(userUtil.getUserId()));
        bbsApproveInfoEntity.setApproveTime(new java.sql.Date(System.currentTimeMillis()));
        bbsApproveInfoEntity.setApproveId(IdUtil.fastSimpleUUID());
        bbsApproveInfoEntity.setCreateTime(null);
        bbsApproveInfoEntity.setModifyTime(null);
        baseService.insert(bbsApproveInfoEntity);
        //新增明细记录
        saveApplyApproveDetailInfo(bbsApproveInfoEntity);
        AppReply msgAppReply = new AppReply(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        //驳回原因
        String rejectReason = vo.getApproveStatus().equals("2") ? vo.getCommentExplanation() : "";
        //审签成功 或者 审签成功/失败 发送信息
        if (Arrays.asList("1", "2").contains(vo.getApproveStatus())) {
            //发送站内信
            AppReply website = sendMessage("website", vo.getParentId(), rejectReason, vo.getApproveStatus());
            //发送短信
            AppReply sms = sendMessage("SMS", vo.getParentId(), rejectReason, vo.getApproveStatus());
            msgAppReply.setCode(website.getCode().equals(AppReply.SUCCESS_CODE) && sms.getCode().equals(AppReply.SUCCESS_CODE) ? AppReply.SUCCESS_CODE : AppReply.ERROR_CODE);
            msgAppReply.setMsg(website.getMsg() + ";" + sms.getMsg());
        }
        return msgAppReply;
    }

    /**
     * 续签申请审核-查（查看详细信息）
     *
     * @param parentId 申请信息id
     * @return com.bonc.ioc.common.util.AppReply 查看详细信息
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @Override
    public RenewalApplyApproveInfoVo selectByIdRecord(String parentId) {
        RenewalApplyApproveInfoVo vo = new RenewalApplyApproveInfoVo();

        if (!StringUtils.isEmpty(parentId)) {
            BbsRenewalApplyApproveInfoEntity entity = baseService.selectById(parentId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }
//    @Override
//    public BbsRenewalApplyInfoEntity selectByIdRecord(String parentId) {
//        if (!StringUtils.isEmpty(parentId)) {
//            return renewalApplyInfoService.selectById(parentId);
//        }
//        return null;
//    }

    /**
     * 续签申请审核-分页
     *
     * @param vo 查看谁的状态
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @Override
    public PageResult<List<RenewalApplyApproveInfoPageResultVo>> selectByPageRecord(RenewalApplyInfoPageVo vo) {
        List<RenewalApplyApproveInfoPageResultVo> result = baseMapper.selectByPageRecord(vo);
        return new PageResult<>(result);
    }

    /**
     * @param type               站内信:website 短信:SMS
     * @param renewalApplyInfoId 申请信息id
     * @param rejectReason       驳回原因
     */
    private AppReply sendMessage(String type, String renewalApplyInfoId, String rejectReason,String approveStatus) {
        //debug调不通 所以不发送
        if (Objects.equals(env.getProperty("spring.profiles.active"), "debug")) {
            return new AppReply<>(AppReply.SUCCESS_CODE, "debug", null);
        }
        //查询 客户名称 房源地址
        BbsRenewalApplyInfoEntity bbsRenewalApplyInfoEntity = renewalApplyInfoMapper.selectById(renewalApplyInfoId);
        if (null == bbsRenewalApplyInfoEntity) {
            log.info("未查询到客户名称或房源地址");
            return new AppReply<>(AppReply.ERROR_CODE, "未查询到客户名称或房源地址", null);
        }
        String housingAddress = StringUtils.isNotBlank(bbsRenewalApplyInfoEntity.getProductName()) ? bbsRenewalApplyInfoEntity.getProductName() : "未查询到房源地址";
        String customerName = StringUtils.isNotBlank(bbsRenewalApplyInfoEntity.getTenantName()) ? bbsRenewalApplyInfoEntity.getTenantName() : "未查询到客户名称";
        String phone = StringUtils.isNotBlank(bbsRenewalApplyInfoEntity.getCustomerTel()) ? bbsRenewalApplyInfoEntity.getCustomerTel() : "";
        if ("SMS".equals(type) && StringUtils.isEmpty(phone)) {
            log.error("发送短信失败====>>>>>未查询到客户手机号码{}", renewalApplyInfoId);
            return new AppReply<>(AppReply.ERROR_CODE, "发送短信失败,未查询到客户手机号码", null);
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("housingAddress", housingAddress);
        paramMap.put("customerName", customerName);
        paramMap.put("toExamineResult", rejectReason);
        List<WebsiteMsgContentVo> websiteMsgContentVo = Arrays.asList(new WebsiteMsgContentVo(paramMap, phone, renewalApplyInfoId));
        //发送信息
        AppReply<JSONObject> appReply = null;
        StringBuilder message = new StringBuilder();
        if ("website".equals(type)) {
            message.append("发送站内信");
            //站内信
            if ("1".equals(approveStatus)) {
                //通过
                paramMap.remove("toExamineResult");
                WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(systemSuccess, "系统站内信通知", websiteMsgContentVo);
                log.info("发送站内信==={}", JSON.toJSONString(websiteMsgVo));
                appReply = bzfBusinessMessageFeignClient.sendWebsiteMsg(websiteMsgVo);
            } else {
                //未通过
                WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(systemFailed, "系统站内信通知", websiteMsgContentVo);
                log.info("发送站内信==={}", JSON.toJSONString(websiteMsgVo));
                appReply = bzfBusinessMessageFeignClient.sendWebsiteMsg(websiteMsgVo);
            }
        }
        if ("SMS".equals(type) && StringUtils.isNotEmpty(phone)) {
            message.append("发送手机短信");
            //手机短信
            if ("1".equals(approveStatus)) {
                //通过
                paramMap.remove("toExamineResult");
                WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(mobileSuccess, "系统站内信审核成功通知", websiteMsgContentVo);
                log.info("发送短信==={}", JSON.toJSONString(websiteMsgVo));
                appReply = bzfBusinessMessageFeignClient.sendSMS(websiteMsgVo);
            } else {
                //未通过
                WebsiteMsgVo websiteMsgVo = new WebsiteMsgVo(mobileFailed, "系统站内信审核失败通知", websiteMsgContentVo);
                log.info("发送短信==={}", JSON.toJSONString(websiteMsgVo));
                appReply = bzfBusinessMessageFeignClient.sendSMS(websiteMsgVo);
            }
        }

        assert appReply != null;
        log.info("发送站内信/短信成功结果==={}", JSON.toJSONString(appReply));
        if (appReply.getCode().equals("1")) {
            List<Map> data = (List<Map>) appReply.getData().get("detailVos");
            if (null != data && null != data.get(0)) {
                Map detail = data.get(0);
                if (detail.get("code").equals("00")) {
                    log.info("发送站内信/短信成功==={}", JSON.toJSONString(appReply));
                    message.append("成功");
                    return new AppReply<>(AppReply.SUCCESS_CODE, message.toString(), null);
                } else {
                    message.append("失败");
                    log.info("发送站内信/短信失败==={}", JSON.toJSONString(appReply));
                }
            } else {
                message.append("失败");
                log.info("发送站内信/短信失败==={}", JSON.toJSONString(appReply));

            }
        } else {
            message.append("失败");
        }
        return new AppReply<>(AppReply.ERROR_CODE, message.toString(), null);
    }

    /**
     * 续签申请审核-提交申请审核，接收app端信息，保存信息的接口
     *
     * @param renewalApplyInfoId 申请信息id
     * @return com.bonc.ioc.common.util.AppReply 文件的数据
     * <AUTHOR>
     * @date 2023-09-21
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void addApplyApproveReviewInfo(String renewalApplyInfoId) {
        //先清理实时表记录
        deleteBySignId(renewalApplyInfoId);
        //再保存最新记录
        BbsRenewalApplyApproveInfoEntity entity = new BbsRenewalApplyApproveInfoEntity();
        entity.setApproveId(IdUtil.fastSimpleUUID());
        entity.setParentId(renewalApplyInfoId);
        entity.setApproveStatus("3");
        entity.setSubmitUserId(userUtil.getUserId());
        entity.setSubmitUserName(userUtil.getUserName(userUtil.getUserId()));
        entity.setSubmitTime(new Date(System.currentTimeMillis()));
        entity.setDelFlag(1);
        baseService.insert(entity);
        //新增明细记录
        saveApplyApproveDetailInfo(entity);
    }

    private void deleteBySignId(String renewalApplyInfoId) {
        Map<String, Object> delMap = new HashMap<>();
        delMap.put("parent_id", renewalApplyInfoId);
        baseService.removeByMap(delMap);
    }

    private void saveApplyApproveDetailInfo(BbsRenewalApplyApproveInfoEntity entity) {
        BbsRenewalApplyApproveDetailInfoEntity approveDetailInfoEntity = new BbsRenewalApplyApproveDetailInfoEntity();
        BeanUtils.copyProperties(entity, approveDetailInfoEntity);
        approveDetailInfoEntity.setApproveDetailId(IdUtil.fastSimpleUUID());
        renewalApplyApproveDetailInfoService.insert(approveDetailInfoEntity);
    }

}
