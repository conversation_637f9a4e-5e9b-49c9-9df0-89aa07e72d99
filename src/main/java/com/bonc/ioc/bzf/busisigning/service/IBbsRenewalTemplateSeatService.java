package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalTemplateSeatEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import java.util.Map;

import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 模板属性表 服务类
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
public interface IBbsRenewalTemplateSeatService extends IMcpBaseService<BbsRenewalTemplateSeatEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    String insertRecord(BbsRenewalTemplateSeatVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    List<String> insertBatchRecord(List<BbsRenewalTemplateSeatVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param seatId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void removeByIdRecord(String seatId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param seatIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void removeByIdsRecord(List<String> seatIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void updateByIdRecord(BbsRenewalTemplateSeatVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void updateBatchByIdRecord(List<BbsRenewalTemplateSeatVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void saveByIdRecord(BbsRenewalTemplateSeatVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的模板属性表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    void saveBatchByIdRecord(List<BbsRenewalTemplateSeatVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param seatId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    BbsRenewalTemplateSeatVo selectByIdRecord(String seatId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    PageResult<List<BbsRenewalTemplateSeatPageResultVo>> selectByPageRecord(BbsRenewalTemplateSeatPageVo vo);

    /**
     * 根据上级id查询map集
     *
     * @param parentId 上级id
     * @return 模板属性map集
     */
    Map<String, String> selectMapByParentId(String parentId);
}
