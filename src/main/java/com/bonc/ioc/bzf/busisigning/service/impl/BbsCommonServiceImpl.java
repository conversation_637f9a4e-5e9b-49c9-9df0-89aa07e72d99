package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.feign.feign.BbsGeographicFeignClient;
import com.bonc.ioc.bzf.busisigning.service.IBbsCommonService;
import com.bonc.ioc.bzf.busisigning.utils.ResultUtils;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 说明
 *
 * <AUTHOR>
 * @since 2024/8/30
 */
@Slf4j
@Service
public class BbsCommonServiceImpl implements IBbsCommonService {

    /**
     * 地理中心 feign实例
     */
    @Resource
    private BbsGeographicFeignClient geographicFeignClient;

    /**
     * 获取省份列表
     *
     * @return 省份列表
     */
    @Override
    public Object selectProvince() {
        AppReply<Object> appReply = geographicFeignClient.selectProvince();
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("获取省份列表失败[详情: %s]", appReply));
        }
        return appReply.getData();
    }


    /**
     * 获取市级列表
     *
     * @param proCode 省份编号
     * @return 市级列表
     */
    @Override
    public Object selectCity(String proCode) {
        AppReply<Object> appReply = geographicFeignClient.selectCity(proCode);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("获取市级列表失败[详情: %s, 省份编号: %s]", appReply, proCode));
        }
        return appReply.getData();
    }
}
