package com.bonc.ioc.bzf.busisigning.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsChangeIncrementalConfigEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsChangeIncrementalConfigMapper;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.service.IBbsChangeIncrementalConfigService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更租金递增设置 服务类实现
 *
 * <AUTHOR>
 * @date 2024-09-09
 * @change 2024-09-09 by tbh for init
 */
@Slf4j
@Service
public class BbsChangeIncrementalConfigServiceImpl extends McpBaseServiceImpl<BbsChangeIncrementalConfigEntity> implements IBbsChangeIncrementalConfigService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsChangeIncrementalConfigMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsChangeIncrementalConfigService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsChangeIncrementalConfigVo vo) {
        if(vo == null) {
            return null;
        }

        BbsChangeIncrementalConfigEntity entity = new BbsChangeIncrementalConfigEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setSicId(null);
        if(!baseService.insert(entity)) {
            log.error("合同变更租金递增设置新增失败:" + entity.toString());
            throw new McpException("合同变更租金递增设置新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getSicId(),1)) {
                log.error("合同变更租金递增设置新增后保存历史失败:" + entity.toString());
                throw new McpException("合同变更租金递增设置新增后保存历史失败");
            }

            log.debug("合同变更租金递增设置新增成功:"+entity.getSicId());
            return entity.getSicId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsChangeIncrementalConfigVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsChangeIncrementalConfigEntity> entityList = new ArrayList<>();
        for (BbsChangeIncrementalConfigVo item:voList) {
            BbsChangeIncrementalConfigEntity entity = new BbsChangeIncrementalConfigEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsChangeIncrementalConfigEntity item:entityList){
            item.setSicId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("合同变更租金递增设置新增失败");
            throw new McpException("合同变更租金递增设置新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsChangeIncrementalConfigEntity::getSicId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("合同变更租金递增设置批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("合同变更租金递增设置批量新增后保存历史失败");
            }

            log.debug("合同变更租金递增设置新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param sicId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String sicId) {
        if(!StringUtils.isEmpty(sicId)) {
            if(!baseService.saveOperationHisById(sicId,3)) {
                log.error("合同变更租金递增设置删除后保存历史失败:" + sicId);
                throw new McpException("合同变更租金递增设置删除后保存历史失败");
            }

            if(!baseService.removeById(sicId)) {
                log.error("合同变更租金递增设置删除失败");
                throw new McpException("合同变更租金递增设置删除失败"+sicId);
            }
        } else {
            throw new McpException("合同变更租金递增设置删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param sicIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> sicIdList) {
        if(!CollectionUtils.isEmpty(sicIdList)) {
            int oldSize = sicIdList.size();
            sicIdList = sicIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(sicIdList) || oldSize != sicIdList.size()) {
                throw new McpException("合同变更租金递增设置批量删除失败 存在主键id为空的记录"+StringUtils.join(sicIdList));
            }

            if(!baseService.saveOperationHisByIds(sicIdList,3)) {
                log.error("合同变更租金递增设置批量删除后保存历史失败:" + StringUtils.join(sicIdList));
                throw new McpException("合同变更租金递增设置批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(sicIdList)) {
                log.error("合同变更租金递增设置批量删除失败");
                throw new McpException("合同变更租金递增设置批量删除失败"+StringUtils.join(sicIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的合同变更租金递增设置
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsChangeIncrementalConfigVo vo) {
        if(vo != null) {
            BbsChangeIncrementalConfigEntity entity = new BbsChangeIncrementalConfigEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getSicId())) {
                throw new McpException("合同变更租金递增设置更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("合同变更租金递增设置更新失败");
                throw new McpException("合同变更租金递增设置更新失败"+entity.getSicId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSicId(),2)) {
                    log.error("合同变更租金递增设置更新后保存历史失败:" + entity.getSicId());
                    throw new McpException("合同变更租金递增设置更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("合同变更租金递增设置更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的合同变更租金递增设置
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsChangeIncrementalConfigVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsChangeIncrementalConfigEntity> entityList = new ArrayList<>();

            for (BbsChangeIncrementalConfigVo item:voList){
                BbsChangeIncrementalConfigEntity entity = new BbsChangeIncrementalConfigEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getSicId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("合同变更租金递增设置批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("合同变更租金递增设置批量更新失败");
                throw new McpException("合同变更租金递增设置批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSicId())).map(BbsChangeIncrementalConfigEntity::getSicId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("合同变更租金递增设置批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("合同变更租金递增设置批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的合同变更租金递增设置
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsChangeIncrementalConfigVo vo) {
        if(vo != null) {
            BbsChangeIncrementalConfigEntity entity = new BbsChangeIncrementalConfigEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("合同变更租金递增设置保存失败");
                throw new McpException("合同变更租金递增设置保存失败"+entity.getSicId());
            } else {
                if(!baseService.saveOperationHisById(entity.getSicId(),4)) {
                    log.error("合同变更租金递增设置保存后保存历史失败:" + entity.getSicId());
                    throw new McpException("合同变更租金递增设置保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("合同变更租金递增设置保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的合同变更租金递增设置
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsChangeIncrementalConfigVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsChangeIncrementalConfigEntity> entityList = new ArrayList<>();

            for (BbsChangeIncrementalConfigVo item:voList){
                BbsChangeIncrementalConfigEntity entity = new BbsChangeIncrementalConfigEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("合同变更租金递增设置批量保存失败");
                throw new McpException("合同变更租金递增设置批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getSicId())).map(BbsChangeIncrementalConfigEntity::getSicId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("合同变更租金递增设置批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("合同变更租金递增设置批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param sicId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsChangeIncrementalConfigVo selectByIdRecord(String sicId) {
        BbsChangeIncrementalConfigVo vo = new BbsChangeIncrementalConfigVo();

        if(!StringUtils.isEmpty(sicId)) {
            BbsChangeIncrementalConfigEntity entity = baseService.selectById(sicId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-09-09
     * @change
     * 2024-09-09 by tbh for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsChangeIncrementalConfigPageResultVo>> selectByPageRecord(BbsChangeIncrementalConfigPageVo vo) {
        List<BbsChangeIncrementalConfigPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据合同变更id和类型(变更前变更后类型+标准类型)查询合同变更递增规则列表
     *
     * @param ccId         合同变更id
     * @param type         变更前变更后类型
     * @param standardType 标准类型
     * @return 合同变更递增规则列表
     */
    @Override
    public List<BbsChangeIncrementalConfigVo> selectByCcIdAndType(String ccId, String type, String standardType) {
        List<BbsChangeIncrementalConfigEntity> entityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BbsChangeIncrementalConfigEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsChangeIncrementalConfigEntity::getCcId, ccId)
                .eq(BbsChangeIncrementalConfigEntity::getType, type)
                .eq(BbsChangeIncrementalConfigEntity::getStandardType, standardType)
                .list();
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        } else {
            List<BbsChangeIncrementalConfigVo> voList = new ArrayList<>();
            for (BbsChangeIncrementalConfigEntity entity : entityList) {
                BbsChangeIncrementalConfigVo vo = new BbsChangeIncrementalConfigVo();
                BeanUtils.copyProperties(entity, vo);
                voList.add(vo);
            }
            return voList;
        }
    }
}
