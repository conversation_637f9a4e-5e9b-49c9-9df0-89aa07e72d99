package com.bonc.ioc.bzf.busisigning.service;

import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeEntity;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;
import com.tencentcloudapi.ocr.v20181119.models.BankCardOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;

import java.util.List;


/**
 * 合并变更表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by fzq for init
 */
public interface IBbsiContractChangeAppService extends IMcpBaseService<BbsiContractChangeEntity> {
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by fzq for init
     */
    String insertRecord(BbsiContractChangeVo vo);


    List<BbsiContractChangeRecordVo> selectChangeRecordList(String contractCode);

    /**
     * selectByIdRecord 根据主键查询
     * @param ccId 需要查询的
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by fzq for init
     */
    BbsiContractChangeVo selectByIdRecord(String ccId);

    List<BbsContractChangeApproveDetailVo> selectApproveDetail(String ccId);

    /**
     * removeByIdRecord 根据主键删除
     * @param ccId 需要删除的
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change
     * 2023-08-30 by fzq for init
     */
    void removeByIdRecord(String ccId);

    BankCardOCRResponse bankCardOCR(String imageBase64);

    void getVerificationCode(VerificationCodeVo vo);

    Boolean checkVerificationCode(VerificationCodeVo vo);

    BbsiContractChangeVo selectByContractCodeAndApproveStatus(String contractCode);
}
