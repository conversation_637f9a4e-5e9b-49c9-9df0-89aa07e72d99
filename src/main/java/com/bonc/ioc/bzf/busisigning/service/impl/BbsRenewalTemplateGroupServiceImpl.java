package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalTemplateGroupEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalTemplateGroupMapper;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalTemplateGroupService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 通知模板组表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
@Slf4j
@Service
public class BbsRenewalTemplateGroupServiceImpl extends McpBaseServiceImpl<BbsRenewalTemplateGroupEntity> implements IBbsRenewalTemplateGroupService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalTemplateGroupMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsRenewalTemplateGroupService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsRenewalTemplateGroupVo vo) {
        if(vo == null) {
            return null;
        }

        BbsRenewalTemplateGroupEntity entity = new BbsRenewalTemplateGroupEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setTemplateGroupId(null);
        if(!baseService.insert(entity)) {
            log.error("通知模板组表新增失败:" + entity.toString());
            throw new McpException("通知模板组表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getTemplateGroupId(),1)) {
                log.error("通知模板组表新增后保存历史失败:" + entity.toString());
                throw new McpException("通知模板组表新增后保存历史失败");
            }

            log.debug("通知模板组表新增成功:"+entity.getTemplateGroupId());
            return entity.getTemplateGroupId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsRenewalTemplateGroupVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsRenewalTemplateGroupEntity> entityList = new ArrayList<>();
        for (BbsRenewalTemplateGroupVo item:voList) {
            BbsRenewalTemplateGroupEntity entity = new BbsRenewalTemplateGroupEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsRenewalTemplateGroupEntity item:entityList){
            item.setTemplateGroupId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("通知模板组表新增失败");
            throw new McpException("通知模板组表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsRenewalTemplateGroupEntity::getTemplateGroupId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("通知模板组表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("通知模板组表批量新增后保存历史失败");
            }

            log.debug("通知模板组表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param templateGroupId 需要删除的模板组id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String templateGroupId) {
        if(!StringUtils.isEmpty(templateGroupId)) {
            if(!baseService.saveOperationHisById(templateGroupId,3)) {
                log.error("通知模板组表删除后保存历史失败:" + templateGroupId);
                throw new McpException("通知模板组表删除后保存历史失败");
            }

            if(!baseService.removeById(templateGroupId)) {
                log.error("通知模板组表删除失败");
                throw new McpException("通知模板组表删除失败"+templateGroupId);
            }
        } else {
            throw new McpException("通知模板组表删除失败模板组id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param templateGroupIdList 需要删除的模板组id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> templateGroupIdList) {
        if(!CollectionUtils.isEmpty(templateGroupIdList)) {
            int oldSize = templateGroupIdList.size();
            templateGroupIdList = templateGroupIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(templateGroupIdList) || oldSize != templateGroupIdList.size()) {
                throw new McpException("通知模板组表批量删除失败 存在主键id为空的记录"+StringUtils.join(templateGroupIdList));
            }

            if(!baseService.saveOperationHisByIds(templateGroupIdList,3)) {
                log.error("通知模板组表批量删除后保存历史失败:" + StringUtils.join(templateGroupIdList));
                throw new McpException("通知模板组表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(templateGroupIdList)) {
                log.error("通知模板组表批量删除失败");
                throw new McpException("通知模板组表批量删除失败"+StringUtils.join(templateGroupIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsRenewalTemplateGroupVo vo) {
        if(vo != null) {
            BbsRenewalTemplateGroupEntity entity = new BbsRenewalTemplateGroupEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getTemplateGroupId())) {
                throw new McpException("通知模板组表更新失败传入模板组id为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("通知模板组表更新失败");
                throw new McpException("通知模板组表更新失败"+entity.getTemplateGroupId());
            } else {
                if(!baseService.saveOperationHisById(entity.getTemplateGroupId(),2)) {
                    log.error("通知模板组表更新后保存历史失败:" + entity.getTemplateGroupId());
                    throw new McpException("通知模板组表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("通知模板组表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsRenewalTemplateGroupVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalTemplateGroupEntity> entityList = new ArrayList<>();

            for (BbsRenewalTemplateGroupVo item:voList){
                BbsRenewalTemplateGroupEntity entity = new BbsRenewalTemplateGroupEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getTemplateGroupId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("通知模板组表批量更新失败 存在模板组id为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("通知模板组表批量更新失败");
                throw new McpException("通知模板组表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getTemplateGroupId())).map(BbsRenewalTemplateGroupEntity::getTemplateGroupId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("通知模板组表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("通知模板组表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsRenewalTemplateGroupVo vo) {
        if(vo != null) {
            BbsRenewalTemplateGroupEntity entity = new BbsRenewalTemplateGroupEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("通知模板组表保存失败");
                throw new McpException("通知模板组表保存失败"+entity.getTemplateGroupId());
            } else {
                if(!baseService.saveOperationHisById(entity.getTemplateGroupId(),4)) {
                    log.error("通知模板组表保存后保存历史失败:" + entity.getTemplateGroupId());
                    throw new McpException("通知模板组表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("通知模板组表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的通知模板组表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsRenewalTemplateGroupVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalTemplateGroupEntity> entityList = new ArrayList<>();

            for (BbsRenewalTemplateGroupVo item:voList){
                BbsRenewalTemplateGroupEntity entity = new BbsRenewalTemplateGroupEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("通知模板组表批量保存失败");
                throw new McpException("通知模板组表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getTemplateGroupId())).map(BbsRenewalTemplateGroupEntity::getTemplateGroupId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("通知模板组表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("通知模板组表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param templateGroupId 需要查询的模板组id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsRenewalTemplateGroupVo selectByIdRecord(String templateGroupId) {
        BbsRenewalTemplateGroupVo vo = new BbsRenewalTemplateGroupVo();

        if(!StringUtils.isEmpty(templateGroupId)) {
            BbsRenewalTemplateGroupEntity entity = baseService.selectById(templateGroupId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsRenewalTemplateGroupPageResultVo>> selectByPageRecord(BbsRenewalTemplateGroupPageVo vo) {
        List<BbsRenewalTemplateGroupPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
