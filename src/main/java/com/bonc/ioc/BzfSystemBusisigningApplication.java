package com.bonc.ioc;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

@EnableDiscoveryClient
@EnableFeignClients
@ComponentScan(value = {"com.bonc.ioc", "cn.hutool.extra.spring"})
@MapperScan("com.bonc.ioc.bzf.busisigning.dao")
@SpringBootApplication
@EnableScheduling
public class BzfSystemBusisigningApplication {
	public static void main(String[] args) {
		SpringApplication.run(BzfSystemBusisigningApplication.class, args);
	}
	@Bean(name="CCRestTemplate")
	public RestTemplate restTemplate(RestTemplateBuilder builder) {
		return builder.build();
	}

}
