# 商业违约金系统开发计划

## 1. 项目概述

### 1.1 项目背景
开发商业违约金管理系统，实现对超期未缴费账单的违约金计算、管理和处置功能。

### 1.2 核心功能
- 违约金试算账单管理
- 违约金计费完结账单管理  
- 违约金台账管理
- 定时任务自动计算违约金
- 违约金处置审批流程

## 2. 功能模块分解

### 2.1 违约金试算账单模块
**功能描述**: 每日定时计算超期账单的违约金

**核心特性**:
- 定时任务执行（每日凌晨）
- 断点续传功能
- 分阶段违约金计算
- 试算账单列表展示
- 违约金明细查看

**主要字段**:
账单周期、项目名称、房源地址、商户名称、应缴费日期、应缴金额、实际收款金额、未缴金额、逾期天数、每日逾期率、违约金总金额、关联账单唯一识别编码、关联费用项目、合同编号、创建时间

### 2.2 违约金计费完结账单模块  
**功能描述**: 管理已完结的违约金账单

**核心特性**:
- 已足额支付账单自动转入
- 退租办结账单自动转入
- 违约金处置选择功能
- 完结账单列表展示

**主要字段**:
账单周期、项目名称、房源地址、商户名称、应缴费日期、应缴金额、实际缴费日期、实际收款金额、未缴金额、逾期天数、每日逾期率、违约金总金额、减免金额、生成违约金账单金额、关联账单唯一识别编码、关联费用项目、合同编号、创建时间、关联违约金处置编号、完结原因、处置状态、处置类型

### 2.3 违约金台账模块
**功能描述**: 管理违约金处置流程

**核心特性**:
- 违约金处置申请
- 审批流程管理
- 处置记录台账

**主要字段**:
违约金处置编号、状态(0-未通过、1-审批中、2-已通过)、项目名称、房源地址、商户名称、合同编号、处置类型、减免金额、生成金额、创建人、创建时间、违约金处置依据文件id、违约金处置依据说明

## 3. 技术方案

### 3.1 技术栈
- **后端框架**: Spring Boot
- **数据库**: MySQL
- **定时任务**: Spring Task
- **工作流**: 现有工作流系统
- **文件处理**: 现有文件管理系统

### 3.2 架构设计
```
Controller层 -> Service层 -> DAO层 -> 数据库
              ↓
        定时任务调度器
              ↓
        工作流集成
```

### 3.3 违约金计算规则实现
1. **计费开始日期**: 账单账期首日（0点）
2. **计费截止日期**: 
   - 已缴费：银行回单日
   - 退租未缴完：实际退租日期
3. **分阶段计算**: 按部分缴费时间点分段计算
4. **逾期率**: 从合同获取（0.05%/天）

## 4. 数据库设计

### 4.1 违约金试算账单表 (penalty_trial_bill)
```sql
CREATE TABLE penalty_trial_bill (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    bill_cycle VARCHAR(20) COMMENT '账单周期',
    project_name VARCHAR(100) COMMENT '项目名称',
    house_address VARCHAR(200) COMMENT '房源地址',
    merchant_name VARCHAR(100) COMMENT '商户名称',
    due_date DATE COMMENT '应缴费日期',
    due_amount DECIMAL(12,2) COMMENT '应缴金额',
    actual_amount DECIMAL(12,2) COMMENT '实际收款金额',
    unpaid_amount DECIMAL(12,2) COMMENT '未缴金额',
    overdue_days INT COMMENT '逾期天数',
    daily_penalty_rate DECIMAL(8,6) COMMENT '每日逾期率',
    total_penalty_amount DECIMAL(12,2) COMMENT '违约金总金额',
    related_bill_code VARCHAR(50) COMMENT '关联账单唯一识别编码',
    related_fee_item VARCHAR(100) COMMENT '关联费用项目',
    contract_no VARCHAR(50) COMMENT '合同编号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status TINYINT DEFAULT 1 COMMENT '状态：1-试算中，2-已完结'
);
```

### 4.2 违约金计费完结账单表 (penalty_final_bill)
```sql
CREATE TABLE penalty_final_bill (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    trial_bill_id BIGINT COMMENT '关联试算账单ID',
    bill_cycle VARCHAR(20) COMMENT '账单周期',
    project_name VARCHAR(100) COMMENT '项目名称',
    house_address VARCHAR(200) COMMENT '房源地址',
    merchant_name VARCHAR(100) COMMENT '商户名称',
    due_date DATE COMMENT '应缴费日期',
    due_amount DECIMAL(12,2) COMMENT '应缴金额',
    actual_pay_date DATE COMMENT '实际缴费日期',
    actual_amount DECIMAL(12,2) COMMENT '实际收款金额',
    unpaid_amount DECIMAL(12,2) COMMENT '未缴金额',
    overdue_days INT COMMENT '逾期天数',
    daily_penalty_rate DECIMAL(8,6) COMMENT '每日逾期率',
    total_penalty_amount DECIMAL(12,2) COMMENT '违约金总金额',
    reduction_amount DECIMAL(12,2) DEFAULT 0 COMMENT '减免金额',
    generate_penalty_amount DECIMAL(12,2) COMMENT '生成违约金账单金额',
    related_bill_code VARCHAR(50) COMMENT '关联账单唯一识别编码',
    related_fee_item VARCHAR(100) COMMENT '关联费用项目',
    contract_no VARCHAR(50) COMMENT '合同编号',
    penalty_disposal_no VARCHAR(50) COMMENT '关联违约金处置编号',
    final_reason VARCHAR(100) COMMENT '完结原因',
    disposal_status TINYINT COMMENT '处置状态',
    disposal_type VARCHAR(50) COMMENT '处置类型',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 4.3 违约金台账表 (penalty_ledger)
```sql
CREATE TABLE penalty_ledger (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    disposal_no VARCHAR(50) UNIQUE COMMENT '违约金处置编号',
    status TINYINT COMMENT '状态：0-未通过，1-审批中，2-已通过',
    project_name VARCHAR(100) COMMENT '项目名称',
    house_address VARCHAR(200) COMMENT '房源地址',
    merchant_name VARCHAR(100) COMMENT '商户名称',
    contract_no VARCHAR(50) COMMENT '合同编号',
    disposal_type VARCHAR(50) COMMENT '处置类型',
    reduction_amount DECIMAL(12,2) COMMENT '减免金额',
    generate_amount DECIMAL(12,2) COMMENT '生成金额',
    creator VARCHAR(50) COMMENT '创建人',
    disposal_basis_file_id VARCHAR(50) COMMENT '违约金处置依据文件id',
    disposal_basis_desc TEXT COMMENT '违约金处置依据说明',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 4.4 违约金明细表 (penalty_detail)
```sql
CREATE TABLE penalty_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    trial_bill_id BIGINT COMMENT '试算账单ID',
    stage_no INT COMMENT '阶段序号',
    start_date DATE COMMENT '计费开始日期',
    end_date DATE COMMENT '计费结束日期',
    unpaid_amount DECIMAL(12,2) COMMENT '未缴金额',
    overdue_days INT COMMENT '逾期天数',
    daily_penalty_rate DECIMAL(8,6) COMMENT '每日逾期率',
    stage_penalty_amount DECIMAL(12,2) COMMENT '阶段违约金',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 5. API设计

### 5.1 违约金试算账单API
```java
// 查询试算账单列表
GET /api/penalty/trial/list
// 查看违约金明细
GET /api/penalty/trial/{id}/detail
// 手动触发计算
POST /api/penalty/trial/calculate
```

### 5.2 违约金完结账单API
```java
// 查询完结账单列表
GET /api/penalty/final/list
// 批量选择处置
POST /api/penalty/final/dispose
// 转移到台账
POST /api/penalty/final/transfer
```

### 5.3 违约金台账API
```java
// 查询台账列表
GET /api/penalty/ledger/list
// 创建处置申请
POST /api/penalty/ledger/create
// 更新处置状态
PUT /api/penalty/ledger/{id}/status
```

## 6. 开发阶段规划

### 第一阶段：基础设施搭建（3天）
- [ ] 数据库表结构设计与创建
- [ ] 基础实体类和Mapper创建
- [ ] 基础Service和Controller框架搭建

### 第二阶段：违约金计算核心逻辑（5天）
- [ ] 违约金计算规则实现
- [ ] 分阶段计算逻辑开发
- [ ] 定时任务调度器开发
- [ ] 断点续传功能实现

### 第三阶段：试算账单管理（4天）
- [ ] 试算账单列表查询
- [ ] 违约金明细查看功能
- [ ] 试算账单状态管理
- [ ] 手动触发计算功能

### 第四阶段：完结账单管理（4天）
- [ ] 完结账单自动转换逻辑
- [ ] 完结账单列表查询
- [ ] 批量处置选择功能
- [ ] 完结原因记录

### 第五阶段：违约金台账管理（5天）
- [ ] 台账创建和管理
- [ ] 工作流集成
- [ ] 审批状态跟踪
- [ ] 处置依据文件上传

### 第六阶段：系统集成与测试（4天）
- [ ] 与工银账单系统集成
- [ ] 与合同系统逾期率获取集成
- [ ] 系统测试和Bug修复
- [ ] 性能优化

## 7. 开发任务分配

### 7.1 后端开发（2人）
**开发者A**:
- 违约金计算核心逻辑
- 定时任务开发
- 试算账单管理

**开发者B**:
- 完结账单管理
- 违约金台账管理
- 工作流集成

### 7.2 前端开发（1人）
- 三个主要页面开发
- 违约金明细弹窗
- 处置功能界面

### 7.3 测试（1人）
- 功能测试
- 集成测试
- 性能测试

## 8. 风险评估与应对

### 8.1 技术风险
**风险**: 违约金分阶段计算逻辑复杂
**应对**: 详细梳理计算规则，编写完整单元测试

**风险**: 定时任务性能问题
**应对**: 分批处理，增加监控和报警

### 8.2 集成风险
**风险**: 与工银系统集成可能存在数据不一致
**应对**: 增加数据校验和异常处理机制

**风险**: 工作流系统集成复杂度
**应对**: 提前与工作流系统负责人沟通，制定详细集成方案

## 9. 时间计划

| 阶段 | 时间安排 | 里程碑 |
|------|----------|--------|
| 第一阶段 | 第1-3天 | 基础架构完成 |
| 第二阶段 | 第4-8天 | 违约金计算逻辑完成 |
| 第三阶段 | 第9-12天 | 试算账单功能完成 |
| 第四阶段 | 第13-16天 | 完结账单功能完成 |
| 第五阶段 | 第17-21天 | 台账管理功能完成 |
| 第六阶段 | 第22-25天 | 系统集成测试完成 |

**项目总工期**: 25个工作日（约5周）

## 10. 验收标准

### 10.1 功能验收
- [ ] 定时任务正常执行违约金计算
- [ ] 违约金计算结果准确
- [ ] 三个主要功能模块正常运行
- [ ] 工作流集成正常

### 10.2 性能验收
- [ ] 定时任务在合理时间内完成
- [ ] 页面响应时间<3秒
- [ ] 支持并发用户操作

### 10.3 质量验收
- [ ] 代码覆盖率>80%
- [ ] 无严重和高优先级Bug
- [ ] 系统稳定运行7天以上

## 11. 后续优化建议

1. **数据归档**: 建立历史数据归档机制
2. **报表分析**: 增加违约金统计分析报表
3. **预警机制**: 建立逾期预警通知
4. **移动端**: 考虑开发移动端查看功能
5. **AI优化**: 未来可考虑AI预测逾期风险