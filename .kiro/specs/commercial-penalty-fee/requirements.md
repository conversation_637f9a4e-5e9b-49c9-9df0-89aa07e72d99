# 商业违约金系统需求文档

## 1. 项目概述

### 1.1 项目背景
商业违约金系统是一个基于 Spring Boot 的微服务应用，用于处理逾期付款的违约金计算和管理。系统需要与工银账单系统、合同中心、缴费中心等外部系统集成，实现违约金的自动计算、审批流程管理和台账记录。

### 1.2 项目目标
- 实现违约金的自动化计算和管理
- 提供完整的违约金处置审批流程
- 支持违约金计算的精确调整和红冲机制
- 提供完整的数据追溯和审计功能
- 支持批量处理和断点续传功能

## 2. 功能需求

### 2.1 违约金试算管理

#### 2.1.1 试算账单创建
- **需求描述**：系统每日自动查询逾期账单，创建违约金试算账单
- **输入**：工银账单系统的逾期账单数据
- **输出**：违约金试算账单记录
- **业务规则**：
  - 只处理逾期且未完结的账单
  - 支持多种收费科目的违约金计算
  - 按每日逾期率计算违约金

#### 2.1.2 试算账单查询
- **需求描述**：提供试算账单的分页查询功能
- **查询条件**：项目ID、租户信息、合同编号、账单状态等
- **输出**：试算账单列表，包含基础信息和违约金汇总

#### 2.1.3 试算账单详情
- **需求描述**：查看单个试算账单的详细信息
- **功能包含**：
  - 账单基础信息
  - 违约金计算明细
  - 分阶段汇总信息
  - 每日计算明细

### 2.2 违约金计算引擎

#### 2.2.1 每日违约金计算
- **需求描述**：每日定时计算所有试算账单的违约金
- **计算逻辑**：
  - 基于未缴金额和每日逾期率计算
  - 考虑收款单的对账状态
  - 支持分阶段计算（收款前后不同基数）
- **输出**：每日违约金明细记录

#### 2.2.2 对账状态变化处理
- **需求描述**：当收款单对账状态变化时，自动调整违约金计算
- **处理方式**：
  - 采用红冲调整机制
  - 只调整差额部分，保持历史记录
  - 记录调整原因和依据

#### 2.2.3 违约金汇总计算
- **需求描述**：实时计算试算账单的违约金汇总信息
- **汇总内容**：
  - 总违约金金额
  - 逾期天数
  - 违约金计算截止日期
  - 分阶段汇总信息

### 2.3 完结账单管理

#### 2.3.1 账单完结
- **需求描述**：将满足条件的试算账单移至完结状态
- **完结条件**：
  - 已缴足额支付
  - 退租办结
- **完结方式**：
  - 手动完结
  - 自动完结（基于业务规则）

#### 2.3.2 完结账单查询
- **需求描述**：提供完结账单的分页查询功能
- **查询条件**：项目信息、租户信息、完结原因、处置状态等
- **输出**：完结账单列表

#### 2.3.3 完结账单处置
- **需求描述**：对完结账单进行处置操作
- **处置类型**：
  - 减免违约金
  - 自动生成违约金账单
- **处置状态**：未处置 → 处置中 → 已处置

### 2.4 违约金台账管理

#### 2.4.1 台账创建
- **需求描述**：基于违约金处置编号创建台账记录
- **数据来源**：多个完结账单按处置编号汇总
- **台账内容**：
  - 处置编号和基础信息
  - 处置类型和金额信息
  - 处置依据文件和说明

#### 2.4.2 台账审批流程
- **需求描述**：提供完整的台账审批流程
- **审批状态**：
  - 0-暂存：数据已保存但未提交审批
  - 1-审批中：正在进行审批流程
  - 2-已通过：审批通过可以执行
  - 3-未通过：审批未通过或被驳回

#### 2.4.3 台账查询和导出
- **需求描述**：提供台账的查询和导出功能
- **查询条件**：项目信息、处置类型、审批状态等
- **导出格式**：Excel格式

### 2.5 系统管理功能

#### 2.5.1 定时任务管理
- **需求描述**：管理违约金计算的定时任务
- **功能包含**：
  - 每日违约金计算任务
  - 对账状态变化检查任务
  - 自动完结检查任务
- **任务特性**：
  - 支持断点续传
  - 异常处理和重试机制
  - 任务执行状态记录

#### 2.5.2 接口调用监控
- **需求描述**：监控外部接口的调用情况
- **监控内容**：
  - 接口调用日志
  - 调用耗时统计
  - 成功率监控
  - 异常信息记录

## 3. 非功能需求

### 3.1 性能需求
- **响应时间**：页面查询响应时间 < 3秒
- **并发处理**：支持100个并发用户
- **批量处理**：支持单次处理1000个账单
- **数据量**：支持百万级账单数据

### 3.2 可靠性需求
- **系统可用性**：99.9%
- **数据一致性**：确保违约金计算的准确性
- **故障恢复**：支持断点续传和任务重启
- **数据备份**：定期数据备份和恢复

### 3.3 安全需求
- **访问控制**：基于角色的权限控制
- **数据安全**：敏感数据加密存储
- **审计日志**：完整的操作审计记录
- **接口安全**：API接口鉴权和限流

### 3.4 可维护性需求
- **代码规范**：遵循Java编码规范
- **文档完整**：提供完整的技术文档
- **日志记录**：详细的业务和技术日志
- **监控告警**：关键指标监控和告警

## 4. 接口需求

### 4.1 外部接口集成

#### 4.1.1 工银账单系统
- **账单查询接口**：查询逾期账单信息
- **收款单查询接口**：查询收款单和对账状态
- **数据格式**：JSON格式
- **调用频率**：每日定时调用

#### 4.1.2 合同中心系统
- **合同信息查询**：获取合同基础信息
- **逾期率配置查询**：获取合同的违约金率配置
- **调用方式**：RESTful API

#### 4.1.3 缴费中心系统
- **缴费状态查询**：获取账单的缴费状态
- **对账状态通知**：接收对账状态变化通知
- **集成方式**：消息队列或API调用

### 4.2 内部接口设计

#### 4.2.1 试算账单接口
- **查询接口**：分页查询、详情查询、汇总查询
- **操作接口**：完结操作、状态更新
- **数据格式**：统一的JSON响应格式

#### 4.2.2 完结账单接口
- **查询接口**：分页查询、详情查询
- **处置接口**：批量处置、单个处置
- **台账接口**：移至台账操作

#### 4.2.3 台账管理接口
- **查询接口**：分页查询、详情查询
- **审批接口**：提交审批、审批操作
- **导出接口**：数据导出功能

## 5. 数据需求

### 5.1 数据模型
- **7个核心业务表**：试算账单、完结账单、台账、每日明细、分阶段汇总、任务状态、接口日志
- **数据关系**：一对一、一对多、多对一关系
- **数据完整性**：外键约束和业务规则约束

### 5.2 数据存储
- **数据库**：MySQL 8.0
- **缓存**：Redis (Redisson)
- **数据量预估**：
  - 试算账单：10万条/年
  - 每日明细：100万条/年
  - 完结账单：5万条/年

### 5.3 数据安全
- **数据加密**：敏感字段加密存储
- **访问控制**：数据库访问权限控制
- **数据备份**：定期备份和归档
- **数据清理**：历史数据定期清理策略

## 6. 部署需求

### 6.1 环境要求
- **操作系统**：Linux (CentOS 7+)
- **Java版本**：JDK 8+
- **数据库**：MySQL 8.0
- **缓存**：Redis 6.0+
- **应用服务器**：内置Tomcat

### 6.2 部署架构
- **微服务架构**：独立部署的微服务
- **负载均衡**：支持多实例部署
- **服务发现**：Spring Cloud服务注册发现
- **配置管理**：外部化配置管理

### 6.3 监控需求
- **应用监控**：Spring Boot Actuator
- **性能监控**：JVM性能指标
- **业务监控**：关键业务指标
- **日志监控**：ELK日志分析

## 7. 验收标准

### 7.1 功能验收
- **核心功能**：违约金计算、账单管理、台账审批功能正常
- **接口集成**：外部系统集成正常，数据同步准确
- **异常处理**：各种异常情况处理正确
- **性能指标**：满足性能需求指标

### 7.2 质量验收
- **代码质量**：代码覆盖率 > 80%
- **文档完整**：技术文档和用户文档完整
- **安全测试**：通过安全漏洞扫描
- **压力测试**：通过性能压力测试

### 7.3 部署验收
- **环境部署**：开发、测试、生产环境部署成功
- **数据迁移**：历史数据迁移正确
- **监控告警**：监控系统正常运行
- **备份恢复**：备份恢复机制验证通过