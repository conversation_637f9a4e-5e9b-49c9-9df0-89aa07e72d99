{"recommendations": ["alphabotsec.vscode-eclipse-keybindings", "dbaeumer.vscode-eslint", "donjayamanne.python-environment-manager", "formulahendry.terminal", "johnstoncode.svn-scm", "labring.devbox-ai", "ms-ceintl.vscode-language-pack-zh-hans", "ms-python.debugpy", "ms-python.python", "ms-python.vscode-pylance", "ms-toolsai.jupyter", "ms-toolsai.jupyter-renderers", "ms-toolsai.vscode-jupyter-cell-tags", "ms-toolsai.vscode-jupyter-slideshow", "ms-vscode-remote.remote-ssh", "ms-vscode-remote.remote-ssh-edit", "ms-vscode.live-server", "ms-vscode.remote-explorer", "octref.vetur", "roysun.vuehelper", "redhat.java", "redhat.vscode-yaml", "ritwickdey.liveserver", "visualstudioexptteam.intellicode-api-usage-examples", "visualstudioexptteam.vscodeintellicode", "vmware.vscode-boot-dev-pack", "vmware.vscode-spring-boot", "vscjava.vscode-gradle", "vscjava.vscode-java-debug", "vscjava.vscode-java-dependency", "vscjava.vscode-java-pack", "vscjava.vscode-java-test", "vscjava.vscode-maven", "vscjava.vscode-spring-boot-dashboard", "vscjava.vscode-spring-initializr", "vue.volar", "zignd.html-css-class-completion"]}