-- 商业违约金系统测试数据
-- 基于账单接口返回数据生成的样例数据

-- 1. 违约金试算账单表 (bbpm_penalty_fee_trial_bill)
INSERT INTO `bbpm_penalty_fee_trial_bill` (
    `trial_bill_id`, `bill_id`, `bill_cycle`, `project_name`, `house_name`, 
    `tenant_code`, `tenant_name`, `contract_code`, `charge_subject_begin_date`, 
    `charge_subject_end_date`, `charge_subject_period`, `payable_date`, 
    `should_pay_amount`, `payed_amount`, `replace_pay_amount`, `overdue_days`, 
    `daily_overdue_rate`, `total_penalty_amount`, `bill_charge_subject`, 
    `bill_status`, `account_status`, `status`, `del_flag`, `project_id`, 
    `create_user`, `create_time`, `modify_user`, `modify_time`, `tenant_id`, 
    `cid`, `object_version_number`
) VALUES 
(
    'TRIAL_001', '1947489066907766817', '第14期(2026-08-22至2026-09-21)', 
    '（商）燕保·百安家园项目', '汤臣10品001号楼01单元0507', 
    'PC-4c8722e33cfb4f29953f882cae086b34', '吴亚宁', 'YX-YB-250722-00001-00', 
    '2026-08-22', '2026-09-21', 14, '2026-08-22', 
    5000.00, 0.00, 5000.00, 30, 
    0.05, 75.00, '01', 
    '03', '02', '1', '1', '0303H50025', 
    'system', NOW(), 'system', NOW(), 1, 
    1, 1
),
(
    'TRIAL_002', '1947489066907766818', '第15期(2026-09-22至2026-10-21)', 
    '（商）燕保·百安家园项目', '汤臣10品001号楼01单元0508', 
    'PC-4c8722e33cfb4f29953f882cae086b35', '张三', 'YX-YB-250722-00002-00', 
    '2026-09-22', '2026-10-21', 15, '2026-09-22', 
    4500.00, 2000.00, 2500.00, 15, 
    0.05, 18.75, '01', 
    '02', '02', '1', '1', '0303H50025', 
    'system', NOW(), 'system', NOW(), 1, 
    2, 1
),
(
    'TRIAL_003', '1947489066907766819', '第16期(2026-10-22至2026-11-21)', 
    '（商）燕保·百安家园项目', '汤臣10品002号楼02单元0201', 
    'PC-4c8722e33cfb4f29953f882cae086b36', '李四', 'YX-YB-250722-00003-00', 
    '2026-10-22', '2026-11-21', 16, '2026-10-22', 
    6000.00, 6000.00, 0.00, 0, 
    0.05, 0.00, '01', 
    '01', '01', '2', '1', '0303H50025', 
    'system', NOW(), 'system', NOW(), 1, 
    3, 1
);

-- 2. 违约金计费完结账单表 (bbpm_penalty_fee_finalized_bill)
INSERT INTO `bbpm_penalty_fee_finalized_bill` (
    `finalized_bill_id`, `trial_bill_id`, `bill_id`, `bill_cycle`, `project_name`, 
    `house_name`, `tenant_code`, `tenant_name`, `contract_code`, 
    `charge_subject_begin_date`, `charge_subject_end_date`, `charge_subject_period`, 
    `payable_date`, `should_pay_amount`, `payed_amount`, `actual_pay_date`, 
    `replace_pay_amount`, `overdue_days`, `daily_overdue_rate`, `total_penalty_amount`, 
    `reduction_amount`, `bill_generation_amount`, `bill_charge_subject`, 
    `finalized_reason`, `finalized_date`, `penalty_disposal_no`, `disposal_status`, 
    `disposal_type`, `process_time`, `process_remark`, `del_flag`, `project_id`, 
    `create_user`, `create_time`, `modify_user`, `modify_time`, `tenant_id`, 
    `cid`, `object_version_number`
) VALUES 
(
    'FINAL_001', 'TRIAL_003', '1947489066907766819', '第16期(2026-10-22至2026-11-21)', 
    '（商）燕保·百安家园项目', '汤臣10品002号楼02单元0201', 
    'PC-4c8722e33cfb4f29953f882cae086b36', '李四', 'YX-YB-250722-00003-00', 
    '2026-10-22', '2026-11-21', 16, '2026-10-22', 
    6000.00, 6000.00, '2026-10-22', 0.00, 0, 
    0.05, 0.00, 0.00, 0.00, '01', 
    '1', '2026-10-22', 'PD202610220001', '3', 
    '2', NOW(), '账单已足额缴费，无违约金', '1', '0303H50025', 
    'system', NOW(), 'system', NOW(), 1, 
    1, 1
),
(
    'FINAL_002', NULL, '1947489066907766820', '第13期(2026-07-22至2026-08-21)', 
    '（商）燕保·百安家园项目', '汤臣10品003号楼01单元0301', 
    'PC-4c8722e33cfb4f29953f882cae086b37', '王五', 'YX-YB-250722-00004-00', 
    '2026-07-22', '2026-08-21', 13, '2026-07-22', 
    5500.00, 3000.00, '2026-08-15', 2500.00, 24, 
    0.05, 30.00, 10.00, 20.00, '01', 
    '1', '2026-08-15', 'PD202608150001', '1', 
    NULL, NULL, NULL, '1', '0303H50025', 
    'system', NOW(), 'system', NOW(), 1, 
    2, 1
);

-- 3. 违约金台账表 (bbpm_penalty_fee_ledger)
INSERT INTO `bbpm_penalty_fee_ledger` (
    `ledger_id`, `penalty_disposal_no`, `status`, `project_id`, `project_name`, 
    `tenant_code`, `tenant_name`, `house_name`, `contract_code`, `disposal_type`, 
    `reduction_amount`, `bill_generation_amount`, `disposal_basis_file_id`, 
    `disposal_basis_description`, `del_flag`, `create_user`, `create_time`, 
    `modify_user`, `modify_time`, `tenant_id`, `cid`, `object_version_number`
) VALUES 
(
    'LEDGER_001', 'PD202608150001', 1, '0303H50025', '（商）燕保·百安家园项目', 
    'PC-4c8722e33cfb4f29953f882cae086b37', '王五', '汤臣10品003号楼01单元0301', 
    'YX-YB-250722-00004-00', '1', 10.00, 20.00, 'FILE_001,FILE_002', 
    '租户因疫情影响导致延期缴费，申请减免部分违约金', '1', 'admin', NOW(), 
    'admin', NOW(), 1, 1, 1
),
(
    'LEDGER_002', 'PD202610220001', 2, '0303H50025', '（商）燕保·百安家园项目', 
    'PC-4c8722e33cfb4f29953f882cae086b36', '李四', '汤臣10品002号楼02单元0201', 
    'YX-YB-250722-00003-00', '2', 0.00, 0.00, NULL, 
    '账单已足额缴费，无违约金产生', '1', 'admin', NOW(), 
    'admin', NOW(), 1, 2, 1
);

-- 4. 违约金每日计算明细表 (bbpm_penalty_fee_daily_detail)
INSERT INTO `bbpm_penalty_fee_daily_detail` (
    `daily_detail_id`, `bill_id`, `calculation_date`, `replace_pay_amount`, 
    `daily_overdue_rate`, `daily_penalty_amount`, `entry_type`, `adjustment_reason`, 
    `bill_status`, `account_status`, `charge_time`, `original_entry_id`, 
    `adjustment_seq`, `del_flag`, `project_id`, `create_user`, `create_time`, 
    `modify_user`, `modify_time`, `tenant_id`, `cid`, `object_version_number`
) VALUES 
-- 账单1947489066907766817的每日明细（连续30天逾期）
('DAILY_001_001', '1947489066907766817', '2026-08-23', 5000.00, 0.05, 2.50, '1', NULL, '03', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 1, 1),
('DAILY_001_002', '1947489066907766817', '2026-08-24', 5000.00, 0.05, 2.50, '1', NULL, '03', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 2, 1),
('DAILY_001_003', '1947489066907766817', '2026-08-25', 5000.00, 0.05, 2.50, '1', NULL, '03', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 3, 1),
('DAILY_001_004', '1947489066907766817', '2026-08-26', 5000.00, 0.05, 2.50, '1', NULL, '03', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 4, 1),
('DAILY_001_005', '1947489066907766817', '2026-08-27', 5000.00, 0.05, 2.50, '1', NULL, '03', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 5, 1),

-- 账单1947489066907766818的每日明细（部分缴费后的逾期）
('DAILY_002_001', '1947489066907766818', '2026-09-23', 4500.00, 0.05, 2.25, '1', NULL, '03', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 6, 1),
('DAILY_002_002', '1947489066907766818', '2026-09-24', 4500.00, 0.05, 2.25, '1', NULL, '03', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 7, 1),
('DAILY_002_003', '1947489066907766818', '2026-09-25', 4500.00, 0.05, 2.25, '1', NULL, '03', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 8, 1),
-- 部分缴费后的调整
('DAILY_002_004', '1947489066907766818', '2026-10-01', 2500.00, 0.05, 1.25, '1', NULL, '02', '02', '2026-10-01', NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 9, 1),
('DAILY_002_005', '1947489066907766818', '2026-10-02', 2500.00, 0.05, 1.25, '1', NULL, '02', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 10, 1),

-- 账单1947489066907766819的每日明细（及时缴费，无违约金）
('DAILY_003_001', '1947489066907766819', '2026-10-22', 0.00, 0.05, 0.00, '1', NULL, '01', '01', '2026-10-22', NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 11, 1),

-- 账单1947489066907766820的每日明细（已完结账单的历史明细）
('DAILY_004_001', '1947489066907766820', '2026-07-23', 5500.00, 0.05, 2.75, '1', NULL, '03', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 12, 1),
('DAILY_004_002', '1947489066907766820', '2026-07-24', 5500.00, 0.05, 2.75, '1', NULL, '03', '02', NULL, NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 13, 1),
-- 部分缴费调整
('DAILY_004_003', '1947489066907766820', '2026-08-15', 2500.00, 0.05, 1.25, '1', NULL, '02', '01', '2026-08-15', NULL, 1, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 14, 1);

-- 5. 违约金分阶段汇总表 (bbpm_penalty_fee_stage_summary)
INSERT INTO `bbpm_penalty_fee_stage_summary` (
    `stage_summary_id`, `trial_bill_id`, `penalty_start_date`, `penalty_end_date`, 
    `overdue_days`, `daily_overdue_rate`, `unpaid_amount`, `stage_penalty_amount`, 
    `del_flag`, `project_id`, `create_user`, `create_time`, `modify_user`, 
    `modify_time`, `tenant_id`, `cid`, `object_version_number`
) VALUES 
-- 账单TRIAL_001的分阶段汇总
('STAGE_001_001', 'TRIAL_001', '2026-08-23', '2026-09-22', 30, 0.05, 5000.00, 75.00, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 1, 1),

-- 账单TRIAL_002的分阶段汇总（两个阶段：全额逾期和部分缴费后逾期）
('STAGE_002_001', 'TRIAL_002', '2026-09-23', '2026-09-30', 8, 0.05, 4500.00, 18.00, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 2, 1),
('STAGE_002_002', 'TRIAL_002', '2026-10-01', '2026-10-07', 7, 0.05, 2500.00, 8.75, '1', '0303H50025', 'system', NOW(), 'system', NOW(), 1, 3, 1);

-- 6. 任务执行状态表 (bbpm_penalty_task_status)
INSERT INTO `bbpm_penalty_task_status` (
    `task_status_id`, `task_date`, `contract_code`, `process_status`, `bill_count`, 
    `start_time`, `end_time`, `error_message`, `del_flag`, `project_id`, 
    `create_user`, `create_time`, `modify_user`, `modify_time`, `tenant_id`, 
    `cid`, `object_version_number`
) VALUES 
('TASK_001', '2026-08-23', 'YX-YB-250722-00001-00', '2', 1, '2026-08-23 01:00:00', '2026-08-23 01:05:30', NULL, 1, '0303H50025', 'system', NOW(), 'system', NOW(), 1, 1, 1),
('TASK_002', '2026-09-23', 'YX-YB-250722-00002-00', '2', 1, '2026-09-23 01:00:00', '2026-09-23 01:03:15', NULL, 1, '0303H50025', 'system', NOW(), 'system', NOW(), 1, 2, 1),
('TASK_003', '2026-10-22', 'YX-YB-250722-00003-00', '2', 1, '2026-10-22 01:00:00', '2026-10-22 01:02:45', NULL, 1, '0303H50025', 'system', NOW(), 'system', NOW(), 1, 3, 1),
('TASK_004', '2026-07-23', 'YX-YB-250722-00004-00', '2', 1, '2026-07-23 01:00:00', '2026-07-23 01:04:20', NULL, 1, '0303H50025', 'system', NOW(), 'system', NOW(), 1, 4, 1),
('TASK_005', '2026-11-01', 'YX-YB-250722-00005-00', '3', 0, '2026-11-01 01:00:00', '2026-11-01 01:00:30', '接口调用超时', 1, '0303H50025', 'system', NOW(), 'system', NOW(), 1, 5, 1);

-- 7. 接口调用记录表 (bbpm_penalty_api_call_log)
INSERT INTO `bbpm_penalty_api_call_log` (
    `api_call_log_id`, `task_status_id`, `task_date`, `contract_code`, `bill_id`, 
    `api_type`, `api_url`, `request_params`, `response_data`, `call_duration`, 
    `call_status`, `error_message`, `call_time`, `del_flag`, `project_id`, 
    `create_user`, `create_time`, `modify_user`, `modify_time`, `tenant_id`, 
    `cid`, `object_version_number`
) VALUES 
-- 账单接口调用记录
('API_001', 'TASK_001', '2026-08-23', 'YX-YB-250722-00001-00', NULL, '1', 
 '/api/bills/overdue', '{"contractNo":"YX-YB-250722-00001-00","queryDate":"2026-08-23"}', 
 '{"billId":"1947489066907766817","billNo":"1947489066907766817","contractCode":"YX-YB-250722-00001-00",...}', 
 1250, '1', NULL, '2026-08-23 01:00:15', 1, '0303H50025', 'system', NOW(), 'system', NOW(), 1, 1, 1),

-- 收款单接口调用记录
('API_002', 'TASK_001', '2026-08-23', 'YX-YB-250722-00001-00', '1947489066907766817', '2', 
 '/api/payments/by-bill', '{"billId":"1947489066907766817"}', 
 '[]', 850, '1', NULL, '2026-08-23 01:01:30', 1, '0303H50025', 'system', NOW(), 'system', NOW(), 1, 2, 1),

-- 失败的接口调用记录
('API_003', 'TASK_005', '2026-11-01', 'YX-YB-250722-00005-00', NULL, '1', 
 '/api/bills/overdue', '{"contractNo":"YX-YB-250722-00005-00","queryDate":"2026-11-01"}', 
 NULL, 30000, '2', '接口调用超时', '2026-11-01 01:00:15', 1, '0303H50025', 'system', NOW(), 'system', NOW(), 1, 3, 1);